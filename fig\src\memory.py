import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import sys

# 获取脚本所在目录
script_dir = os.path.dirname(os.path.abspath(__file__))

# 设置字体
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号
plt.rcParams['font.size'] = 28  # 增加默认字体大小

# 函数列表和对应的数据 - 按照init_func_map的顺序排列，但反向排序
functions = ['DS-Java', 'DG-Java', 'MD-Py', 'FC-Py', 'DV-Py', 'IR-Py', 'TN-Js', 'UL-Js', 'IS-Js', 'AC-Js']

# 每个函数的三段数据（Setup env.、Init.lang.、Load lib/code）
# 格式为 [Setup env., Init.lang., Load lib/code]
data = {
    # Node.js - 统一Bare层(Setup env.)为30，Lang层(Init.lang.)为120
    'AC-Js': [30, 120, 50],     
    'IS-Js': [30, 120, 65],     
    'UL-Js': [30, 120, 60],     
    'TN-Js': [30, 120, 40],     
    # Python - 统一Bare层(Setup env.)为35，Lang层(Init.lang.)为50
    'IR-Py': [35, 50, 360],     
    'DV-Py': [35, 50, 70],      
    'FC-Py': [35, 50, 120],     
    'MD-Py': [35, 50, 100],     
    # Java - 统一Bare层(Setup env.)为30，Lang层(Init.lang.)为175
    'DG-Java': [30, 175, 90],   
    'DS-Java': [30, 175, 110]   
}

# 创建图形和坐标轴
fig, ax = plt.subplots(figsize=(14, 10), dpi=400)  # 增加图表高度以适应更多函数

# 设置Y轴刻度（函数名）
ax.set_yticks(range(len(functions)))
ax.set_yticklabels(functions, fontsize=42)  # 增大字体

# 设置Y轴标签
ax.set_ylabel('Function', fontsize=46)  # 增大字体

# 设置X轴刻度标签字体大小
ax.tick_params(axis='x', labelsize=42)  # 增大字体

# 设置X轴范围
max_total = max([sum(data[func]) for func in functions])
ax.set_xlim(0, max_total * 1.05)  # 给最大值增加5%的空间

# 设置网格线（只显示水平网格线）
ax.grid(axis='x', linestyle='--', alpha=0.7)

# 绘制条形图
left_positions = np.zeros(len(functions))  # 记录每个函数的起始位置

# 颜色定义
colors = ['#F8E0A0', '#AAD9A5', '#F9A3A3']  # 黄色、绿色、红色

# 标签定义
labels = ['Setup env.', 'Init. lang.', 'Load lib/code']

# 绘制三段数据
for i, color in enumerate(colors):
    # 获取每个函数对应段的数据
    segment_data = [data[func][i] for func in functions]
    
    # 绘制条形图
    ax.barh(range(len(functions)), segment_data, left=left_positions, 
            height=0.6, color=color, edgecolor='black', linewidth=0.5, label=labels[i])
    
    # 更新下一段的起始位置
    left_positions += segment_data

# 添加图例
ax.legend(fontsize=42, frameon=True, edgecolor='black', borderpad=0.4, 
          handletextpad=0.5, loc='upper right', bbox_to_anchor=(1, 0.5))

# 调整布局
plt.tight_layout()

# 保存图像到脚本所在目录
output_path = os.path.join(script_dir, 'memory.png')
plt.savefig(output_path, bbox_inches='tight')
print(f"图表已保存至 {output_path}")