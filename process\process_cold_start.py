#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import csv
import glob
import pandas as pd
from collections import defaultdict

def process_cold_start_frequency():
    """
    分析result目录下所有CSV文件的冷启动频率，只输出LayerCache方法的结果
    """
    print("\n" + "=" * 80)
    print("LayerCache方法冷启动频率统计结果")
    print("=" * 80)
    
    # 获取result目录下所有CSV文件
    result_files = glob.glob('./result/*.csv')
    
    if not result_files:
        print("未找到任何结果文件！")
        return
    
    # LayerCache方法的结果
    layercache_results = []
    
    # 处理每个CSV文件
    for file_path in result_files:
        # 从文件名解析方法和beta值
        file_name = os.path.basename(file_path)
        parts = file_name.split('-')
        
        if len(parts) < 3:
            print(f"警告: 跳过格式不正确的文件名: {file_name}")
            continue
        
        method = parts[0]
        
        # 只处理LayerCache方法的文件
        if method.lower() != "layercache":
            continue
            
        beta = float(parts[1])
        
        try:
            # 读取CSV文件的第一行获取冷启动频率
            with open(file_path, 'r') as f:
                reader = csv.DictReader(f)
                first_row = next(reader)
                cold_start_frequency = float(first_row.get('cold_start_frequency', 0))
                
                # 将结果添加到列表中
                layercache_results.append({
                    'beta': beta,
                    'cold_start_frequency': cold_start_frequency,
                    'file_name': file_name
                })
        except Exception as e:
            print(f"处理文件 {file_name} 时出错: {e}")
    
    # 如果没有找到LayerCache方法的结果
    if not layercache_results:
        print("未找到LayerCache方法的结果文件！")
        return
    
    # 输出LayerCache方法的结果
    print("\n方法: LayerCache")
    print("-" * 60)
    print(f"{'Beta值':^10} | {'冷启动频率':^15} | {'文件名'}")
    print("-" * 60)
    
    # 按beta值排序
    sorted_results = sorted(layercache_results, key=lambda x: x['beta'])
    
    for result in sorted_results:
        print(f"{result['beta']:^10.2f} | {result['cold_start_frequency']:^15.4f} | {result['file_name']}")

if __name__ == "__main__":
    process_cold_start_frequency() 