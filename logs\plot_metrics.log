2025-07-17 21:25:54,691 - INFO - 开始运行性能指标绘图脚本
2025-07-17 21:25:54,692 - INFO - 使用输入目录: ./result/Telecom
2025-07-17 21:25:54,692 - INFO - 提取的文件名后缀: _Telecom
2025-07-17 21:25:54,692 - INFO - 使用所有提供的beta值: [0.5, 0.75, 1.0, 1.25, 1.5]
2025-07-17 21:25:54,692 - INFO - 冷启动比例图将保存为: ./fig/Telecom\cold_start_ratio_Telecom.png
2025-07-17 21:25:54,692 - INFO - 冷启动频率柱状图将保存为: ./fig/Telecom\bar_cold_start_frequency_Telecom.png
2025-07-17 21:25:54,693 - INFO - 冷启动频率折线图将保存为: ./fig/Telecom\line_cold_start_frequency_Telecom.png
2025-07-17 21:25:54,693 - INFO - 平均响应时间柱状图将保存为: ./fig/Telecom\bar_response_time_Telecom.png
2025-07-17 21:25:54,693 - INFO - 平均响应时间折线图将保存为: ./fig/Telecom\line_response_time_Telecom.png
2025-07-17 21:25:54,693 - INFO - 平均成本柱状图将保存为: ./fig/Telecom\bar_cost_Telecom.png
2025-07-17 21:25:54,694 - INFO - 平均成本折线图将保存为: ./fig/Telecom\line_cost_Telecom.png
2025-07-17 21:25:54,694 - INFO - 平均内存使用柱状图将保存为: ./fig/Telecom\bar_memory_usage_Telecom.png
2025-07-17 21:25:54,694 - INFO - 平均内存使用折线图将保存为: ./fig/Telecom\line_memory_usage_Telecom.png
2025-07-17 21:25:54,694 - INFO - 将加载beta值 [0.5, 0.75, 1.0, 1.25, 1.5] 的数据
2025-07-17 21:25:54,694 - INFO - 查找 beta=0.50 下的各方法结果文件
2025-07-17 21:25:54,695 - INFO - 在 beta=0.50  下找到的文件:
2025-07-17 21:25:54,695 - INFO -   LayerCache: ['./result/Telecom\\LayerCache-0.50-0.015.csv']
2025-07-17 21:25:54,695 - INFO -   FaaSCache: ['./result/Telecom\\FaaSCache-0.50-0.015.csv']
2025-07-17 21:25:54,695 - INFO -   OpenWhisk: ['./result/Telecom\\OpenWhisk-0.50-0.015.csv']
2025-07-17 21:25:54,695 - INFO -   CrossEdge: ['./result/Telecom\\CrossEdge-0.50-0.015.csv']
2025-07-17 21:25:54,695 - INFO - 成功加载 LayerCache 方法的 beta=0.50 数据
2025-07-17 21:25:54,696 - INFO - 成功加载 FaaSCache 方法的 beta=0.50 数据
2025-07-17 21:25:54,696 - INFO - 成功加载 OpenWhisk 方法的 beta=0.50 数据
2025-07-17 21:25:54,697 - INFO - 成功加载 CrossEdge 方法的 beta=0.50 数据
2025-07-17 21:25:54,697 - INFO - 查找 beta=0.75 下的各方法结果文件
2025-07-17 21:25:54,698 - INFO - 在 beta=0.75  下找到的文件:
2025-07-17 21:25:54,698 - INFO -   LayerCache: ['./result/Telecom\\LayerCache-0.75-0.015.csv']
2025-07-17 21:25:54,698 - INFO -   FaaSCache: ['./result/Telecom\\FaaSCache-0.75-0.015.csv']
2025-07-17 21:25:54,698 - INFO -   OpenWhisk: ['./result/Telecom\\OpenWhisk-0.75-0.015.csv']
2025-07-17 21:25:54,698 - INFO -   CrossEdge: ['./result/Telecom\\CrossEdge-0.75-0.015.csv']
2025-07-17 21:25:54,698 - INFO - 成功加载 LayerCache 方法的 beta=0.75 数据
2025-07-17 21:25:54,698 - INFO - 成功加载 FaaSCache 方法的 beta=0.75 数据
2025-07-17 21:25:54,698 - INFO - 成功加载 OpenWhisk 方法的 beta=0.75 数据
2025-07-17 21:25:54,698 - INFO - 成功加载 CrossEdge 方法的 beta=0.75 数据
2025-07-17 21:25:54,698 - INFO - 查找 beta=1.00 下的各方法结果文件
2025-07-17 21:25:54,700 - INFO - 在 beta=1.00  下找到的文件:
2025-07-17 21:25:54,700 - INFO -   LayerCache: ['./result/Telecom\\LayerCache-1.00-0.015.csv']
2025-07-17 21:25:54,700 - INFO -   FaaSCache: ['./result/Telecom\\FaaSCache-1.00-0.015.csv']
2025-07-17 21:25:54,700 - INFO -   OpenWhisk: ['./result/Telecom\\OpenWhisk-1.00-0.015.csv']
2025-07-17 21:25:54,700 - INFO -   CrossEdge: ['./result/Telecom\\CrossEdge-1.00-0.015.csv']
2025-07-17 21:25:54,700 - INFO - 成功加载 LayerCache 方法的 beta=1.00 数据
2025-07-17 21:25:54,700 - INFO - 成功加载 FaaSCache 方法的 beta=1.00 数据
2025-07-17 21:25:54,700 - INFO - 成功加载 OpenWhisk 方法的 beta=1.00 数据
2025-07-17 21:25:54,701 - INFO - 成功加载 CrossEdge 方法的 beta=1.00 数据
2025-07-17 21:25:54,701 - INFO - 查找 beta=1.25 下的各方法结果文件
2025-07-17 21:25:54,702 - INFO - 在 beta=1.25  下找到的文件:
2025-07-17 21:25:54,702 - INFO -   LayerCache: ['./result/Telecom\\LayerCache-1.25-0.015.csv']
2025-07-17 21:25:54,702 - INFO -   FaaSCache: ['./result/Telecom\\FaaSCache-1.25-0.015.csv']
2025-07-17 21:25:54,702 - INFO -   OpenWhisk: ['./result/Telecom\\OpenWhisk-1.25-0.015.csv']
2025-07-17 21:25:54,702 - INFO -   CrossEdge: ['./result/Telecom\\CrossEdge-1.25-0.015.csv']
2025-07-17 21:25:54,702 - INFO - 成功加载 LayerCache 方法的 beta=1.25 数据
2025-07-17 21:25:54,702 - INFO - 成功加载 FaaSCache 方法的 beta=1.25 数据
2025-07-17 21:25:54,703 - INFO - 成功加载 OpenWhisk 方法的 beta=1.25 数据
2025-07-17 21:25:54,703 - INFO - 成功加载 CrossEdge 方法的 beta=1.25 数据
2025-07-17 21:25:54,703 - INFO - 查找 beta=1.50 下的各方法结果文件
2025-07-17 21:25:54,704 - INFO - 在 beta=1.50  下找到的文件:
2025-07-17 21:25:54,704 - INFO -   LayerCache: ['./result/Telecom\\LayerCache-1.50-0.015.csv']
2025-07-17 21:25:54,704 - INFO -   FaaSCache: ['./result/Telecom\\FaaSCache-1.50-0.015.csv']
2025-07-17 21:25:54,704 - INFO -   OpenWhisk: ['./result/Telecom\\OpenWhisk-1.50-0.015.csv']
2025-07-17 21:25:54,704 - INFO -   CrossEdge: ['./result/Telecom\\CrossEdge-1.50-0.015.csv']
2025-07-17 21:25:54,704 - INFO - 成功加载 LayerCache 方法的 beta=1.50 数据
2025-07-17 21:25:54,705 - INFO - 成功加载 FaaSCache 方法的 beta=1.50 数据
2025-07-17 21:25:54,705 - INFO - 成功加载 OpenWhisk 方法的 beta=1.50 数据
2025-07-17 21:25:54,705 - INFO - 成功加载 CrossEdge 方法的 beta=1.50 数据
2025-07-17 21:25:55,294 - INFO - 冷启动频率图已保存为 './fig/Telecom\bar_cold_start_frequency_Telecom.png'
2025-07-17 21:25:55,396 - INFO - 已绘制冷启动频率柱状图: ./fig/Telecom\bar_cold_start_frequency_Telecom.png
2025-07-17 21:25:56,052 - INFO - 冷启动频率折线图已保存为 './fig/Telecom\line_cold_start_frequency_Telecom.png'
2025-07-17 21:25:56,162 - INFO - 已绘制冷启动频率折线图: ./fig/Telecom\line_cold_start_frequency_Telecom.png
2025-07-17 21:25:56,797 - INFO - 平均响应时间图已保存为 './fig/Telecom\bar_response_time_Telecom.png'
2025-07-17 21:25:56,892 - INFO - 已绘制平均响应时间柱状图: ./fig/Telecom\bar_response_time_Telecom.png
2025-07-17 21:25:57,535 - INFO - 平均响应时间折线图已保存为 './fig/Telecom\line_response_time_Telecom.png'
2025-07-17 21:25:57,580 - INFO - 已绘制平均响应时间折线图: ./fig/Telecom\line_response_time_Telecom.png
2025-07-17 21:25:58,019 - INFO - 平均成本图已保存为 './fig/Telecom\bar_cost_Telecom.png'
2025-07-17 21:25:58,019 - INFO - 已绘制平均成本柱状图: ./fig/Telecom\bar_cost_Telecom.png
2025-07-17 21:25:58,520 - INFO - 平均成本折线图已保存为 './fig/Telecom\line_cost_Telecom.png'
2025-07-17 21:25:58,722 - INFO - 已绘制平均成本折线图: ./fig/Telecom\line_cost_Telecom.png
2025-07-17 21:25:59,247 - INFO - 平均内存使用图已保存为 './fig/Telecom\bar_memory_usage_Telecom.png'
2025-07-17 21:25:59,320 - INFO - 已绘制平均内存使用柱状图: ./fig/Telecom\bar_memory_usage_Telecom.png
2025-07-17 21:25:59,854 - INFO - 平均内存使用折线图已保存为 './fig/Telecom\line_memory_usage_Telecom.png'
2025-07-17 21:25:59,909 - INFO - 已绘制平均内存使用折线图: ./fig/Telecom\line_memory_usage_Telecom.png
2025-07-17 21:26:00,516 - INFO - 冷启动/热启动比例图已保存为 './fig/Telecom\cold_start_ratio_Telecom.png'
2025-07-17 21:26:00,517 - INFO - 已绘制冷启动/热启动比例图: ./fig/Telecom\cold_start_ratio_Telecom.png
2025-07-17 21:26:01,216 - INFO - 横向新内存使用柱状图(β=0.5)已保存为 './fig/Telecom\total_instant_memory_Telecom.png'
2025-07-17 21:26:01,216 - INFO - 已绘制横向总内存使用柱状图(β=0.5): ./fig/Telecom\total_instant_memory_Telecom.png
2025-07-17 21:26:01,589 - INFO - 横向内存使用柱状图(β=0.5)已保存为 './fig/Telecom\avg_instant_memory_Telecom.png'
2025-07-17 21:26:01,589 - INFO - 已绘制横向平均内存使用柱状图(β=0.5): ./fig/Telecom\avg_instant_memory_Telecom.png
2025-07-17 21:26:01,589 - INFO - 绘图脚本运行完成
