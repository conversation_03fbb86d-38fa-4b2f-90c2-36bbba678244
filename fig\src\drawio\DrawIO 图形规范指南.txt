# DrawIO 图形规范指南（完整版）

drawio文件是基于mxGraph的XML结构

## 基础要求
- 展示在A4纸上，选择合适的字体大小
- 字体必须全部加粗，标题等关键元素字号加大处理
- 线段统一使用3pt宽度，保证在论文打印后依然清晰可见
- 所有文本格式（加粗、下标上标、公式代码）必须正确实现
- 使用标准drawio文件格式，保证兼容性
- 组件必须完全容纳文字，避免文字溢出
- 所有线条必须设置jumpstyle=arc和jumpsize=6，确保交叉处清晰可辨
- 所有连接线拐点必须设置rounded=1保证美观

## 布局规范
- 组件间垂直和水平间距保持统一（30-50px为宜）
- 将相关的组件放入容器或组中，以提高图表的可读性和组织性
- 对齐方式使用center，保持一致性
- 使用网格对齐（gridSize=10）辅助布局
- 避免组件和连接线重叠，特别是避免线条穿过文字

## 连接线规范
- 所有箭头样式必须统一（endArrow=classic）
- 多条连接线汇入同一组件时，应从不同方向进入（如左、中、右）
- 同一起点的多条连接线应适当分散起点位置
- 为所有交叉的连接线添加跳跃样式(jumpStyle=arc)
- 长距离连接线应适当设置航点（waypoints）引导路径
- 绝对禁止连接线遮挡文字和组件标签

## 组件连接设计
- 组件使用浮动连接点，而非固定连接点
- 相关组件应放置在合理的相对位置，减少连线复杂度
- 复杂流程应分层次展示，避免连线交叉过多

## 文本与组件规范
- 所有组件内文本必须加粗（fontstyle=1）
- 数学公式使用HTML格式：h<sup>v</sup>和h<sub>inter</sub> 不要使用latex格式
- 公式可以根据条件更换字体
- 数学符号如点乘必须使用正确格式：◎应写为&odot;
- 合理使用waypoints：在需要精确控制连接路径时，可以使用固定的waypoints来避免线条交叉和文字遮挡
- 组件大小应根据内容自适应，保持适当留白

## 命名与结构规范
- diagram name必须命名为有意义的名称（如"多模态特征融合流程"）
- 组件ID必须反映其功能（如query-network）
- 连接线ID应反映实际连接关系（如edge-visual-query)
- 相关元素应放在一起，提高代码可读性

## 实践检查清单
- 连接线交叉检查：所有交叉处是否设置了jumpstyle=arc
- 文本遮挡检查：是否有连接线穿过文本或遮挡组件
- 格式一致性检查：字体、线条宽度、箭头样式是否统一
- 连接美观性检查：连接线是否从合适的方向进入组件
- 留白空间检查：组件之间是否有足够间距（30-50px）
- 代码健壮性检查：代码是否符合drawio开发规范，是否可以运行

## 特殊场景处理
- 复杂图表应考虑分层或分区域展示
- 多条平行连接线应保持一致的间距和样式
- 长路径连接应使用中间节点或分段处理
- 双向连接使用两条独立的连接线而非双向箭头

## 参考资源
- DrawIO官方文档：https://www.drawio.com/
- DrawIO学习教程：https://www.drawzh.com/
- 在线编辑器：https://app.diagrams.net/
- MxGraph语法：https://jgraph.github.io/mxgraph/docs/tutorial.html​​​​​​​​​​​​​​​​