import pandas as pd
import numpy as np
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt

# 读取数据
print("读取数据...")
df = pd.read_csv('data/topo/shanghai-data.csv')
print(f"原始数据行数: {len(df)}")

# 查看数据范围
print(f"经度范围: {df['LONGITUDE'].min()} - {df['LONGITUDE'].max()}")
print(f"纬度范围: {df['LATITUDE'].min()} - {df['LATITUDE'].max()}")

# 过滤异常值 (上海地区大致范围)
df_filtered = df[(df['LONGITUDE'] >= 120) & (df['LONGITUDE'] <= 122) & 
                (df['LATITUDE'] >= 30) & (df['LATITUDE'] <= 32)]
print(f"过滤后数据行数: {len(df_filtered)}")

# 使用K-means聚类选择代表性样本
print("执行K-means聚类...")
X = df_filtered[['LATITUDE', 'LONGITUDE']].values
kmeans = KMeans(n_clusters=125, random_state=42, n_init=10).fit(X)

# 获取每个点的聚类标签
df_filtered['cluster'] = kmeans.labels_

# 找到最接近聚类中心的点
centers = kmeans.cluster_centers_
representative_indices = []

print("选择代表性样本...")
for i in range(125):
    cluster_points = df_filtered[df_filtered['cluster'] == i]
    if len(cluster_points) > 0:
        # 计算到中心点的距离
        distances = np.sqrt(((cluster_points[['LATITUDE', 'LONGITUDE']].values - centers[i])**2).sum(axis=1))
        closest_idx = distances.argmin()
        representative_indices.append(cluster_points.iloc[closest_idx].name)

# 获取代表性样本
sampled_df = df.loc[representative_indices]
print(f"选择的样本数: {len(sampled_df)}")

# 可视化结果
plt.figure(figsize=(10, 8))
plt.scatter(df_filtered['LONGITUDE'], df_filtered['LATITUDE'], c='lightgray', s=10, alpha=0.3, label='原始数据')
plt.scatter(sampled_df['LONGITUDE'], sampled_df['LATITUDE'], c='red', s=20, label='选择的样本')
plt.title('上海地区数据点分布')
plt.xlabel('经度')
plt.ylabel('纬度')
plt.legend()
plt.grid(True)
try:
    plt.savefig('data_distribution.png')
    print("已保存可视化结果到 data_distribution.png")
except:
    print("无法保存可视化结果")

# 保存结果
sampled_df.to_csv('data/topo/shanghai-data-sampled.csv', index=False)
print("已保存125个代表性点到 data/topo/shanghai-data-sampled.csv")

# 打印数据统计信息
print("\n原始数据统计:")
print(df[['LATITUDE', 'LONGITUDE']].describe())

print("\n采样后数据统计:")
print(sampled_df[['LATITUDE', 'LONGITUDE']].describe()) 