import numpy as np
from typing import List, Dict, Tuple, Optional

# 配置参数
class Config:
    def __init__(self, topo_file: str, request_file: str,
                 latency_para: float, mem_cap: float, node_num: int,
                 beta: float, slot_num: int, redu_factor: float):
        self.topo_file = topo_file  # 拓扑名称
        self.request_file = request_file  # 请求文件
        self.latency_para = latency_para  # 延迟参数
        self.mem_cap = mem_cap  # 内存容量
        self.node_num = node_num  # 节点数量
        self.beta = beta  # β参数
        self.slot_num = slot_num  # 插槽数量
        self.redu_factor = redu_factor  # 减少因子

# 单个请求
class Request:
    # 使用字符串来引用尚未定义的类以实现嵌套 'Function' 'PhyNode'
    def __init__(self, id: int = 0, function: 'Function' = None, ingress: 'PhyNode' = None, 
                 arriveTime: int = 0, served: bool = False, latencyPara: float = 0.0, 
                 isColdStart: bool = False, deployNode: 'PhyNode' = None):
        self.id = id  # 请求ID
        self.function = function  # 函数对象
        self.ingress = ingress  # 入口节点
        self.arriveTime = arriveTime  # 到达时间
        self.served = served  # 是否已服务 succ, failed
        self.latencyPara = latencyPara  # 延迟参数
        self.isColdStart = isColdStart  # 是否冷启动
       
    def update(self, function: 'Function', deployNode: 'PhyNode', isColdStart: bool):
        """更新请求的函数、部署节点和冷启动状态"""
        self.function = function
        self.served = True
        self.deployNode = deployNode
        self.isColdStart = isColdStart

# 请求列表(List[Request])
class Requests:
    def __init__(self):
        self.requests: List[Request] = []  # 请求列表

    def add(self, request: Request):  # 添加请求
        self.requests.append(request)

# 请求映射(time slot<->req)
class RequestsMap:
    def __init__(self):
        self.map: Dict[int, Requests] = {}  # 时间槽到请求的映射

    def add(self, request: Request, time_slot: int):  # 添加请求到指定时间槽
        if time_slot not in self.map:
            self.map[time_slot] = Requests()  # 初始化请求列表
        self.map[time_slot].add(request)  # 添加请求

    def get(self, time_slot: int) -> Tuple[List[Request], bool]:  # 获取指定时间槽的请求
        if time_slot in self.map:
            return self.map[time_slot].requests, True  # 返回请求列表和找到的标志
        return [], False  # 返回空列表和未找到的标志
          
# 存储从 CSV 文件中读取的请求数据
class RequestFile:
    def __init__(self):
        self.time: List[int] = []  # 时间列表
        self.app1: List[int] = []  # 应用1请求列表
        self.app2: List[int] = []  # 应用2请求列表
        self.app3: List[int] = []  # 应用3请求列表
        self.app4: List[int] = []  # 应用4请求列表

# 物理节点
class PhyNode:
    def __init__(self, id: int, lat: float, long: float, mem: float):
        self.id = id
        self.lat = lat
        self.long = long
        self.mem = mem  # 节点的内存容量

    def getMem(self) -> float:
        """返回节点的内存容量"""
        return self.mem

# 拓扑结构(List[PhyNode])
class Topology:
    def __init__(self):
        self.nodes: List[PhyNode] = []  # 物理节点列表

    def add_node(self, node: PhyNode):
        """添加物理节点到拓扑结构"""
        self.nodes.append(node)  # 将节点添加到列表中

# 节点间距离(node id, distance)
class Distance:
    def __init__(self, phyNodeID: int, distance: float):
        self.phyNodeID = phyNodeID  # 物理节点ID
        self.distance = distance  # 距离

# 节点列表(List[Distance]距离排序)
class DistSlice:
    def __init__(self):
        self.slice: List[Distance] = []  # 距离切片列表

# 单个函数
class Function:
    def __init__(self, name: str = None, size: float = 0.0, type: int = 0, coldStartTime: float = 0.0, 
                 processTime: float = 0.0, clock: float = 0.0, priority: float = 0.0, phyNode: PhyNode = None):
        self.name = name  # 函数名称
        self.size = size  # 大小(优先级相关)
        self.type = type  # 容器的类型，在例子中为 1-4
        self.coldStartTime = coldStartTime  # 冷启动时间(优先级相关)
        self.processTime = processTime  # 处理时间
        self.clock = clock  # 时钟(优先级相关)
        self.priority = priority  # 优先级
        self.phyNode = phyNode  # 物理节点

    def init(self, phyNode: PhyNode):
        """
        初始化函数名称 "container-type-count_G, 如container-1-166"
        """
        name = f"container-{self.type}-{count_G}"
        self.name = name
        self.phyNode = phyNode

    def active_priority(self):
        """计算活动优先级"""
        freq = functionfreq_G.get(self.type)
        self.clock = clock_G
        self.priority = self.clock + (float(freq) * self.coldStartTime) / (self.size)
        #self.priority = self.clock + (float(freq) * self.coldStartTime) / (self.size / 1000)

    def cache_priority(self):
        """计算缓存优先级 如果cache不使用当前clock"""
        freq = functionfreq_G.get(self.type)
        self.priority = self.clock + (float(freq) * self.coldStartTime) / (self.size)  # MB 单位
        #self.priority = self.clock + (float(freq) * self.coldStartTime) / (self.size / 1000)  # GB 单位

    def show_priority(self):
        """显示函数优先级"""
        print(f"Function {self.name} Priority {self.priority}")  # 使用 print 替代 log

# 一种类型的活动函数(type, List[Function])
class Functions:
    def __init__(self, func_type: int):
        self.type = func_type  # 函数类型
        self.slice: List[Function] = []  # 存储函数的列表

    def add(self, function: Function):
        self.slice.append(function)  # 添加函数到列表

    def delete(self, index: int):
        if 0 <= index < len(self.slice):
            self.slice.pop(index)  # 从列表中删除指定索引的函数

    def show_priority(self):
        for func in self.slice:
            print(f"Function {func.name} Priority {func.priority}")  # 显示函数的优先级

# 函数频率(type<->freq)(active+,cache-)
class FunctionFreq:
    def __init__(self):
        self.map: Dict[int, int] = {}  # 函数类型到频率的映射

    def get(self, funcType: int) -> int:  # 获取函数类型的频率
        return self.map.get(funcType, 0)  # 返回频率，如果不存在则返回0    

    def add(self, funcType: int):  # 增加函数类型的频率
        if funcType in self.map:
            self.map[funcType] += 1  # 增加频率
        else:
            self.map[funcType] = 1  # 初始化频率

    def minus(self, funcType: int):  # 减少函数类型的频率
        if funcType in self.map:
            self.map[funcType] -= 1  # 减少频率

# 不同类型的活动函数(node id, type<->Functions)
class NodeFunctions:
    def __init__(self, phy_node_id: int):
        self.phyNodeID = phy_node_id  # 物理节点ID
        self.functions: Dict[int, Functions] = {}  # <funcType, FunctionSlice>

    def show(self, node_id: int):
        for func_type, functions in self.functions.items():
            print(f"Node id {node_id} funcType: {func_type} => active container num: {len(functions.slice)}")

    def show_priority(self):
        for functions in self.functions.values():
            functions.show_priority()  # 调用 Functions 的 show_priority 方法

    def add(self, function: Function):
        if function.type not in self.functions:
            self.functions[function.type] = Functions(function.type)  # 初始化 Functions
        self.functions[function.type].add(function)  # 将函数添加到对应的 Functions

    def delete(self, func_type: int, index: int):
        if func_type in self.functions:
            self.functions[func_type].delete(index)  # 从对应的 Functions 中删除函数

# 所有节点上的活动函数(node id<->NodeFunctions)
class ActiveFunctions:
    def __init__(self):
        self.map: Dict[int, NodeFunctions] = {}  # 节点ID到节点函数的映射

    def show(self):
        if not self.map:
            print("ActiveFunctions is empty")
            return

        print("show ActiveFunctions....")
        for nodeID, nf in self.map.items():
            nf.show(nodeID)  # 调用 NodeFunctions 的 show 方法

    def show_priority(self):
        if not self.map:
            print("ActiveFunctions is empty")
            return

        print("show priority....")
        for nf in self.map.values():
            nf.show_priority()  # 调用 NodeFunctions 的 showPriority 方法

    def add(self, function: Function, phyNodeID: int):
        if phyNodeID not in self.map:
            self.map[phyNodeID] = NodeFunctions(phyNodeID)  # 初始化 NodeFunctions

        # 更新函数频率
        functionfreq_G.add(function.type)
        function.active_priority()  # 假设 active_priority 是 Function 的一个方法
        self.map[phyNodeID].add(function)  # 将函数添加到对应的 NodeFunctions

    def delete(self, phyNodeID: int, funcType: int, index: int):
        if phyNodeID in self.map:
            nf = self.map[phyNodeID]
            nf.delete(funcType, index)  # 调用 NodeFunctions 的 delete 方法
            self.map[phyNodeID] = nf  # 更新映射

# 单个节点的缓存列表(node id, List[Function]优先级升序)
class Cache:
    def __init__(self, phyNodeID: int):
        self.phyNodeID = phyNodeID  # 物理节点ID
        self.functionList: List[Function] = []  # 优先级函数列表，升序

    def show(self):
        print(f"node {self.phyNodeID} cache size : {len(self.functionList)}")

    def show_priority(self):
        for function in self.functionList:
            print(f" {function.name} Priority : {function.priority}")

    def sort_list(self):
        self.functionList.sort(key=lambda f: f.priority)  # 按优先级排序

    def find(self, functionName: str) -> Tuple[int, Optional[str]]:
        for i in range(len(self.functionList)):
            if functionName == self.functionList[i].name:
                return i, None  # 返回索引和无错误
        return -1, "can't find this function by name"  # 返回未找到的错误信息

    def add(self, function: Function):
        functionfreq_G.minus(function.type)  # 假设 functionfreq_G 是全局变量
        function.cache_priority()  # 假设 cache_priority 是 Function 的一个方法
        self.functionList.append(function)
        self.sort_list()  # 排序函数列表

    def delete(self, i: int):
        # 检查索引是否存在
        if 0 <= i < len(self.functionList):
            self.functionList.pop(i)  # 从列表中删除指定索引的函数

# 多个节点的缓存映射(node id<->cache)
class CacheMap:
    def __init__(self):
        self.caches: Dict[int, Cache] = {}  # 物理节点ID到缓存的映射

    def show(self):
        if not self.caches:
            print("CacheMap is empty")
            return

        print("--------CacheMap--------")
        for cache in self.caches.values():
            cache.show()  # 调用 Cache 的 show 方法

    def show_priority(self):
        if not self.caches:
            print("CacheMap is empty")
            return

        print("--------CacheMap Priority--------")
        for cache in self.caches.values():
            cache.show_priority()  # 调用 Cache 的 show_priority 方法

    def add(self, function: Function):
        if function.phyNode.id in self.caches:
            self.caches[function.phyNode.id].add(function)  # 添加到现有缓存
        else:
            new_cache = Cache(function.phyNode.id)  # 创建新的 Cache 实例
            new_cache.add(function)  # 添加函数
            self.caches[function.phyNode.id] = new_cache  # 将新缓存添加到映射中

    # 寻找可用缓存容器
    def get_idle_function(self, phyNodeID: int, funcType: int) -> Tuple[Function, int]:
        if phyNodeID in self.caches:
            cache = self.caches[phyNodeID]
            for i, function in enumerate(cache.functionList):
                if function.type == funcType:
                    return function, i  # 返回找到的函数和索引
        return Function(), -1  # 返回空函数和-1表示未找到

    def delete(self, phyNodeID: int, funcIndex: int):
        if phyNodeID in self.caches:
            self.caches[phyNodeID].delete(funcIndex)  # 从缓存中删除函数

    def get_lowest_priority(self, phyNodeID: int) -> float:
        if phyNodeID in self.caches and self.caches[phyNodeID].functionList:
            return self.caches[phyNodeID].functionList[0].priority  # 返回最低优先级
        return 0.0  # 如果没有函数，返回0

    def delete_low_function(self, phyNodeID: int) -> Tuple[Function, bool]:
        if phyNodeID in self.caches and self.caches[phyNodeID].functionList:
            function = self.caches[phyNodeID].functionList[0]  # 获取最低优先级的函数
            self.caches[phyNodeID].delete(0)  # 删除该函数
            return function, True  # 返回函数和成功标志
        return Function(), False  # 返回空函数和失败标志

    def sort(self):
        """对所有缓存进行排序"""
        for cache in self.caches.values():
            cache.sort_list()  # 调用 Cache 的 sort_list 方法

# ----------------------需要修改的地方----------------------
# 单个容器
class Container:
    def __init__(self, phyNodeID: int, function: 'Function', memory: float):
        self.phyNodeID = phyNodeID      # 物理节点ID                    
        self.function = function        # 关联的函数
        self.memory = memory            # 容器的内存容量

# 容器池
class ContainerPool:
    def __init__(self):
        self.pool: List[Container] = []  # 存储容器的列表

    def clear_pool(self):
        """清空容器池"""
        self.pool.clear()

# 全局变量
config_G = Config(
    topo_file='./data/final/pi_nodes.csv',
    request_file='./data/final/prediction.csv',
    latency_para=0.000003336,
    mem_cap=1000,
    node_num=10,
    beta=1.0,   # 符合Zipf-β流行度定律的请求(0.5<β<1.5)
    slot_num=3,
    redu_factor=10000    # 调整请求数量
    )

activeFunctions_G = ActiveFunctions()  # 活动函数
cacheMap_G = CacheMap()  # 缓存函数
functionfreq_G = FunctionFreq()  # 函数频率
requestsMap_G = RequestsMap()  # 请求映射

topo_G = Topology()  # 拓扑
containerpool_G = ContainerPool() # 容器池

count_G = 0  # 容器总数计数
clock_G = 1  # 每轮开始时更新
requestCount_G = 0  # 请求计数
