import os
import pandas as pd
import glob
from tabulate import tabulate

def main():
    # 定义method, beta, alpha
    methods = ['LayerCache', 'CrossEdge', 'FaaSCache', 'OpenWhisk']
    betas = ['0.50', '0.75', '1.00', '1.25', '1.50']
    alphas = ['0.005', '0.015', '0.025', '0.035', '0.045']

    # 创建结果数据框
    result_data = {
        'Method': [],
        'Beta': [],
        'Success_Rate': [],
        'Cold_Start_Frequency': [],
        'Avg_Response_Time': [],
        'Weighted_Response_Time': [],
        'Avg_Cost': [],
    }

    # 处理每个CSV文件
    for method in methods:
        for beta in betas:
            file_path = f"result/{method}-{beta}-10.csv"
            if os.path.exists(file_path):
                try:
                    # 读取CSV文件的第一行，获取总体指标
                    metrics_df = pd.read_csv(file_path, nrows=1)
                    
                    # 计算成功率
                    total_req_count = metrics_df['total_req_count'].values[0]
                    served_req_count = metrics_df['served_req_count'].values[0]
                    success_rate = served_req_count / total_req_count if total_req_count > 0 else 0
                    
                    # 获取平均响应时间
                    avg_response_time = metrics_df['avg_response_time'].values[0]
                    
                    # 获取加权平均响应时间
                    weighted_response_time = metrics_df['weighted_response_time'].values[0] if 'weighted_response_time' in metrics_df.columns else 0.0
                    
                    # 获取平均成本
                    avg_cost = metrics_df['avg_cost'].values[0] if 'avg_cost' in metrics_df.columns else 0.0
                    
                    # 获取冷启动频率
                    cold_start_frequency = metrics_df['cold_start_frequency'].values[0] if 'cold_start_frequency' in metrics_df.columns else 0.0
                    
                    # 添加到结果中
                    result_data['Method'].append(method)
                    result_data['Beta'].append(beta)
                    result_data['Success_Rate'].append(round(success_rate, 2))
                    result_data['Avg_Response_Time'].append(avg_response_time)
                    result_data['Weighted_Response_Time'].append(weighted_response_time)
                    result_data['Avg_Cost'].append(avg_cost)
                    result_data['Cold_Start_Frequency'].append(cold_start_frequency)
                    
                    # print(f"处理 {method}-{beta}: 成功率={success_rate:.2f}, 平均响应时间={avg_response_time:.2f}, 平均成本={avg_cost:.2f}, 冷启动频率={cold_start_frequency:.2f}")
                except Exception as e:
                    print(f"处理文件 {file_path} 时出错: {e}")
            else:
                print(f"文件不存在: {file_path}")

    # 创建结果数据框
    result_df = pd.DataFrame(result_data)

    # 保存结果到CSV文件
    output_file = "method_beta_metrics.csv"
    result_df.to_csv(output_file, index=False, float_format='%.3f')
    print(f"\n结果已保存到 {output_file}")

    # 创建一个整合的表格，按beta值分组
    print("\n================================== 数据对比表 ==================================")
    
    # 创建一个空的表格数据列表
    table_data = []
    headers = ["Method", "Success Rate", "Cold Start Freq", "Avg Response Time", "Weighted Resp Time", "Avg Cost"]
    
    # 按beta值分组
    for beta in betas:
        beta_data = result_df[result_df['Beta'] == beta]
        
        # 添加beta值作为分组标题行
        table_data.append([f"Beta = {beta}", "", "", "", "", ""])
        
        # 添加每个方法的数据
        for _, row in beta_data.iterrows():
            method = row['Method']
            success_rate = row['Success_Rate']
            cold_start_freq = row['Cold_Start_Frequency']
            avg_response_time = row['Avg_Response_Time']
            weighted_response_time = row['Weighted_Response_Time']
            avg_cost = row['Avg_Cost']
            
            table_data.append([
                method, 
                f"{success_rate:.3f}", 
                f"{cold_start_freq:.3f}", 
                f"{avg_response_time:.3f}", 
                f"{weighted_response_time:.3f}",
                f"{avg_cost:.3f}"
            ])
        
        # 添加一个空行作为分隔
        if beta != betas[-1]:
            table_data.append(["", "", "", "", "", ""])
    
    # 使用tabulate打印表格
    print(tabulate(table_data, headers=headers, tablefmt="grid"))

if __name__ == "__main__":
    main() 