import os
import pandas as pd
import numpy as np
import glob
from tabulate import tabulate
import matplotlib.pyplot as plt
import argparse
import sys

# 获取脚本所在目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

def process_dataset(dataset_path, dataset_name):
    """
    处理指定数据集目录下的所有CSV文件，并生成统计结果
    
    参数:
    dataset_path: 数据集目录路径
    dataset_name: 数据集名称
    
    返回:
    统计结果数据框
    """
    print(f"\n处理数据集: {dataset_name}")
    
    # 获取目录下所有CSV文件
    csv_files = glob.glob(os.path.join(dataset_path, "*.csv"))
    
    # 创建结果数据框
    result_data = {
        'method': [],
        'beta': [],
        'cold_start_frequency': [],
        'weighted_response_time': [],
        'avg_cost': [],
        'new_memory_usage': []
    }
    
    # 处理每个CSV文件
    for file_path in csv_files:
        try:
            # 从文件名中提取方法和负载因子
            file_name = os.path.basename(file_path)
            parts = file_name.replace('.csv', '').split('-')
            method = parts[0]
            beta = parts[1]
            alpha = parts[2]
            
            print(f"  处理文件: {file_name}")
            
            # 使用pandas读取CSV文件的第一行
            df = pd.read_csv(file_path, nrows=1)
            
            # 获取所需指标
            cold_start_frequency = df['cold_start_frequency'].values[0] if 'cold_start_frequency' in df.columns else 0.0
            weighted_response_time = df['weighted_response_time'].values[0] if 'weighted_response_time' in df.columns else 0.0
            avg_cost = df['avg_cost'].values[0] if 'avg_cost' in df.columns else 0.0
            new_memory_usage = df['new_memory_usage'].values[0] if 'new_memory_usage' in df.columns else 0.0
            
            # 添加到结果中
            result_data['method'].append(method)
            result_data['beta'].append(beta)
            result_data['cold_start_frequency'].append(cold_start_frequency)
            result_data['weighted_response_time'].append(weighted_response_time)
            result_data['avg_cost'].append(avg_cost)
            result_data['new_memory_usage'].append(new_memory_usage)
            
        except Exception as e:
            print(f"  处理文件 {file_path} 时出错: {e}")
    
    # 创建结果数据框
    result_df = pd.DataFrame(result_data)
    
    return result_df

def save_statistics_to_csv(result_df, output_file):
    """
    将统计结果保存到CSV文件
    
    参数:
    result_df: 统计结果数据框
    output_file: 输出文件路径
    """
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        
    result_df.to_csv(output_file, index=False, float_format='%.3f')
    print(f"\n结果已保存到 {output_file}")

def print_statistics_table(result_df, dataset_name):
    """
    打印统计结果表格
    
    参数:
    result_df: 统计结果数据框
    dataset_name: 数据集名称
    """
    print(f"\n===================== {dataset_name} 数据集统计结果 =====================")
    
    # 创建一个空的表格数据列表
    table_data = []
    headers = ["method", "beta", "cold_start_frequency", "weighted_response_time", "avg_cost", "new_memory_usage"]
    
    # 按负载因子分组
    for beta in sorted(result_df['beta'].unique()):
        beta_data = result_df[result_df['beta'] == beta]
        
        # 添加负载因子值作为分组标题行
        table_data.append([f"beta = {beta}", "", "", "", "", ""])
        
        # 添加每个方法的数据
        for _, row in beta_data.iterrows():
            method = row['method']
            cold_start_freq = row['cold_start_frequency']
            weighted_response_time = row['weighted_response_time']
            avg_cost = row['avg_cost']
            memory_usage = row['new_memory_usage']
            
            table_data.append([
                method, 
                beta,
                f"{cold_start_freq:.3f}", 
                f"{weighted_response_time:.3f}",
                f"{avg_cost:.3f}",
                f"{memory_usage:.0f}"
            ])
        
        # 添加一个空行作为分隔
        if beta != sorted(result_df['beta'].unique())[-1]:
            table_data.append(["", "", "", "", "", ""])
    
    # 使用tabulate打印表格
    print(tabulate(table_data, headers=headers, tablefmt="grid"))

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='统计实验数据并生成统计报告')
    parser.add_argument('--exp-dir', type=str, default="result/exp/7.15-Exp",
                        help='实验数据目录路径 (默认: result/exp/7.15-Exp)')
    parser.add_argument('--datasets', type=str, default="EUA,Telecom",
                        help='数据集名称，用逗号分隔 (默认: EUA,Telecom)')
    parser.add_argument('--output-dir', type=str, default="",
                        help='输出目录路径 (默认: 脚本所在目录)')
    
    args = parser.parse_args()
    
    # 解析数据集名称
    datasets = args.datasets.split(',')
    
    # 设置输出目录
    output_dir = args.output_dir if args.output_dir else SCRIPT_DIR
    
    # 处理每个数据集
    all_results = {}
    for dataset in datasets:
        dataset_path = os.path.join(args.exp_dir, dataset)
        if os.path.exists(dataset_path):
            # 处理数据集
            result_df = process_dataset(dataset_path, dataset)
            all_results[dataset] = result_df
            
            # 保存统计结果
            output_file = os.path.join(output_dir, f"exp_{dataset}.csv")
            save_statistics_to_csv(result_df, output_file)
        else:
            print(f"数据集目录不存在: {dataset_path}")
    
    print("\n统计完成!")

if __name__ == "__main__":
    main() 