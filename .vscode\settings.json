{"files.associations": {"array": "cpp", "deque": "cpp", "string": "cpp", "unordered_map": "cpp", "vector": "cpp", "string_view": "cpp", "initializer_list": "cpp", "*.tcc": "cpp", "algorithm": "cpp", "random": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cwchar": "cpp", "cwctype": "cpp", "exception": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "iosfwd": "cpp", "limits": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "typeinfo": "cpp"}, "commentTranslate.multiLineMerge": true, "window.zoomLevel": 4, "editor.mouseWheelZoom": true, "kiroAgent.enableTabAutocomplete": false}