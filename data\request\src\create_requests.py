#!/usr/bin/env python3

import csv
import os
import random
import math
import argparse
import sys

class ZipfGenerator:
    """Python implementation of the Zipf distribution generator from zipf.cc"""
    
    def __init__(self):
        self.first = True
        self.c = 0
        self.sum_probs = None
        self.x = 1  # Random int value
    
    def zipf(self, alpha, n):
        """Generate a Zipf distributed random variable"""
        # Compute normalization constant on first call only
        if self.first:
            self.c = 0
            for i in range(1, n+1):
                self.c += 1.0 / math.pow(i, alpha)
            self.c = 1.0 / self.c

            self.sum_probs = [0] * (n+1)
            self.sum_probs[0] = 0
            for i in range(1, n+1):
                self.sum_probs[i] = self.sum_probs[i-1] + self.c / math.pow(i, alpha)
            self.first = False

        # Pull a uniform random number (0 < z < 1)
        z = 0
        while z == 0 or z == 1:
            z = self.rand_val(0)

        # Map z to the value using binary search
        low, high = 1, n
        while low <= high:
            mid = math.floor((low + high) / 2)
            if self.sum_probs[mid] >= z and self.sum_probs[mid-1] < z:
                zipf_value = mid
                break
            elif self.sum_probs[mid] >= z:
                high = mid - 1
            else:
                low = mid + 1
        
        # Ensure zipf_value is between 1 and n
        assert (zipf_value >= 1) and (zipf_value <= n), f"zipf_value: {zipf_value}, alpha: {alpha}, n: {n}, z: {z}"
        
        return zipf_value

    def rand_val(self, seed):
        """Multiplicative LCG for generating uniform(0.0, 1.0) random numbers"""
        a = 16807        # Multiplier
        m = 2147483647   # Modulus
        q = 127773       # m div a
        r = 2836         # m mod a

        # Set the seed if argument is non-zero and then return zero
        if seed > 0:
            self.x = seed
            return 0.0

        # RNG using integer arithmetic
        x_div_q = self.x // q
        x_mod_q = self.x % q
        x_new = (a * x_mod_q) - (r * x_div_q)
        
        if x_new > 0:
            self.x = x_new
        else:
            self.x = x_new + m

        # Return a random value between 0.0 and 1.0
        return self.x / m

class MyAlgorithm:
    """Python implementation of the MyAlgorithm class functionality needed for request generation"""
    
    def __init__(self):
        self.config = {}
        self.node_map = {}
        self.req_num = {}  # <func_type, ReqNumMap>
    
    def set_config(self):
        """Set configuration parameters directly in the script"""
        # Default configuration values
        self.config['RequestFile'] = 'data/request/final_d01.csv'
        self.config['TopoName'] = 'data/topo/site-optus-melbCBD.csv'
        self.config['SlotNum'] = 30
        self.config['NodeNum'] = 10
        self.config['CommCostPara'] = 1
        self.config['MemCap'] = 10000
        self.config['ReduFactor'] = 10
        self.config['Alpha'] = 0.05
        self.config['cpuFreq'] = 3
        
        print(f"Using configuration: RequestFile={self.config.get('RequestFile')}, TopoName={self.config.get('TopoName')}")
    
    def load_topo(self):
        """Load topology information from site-optus-melbCBD.csv"""
        topo_file = self.config.get('TopoName', 'data/topo/site-optus-melbCBD.csv')
        print(f"Loading topology from {topo_file}")
        
        if not os.path.exists(topo_file):
            print(f"Warning: Topology file {topo_file} not found, using default node IDs")
            # Fallback to default node IDs
            node_count = self.config.get('NodeNum', 10)
            for i in range(1, node_count + 1):
                self.node_map[i] = {"id": i, "latitude": 0, "longitude": 0}
        else:
            # First try to understand the file structure
            has_type_column = False
            with open(topo_file, 'r') as f:
                reader = csv.reader(f)
                header = next(reader, None)
                if header:
                    has_type_column = 'TYPE' in header
            
            node_id = 1  # We'll assign sequential IDs starting from 1
            with open(topo_file, 'r') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    # Check if we have enough fields
                    if 'SITE_ID' in row and 'LATITUDE' in row and 'LONGITUDE' in row:
                        # If TYPE column exists, only add Edge Server nodes
                        include_node = True
                        if has_type_column and 'TYPE' in row:
                            include_node = row['TYPE'] == 'Edge Server'
                        
                        if include_node:
                            try:
                                # For Edge Server types, use the actual SITE_ID
                                site_id_str = row['SITE_ID']
                                # For "Border" sites, they might be numbered 1-19
                                try:
                                    site_id = int(site_id_str)
                                    self.node_map[node_id] = {
                                        "id": node_id,
                                        "site_id": site_id,
                                        "latitude": float(row['LATITUDE']),
                                        "longitude": float(row['LONGITUDE']),
                                        "type": row.get('TYPE', '')
                                    }
                                    node_id += 1
                                except ValueError:
                                    # Skip if it's not a valid integer
                                    continue
                            except (ValueError, KeyError):
                                print(f"Warning: Could not parse site ID from {row}")
            
            # If we didn't load any nodes, fall back to NodeNum
            if not self.node_map:
                print("Warning: No nodes loaded from topology file, using default node IDs")
                node_count = self.config.get('NodeNum', 10)
                for i in range(1, node_count + 1):
                    self.node_map[i] = {"id": i, "latitude": 0, "longitude": 0}
        
        print(f"Loaded {len(self.node_map)} nodes")
    
    def load_request(self):
        """Load request information from final_d01.csv"""
        print(f"Loading requests from {self.config.get('RequestFile')}")
        request_file = self.config.get('RequestFile', 'data/request/final_d01.csv')
        
        if not os.path.exists(request_file):
            print(f"Error: Request file {request_file} not found")
            return
        
        with open(request_file, 'r') as f:
            reader = csv.reader(f)
            row_num = 0
            req_num_map = {}
            
            for row in reader:
                # Skip empty rows
                if not row:
                    continue
                
                # First row is node IDs, second row is values for function type 1, etc.
                if row_num % 2 == 0:
                    # This is a row with node IDs
                    pass
                else:
                    # This is a row with request counts for a function type
                    req_num_map = {}
                    count = 1
                    
                    for item in row:
                        # time slot starts from 1
                        req_num_map[count] = int(item)
                        count += 1
                    
                    func_type = row_num // 2 + 1
                    print(f"Inserting function type {func_type}")
                    self.req_num[func_type] = req_num_map
                
                row_num += 1
    
    def gen_zipf_num(self, num_values, alpha, max_num):
        """Generate Zipf distributed request numbers"""
        zipf_gen = ZipfGenerator()
        seed = 1
        zipf_gen.rand_val(seed)
        
        req_map = {}  # <node id, request_num>
        for i in range(1, num_values + 1):
            zipf_rv = zipf_gen.zipf(alpha, max_num)
            req_map[i] = zipf_rv
        
        return req_map

def write_file(filename, req_map, total_columns=125):
    """Write request map to file, ensuring 125 columns output"""
    with open(filename, 'a') as f:
        values = []
        # Get all values from req_map
        for i in range(1, len(req_map) + 1):
            if i in req_map:
                values.append(str(req_map[i]))
            else:
                print(f"Cannot find node number {i}")
                break
        
        # If we have fewer than 125 columns, pad with 1s
        # The value 1 is used instead of 0 to match the C++ implementation behavior
        while len(values) < total_columns:
            values.append("1")
        
        # If we have more than 125 columns, truncate
        if len(values) > total_columns:
            values = values[:total_columns]
            
        f.write(','.join(values) + '\n')

def generate_requests_for_beta(beta, ma, debug=False):
    """Generate requests for a specific beta value"""
    if debug:
        print(f"\n{'='*60}")
        print(f"Generating requests for beta = {beta:.2f}")
        print(f"{'='*60}")
    
    slot_num = ma.config.get('SlotNum', 30)
    
    # Output file name with beta value
    file_name = f"data/request/requests-{beta:.2f}.csv"
    
    # Delete existing file if it exists
    if os.path.exists(file_name):
        os.remove(file_name)
    
    # For different types of requests
    for func_type, req_num_map in ma.req_num.items():
        if debug:
            print(f"Processing function type {func_type}")
        
        # Write function type header
        with open(file_name, 'a') as f:
            f.write(f"funcType,{func_type}\n")
        
        # For each time slot
        for i in range(1, slot_num + 1):
            if i in req_num_map:
                total_req_num = req_num_map[i]
                
                # Generate Zipf-distributed request numbers
                req_map = ma.gen_zipf_num(len(ma.node_map), beta, total_req_num // len(ma.node_map))
                
                # Write to file, ensuring 125 columns output
                write_file(file_name, req_map)
            else:
                print(f"Error: Slot {i} not found in request map for function type {func_type}")
    
    if debug:
        print(f"Generated request file: {file_name}")

def main():
    """Main function to generate requests based on Zipf distribution"""
    parser = argparse.ArgumentParser(description='Generate requests based on Zipf distribution')
    parser.add_argument('--beta', type=float, nargs='+', default=None,
                         help='Beta parameter(s) for Zipf distribution. Can specify multiple values.')
    parser.add_argument('--all-betas', action='store_true', default=True,
                        help='Generate requests for all default beta values')
    parser.add_argument('--request-file', type=str, default='data/request/final_d01.csv',
                         help='Path to request file')
    parser.add_argument('--topo-file', type=str, default='data/topo/site-optus-melbCBD.csv',
                         help='Path to topology file')
    parser.add_argument('--slot-num', type=int, default=30,
                         help='Number of time slots')
    parser.add_argument('--node-num', type=int, default=10,
                         help='Number of nodes')
    parser.add_argument('--debug', action='store_true',
                         help='Enable debug output')
    args = parser.parse_args()
    
    # Initialize debugging if requested
    debug = args.debug
    if debug:
        import logging
        logging.basicConfig(level=logging.DEBUG)
    
    # Set up algorithm
    ma = MyAlgorithm()
    ma.set_config()
    
    # Override configuration with command-line arguments
    ma.config['RequestFile'] = args.request_file
    ma.config['TopoName'] = args.topo_file
    ma.config['SlotNum'] = args.slot_num
    ma.config['NodeNum'] = args.node_num
    
    # Load topology and request data (only need to do this once)
    ma.load_topo()
    ma.load_request()
    
    # Determine which beta values to process
    if args.beta is not None:
        # 如果指定了特定的beta值，使用指定的值并关闭all_betas标志
        beta_values = args.beta
        args.all_betas = False
        print(f"Generating requests for specified beta values: {beta_values}")
    elif args.all_betas:
        # 否则使用默认的所有beta值
        beta_values = [0.50, 0.75, 1.00, 1.25, 1.50]
        print(f"Generating requests for all default beta values: {beta_values}")
    else:
        # 这种情况不应该发生，因为all_betas默认为True
        beta_values = [1.00]  # 提供一个默认值
        print(f"No beta values specified, using default: {beta_values}")
    
    # Track total time
    import time
    start_time = time.time()
    
    # Process each beta value
    total_betas = len(beta_values)
    for idx, beta in enumerate(beta_values, 1):
        print(f"\nProcessing beta {idx}/{total_betas}: {beta:.2f}")
        generate_requests_for_beta(beta, ma, debug)
    
    # Print summary
    elapsed_time = time.time() - start_time
    print(f"\nRequest generation completed for {len(beta_values)} beta values in {elapsed_time:.2f} seconds")
    print(f"Generated files:")
    for beta in beta_values:
        file_name = f"data/request/requests-{beta:.2f}.csv"
        if os.path.exists(file_name):
            file_size = os.path.getsize(file_name) / 1024  # in KB
            print(f"  - {file_name} ({file_size:.1f} KB)")

if __name__ == "__main__":
    main() 
    # 生成所有默认beta值的请求:
    # python data/request/create_requests.py --all-betas