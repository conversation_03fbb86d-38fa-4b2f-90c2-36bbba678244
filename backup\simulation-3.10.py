import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Optional
import csv
import math
import logging
#from data_struct import * 

# 配置参数及日志
class Config:
    def __init__(self, topo_file: str,latency_para: float, mem_cap: int, 
                 node_num: int, alpha: float, beta: float, 
                 slot_num: int, redu_factor: int, cpu_Freq: int):
        self.topo_file = topo_file          # 拓扑名称
        self.latency_para = latency_para    # 延迟参数
        self.mem_cap = mem_cap              # 内存容量
        self.node_num = node_num            # 节点数量
        self.alpha = alpha                  # α参数
        self.beta = beta                    # β参数
        self.slot_num = slot_num            # 插槽数量
        self.redu_factor = redu_factor      # 减少因子
        self.cpu_Freq = cpu_Freq            # cpu频率    
config_G = Config(
    node_num=10,        # 节点数量
    slot_num=30,        # 时间槽数量 slot_num=5满足论文结果
 
    # topo_file='./data/topo/pi_nodes.csv',             # 10个节点
    topo_file='./data/topo/site-optus-melbCBD.csv',     # 125个节点
    
    # Cross-Edge
    cpu_Freq= 3,                  # CPU频率
    alpha = 0.015,                # 运行成本参数 α
    latency_para=1,               # 延迟参数
    mem_cap=10000,                # 节点内存容量
    beta = 0.5,                   # Zipf-β请求分布   0.5, 1, 1.5
    redu_factor=10                # 请求数量减少参数  

    # S-Cache
    # latency_para=0.000003336,   
    # mem_cap=1000,                
    # beta=1.5,                   
    # redu_factor=12500           # 10000, 12500
    )

def initGlobal():
    global clock_G, count_G, activeFunctions_G, cacheMap_G, functionfreq_G
    global requestsMap_G, funcInfoMap_G, rontt_G
    global total_req_count_G, cold_req_count_G, req_count_G, served_req_count_G

    activeFunctions_G = ActiveFunctions()   # 活动函数
    cacheMap_G = CacheMap()                 # 缓存函数
    functionfreq_G = FunctionFreq()         # 函数频率
    requestsMap_G = RequestsMap()           # 请求映射
    funcInfoMap_G = FunctionInfoMap()       # 函数属性
    rontt_G = Req_on_Nodes_Time_Type()      # funcType<->Req_Nodes_Time
    # containerpool_G = ContainerPool()     # 容器池
    
    clock_G = 1                             # 每轮开始时更新时钟
    count_G = 0                             # 容器总数计数
    req_count_G = 0                         # 创建请求计数
    total_req_count_G = 0                   # 总请求计数
    cold_req_count_G = 0                    # 冷启动请求计数
    served_req_count_G = 0                  # 服务请求计数

logging.basicConfig(
    filename=f'simulation-{config_G.beta}-{config_G.redu_factor}.log',      # 日志文件名
    level=logging.INFO,             # 日志级别
    format='%(asctime)s - %(levelname)s - %(message)s',  # 日志格式
    filemode='w',  # 使用 'w' 模式覆盖文件
)

#region ----------------------请求相关类----------------------
# ----------------Cross-Edge----------------
# 节点请求数量
class Req_on_Nodes:
    def __init__(self):
        self.numVector: List[int] = []  # 存储每个节点的请求数量

    def add(self, num: int):
        """添加请求数量"""
        self.numVector.append(num)

    def clear(self):
        """清空请求数量"""
        self.numVector.clear()

# 每个时间槽的请求数量(timeslot<->req_num) 
class Req_on_Nodes_Time:
    def __init__(self):
        self.numVector: List[Req_on_Nodes] = []  # 存储每个时间槽的请求数量

    def add(self, ron: Req_on_Nodes):
        """添加请求数量"""
        self.numVector.append(ron)

    def clear(self):
        """清空请求数量"""
        self.numVector.clear()

# 请求数量映射(funcType<->Req_Nodes_Time)
class Req_on_Nodes_Time_Type:
    def __init__(self):
        self.numMap: Dict[int, Req_on_Nodes_Time] = {}  # 存储函数类型与请求数量的映射

    def add(self, funcType: int, ront: Req_on_Nodes_Time):
        """添加函数类型的请求数量"""
        self.numMap[funcType] = ront
# ----------------Cross-Edge----------------

# 单个请求 request -> function -> phynode
class Request:
    # 使用字符串来引用尚未定义的类以实现嵌套 'Function' 'PhyNode'
    def __init__(self, id: int = 0, function: 'Function' = None, ingress: 'PhyNode' = None, 
                 arriveTime: int = 0, served: bool = False, isColdStart: bool = False, 
                 deployNode: 'PhyNode' = None, linkDelay: float = 0.0):
        self.id = id                    # 请求ID
        self.function = function        # 函数对象
        self.ingress = ingress          # 入口节点
        self.arriveTime = arriveTime    # 到达时间
        self.served = served            # 是否已服务
        self.isColdStart = isColdStart  # 是否冷启动
        self.deployNode = deployNode    # 部署节点
        self.linkDelay = linkDelay      # 传输延迟
       
    def update(self, function: 'Function', deployNode: 'PhyNode', isColdStart: bool):
        """更新请求的函数、部署节点和冷启动状态"""
        self.function = function
        self.served = True
        self.deployNode = deployNode
        self.isColdStart = isColdStart

# 请求列表(List[Request])
class Requests:
    def __init__(self):
        self.requests: List[Request] = []  # 请求列表

    def add(self, request: Request):  # 添加请求
        self.requests.append(request)

# 请求映射(time slot<->request)
class RequestsMap:
    def __init__(self):
        self.map: Dict[int, Requests] = {}  # 时间槽到请求的映射

    def add(self, request: Request, time_slot: int):  # 添加请求到指定时间槽
        if time_slot not in self.map:
            self.map[time_slot] = Requests()  # 初始化请求列表
        self.map[time_slot].add(request)  # 添加请求

    def get(self, time_slot: int) -> Tuple[List[Request], bool]:  # 获取指定时间槽的请求
        if time_slot in self.map:
            return self.map[time_slot].requests, True  # 返回请求列表和找到的标志
        return [], False  # 返回空列表和未找到的标志

    def size(self):
        return len(self.map)

# 存储请求数据
class RequestFile:
    def __init__(self):
        self.time: List[int] = []  # 时间列表
        self.app1: List[int] = []  # 应用1请求列表
        self.app2: List[int] = []  # 应用2请求列表
        self.app3: List[int] = []  # 应用3请求列表
        self.app4: List[int] = []  # 应用4请求列表
# endregion

#region ---------------------节点相关类----------------------
# 物理节点
class PhyNode:
    def __init__(self, id: int = 0, lat: float = 0.0, long: float = 0.0, 
                 mem: float = 0.0, cpuFreq: float = 0.0):
        self.id = id
        self.lat = lat
        self.long = long
        self.mem = mem  
        self.cpuFreq = cpuFreq
        self.funcFreq: Dict[int, float] = {}  # <functype, freq>
        self.recency: Dict[int, float] = {}  # <functype, time>

    def getFreq(self, funcType: int) -> float:
        if funcType not in self.funcFreq or self.funcFreq[funcType] <= 0:
            self.funcFreq[funcType] = 1.0
            return 1.0
        return self.funcFreq[funcType]

    def getRecency(self, funcType: int) -> float:
        if funcType not in self.recency or self.recency[funcType] <= 0:
            self.recency[funcType] = 1.0
            return 1.0
        return self.recency[funcType]

    def setRecency(self, funcType: int, recency: float):
        self.recency[funcType] = recency

    def setFreq(self, funcType: int, freq: float):
        self.funcFreq[funcType] = freq

    def addFreq(self, funcType: int):
        if funcType not in self.funcFreq:
            self.funcFreq[funcType] = 1.0
        else:
            self.funcFreq[funcType] += 1.0

    def minusFreq(self, funcType: int):
        if funcType in self.funcFreq:
            self.funcFreq[funcType] -= 1.0

    def getMem(self) -> float:
        return self.mem

# 拓扑结构(node id, PhyNode)
class Topology:
    def __init__(self):
        self.nodes: Dict[int, PhyNode] = {}  

    def get(self, phyNodeID: int) -> PhyNode:
        """获取物理节点"""
        return self.nodes.get(phyNodeID, PhyNode(0, 0.0, 0.0, 0.0))  # 返回默认 PhyNode

    def add_node(self, phyNodeID: int, p: PhyNode):
        """添加物理节点到拓扑结构"""
        self.nodes[phyNodeID] = p  # 将节点添加到列表中

    def update(self, operation: str, phyNodeID: int, size: float):
        """更新物理节点的内存"""
        if operation == "add":
            self.nodes[phyNodeID].mem += size
        elif operation == "minus":
            self.nodes[phyNodeID].mem -= size

    def size(self) -> int:
        """返回节点数量"""
        return len(self.nodes)

    def minusFreq(self, phyNodeID: int, funcType: int):
        """减少函数类型的频率"""
        p = self.get(phyNodeID)
        p.minusFreq(funcType)
        self.nodes[phyNodeID] = p
    
    def addFreq(self, phyNodeID: int, funcType: int):
        """增加函数类型的频率"""
        p = self.get(phyNodeID)
        p.addFreq(funcType)
        self.nodes[phyNodeID] = p

    def setRecency(self, phyNodeID: int, funcType: int, recen: float):
        """设置函数类型的最近使用时间"""
        p = self.get(phyNodeID)
        p.setRecency(funcType, recen)
        self.nodes[phyNodeID] = p

    def addFreqAll(self, funcType: int):
        """对所有物理节点增加函数类型的频率"""
        for p in self.nodes.values():
            p.addFreq(funcType)

    def setRecencyAll(self, funcType: int, recen: float):
        """对所有物理节点设置函数类型的最近使用时间"""
        for p in self.nodes.values():
            p.setRecency(funcType, recen)

# 节点经纬度
class Location:
    def __init__(self, latitude: float = 0.0, longitude: float = 0.0):
        self.latitude = latitude
        self.longitude = longitude

    def init(self, lat1: float, long1: float):
        self.latitude = lat1
        self.longitude = long1

# 节点间距离(node id, distance)
class Distance:
    def __init__(self, phyNodeID: int, distance: float):
        self.phyNodeID = phyNodeID  # 物理节点ID
        self.distance = distance  # 距离
    
    def get_id(self):
        return self.phyNodeID

# 节点列表(List[Distance]距离排序)
class DistSlice:
    def __init__(self):
        self.slice: List[Distance] = []  # 距离切片列表
#endregion

#region ---------------------函数相关类----------------------
# 单个函数 function -> phynode
class Function:
    def __init__(self, name: str = None, type: int = 0, size: float = 0.0, clock: float = 0.0, 
                 coldStartTime: float = 0.0, priority: float = 1.0, processTime: float = 0.0,  
                 phyNode: PhyNode = None, lifetime: int = 0, lastUseTime: int = 0):
        self.name = name                    # 函数名称
        self.type = type                    # 容器的类型，在例子中为 1-4
        
        # 优先级相关(S-Cache)
        self.size = size                    # 大小
        self.clock = clock                  # 时钟
        self.coldStartTime = coldStartTime  # 冷启动时间
        self.priority = priority            # 优先级

        self.processTime = processTime      # 处理时间
        self.phyNode = phyNode              # 部署节点

        # 概率计算相关(Cross-Edge)
        self.lifetime = lifetime
        self.lastUseTime = lastUseTime

    def init(self, phyNode: PhyNode):
        """
        初始化函数名称 "container-type-count_G, 如container-1-166"
        """
        name = f"container-{self.type}-{count_G}"
        self.name = name
        self.phyNode = phyNode

    def active_priority(self):
        """计算活动优先级"""
        freq = functionfreq_G.get(self.type)
        self.clock = clock_G
        self.priority = self.clock + (float(freq) * self.coldStartTime) / (self.size)   # MB 单位

    def cache_priority(self):
        """计算缓存优先级 如果cache不使用当前clock"""
        freq = functionfreq_G.get(self.type)
        self.priority = self.clock + (float(freq) * self.coldStartTime) / (self.size)  # MB 单位

    def show_priority(self):
        """显示函数优先级"""
        print(f"Function {self.name} Priority {self.priority}")  # 使用 print 替代 log

# 一种类型的活动函数(type, List[Function])
class Functions:
    def __init__(self, func_type: int):
        self.type = func_type  # 函数类型
        self.slice: List[Function] = []  # 存储函数的列表

    def add(self, function: Function):
        self.slice.append(function)  # 添加函数到列表

    def delete(self, index: int):
        if 0 <= index < len(self.slice):
            self.slice.pop(index)  # 从列表中删除指定索引的函数

    def show_priority(self):
        for func in self.slice:
            print(f"Function {func.name} Priority {func.priority}")  # 显示函数的优先级

# ----------------Cross-Edge----------------
class FunctionInfo:
    def __init__(self, processTime: float = 0.0, coldStartTime: float= 0.0, 
                 size: float = 0.0, lifetime: int = 0):
        self.processTime = processTime            # 处理时间（毫秒）
        self.coldStartTime = coldStartTime        # 冷启动时间（秒）
        self.size = size                          # 大小（MB）
        self.lifetime = lifetime                  # 生命周期（分钟）

# (funcType<->FunctionInfo)
class FunctionInfoMap:
    def __init__(self):
        self.func_map: Dict[int, FunctionInfo] = {}  

    def add(self, func_type: int, fi: FunctionInfo):

        """添加函数信息"""
        self.func_map[func_type] = fi

        return None, False
    
    def get_size(self, func_type: int) -> int:
        """获取函数大小"""
        if func_type in self.func_map:
            return self.func_map[func_type].size
        return 0
# ----------------Cross-Edge----------------

# 函数频率(type<->freq)(active+,cache-)
class FunctionFreq:
    def __init__(self):
        self.map: Dict[int, int] = {}  # 函数类型到频率的映射

    def get(self, funcType: int) -> int:  # 获取函数类型的频率
        return self.map.get(funcType, 0)  # 返回频率，如果不存在则返回0    

    def add(self, funcType: int):  # 增加函数类型的频率
        if funcType in self.map:
            self.map[funcType] += 1  # 增加频率
        else:
            self.map[funcType] = 1  # 初始化频率

    def minus(self, funcType: int):  # 减少函数类型的频率
        if funcType in self.map:
            self.map[funcType] -= 1  # 减少频率

# 不同类型的活动函数(node id, type<->Functions)
class NodeFunctions:
    def __init__(self, phy_node_id: int):
        self.phyNodeID = phy_node_id  # 物理节点ID
        self.functions: Dict[int, Functions] = {}  # <funcType, FunctionSlice>

    def show(self, node_id: int):
        for func_type, functions in self.functions.items():
            print(f"Node id {node_id} funcType: {func_type} => active container num: {len(functions.slice)}")

    def show_priority(self):
        for functions in self.functions.values():
            functions.show_priority()  # 调用 Functions 的 show_priority 方法

    def add(self, function: Function):
        if function.type not in self.functions:
            self.functions[function.type] = Functions(function.type)  # 初始化 Functions
        self.functions[function.type].add(function)  # 将函数添加到对应的 Functions

    def delete(self, func_type: int, index: int):
        if func_type in self.functions:
            self.functions[func_type].delete(index)  # 从对应的 Functions 中删除函数

# 所有节点上的活动函数(node id<->NodeFunctions)
class ActiveFunctions:
    def __init__(self):
        self.map: Dict[int, NodeFunctions] = {}  # 节点ID到节点函数的映射

    def show(self):
        if not self.map:
            print("ActiveFunctions is empty")
            return

        print("show ActiveFunctions....")
        for nodeID, nf in self.map.items():
            nf.show(nodeID)  # 调用 NodeFunctions 的 show 方法

    def show_priority(self):
        if not self.map:
            print("ActiveFunctions is empty")
            return

        print("show priority....")
        for nf in self.map.values():
            nf.show_priority()  # 调用 NodeFunctions 的 showPriority 方法

    def add(self, function: Function, phyNodeID: int):
        if phyNodeID not in self.map:
            self.map[phyNodeID] = NodeFunctions(phyNodeID)  # 初始化 NodeFunctions

        # 更新函数频率
        functionfreq_G.add(function.type)
        function.active_priority()      
        self.map[phyNodeID].add(function)  # 将函数添加到对应的 NodeFunctions

    def delete(self, phyNodeID: int, funcType: int, index: int):
        if phyNodeID in self.map:
            nf = self.map[phyNodeID]
            nf.delete(funcType, index)  # 调用 NodeFunctions 的 delete 方法
            self.map[phyNodeID] = nf  # 更新映射

# 单个节点的缓存列表(node id, List[Function]优先级升序)
class Cache:
    def __init__(self, phyNodeID: int):
        self.phyNodeID = phyNodeID  # 物理节点ID
        self.functionList: List[Function] = []  # 优先级函数列表，升序

    def show(self):
        print(f"node {self.phyNodeID} cache size : {len(self.functionList)}")

    def show_priority(self):
        for function in self.functionList:
            print(f" {function.name} Priority : {function.priority}")

    def sort_list(self):
        self.functionList.sort(key=lambda f: f.priority)  # 按优先级排序

    def find(self, functionName: str) -> Tuple[int, Optional[str]]:
        for i in range(len(self.functionList)):
            if functionName == self.functionList[i].name:
                return i, None  # 返回索引和无错误
        return -1, "can't find this function by name"  # 返回未找到的错误信息

    def add(self, function: Function):
        functionfreq_G.minus(function.type)  
        function.cache_priority()  
        self.functionList.append(function)
        self.sort_list()  

    def delete(self, i: int):
        # 检查索引是否存在
        if 0 <= i < len(self.functionList):
            self.functionList.pop(i)  # 从列表中删除指定索引的函数

    def removeType(self, funcType: int) -> bool:
        for i, function in enumerate(self.functionList):
            if function.type == funcType:
                del self.functionList[i]
                return True
        return False

# 多个节点的缓存映射(node id<->cache)
class CacheMap:
    def __init__(self):
        self.caches: Dict[int, Cache] = {}  # 物理节点ID到缓存的映射

    def show(self):
        if not self.caches:
            print("CacheMap is empty")
            return

        print("--------CacheMap--------")
        for cache in self.caches.values():
            cache.show()  # 调用 Cache 的 show 方法

    def show_priority(self):
        if not self.caches:
            print("CacheMap is empty")
            return

        print("--------CacheMap Priority--------")
        for cache in self.caches.values():
            cache.show_priority()  # 调用 Cache 的 show_priority 方法

    def add(self, function: Function):
        if function.phyNode.id in self.caches:
            self.caches[function.phyNode.id].add(function)  # 添加到现有缓存
        else:
            new_cache = Cache(function.phyNode.id)  # 创建新的 Cache 实例
            new_cache.add(function)  # 添加函数
            self.caches[function.phyNode.id] = new_cache  # 将新缓存添加到映射中

    # 寻找可用缓存容器
    def get_idle_function(self, phyNodeID: int, funcType: int) -> Tuple[Function, int]:
        if phyNodeID in self.caches:
            cache = self.caches[phyNodeID]
            for i, function in enumerate(cache.functionList):
                if function.type == funcType:
                    return function, i  # 返回找到的函数和索引
        return Function(), -1  # 返回空函数和-1表示未找到

    def delete(self, phyNodeID: int, funcIndex: int):
        if phyNodeID in self.caches:
            self.caches[phyNodeID].delete(funcIndex)  # 从缓存中删除函数

    def get_lowest_priority(self, phyNodeID: int) -> float:
        if phyNodeID in self.caches and self.caches[phyNodeID].functionList:
            return self.caches[phyNodeID].functionList[0].priority  # 返回最低优先级
        return 0.0  # 如果没有函数，返回0

    def delete_low_function(self, phyNodeID: int) -> Tuple[Function, bool]:
        if phyNodeID in self.caches and self.caches[phyNodeID].functionList:
            function = self.caches[phyNodeID].functionList[0]  # 获取最低优先级的函数
            self.caches[phyNodeID].delete(0)  # 删除该函数
            return function, True  # 返回函数和成功标志 
        return Function(), False  # 返回空函数和失败标志

    def sort(self):
        """对所有缓存进行排序"""
        for cache in self.caches.values():
            cache.sort_list()  # 调用 Cache 的 sort_list 方法

    def get(self, phyNodeID: int) -> Cache:
        return self.caches.get(phyNodeID, Cache(phyNodeID))

    def size(self) -> int:
        return sum(cache.size() for cache in self.caches.values())

    def delete_Prob(self, phyNodeID: int, funcType: int) -> bool:
        cache = self.get(phyNodeID)
        if cache.removeType(funcType):
            self.caches[phyNodeID] = cache
            topo_G.minusFreq(phyNodeID, funcType)
            return True
        return False
#endregion

#region ---------------------方法相关类----------------------
# 概率计算类
class ProbPair:
    def __init__(self, func_type: int, probability: float):
        self.func_type = func_type  # <funcType>
        self.probability = probability  # <probability>

class ProbPairVec:
    def __init__(self):
        self.prob_pair_v: List[ProbPair] = []  # 存储 ProbPair 对象的列表

    def sort_vec(self):
        self.prob_pair_v.sort(key=lambda pp: pp.probability)  # 按概率排序

    def push_back(self, pp: ProbPair):
        self.prob_pair_v.append(pp)  # 添加 ProbPair 对象

# Zipf样本类
class Zipf:
    def __init__(self, a, b, s):
        q = 1 - s
        self.qInv = 1 / q
        self.aPowQ = math.exp(math.log(a) * q)
        bPowQ = math.exp(math.log(b) * q)

        self.c = q / (bPowQ - self.aPowQ)
        self.qDivC = q / self.c

    def float64(self, u):
        ln = math.log(u * self.qDivC + self.aPowQ)
        t = math.exp(ln * self.qInv)
        return t

#endregion

############################################################

#region ---------------------函数初始化---------------------
# 创建4种函数实例
def create_func(func_type: int):
    """
    根据类型创建新的函数实例
    - func_type: 函数类型(1-4对应不同容器)
    """
    if func_type == 1:
        container_1 = Function(
            type=1,
            processTime=35,
            coldStartTime=5.31,
            size=55,
        )
        return container_1, None 
     
    elif func_type == 2:
        container_2 = Function(
            type=2,
            processTime=63,
            coldStartTime=5.33,
            size=158,
        )
        return container_2, None
    
    elif func_type == 3:
        container_3 = Function(
            type=3,
            processTime=20,
            coldStartTime=5.34,
            size=332,
        )
        return container_3, None
    
    elif func_type == 4:
        container_4 = Function(
            type=4,
            processTime=2076,
            coldStartTime=4.89,
            size=92,
        )
        return container_4, None
    
    return None, "Fail to getFunc...."

def init_func_map():
    fi = FunctionInfo(35, 5.31, 55)
    funcInfoMap_G.add(1, fi)

    fi = FunctionInfo(63, 5.33, 158)
    funcInfoMap_G.add(2, fi)

    fi = FunctionInfo(20, 5.34, 332)
    funcInfoMap_G.add(3, fi)

    fi = FunctionInfo(2076, 4.89, 92)
    funcInfoMap_G.add(4, fi)
#endregion

#region ----------------------拓扑相关----------------------
def load_topo():
    try:
        f = open(config_G.topo_file, 'r')
        # print("open topo file succeessfully.....")
        logging.info("open topo file succeessfully.....")
    except FileNotFoundError:
        print(f"无法找到拓扑文件 {config_G.topo_file}")
        return  # 如果文件未找到，直接返回

    csv_reader = csv.reader(f)
    node_id = 1
    for index, row in enumerate(csv_reader):
        if index == 0:  # 跳过标题行
            continue
        if row[0] == "":  # 跳过空行
            continue

        # 解析 CSV 行并创建 PhyNode 实例
        lat = float(row[1])
        long = float(row[2])

        loc = Location()
        loc.init(lat, long)
        node_map_G[node_id] = loc
        # 创建 PhyNode 实例并添加到 topo_G.nodes
        phynode = PhyNode(
            id=node_id,
            lat=lat,
            long=long,
            mem=config_G.mem_cap,          
            cpuFreq=config_G.cpu_Freq      
        )
        logging.info(f"node {node_id} lat {lat} long {long}")
        topo_G.add_node(node_id, phynode)
        node_id += 1
    f.close()
    return

def update_topo(operator: str, phyNodeID: int, mem: float):
    if operator == "add":
        topo_G.update("add", phyNodeID, mem)  # 增加内存
    elif operator == "minus":
        topo_G.update("minus", phyNodeID, mem)  # 减少内存

def get_phy_node(ingress_id: int):
    """根据入口节点 ID 获取物理节点"""
    for node in topo_G.nodes.values():
        if node.id == ingress_id:
            return node, None
    logging.error(f"phyNode {ingress_id} does not exist.....")
    return None, f"Non-exist phyNode {ingress_id}"  

def sort_phynodes(request: Request) -> DistSlice:
    """
    根据请求到物理节点的距离对节点进行排序
    """
    ds = DistSlice() 
    for node in topo_G.nodes.values():
        dist = calculate_distance(request.ingress, node)  # 计算距离
        d = Distance(phyNodeID=node.id, distance=dist)  # 实例化 Distance 对象
        ds.slice.append(d)  # 将 Distance 对象添加到 DistSlice 的切片中

    # 按距离排序
    ds.slice.sort(key=lambda d: d.distance)  # 使用 Python 的 sort 方法进行排序

    return ds  # 返回排序后的 DistSlice 对象

def calculate_distance(phyNode1: PhyNode, phyNode2: PhyNode, unit: str = "K") -> float:
    """
    计算两个物理节点之间的距离
    """
    #print(f"phyNode1 type: {type(phyNode1)}")
    #print(f"phyNode2 type: {type(phyNode2)}")
    lat1 = phyNode1.lat
    lng1 = phyNode1.long
    lat2 = phyNode2.lat
    lng2 = phyNode2.long

    # 如果坐标相同，距离为0
    if lat1 == lat2 and lng1 == lng2:
        return 0.0

    radlat1 = math.radians(lat1)
    radlat2 = math.radians(lat2)

    theta = lng1 - lng2
    radtheta = math.radians(theta)

    dist = math.sin(radlat1) * math.sin(radlat2) + math.cos(radlat1) * math.cos(radlat2) * math.cos(radtheta)
    if dist > 1:
        dist = 1

    dist = math.acos(dist)
    dist = math.degrees(dist)
    dist = dist * 60 * 1.1515  # 转换为英里

    if unit == "K":
        dist = dist * 1.609344  # 转换为公里
    elif unit == "N":
        dist = dist * 0.8684  # 转换为海里

    return dist

def show_nodes_memory():
        # print("----node memomry----")
        logging.info("----node memomry----")
        for node_id in topo_G.nodes:        
            logging.info(f"node {node_id} : {topo_G.nodes[node_id].mem} MB")
        # for i in range(len(topo_G.nodes)):
        #     print(f"node {i} : {topo_G.nodes[i].mem} MB") 
#endregion

#region --------------------请求相关(S-Cache)----------------
def gen_req_zipf(request_file: str):
    """
    使用Zipf分布生成请求
    """
    rFile = load_request(request_file, config_G.redu_factor)  # 加载请求数据
    # load_requests_to_csv(rFile, './result/load_requests.csv')

    for i in range(len(rFile.app1)):  # 遍历应用1请求
    # for i in range(1):  # 遍历应用1请求
        sample = generate_zipf_samples(config_G.node_num, rFile.app1[i], config_G.beta)  # 生成Zipf分布样本
        gen_requests(sample, 1, i)  # 生成请求

    for i in range(len(rFile.app2)):  
        sample = generate_zipf_samples(config_G.node_num, rFile.app2[i], config_G.beta)
        gen_requests(sample, 2, i)  

    for i in range(len(rFile.app3)): 
        sample = generate_zipf_samples(config_G.node_num, rFile.app3[i], config_G.beta)  
        gen_requests(sample, 3, i)  

    for i in range(len(rFile.app4)): 
        sample = generate_zipf_samples(config_G.node_num, rFile.app4[i], config_G.beta)  
        gen_requests(sample, 4, i) 

def load_requests_to_csv(rFile: RequestFile, output_file: str):
    with open(output_file, mode='w', newline='') as file:
        writer = csv.writer(file)
        
        # 写入CSV头
        writer.writerow(['Time', 'App1 Requests', 'App2 Requests', 'App3 Requests', 'App4 Requests'])
        
        # 找到最大请求数
        max_length = max(len(rFile.app1), len(rFile.app2), len(rFile.app3), len(rFile.app4))
        
        for i in range(max_length):
            row = [rFile.time[i] if i < len(rFile.time) else '']  # 时间
            row.append(rFile.app1[i] if i < len(rFile.app1) else 0)  # 应用1请求
            row.append(rFile.app2[i] if i < len(rFile.app2) else 0)  # 应用2请求
            row.append(rFile.app3[i] if i < len(rFile.app3) else 0)  # 应用3请求
            row.append(rFile.app4[i] if i < len(rFile.app4) else 0)  # 应用4请求
            writer.writerow(row)

def load_request(rFile: str, reduct_factor: float) -> RequestFile:
    """
    从CSV文件加载请求数据
    """
    data = RequestFile()  # 初始化请求数据对象
    try:
        with open(rFile, mode='r') as file:
            # print("-----open request file succeessfully-----")
            logging.info("-----open request file succeessfully-----")   
            reader = csv.reader(file)
            csv_lines = list(reader)
    except FileNotFoundError:
        print(f"无法找到请求文件 {rFile}")
        return data  # 返回空数据

    for index, line in enumerate(csv_lines):
        if index == 0:  # 第一行是时间
            for value in line:
                value = float(value)  # 解析为浮点数
                data.time.append(int(value))  # 添加到时间列表
        elif index == 1:
            for value in line:
                value = float(value)  # 解析为浮点数
                data.app1.append(int(value / reduct_factor))  # 添加到应用1请求列表
        elif index == 3:
            for value in line:
                value = float(value)  
                data.app2.append(int(value / reduct_factor))  # 添加到应用2请求列表
        elif index == 5:
            for value in line:
                value = float(value)  
                data.app3.append(int(value / reduct_factor))  # 添加到应用3请求列表
        elif index == 7:
            for value in line:
                value = float(value) 
                data.app4.append(int(value / reduct_factor))  # 添加到应用4请求列表
    file.close()
    return data  # 返回请求数据对象

def generate_zipf_samples(node_num: int, max_num: int, beta: float) -> List[int]:
    """
    生成符合Zipf分布的样本
    """
    node_num += 1
    a = float(1)
    b = float(node_num)
    if beta == 1:
        beta += 0.01  
    s = float(beta)
    z = new_zipf(a,b,s)
    
    sample_cnt = float(max_num)
    sample = [0] * node_num  
    for u in np.arange(0, 1, 1 / sample_cnt):
        x = int(z.float64(u))
        if x < 0 or x >= node_num:
            continue
        sample[x] += 1

    sample = sample[1:]  

    if len(sample) > node_num - 1:
        sample = sample[:node_num - 1]

    return sample

def gen_request(type: int, IngressID: int, arriveTime: int):
    global req_count_G
    r = Request()  # 初始化请求对象

    # 创建函数实例
    function, err = create_func(type)
    if err:
        logging.error(f"Fail to create_func: {err}")
        return None, err 

    # 获取物理节点
    ingress, err = get_phy_node(IngressID)
    if err:
        logging.error(f"Fail to genRequest: {err}")
        return None, err  

    # 创建请求对象
    r = Request(
        id=req_count_G,
        function=function,
        ingress=ingress,
        arriveTime=arriveTime,
        served=False,
        isColdStart=False,
        deployNode=None
    )
    req_count_G += 1

    return r, None  # 返回请求对象和无错误信息

def gen_requests(sample: List[int], type: int, timeSlot: int):
    for i in range(1, len(sample)):  # 遍历样本从1开始
        for j in range(sample[i]):  # 根据样本值生成请求
            request, _ = gen_request(type, i, timeSlot)  # 生成请求
            requestsMap_G.add(request, timeSlot)  # 将请求添加到请求映射

def new_zipf(a, b, s):
    return Zipf(a, b, s)
#endregion

#region ------------------请求相关(Cross-Edge)---------------
# 加载Zipf请求文件
def read_requests(request_file: str):
    try:
        with open(request_file, mode='r') as file:
            # print("open request file succeessfully.....")
            logging.info("open request file succeessfully.....")

            ront = Req_on_Nodes_Time()  # 存储每个时间槽的请求分布
            funcType = 0  # 函数类型计数器（1-4）

            for line in file:
                line = line.strip()
                if "funcType" in line:
                    if funcType == 0:
                        funcType += 1
                        continue  # 跳过第一个标题行
                    logging.info(f"add functype {funcType}")
                    # 将当前收集的请求数据存入全局变量
                    rontt_G.add(funcType, ront)
                    ront.clear    # 重置时间序列
                    funcType += 1
                    continue

                # 解析每个节点的请求数量（逗号分割）
                count = 1
                ron = Req_on_Nodes()
                for req_num in line.split(","):
                    logging.info(f"node {count} req_num: {req_num}")
                    count += 1
                    ron.add(int(req_num))
                ront.add(ron)  # 添加到当前时间槽

            # 所有结束时添加函数类型4
            if funcType > 0:
                logging.info(f"add functype {funcType}")
                rontt_G.add(funcType, ront)

            file.close()
            return

    except FileNotFoundError:
        print(f"无法打开请求文件: {request_file}")
        return    

# 通过rontt_G创建所有请求
def create_requests():
    # 遍历 rontt_G 中的每个函数类型
    for func_type, req_on_nodes_time in rontt_G.numMap.items():
        # 遍历每个时间槽
        for time_slot, req_on_nodes in enumerate(req_on_nodes_time.numVector):
            # 为每个时间槽创建请求
            create_request_in_slot(time_slot, func_type, req_on_nodes)

def create_request_in_slot(time_slot: int, func_type: int, req_on_nodes: Req_on_Nodes):
    # 遍历每个节点的请求数量
    for node_index, req_num in enumerate(req_on_nodes.numVector):
        node_id = node_index + 1  # 假设节点ID从1开始
        req_num = int(req_num / config_G.redu_factor)  # 根据减少因子调整请求数量

        # 为每个请求数量生成请求
        for _ in range(req_num):
            request, err = create_request(time_slot, func_type, node_id)
            if not err:
                requestsMap_G.add(request, time_slot)

def create_request(time_slot: int, func_type: int, ingress_id: int):
    global req_count_G
    # 获取物理节点位置
    loc = node_map_G.get(ingress_id)
    if loc:
        # 创建 PhyNode 实例
        ingress = PhyNode(
            id=ingress_id,
            lat=loc.latitude,
            long=loc.longitude,
            mem=config_G.mem_cap,
            cpuFreq=config_G.cpu_Freq
        )

        # 获取函数信息
        function_info = funcInfoMap_G.func_map.get(func_type)
        if function_info:
            # 创建函数实例
            function = Function(
                type=func_type,
                processTime=function_info.processTime,
                coldStartTime=function_info.coldStartTime,
                size=function_info.size
            )

            # 创建请求对象
            request = Request(
                id=req_count_G,
                function=function,
                ingress=ingress,
                arriveTime=time_slot,
                served=False,
                isColdStart=False,
                deployNode=None
            )
            req_count_G += 1
            return request, None
        else:
            logging.error(f"Cannot find funcType {func_type}")
            return None, f"Cannot find funcType {func_type}"
    else:
        logging.error(f"Non-exist phyNode {ingress_id}")
        return None, f"Non-exist phyNode {ingress_id}"
#endregion

#region ----------------------系统成本----------------------
# 获取一个函数的实例化成本
def get_instan_cost(phyNodeID: int, funcType: int) -> float:
    """获取某个物理节点中某个函数类型的实例化成本"""
    cpu_freq = get_cpu(phyNodeID)
    size = get_container_size(funcType)

    if cpu_freq == 0:
        return 0

    instan_cost = size / cpu_freq

    if instan_cost == 0:
        print("instant cost is 0")

    return instan_cost

# 获取特定物理节点的CPU频率
def get_cpu(phyNodeID: int) -> float:
    """获取某个物理节点的CPU频率"""
    phy_node = topo_G.get(phyNodeID)
    if phy_node.id == 0:
        print("Cannot find the node 0")
        return 0
    else:
        return phy_node.cpuFreq

# 获取运行成本
def get_run_cost(phyNodeID: int, funcType: int) -> float:
    """获取某个物理节点中某个函数类型的运行成本"""
    cpu_freq = get_cpu(phyNodeID)
    function_size = get_container_size(funcType)

    if cpu_freq == 0:
        print("run cost cpuFreq is 0")
        return 0

    run_cost = function_size * cpu_freq * config_G.alpha

    if function_size == 0:
        print("run cost size is 0")
    elif config_G.alpha == 0:
        print("run cost Alpha is 0")

    return run_cost
#endregion

#region ----------------------请求分发----------------------
global a, b, c
a = 0
b = 0
c = 0
    
def deploy_request(request: Request):
    """
    部署一个请求
    - request: 请求对象
    """
    # 根据请求到物理节点的距离，对节点进行排序
    ds = sort_phynodes(request)
    # 寻找当前节点上可用的容器
    f, i = cacheMap_G.get_idle_function(request.ingress.id, request.function.type)
    
    if i == -1:
        # 如果在当前节点没有相应的容器，寻找一个邻近节点
        if not deploy_to_neighbour(ds, request):
            # 如果没找到邻近节点，还有未处理的请求，在当前节点创建容器
            global c
            c += 1
            create_to_current(request)
    else:
        global a
        a += 1
        # 当前节点有充足的相应容器，直接在当前节点处理
        place_to_current(request, f, i)

def deploy_requests():
    global total_req_count_G, clock_G

    for i in range(1, config_G.slot_num + 1):
        requests, found = requestsMap_G.get(i)  # 从请求映射中获取当前时间槽的请求
        if not found:
            print(f"--------cannot find time slot {i}--------")
            continue

        for request in requests:
            deploy_request(request)
            total_req_count_G += 1 
        
        clock_G += 1
        update_cache()

def place_to_current(request: Request, function: Function, index: int):
    """
    将请求分配给当前节点的缓存容器
    - request: 请求对象
    - function: 函数对象
    - index: 缓存索引
    """
    activeFunctions_G.add(function, request.ingress.id)  # 将函数添加到活动列表
    topo_G.addFreqAll(request.function.type)
    topo_G.setRecencyAll(request.function.type, float(request.arriveTime))
    cacheMap_G.delete(request.ingress.id, index)  # 从缓存列表中删除
    request.update(function, request.ingress, False)  # 更新请求状态

def deploy_to_neighbour(distances: DistSlice, request: Request) -> bool:
    """
    尝试将请求分配给邻近的缓存函数
    """
    for slice in distances.slice[1:]:  
        node_id = slice.get_id()
        instanCost = get_instan_cost(node_id, request.function.type)
        # Cross-Edge 传输延迟 < 实例化成本
        if slice.distance * config_G.latency_para < instanCost:
        # S-Cache 传输延迟 < 冷启动时间
        # if slice.distance * config_G.latency_para < request.function.coldStartTime:
            function, index = cacheMap_G.get_idle_function(slice.phyNodeID, request.function.type)
            if index != -1:
                global b
                b += 1
                place_to_neighbour(request, function, index, slice.phyNodeID)
                return True
            
    return False

def place_to_neighbour(request: Request, function: Function, index: int, phyNodeID: int):
    """
    将请求分配给邻近的缓存函数
    """
    activeFunctions_G.add(function, phyNodeID)  # 将函数添加到活动列表
    topo_G.addFreqAll(function.type)
    topo_G.setRecencyAll(function.type, float(request.arriveTime))
    cacheMap_G.delete(phyNodeID, index)  # 从缓存列表中删除
    
    deploy_node, err = get_phy_node(phyNodeID)  # 获取物理节点
    if err is not None:
        return
    request.update(function, deploy_node, False)  # 更新请求状态

def update_cache():
    """在每个时间间隔结束时，更新优先级，将活动函数移动到缓存函数"""
    for phyNodeID in activeFunctions_G.map.keys():
        for funcType in activeFunctions_G.map[phyNodeID].functions.keys():
            #for k in range(len(activeFunctions_G.map[phyNodeID].functions[funcType].slice)):
            for k in range(len(activeFunctions_G.map[phyNodeID].functions[funcType].slice) - 1, -1, -1):
                f = activeFunctions_G.map[phyNodeID].functions[funcType].slice[k]
                cacheMap_G.add(f)  # 将活动函数添加到缓存
                activeFunctions_G.delete(phyNodeID, funcType, k)  # 从活动函数中删除

    cacheMap_G.sort()

    # 清空活动函数列表
    activeFunctions_G.map.clear()  # 清空活动函数列表
#endregion

#region ----------------------S-Cache----------------------
""" # 请求分发的最坏情况-获取最低优先级的容器来驱逐
def create_to_current(request: Request):
    request.function.active_priority()  # 更新函数优先级
    f = Function() 
    succ_flag = True
    # 如果节点内存不足，先尝试删除低优先级容器
    if request.function.size > topo_G.nodes[request.ingress.id].mem:
        while succ_flag:
            # 获取最低优先级的容器
            lowest_priority = cacheMap_G.get_lowest_priority(request.ingress.id)
            # 且请求的优先级 > 优先级最低的容器
            if lowest_priority < request.function.priority:
                f, succ_flag = cacheMap_G.delete_low_function(request.ingress.id)
                if succ_flag:
                    update_topo("add", request.ingress.id, f.size)  # 更新内存
                # 如果销毁后的内存空间充足
                if request.function.size <= topo_G.nodes[request.ingress.id].mem:
                    request.function.phyNode = request.ingress  # 设置请求的物理节点为当前节点
                    activeFunctions_G.add(request.function, request.ingress.id)  # 更新活动函数列表
                    update_topo("minus", request.ingress.id, request.function.size)  # 更新内存
                    request.update(request.function, request.ingress, True)  # 标记为冷启动
                    return
            else:
                return
    # 内存仍然不足，创建新容器
    else:
        request.function.phyNode = request.ingress  # 设置请求的物理节点为当前节点
        activeFunctions_G.add(request.function, request.ingress.id)  # 更新活动函数列表
        update_topo("minus", request.ingress.id, request.function.size)  # 更新内存
        request.update(request.function, request.ingress, True)  # 标记为冷启动
 """
#endregion

#region ---------------------Cross-Edge--------------------
# 请求分发的最坏情况-获取要被驱逐的容器类型
def create_to_current(request: Request):
    succ_flag = True
    count = 0
    # 检查请求的函数大小超过当前节点的内存容量，尝试驱逐容器
    if request.function.size > topo_G.nodes[request.ingress.id].mem:  
        while count < 1000: 
            count += 1
            func_type = get_evicted_container(request.ingress.id, request.function.type)
            
            # 要驱逐的容器类型与请求的相同或没有可驱逐的容器，则退出
            if func_type == request.function.type or func_type == 0: 
                return

            succ_flag = cacheMap_G.delete_Prob(request.ingress.id, func_type)
            function_size = get_container_size(func_type)
            
            # 如果删除成功，更新节点的内存信息
            if succ_flag:  
                update_topo("add", request.ingress.id, function_size)  # 更新内存
            
            # 节点内存足够以创建新的请求容器，标记为冷启动，更新信息
            if request.function.size <= topo_G.get(request.ingress.id).mem:  
                request.function.phyNode = request.ingress                                         
                activeFunctions_G.add(request.function, request.ingress.id)   
                topo_G.addFreqAll(request.function.type)                                             
                topo_G.setRecencyAll(request.function.type, float(request.arriveTime))             
                update_topo("minus", request.ingress.id, request.function.size)                        
                request.update(request.function, request.ingress, True)                                
                return
            
            # 如果删除不成功且没有足够空间，则退出
            if not succ_flag: 
                return
    else:
        request.function.phyNode = request.ingress                              # 更新部署节点为当前节点
        activeFunctions_G.add(request.function, request.ingress.id)             # 将请求添加到活动函数列表中
        topo_G.addFreqAll(request.function.type)                                # 更新所有节点的频率信息
        topo_G.setRecencyAll(request.function.type, float(request.arriveTime))  # 更新所有节点的最近使用时间
        update_topo("minus", request.ingress.id, request.function.size)         # 更新节点内存信息               
        request.update(request.function, request.ingress, True)                 # 更新请求为冷启动


# 获取容器大小
def get_container_size(funcType: int) -> int:
    return funcInfoMap_G.get_size(funcType)

# 计算边缘节点p中类型n容器的驱逐概率
def get_prob(nodeID: int, funcType: int) -> float:
    phy_node = topo_G.get(nodeID)
    instan_cost = get_instan_cost(nodeID, funcType)
    freq = phy_node.getFreq(funcType)
    recency = phy_node.getRecency(funcType)
    # logging.info(f"functype: {funcType}, freq: {freq}, recent: {recency}")

    size = get_container_size(funcType)

    if freq == 0:
        logging.error(f"cannot find freq for funcType {funcType} at node {nodeID}")
        return 0
    if recency == 0:
        logging.error(f"cannot find recent for funcType {funcType} at node {nodeID}")
        return 0
    if size == 0:
        logging.error(f"cannot find size for funcType {funcType} at node {nodeID}")
        return 0

    # 将单位从MB转换，这将减少size在概率计算中的影响
    # return (float)size / (freq*instanCost + recent)
    return size / (freq + recency) / 1000

# 获取要被驱逐的容器类型
def get_evicted_container(nodeID: int, reqFuncType: int) -> int:
    # 计算请求函数类型的驱逐概率
    threshold = get_prob(nodeID, reqFuncType)

    # 创建一个字典来存储概率
    prob_map = {}
    total_prob = 0

    # 计算每个函数类型的概率
    for funcType in funcInfoMap_G.func_map.keys():
        prob = get_prob(nodeID, funcType)
        prob_map[funcType] = prob
        total_prob += prob

    # 将阈值转换为概率
    threshold = threshold / total_prob

    # 归一化概率
    prob_pair_vec = ProbPairVec()
    for funcType, prob in prob_map.items():
        normalized_prob = prob / total_prob
        prob_pair = ProbPair(funcType, normalized_prob)
        prob_pair_vec.push_back(prob_pair)

    # 对概率进行排序
    prob_pair_vec.sort_vec()

    # 生成一个随机数
    random_value = np.random.rand()
    logging.info(f"Random number: {random_value}, Threshold: {threshold}")

    # 累积概率
    accum_prob = 0
    for prob_pair in prob_pair_vec.prob_pair_v:
        accum_prob += prob_pair.probability
        if random_value < accum_prob:
            logging.info(f"Evicting function type: {prob_pair.func_type}")
            return prob_pair.func_type

    logging.info("No need to evict")
    return 0  # 如果没有需要驱逐的容器，返回 0
#endregion

#region ----------------------打印结果----------------------
def print_result():
    print("----------------------Schedule End----------------------")
    logging.info("----------------------Schedule End----------------------")
    
    logging.info("current node deploy count: %d", a)
    logging.info("neighbor node deploy count: %d", b)
    logging.info("try to create on current node count: %d", c)
    print("部署在当前节点计数:", a)
    print("部署在邻居节点计数:", b)
    print("尝试在当前节点创建新容器计数:", c)
    '''
    activeFunctions_G.show()            # 显示活动函数  
    activeFunctions_G.show_priority()   # 显示活动函数优先级
    cacheMap_G.show()                   # 显示缓存函数
    cacheMap_G.show_priority()          # 显示缓存函数优先级
    '''

    global cold_req_count_G, served_req_count_G
    # single_cost = 0         # 单个请求的总成本
    # total_comm_cost = 0
    # total_instan_cost = 0
    # total_run_cost = 0
    # avg_cost = 0

    for i in range(0, requestsMap_G.size() + 1):
        if i in requestsMap_G.map:
            requests = requestsMap_G.map[i].requests
            for request in requests:
                # 跳过非被服务的请求
                if not request.served:
                    continue
                served_req_count_G += 1
                
                # 计算请求传输延迟
                # dist = calculate_distance(request.ingress, request.deployNode)
                # comm_cost = dist * config_G.latency_para
                # total_comm_cost += comm_cost

                # run_cost = get_run_cost(request.deployNode.id, request.function.type)
                # total_run_cost += run_cost

                if request.isColdStart:
                    cold_req_count_G += 1
                    # instan_cost = get_instan_cost(request.deployNode.id, request.function.type)
                    # total_instan_cost += instan_cost

                # single_cost = comm_cost + run_cost + (instan_cost if request.isColdStart else 0)
                # avg_cost += single_cost
    
    # 打印结果
    cold_start_frequency = cold_req_count_G / served_req_count_G
    logging.info(f"total_req_count_G: {total_req_count_G}, served_req_count_G: {served_req_count_G}")
    logging.info(f"cold_req_count_G: {cold_req_count_G}, cold_start_frequency: {cold_start_frequency:.2f}")
    print(f"实际请求总数: {total_req_count_G}, 服务请求总数: {served_req_count_G}, "
          f"冷启动请求总数: {cold_req_count_G}, 冷启动频率: {cold_start_frequency:.2f}")
    
    # 输出请求情况
    result_to_csv(f'./result/result-{config_G.beta}-{config_G.redu_factor}.csv')

    show_nodes_memory()

def result_to_csv(output_file: str):
    # 打开CSV文件进行写入
    with open(output_file, mode='w', newline='') as file:
        writer = csv.writer(file)

        # 性能指标
        cold_start_frequency = cold_req_count_G / served_req_count_G
        writer.writerow([
            "total_req_count",
            "served_req_count",
            "cold_req_count",
            "cold_start_frequency"
        ])
        writer.writerow([
            total_req_count_G,
            served_req_count_G,
            cold_req_count_G,
            f"{cold_start_frequency:.2f}"
        ])

        # 写入CSV头
        writer.writerow(['Time', 'ID', 'Function', 'Ingress', 'DeployNode', 'IsColdStart'])

        # 遍历请求映射中的每个时间槽
        for config_G.slot_num, requestsMap in requestsMap_G.map.items():
            # 遍历每个时间槽中的请求
            for request in requestsMap.requests:
                if request.served is not False:      # 跳过未被服务的请求
                    writer.writerow([
                        request.arriveTime,
                        request.id,
                        request.function.type,
                        request.ingress.id,
                        request.deployNode.id,
                        request.isColdStart,
                        # request.served
                    ])
#endregion

topo_G = Topology()
node_map_G: Dict[int, Location] = {}    # nodeID, Location

# 调度总逻辑
def schedule_requests():
    
    load_topo()
    initGlobal()
    init_func_map()
    
    # Zipf分布生成请求
    # request_file = './data/request/prediction.csv'
    # gen_req_zipf(request_file)
    request_file = f'./data/request/requests-{config_G.beta}.csv' 
    read_requests(request_file)    
    create_requests()           

    show_nodes_memory()     # 显示物理节点内存信息

    print("----------------------Schedule Start----------------------")
    logging.info("----------------------Schedule Start----------------------")
    # 调度请求
    deploy_requests()
    
    print_result()

if __name__ == "__main__":
    
    schedule_requests()


    