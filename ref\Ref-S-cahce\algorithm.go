package main

import(
    "log"
    "encoding/json"
	"net/http"
    "net/url"
    "bytes"
    "io/ioutil"
    "errors"
    "time"
)

// 活动函数的全局变量
var activeFunctions_G ActiveFunctions //add will auto add freq by 1

// 存储所有缓存函数的缓存
var cacheMap_G CacheMap //add will auto remove freq by 1

var config_G Config

var functionfreq_G FunctionFreq  //add when push to  activeFunctions_G, minus when push to cacheMap_G                                 
//当推送到activeFunctions_G时++，当推送到cacheMap_G时--
var timeInterval int //时间槽 1 ....1440

var topo_G Topology

var clock_G float64 //每轮开始时更新

var count_G int // 容器总数计数，也用于命名新容器

var endPoints_G EndPoints

var requestCount_G int

var requestsMap_G RequestsMap

// log.SetLevel(log.DebugLevel)
// 添加部署节点到请求

// 初始化全局变量
func initGlobal(){
    count_G = 0
    // 始终先初始化映射
    functionfreq_G.init()
    activeFunctions_G.init()
    cacheMap_G.init()
    requestsMap_G.init()
    clock_G = 1
    requestCount_G = 0
}

//-----------------------------请求处理-----------------------------------

// 将请求分配给当前节点的缓存容器
func placeToCurrent(requestPtr *Request, Function Function, i int){ 
    // log.Debug( "start placeToCurrent....")
    // 将其放入活动列表
    activeFunctions_G.add(Function, requestPtr.Ingress.ID)

	// 从缓存列表中删除
    cacheMap_G.delete(requestPtr.Ingress.ID, i)

	requestPtr.update(Function, requestPtr.Ingress, false)
}

// 更新拓扑内存
func updateTopo(operator string, phyNodeID int, mem float64){
    if operator == "add"{
        topo_G.Nodes[phyNodeID].Mem = topo_G.Nodes[phyNodeID].Mem + mem
    }else if operator == "minus" {
        topo_G.Nodes[phyNodeID].Mem = topo_G.Nodes[phyNodeID].Mem - mem
    }
}

// 在当前节点创建新容器
func createToCurrent(requestPtr *Request, i int){
    // log.Debug( "start createToCurrent....")
    // 更新容器优先级
    requestPtr.Function.activePriority() // 使用此请求时，首先更新优先级
    succFlag := true
    var f Function
    // 如果内存不足且该请求优先级高于缓存的优先级
    if requestPtr.Function.Size > topo_G.Nodes[requestPtr.Ingress.ID].Mem{
        // 清空缓存映射
        for{
            p2 := cacheMap_G.getLowestPriority(requestPtr.Ingress.ID)
            // 该请求的优先级 > 优先级最低的容器
            if p2 < requestPtr.Function.Priority{
                // 销毁优先级最低的容器       
                f, succFlag = cacheMap_G.deleteLowFunction(requestPtr.Ingress.ID) // 删除低优先级函数
                if succFlag == true{
                    // 如果成功，终止容器
                    termContainers(f)
                    updateTopo("add", requestPtr.Ingress.ID, f.Size)
                }
                // 如果销毁后的内存空间充足
                if requestPtr.Function.Size <= topo_G.Nodes[requestPtr.Ingress.ID].Mem{
                    // 调用 knative 创建新容器
                    createContainers(requestPtr)
                    // 将其放入活动列表
                    activeFunctions_G.add(requestPtr.Function, requestPtr.Ingress.ID)
                    updateTopo("minus", requestPtr.Ingress.ID, requestPtr.Function.Size)
                    return
                }
                // 这意味着没有更多空间可以删除
                if succFlag == false {
                    return
                }
            }else{
                return
            }
        }
    }else{
        createContainers(requestPtr)
        // 将其放入活动列表
        activeFunctions_G.add(requestPtr.Function, requestPtr.Ingress.ID)
        updateTopo("minus", requestPtr.Ingress.ID, requestPtr.Function.Size)
        return
    }
}

// 将请求分配给邻近的缓存函数
func placeToNeighbour(requestPtr *Request, Function Function, i int, phyNodeID int){
    // log.Debug( "start placeToNeighbour....")
    // 将其放入活动列表
    activeFunctions_G.add(Function, phyNodeID)
	// 从缓存列表中删除
    cacheMap_G.delete(phyNodeID, i)
    
    deployNode, err := getPhyNode(phyNodeID)

    if err != nil{
        return
    }

	requestPtr.update(Function, deployNode, false)
}

// 尝试分配给邻近的缓存函数，否则在当前节点创建新容器
func deployToNeighbour(ds DistSlice, requestPtr *Request) (bool){
    succFlag := false

    // 忽略第一个元素，它是当前节点
    for j := 1; j < len(ds.Slice); j= j+1{
        // 如果链路延迟 < 冷启动时间
        if (ds.Slice[j].distance * config_G.LatencyPara < requestPtr.Function.ColdStartTime){
            f, i := cacheMap_G.getIdleFunction(ds.Slice[j].PhyNodeID, requestPtr.Function.Type)
            if (i == -1){
                // 邻近节点没有此函数，寻找下一个节点
                continue
            }else{
                // 将请求分配给邻近节点
                placeToNeighbour(requestPtr, f, i, ds.Slice[j].PhyNodeID)
                succFlag = true
            }
        }
    }

    return succFlag
}

//-----------------------------容器管理-----------------------------------

// 从活动列表中移除，移除缓存
func createContainers(requestPtr *Request){
    // log.Debug("Starting createContainers()......")
    count_G = count_G + 1
    requestPtr.Function.init(requestPtr.Ingress) // 在当前节点创建新请求

	service, err := createService(requestPtr.Function)

    requestPtr.update(requestPtr.Function, requestPtr.Ingress, true)

	if err != nil {
		log.Printf("fail to createContainers")
	}

    err = postService(service)
    log.Println("containercount %d", count_G)
}

// 从活动列表中移除，移除缓存
func termContainers(f Function) error{
    // log.Debug("Starting termContainers......")
    err := deleteService(f)

	if err != nil {
		log.Printf("fail to termContainers")
		return err
	}

	return nil
}

//-----------------------------缓存更新-----------------------------------

// 一个时间间隔结束，更新优先级，添加到 cacheMap 或终止它
func updateCache(){
    // log.Debug("Starting updateCache......")
    // 在每个时间间隔结束时，将活动容器移动到缓存容器
    for phyNodeID, _ := range activeFunctions_G.Map{nctions_G.Map[phyNodeID].Functions{
            for k := 0; k < len(activeFunctions_G.Map[phyNodeID].F
        for funcType, _ := range activeFuunctions[funcType].Slice); k++{
                  f := activeFunctions_G.Map[phyNodeID].Functions[funcType].Slice[k]
                  cacheMap_G.add(f)
                  activeFunctions_G.delete(phyNodeID, funcType, k)
            }
        }
    }
    
    cacheMap_G.sort()

    // 清空活动函数列表
    activeFunctions_G = ActiveFunctions{}
}

//-----------------------------调度请求-----------------------------------

// 部署一个请求
func deployRequest(requestPtr *Request){
    // 根据请求到物理节点的距离，对节点进行排序
    ds := sortPhyNodes(requestPtr)
    
    // log.Debug("Starting getIdleFunction......")
    // 寻找当前节点上可用的容器
    f, i := cacheMap_G.getIdleFunction(requestPtr.Ingress.ID, requestPtr.Function.Type)

    if i == -1{
        // 如果在当前节点没有相应的容器，寻找一个邻近节点
        if deployToNeighbour(ds, requestPtr) == false{
            // 如果没找到邻近节点，还有未处理的请求，在当前节点创建容器
            createToCurrent(requestPtr, i)
        }
    }else{
        // 当前节点有充足的相应容器，直接在当前节点处理
        placeToCurrent(requestPtr, f, i)
    }
}

// 部署一个时间间隔的请求
func deployRequests(requests []Request){
    for i := 0; i < len(requests); i++{
        requestPtr := &(requests[i])
        deployRequest(requestPtr)
    }
}

//-----------------------------结果发送-----------------------------------

// 请求完成，使用 json 发送结果
func sendResult(r Request) error{
    log.Printf("sendResult......")
    var b []byte
	body := bytes.NewBuffer(b)
	err := json.NewEncoder(body).Encode(r)
	if err != nil {
		return err
	}

    request := &http.Request{
		Body:          ioutil.NopCloser(body),
		ContentLength: int64(body.Len()),
		Header:        make(http.Header),
		Method:        http.MethodPost,
		URL: &url.URL{
			Host:   trafficGenEndpoint,
			Scheme: "http",	
		},
	}
	request.Header.Set("Content-Type", "application/json")

    resp, err := http.DefaultClient.Do(request)

    if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
        log.Printf("sendResult: Unexpected HTTP status code", resp.Status)
		return errors.New("sendResult: Unexpected HTTP status code" + resp.Status)
	}

    // log.Debug("sendResult: finish....")
	return nil
}

func sendResults(requests []Request) {
    for i := 0; i < len(requests); i++{
        sendResult(requests[i])
    }
} 

//-----------------------------主调度函数-----------------------------------

// 生成请求，并在每个时间间隔内调度请求，发送结果，并更新缓存
func scheduleRequests(){
    // 加载配置和拓扑信息
    LoadConfig("./config/my-config.json")
	loadTopo(config_G.TopoName)
	initGlobal()    // 初始化全局变量
                         
    // 生成请求，减少请求量的系数为10000
	genReqZipf(config_G.RequestFile, config_G.ReduFactor) 
    showPhyNodesMem()   // 显示物理节点内存信息

    // 调度请求
    for i := 0; i < config_G.SlotNum; i++{  // 遍历每个时间槽
        requests, found := requestsMap_G.get(i) // 获取当前时间槽的请求
        if found == false{  // 如果没有找到请求
            log.Printf("cannot find time slot %d", i)
            break
        }
        deployRequests(requests)    // 调度请求
    
        // startSchedule()

        // 调用发送 http 请求
        sendResults(requests)
        
        updateCache()   // 更新缓存
        log.Println("result node id", requests[0].Function.PhyNode.ID)
        // 等待容器启动
        time.Sleep(60 * time.Second)
        log.Printf("wait 60 seconds .....")
    }

    getRequestsMapSum() // 获取请求数量的总和      
    showPhyNodesMem()   // 显示物理节点内存信息 
    activeFunctions_G.show()    // 显示活动函数  
    cacheMap_G.show()   // 显示缓存函数
    activeFunctions_G.showPriority()    // 显示活动函数优先级
    cacheMap_G.showPriority()   // 显示缓存函数优先级
    
    log.Printf("All algorithm finished............")
}