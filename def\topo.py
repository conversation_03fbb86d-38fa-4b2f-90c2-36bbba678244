import csv

# 定义 PhyNode 类
class PhyNode:
    def __init__(self, node_id, lat, long, mem):
        self.id = node_id  # 节点的唯一标识符
        self.lat = lat      # 节点的纬度
        self.long = long    # 节点的经度
        self.Mem = mem      # 节点的内存容量

    def __str__(self):
        return f"ID: {self.id}, Latitude: {self.lat}, Longitude: {self.long}, Memory: {self.mem} MB"

# 定义 Topology 类
class Topology:
    def __init__(self):
        self.Nodes = []  # 存储 PhyNode 实例的列表

    def add_node(self, node):
        self.Nodes.append(node)

    def print_topology(self):
        print("Topology Information:")
        for node in self.Nodes:
            print(node)

# 从 CSV 文件加载拓扑信息
def load_topo(topo_file):
    topo_G = Topology()  # 创建 Topology 实例
    with open(topo_file, mode='r') as file:
        csv_reader = csv.reader(file)
        next(csv_reader)  # 跳过表头
        for row in csv_reader:
            if row[0] == "":  # 跳过空行
                continue
            
            # 解析 CSV 行并创建 PhyNode 实例
            node_id = int(row[0])
            lat = float(row[1])
            long = float(row[2])
            mem = 1000  # 假设内存容量为 1000 MB
            
            # 创建 PhyNode 实例并添加到 topo_G.nodes
            phynode = PhyNode(node_id, lat, long, mem)
            topo_G.add_node(phynode)

    return topo_G

def showPhyNodesMem():
    print("start to show phynode mem ....")
    for i in range(len(topo_G.Nodes)):
        print(f"Node {i} remaining mem {topo_G.Nodes[i].Mem} MB")

if __name__ == "__main__":
    topo_file_path = './data/final/pi_nodes.csv' 
    topo_G = load_topo(topo_file_path) 
    #topo_G.print_topology()
    showPhyNodesMem() 