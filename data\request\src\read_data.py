#!/usr/bin/env python3
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import math
import csv
import os

# 读取Azure 2021跟踪数据
def read_azure_2021(file_name='AzureFunctionTrace.txt', func_id='48cc770d590d3c5a7691b3b4e9302f82ec3be5ddc2a037d94ad2e76f44dd8946'):
    with open(file_name, 'r') as f:
        contents = f.readlines()
    app = []
    func = []
    timestamp = []
    duration = []
    startTime = []
    roundStartTime = []
    # the size of all time slot
    count = [0]*1209600

    for index in range(1, len(contents)):
        parameters = contents[index].split(",")
        app.append(parameters[0])
        func.append(parameters[1])
        timestamp.append(float(parameters[2]))
        duration.append(float(parameters[3]))
        startTime.append(float(parameters[2])-float(parameters[3]))
        roundStartTime.append(int(math.floor(float(parameters[2]) - float(parameters[3]))))
        if parameters[1] == func_id:
            count[roundStartTime[-1]] += 1

    data = pd.DataFrame(np.array([count]))
    data = data.transpose()
    data.columns = ['count']

    return data

# 读取Azure 2019跟踪数据
def read_azure_2019(file_name, function_id):
    with open(file_name, 'r') as f:
        contents = f.readlines()
    owner = []
    app = []
    function = []
    trigger = []
    time_slot = []

    for index in range(1, len(contents)):
        parameters = contents[index].split(",")
        if parameters[2] == function_id:
            owner.append(parameters[0])
            app.append(parameters[1])
            function.append(parameters[2])
            trigger.append(parameters[3])
            time_slot = parameters[4:]
            break

    data = pd.DataFrame(np.array([time_slot], dtype=np.float32))
    data = data.transpose()
    data.columns = ['Invocation']

    return data

# 读取CSV文件并转置
# 输入: ./data/request/sorted_d01.csv
def read_file(file_name='./data/request/sorted_d01.csv'):
    df = pd.read_csv(file_name)
    df = df.transpose()
    return df

# 合并数据并计算总调用次数 (输入d01.csv, 输出merged_d01.csv)
# 输入: ./data/dataset/invocations_per_function_md.anon.d01.csv
# 输出: ./data/request/merged_d01.csv
def consolidate_data(file_names, output_name):
    print(f"合并数据 - 输入: {file_names}, 输出: {output_name}")
    for file_name in file_names:
        with open(file_name, 'r') as f:
            contents = f.readlines()

        data_map = {}
        time_slot = [1440]
        
        for index in range(1, len(contents)):
            parameters = contents[index].split(",")
            app_id = parameters[1]
            time = parameters[4:]
            # convert to int
            time_int = list(map(int, time))
            time_slot = np.array(time_int)
                
            if data_map.get(app_id) is None:
                # new key
                data_map.update({app_id: time_slot})
            else:
                # old key
                current_time_slot = data_map[app_id]
                update_time_slot = np.add(time_slot, current_time_slot)
                data_map.update({app_id: update_time_slot})

    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_name), exist_ok=True)
    
    # 写入数据到CSV
    write_data_to_csv(output_name, data_map)

# 写入数据到CSV文件
def write_data_to_csv(output_name, data_map):
    with open(output_name, 'w', newline='') as f:
        # 创建csv写入器
        writer = csv.writer(f)
        
        # 写入表头行
        header = ['App', 'totalNumber'] + list(range(1, 1441))
        writer.writerow(header)
        
        # 写入数据行
        for key, value in data_map.items():
            row = [key, np.sum(value)] + value.tolist()
            writer.writerow(row)
    
    print(f"数据已写入: {output_name}")

# 根据调用次数排序 (输入merged_d01.csv, 输出sorted_d01.csv)
# 输入: ./data/request/merged_d01.csv
# 输出: ./data/request/sorted_d01.csv
def sort_data(file_name, output_name, top_n=4):
    # DataFrame to read our input CSV file
    dataFrame = pd.read_csv(file_name)

    # 根据totalNumber列排序
    dataFrame = dataFrame.sort_values(by=['totalNumber'], ascending=False, na_position='last')
   
    # 选择前top_n个应用并保存
    dataFrame.head(top_n).to_csv(output_name, index=False)
    
    print(f"已选择并保存前{top_n}个应用 - 输出: {output_name}")
    
    return dataFrame.head(top_n)['App'].tolist()

# 查找特定应用
# 输出: ./data/request/invocations_per_function_md.anon.d1.csv
def find_app(file_names, ids, output_dir='./data/request/'):
    print(f"查找应用 - 应用IDs: {ids}")
    file_count = 1
    for file_name in file_names:
        with open(file_name, 'r') as f:
            contents = f.readlines()

        data_map = {}
        time_slot = [1440]
        
        for index in range(1, len(contents)):
            parameters = contents[index].split(",")
            app_id = parameters[1]
            if app_id not in ids:
                continue
            else:
                time = parameters[4:]
                # convert to int
                time_int = list(map(int, time))
                time_slot = np.array(time_int)
                
                if data_map.get(app_id) is None:
                    # new key
                    data_map.update({app_id: time_slot})
                else:
                    # old key
                    current_time_slot = data_map[app_id]
                    update_time_slot = np.add(time_slot, current_time_slot)
                    data_map.update({app_id: update_time_slot})
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        output_file = output_dir + 'invocations_per_function_md.anon.d' + str(file_count) + '.csv'
        write_data_to_csv(output_file, data_map)
        file_count += 1

# 获取调用次数最多的应用
def get_top_apps(file_name, k):
    df = pd.read_csv(file_name)
    app_ids = df.loc[:, 'App'].tolist()
    return app_ids[:k]

# 将sorted_d01.csv转换为CE格式(每两行一个应用)
# 输入: ./data/request/sorted_d01.csv
# 输出: ./data/request/final_d01.csv
def convert_to_final(input_file='./data/request/sorted_d01.csv', output_file='./data/request/final_d01.csv'):
    print(f"转换文件格式 - 输入: {input_file}, 输出: {output_file}")
    
    # 读取输入文件
    df = pd.read_csv(input_file)
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # 创建输出文件
    with open(output_file, 'w', newline='') as f:
        writer = csv.writer(f)
        
        # 获取时间列索引 (从第3列开始，前两列是App和totalNumber)
        time_cols = list(range(1, 1441))
        
        # 为每一行写入两行数据 (时间索引行和数据行)
        for _, row in df.iterrows():
            # 第一行：时间索引
            writer.writerow(time_cols)
            
            # 第二行：调用次数
            call_data = row.iloc[2:].tolist()  # 跳过App和totalNumber列
            writer.writerow(call_data)
    
    print(f"已转换为最终格式并保存到: {output_file}")

# 创建包含前N个应用的数据集
# 处理流程:
# 1. 输入: ./data/dataset/invocations_per_function_md.anon.d01.csv
# 2. 中间文件: ./data/request/merged_d01.csv
# 3. 输出: ./data/request/sorted_d01.csv
# 4. 最终格式输出: ./data/request/final_d01.csv
def create_top_n(n=10):
    print("===== Azure数据处理 =====")
    
    filenames = []
    # 只使用第一天的数据
    filename = './data/dataset/invocations_per_function_md.anon.d01.csv'
    filenames.append(filename)
    
    merge_file = './data/request/merged_d01.csv'
    sorted_file = './data/request/sorted_d01.csv'
    
    # 合并数据
    consolidate_data(filenames, merge_file)
    
    # 排序并选择前n个应用
    app_ids = sort_data(merge_file, sorted_file, n)
    
    # 转换为最终格式
    convert_to_final(sorted_file, './data/request/final_d01.csv')
    
    print("===== 处理完成 =====")
    return app_ids

# 主函数
def main():
    # 处理前10个应用
    create_top_n(10)

if __name__ == "__main__":
    main()