import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import pandas as pd
import re
import glob
import argparse
import logging
import csv
import seaborn as sns

# 设置全局字体和字体大小
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号
plt.rcParams['font.size'] = 28  # 设置默认字体大小

# 设置不同元素的字体大小
TITLE_SIZE = 34     # 标题字体大小
LABEL_SIZE = 32     # 标签字体大小
TICK_SIZE = 32      # 刻度字体大小
LEGEND_SIZE = 28    # 图例字体大小
BORDER_WIDTH = 1.5  # 图形边框粗度

def set_border_width(ax):
    """设置坐标轴边框线宽度"""
    for spine in ax.spines.values():
        spine.set_linewidth(BORDER_WIDTH)

def setup_logging(verbose=False, log_file=None, clean_logs=False):
    """
    设置日志级别和格式
    
    Args:
        verbose (bool): 是否显示详细日志
        log_file (str): 日志文件路径，如果为None则使用默认路径
        clean_logs (bool): 是否清理logs目录下的旧日志文件
    """
    log_level = logging.DEBUG if verbose else logging.INFO
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    
    # 如果未指定日志文件，则使用带时间戳的默认文件名
    if log_file is None:
        # 创建logs目录
        logs_dir = 'logs'
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir, exist_ok=True)
        
        # 如果需要清理，删除logs目录下的所有旧日志文件
        if clean_logs:
            try:
                # 删除旧的metrics日志文件
                for old_log in glob.glob(os.path.join(logs_dir, "plot_metrics_*.log")):
                    try:
                        os.remove(old_log)
                        # 只在控制台输出必要提示信息
                        if verbose:
                            print(f"已删除旧日志文件: {old_log}")
                    except Exception as e:  
                        print(f"删除日志文件 {old_log} 时出错: {e}")
                
                # 删除旧的cold_start日志文件
                for old_log in glob.glob(os.path.join(logs_dir, "plot_cold_start*.log")):
                    try:
                        os.remove(old_log)
                        # 只在控制台输出必要提示信息
                        if verbose:
                            print(f"已删除旧日志文件: {old_log}")
                    except Exception as e:  
                        print(f"删除日志文件 {old_log} 时出错: {e}")
            except Exception as e:
                print(f"清理logs目录时出错: {e}")
        
        log_file = os.path.join(logs_dir, f"plot_metrics.log")
    else:
        # 确保日志文件所在目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
    
    # 重置日志配置
    logging.root.handlers = []  # 更彻底地清除所有处理器
    
    # 配置日志 - 同时输出到文件和控制台
    # 首先清除现有的处理器
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    # 创建文件处理器 - 显式使用'w'模式覆盖文件
    try:
        # 先尝试删除已存在的日志文件
        if os.path.exists(log_file):
            os.remove(log_file)
    except Exception as e:
        print(f"删除已有日志文件时出错: {e}")
    
    file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')  # 明确使用'w'模式覆盖文件
    file_handler.setLevel(log_level)
    file_handler.setFormatter(logging.Formatter(log_format))
    
    # 创建控制台处理器，但只在verbose模式下添加
    if verbose:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(logging.Formatter(log_format))
    
    # 配置根日志记录器
    logging.root.setLevel(log_level)
    logging.root.addHandler(file_handler)
    if verbose:
        logging.root.addHandler(console_handler)
    
    print(f"日志已保存在: {log_file} ")

def extract_suffix_from_path(path):
    """
    从输入目录路径中提取后缀名
    
    Args:
        path (str): 输入目录路径，如 './result/EUA' 或 './result/Telecom'
        
    Returns:
        str: 提取的后缀，如 '_EUA' 或 '_Telecom'，如果无法提取则返回空字符串
    """
    # 规范化路径，移除尾部斜杠
    path = path.rstrip('/')
    path = path.rstrip('\\')
    
    # 提取路径的最后一个部分
    last_part = os.path.basename(path)
    
    # 检查是否是我们期望的后缀
    if last_part in ['EUA', 'Telecom']:
        return f'_{last_part}'
    else:
        # 如果不是期望的后缀，记录警告并返回空字符串
        logging.warning(f"输入目录 '{path}' 不是标准的EUA或Telecom目录，输出文件名不会添加后缀")
        return ''

def find_result_files(beta=0.50, alpha=None, input_dir='./result'):
    """查找指定beta值和alpha值下的各方法结果文件"""
    if alpha is None:
        logging.info(f"查找 beta={beta:.2f} 下的各方法结果文件")
        
        layer_cache_file = glob.glob(f"{input_dir}/LayerCache-{beta:.2f}-*.csv")
        faas_cache_file = glob.glob(f"{input_dir}/FaaSCache-{beta:.2f}-*.csv")
        OpenWhisk_file = glob.glob(f"{input_dir}/OpenWhisk-{beta:.2f}-*.csv")
        cross_edge_file = glob.glob(f"{input_dir}/CrossEdge-{beta:.2f}-*.csv")
    else:
        logging.info(f"查找 beta={beta:.2f}, alpha={alpha:.3f} 下的各方法结果文件")
        
        layer_cache_file = glob.glob(f"{input_dir}/LayerCache-{beta:.2f}-{alpha:.3f}.csv")
        faas_cache_file = glob.glob(f"{input_dir}/FaaSCache-{beta:.2f}-{alpha:.3f}.csv")
        OpenWhisk_file = glob.glob(f"{input_dir}/OpenWhisk-{beta:.2f}-{alpha:.3f}.csv")
        cross_edge_file = glob.glob(f"{input_dir}/CrossEdge-{beta:.2f}-{alpha:.3f}.csv")
    
    # 添加调试日志
    logging.info(f"在 beta={beta:.2f} {'' if alpha is None else f', alpha={alpha:.3f}'} 下找到的文件:")
    logging.info(f"  LayerCache: {layer_cache_file}")
    logging.info(f"  FaaSCache: {faas_cache_file}")
    logging.info(f"  OpenWhisk: {OpenWhisk_file}")
    logging.info(f"  CrossEdge: {cross_edge_file}")
    
    return {
        'LayerCache': layer_cache_file[0] if layer_cache_file else None,
        'FaaSCache': faas_cache_file[0] if faas_cache_file else None,
        'OpenWhisk': OpenWhisk_file[0] if OpenWhisk_file else None,
        'CrossEdge': cross_edge_file[0] if cross_edge_file else None
    }

def read_csv_first_two_rows(file_path):
    """读取CSV文件的前两行（标题行和数据行）"""
    if not os.path.exists(file_path):
        logging.error(f"文件不存在: {file_path}")
        return None, None
    
    try:
        with open(file_path, 'r') as f:
            reader = csv.reader(f)
            headers = next(reader)  # 读取标题行
            data = next(reader)     # 读取数据行
        return headers, data
    except Exception as e:
        logging.error(f"读取文件 {file_path} 时发生错误: {e}")
        return None, None

def read_response_time_data(file_path, max_rows=100000):
    """从CSV文件中读取ResponseTime列数据
    
    Args:
        file_path (str): CSV文件路径
        max_rows (int): 最大读取行数，用于限制数据量，默认100000行
        
    Returns:
        list: ResponseTime数据列表，或者None如果读取失败
    """
    if not os.path.exists(file_path):
        logging.error(f"文件不存在: {file_path}")
        return None
    
    try:
        # 前两行是摘要数据，第三行是列头，从第四行开始才是实际数据
        response_times = []
        with open(file_path, 'r') as f:
            reader = csv.reader(f)
            # 跳过前两行（摘要数据）
            next(reader)
            next(reader)
            
            # 读取第三行（列头）
            headers = next(reader)
            try:
                # 尝试在列头中找ResponseTime，如果找不到就默认使用最后一列
                try:
                    rt_index = headers.index('ResponseTime')
                except ValueError:
                    # 如果找不到ResponseTime列，使用最后一列
                    rt_index = len(headers) - 1
                    logging.warning(f"在文件 {file_path} 中未找到ResponseTime列，使用最后一列 (索引: {rt_index})")
            except Exception:
                logging.error(f"无法确定ResponseTime列在文件 {file_path} 中的位置")
                return None
            
            # 读取后续行中的ResponseTime数据
            count = 0
            for row in reader:
                if count >= max_rows:
                    break
                if len(row) > rt_index:  # 确保行有足够的列
                    try:
                        rt = float(row[rt_index])
                        response_times.append(rt)
                    except (ValueError, TypeError):
                        # 跳过无法转换为浮点数的值
                        pass
                count += 1
                
        logging.info(f"从文件 {file_path} 中读取了 {len(response_times)} 条响应时间数据")
        return response_times
    except Exception as e:
        logging.error(f"读取文件 {file_path} 时发生错误: {e}")
        return None

def extract_cold_start_from_csv(csv_file_path):
    """从CSV文件中提取数据"""
    headers, data = read_csv_first_two_rows(csv_file_path)
    if not headers or not data:
        logging.error(f"无法从文件 {csv_file_path} 读取数据")
        return None
    
    # 将数据转换为字典
    result = {}
    for i, header in enumerate(headers):
        if i < len(data) and data[i]:
            try:
                # 尝试转换为浮点数
                result[header] = float(data[i])
            except (ValueError, TypeError):
                # 如果转换失败，保持原始字符串
                result[header] = data[i]
    
    return result

def load_data_for_beta_values(beta_values=None, input_dir='./result'):
    """加载不同beta值下的模拟结果数据"""
    if beta_values is None:
        beta_values = [0.50, 0.75, 1.00, 1.25, 1.50]  # 默认beta值列表
    
    method_names = ['LayerCache', 'FaaSCache', 'CrossEdge', 'OpenWhisk']
    
    # 为每种方法创建一个字典，存储各beta值下的数据
    methods_data = {method: {} for method in method_names}
    
    # 遍历beta值，加载数据
    for beta in beta_values:
        # 获取这个beta值下所有方法的结果文件
        result_files = find_result_files(beta, input_dir=input_dir)
        
        # 依次处理每个方法
        for method, file_path in result_files.items():
            if file_path:
                # 从CSV文件读取前两行（标题行和数据行）
                headers, data = read_csv_first_two_rows(file_path)
                if headers and data:
                    # 将结果保存在对应方法的字典中，以beta值为键
                    methods_data[method][beta] = {}
                    
                    # 从数据行中提取需要的指标
                    for i, header in enumerate(headers):
                        try:
                            methods_data[method][beta][header] = float(data[i])
                        except (ValueError, IndexError):
                            methods_data[method][beta][header] = data[i] if i < len(data) else None
                    
                    logging.info(f"成功加载 {method} 方法的 beta={beta:.2f} 数据")
            else:
                logging.warning(f"未找到 {method} 方法的 beta={beta:.2f} 数据文件")
    
    return methods_data, beta_values

def load_data_for_alpha_values(beta=1.00, alpha_values=None, input_dir='./result'):
    """加载固定beta值下不同alpha值的模拟结果数据"""
    if alpha_values is None:
        alpha_values = [0.001, 0.003, 0.005, 0.010, 0.015]  # 默认alpha值列表
    
    method_names = ['LayerCache', 'FaaSCache', 'CrossEdge', 'OpenWhisk']
    
    # 为每种方法创建一个字典，存储各alpha值下的数据
    methods_data = {method: {} for method in method_names}
    
    # 遍历alpha值，加载数据
    for alpha in alpha_values:
        # 获取这个beta值和alpha值下所有方法的结果文件
        result_files = find_result_files(beta=beta, alpha=alpha, input_dir=input_dir)
        
        # 依次处理每个方法
        for method, file_path in result_files.items():
            if file_path:
                # 从CSV文件读取前两行（标题行和数据行）
                headers, data = read_csv_first_two_rows(file_path)
                if headers and data:
                    # 将结果保存在对应方法的字典中，以alpha值为键
                    methods_data[method][alpha] = {}
                    
                    # 从数据行中提取需要的指标
                    for i, header in enumerate(headers):
                        try:
                            methods_data[method][alpha][header] = float(data[i])
                        except (ValueError, IndexError):
                            methods_data[method][alpha][header] = data[i] if i < len(data) else None
                    
                    logging.info(f"成功加载 {method} 方法的 beta={beta:.2f}, alpha={alpha:.3f} 数据")
            else:
                logging.warning(f"未找到 {method} 方法的 beta={beta:.2f}, alpha={alpha:.3f} 数据文件")
    
    return methods_data, alpha_values

# ---------------------柱状图版本---------------------

# 冷启动频率
def plot_cold_start_comparison(methods_data, output_file='fig/bar_cold_start_frequency.png', show_plot=False):
    """绘制多种方法在不同beta值下的冷启动频率对比图"""
    # 获取所有方法和beta值
    method_names = list(methods_data.keys())
    all_beta_values = set()
    for method_data in methods_data.values():
        all_beta_values.update(method_data.keys())
    beta_values = sorted(list(all_beta_values))
    
    if not beta_values:
        logging.error("没有找到有效的beta值数据，无法绘制图表")
        return
    
    # 创建图形和坐标轴
    fig, ax = plt.subplots(figsize=(12, 7), dpi=300)
    set_border_width(ax)
    
    # 设置柱状图的宽度和组内的位置
    bar_width = 0.8 / len(method_names)
    
    # 调整方法顺序，确保LayerCache在最左边，OpenWhisk在最右边
    ordered_method_names = []
    # 首先添加LayerCache
    if 'LayerCache' in method_names:
        ordered_method_names.append('LayerCache')
    
    # 添加CrossEdge
    if 'CrossEdge' in method_names:
        ordered_method_names.append('CrossEdge')
    
    # 添加FaaSCache
    if 'FaaSCache' in method_names:
        ordered_method_names.append('FaaSCache')
    
    # 最后添加OpenWhisk
    if 'OpenWhisk' in method_names:
        ordered_method_names.append('OpenWhisk')
    
    # 用于在x轴上定位柱状图，这里x是beta值索引
    x = np.arange(len(beta_values))
    
    # 不同方法使用不同颜色
    # 红色(LayerCache)、黄色(CrossEdge)、绿色(FaaSCache)、蓝色(OpenWhisk)
    method_colors = {
        'LayerCache': '#C03D3E',  # 红色
        'CrossEdge': '#E1812C',  # 黄色
        'FaaSCache': '#3A923A',   # 绿色
        'OpenWhisk': '#3274A1'    # 蓝色
    }
    
    # 为每种方法绘制柱状图组
    for i, method_name in enumerate(ordered_method_names):
        # 获取当前方法在不同beta值下的冷启动频率
        frequencies = []
        for beta in beta_values:
            if method_name in methods_data and beta in methods_data[method_name] and 'cold_start_frequency' in methods_data[method_name][beta]:
                frequencies.append(methods_data[method_name][beta]['cold_start_frequency'])
            else:
                frequencies.append(0)
        
        # 计算柱状图在x轴上的位置
        bar_positions = x - (len(ordered_method_names) - 1) * bar_width / 2 + i * bar_width
        
        # 绘制柱状图
        bars = ax.bar(bar_positions, frequencies, bar_width, 
                     label=method_name, color=method_colors[method_name])
    
    # 获取y轴的最大值
    frequencies_list = []
    for method in ordered_method_names:
        if method in methods_data:
            method_frequencies = []
            for beta in beta_values:
                if beta in methods_data[method] and 'cold_start_frequency' in methods_data[method][beta]:
                    method_frequencies.append(methods_data[method][beta]['cold_start_frequency'])
            if method_frequencies:  # 确保列表非空
                frequencies_list.append(method_frequencies)
    
    if frequencies_list:  # 确保列表非空
        y_max = max(max(frequencies) for frequencies in frequencies_list)
    else:
        y_max = 0.6  # 如果没有数据，设置一个默认值
    
    # 设置y轴范围，增加20%的顶部边距
    ax.set_ylim(0, y_max * 1.2)
    
    # 设置y轴刻度，去掉0.8
    ax.set_yticks([0, 0.2, 0.4, 0.6])
    
    # 设置轴标签
    ax.set_ylabel('Cold-start Frequency')
    
    # 设置x轴刻度标签
    ax.set_xticks(x)
    ax.set_xticklabels([f'β={beta:.2f}' for beta in beta_values])
    
    # 设置y轴刻度标签字体大小，与x轴保持一致
    ax.tick_params(axis='both', labelsize=TICK_SIZE)
    
    # 添加图例 - 调整到右上角，并设置更紧凑的边距
    ax.legend(fontsize=LEGEND_SIZE, frameon=True, edgecolor='black', borderpad=0.4, handletextpad=0.5, 
              loc='upper right', bbox_to_anchor=(0.99, 0.99))
    
    # 添加网格线，提高可读性
    ax.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 保存图表
    plt.tight_layout()
    plt.savefig(output_file, bbox_inches='tight')
    logging.info(f"冷启动频率图已保存为 '{output_file}'")
    
    # 显示图表（如果需要）
    if show_plot:
        plt.show()
    else:
        plt.close()

# 冷启动比例
def plot_cold_start_ratio(methods_data=None, output_file='fig/cold_start_ratio.png', show_plot=False):
    """
    绘制类似于图片中的冷启动/热启动比例堆叠柱状图
    
    Args:
        methods_data (dict): 包含各方法在不同beta值下的数据，如果为None则使用示例数据
        output_file (str): 输出文件路径
        show_plot (bool): 是否显示图表
    """
    # 设置虚线颜色为浅灰色
    plt.rcParams['hatch.color'] = '#CCCCCC'
    
    # 创建图形和坐标轴
    fig, ax = plt.subplots(figsize=(12, 7), dpi=300)
    set_border_width(ax)
    
    # 设置beta值和方法标签
    beta_values = [0.50, 0.75, 1.00, 1.25, 1.50]  # beta值
    methods = ['L', 'C', 'F', 'O']  # 方法标签，对应LayerCache, CrossEdge, FaaSCache, OpenWhisk
    
    # 方法名映射
    method_mapping = {
        'L': 'LayerCache',
        'C': 'CrossEdge',
        'F': 'FaaSCache',
        'O': 'OpenWhisk'
    }
    
    # 如果提供了methods_data，则从中获取冷启动频率数据
    if methods_data:
        cold_start_ratios = {}
        for beta in beta_values:
            cold_start_ratios[beta] = {}
            for short_name, full_name in method_mapping.items():
                if full_name in methods_data and beta in methods_data[full_name] and 'cold_start_frequency' in methods_data[full_name][beta]:
                    cold_start_ratios[beta][short_name] = methods_data[full_name][beta]['cold_start_frequency']
                else:
                    # 如果数据不存在，使用默认值
                    cold_start_ratios[beta][short_name] = 0.0
    
    # 柱状图宽度和间距
    bar_width = 0.2
    group_spacing = 0.1
    
    # 颜色设置 - 使用图片中的颜色
    cold_start_color = '#619FCA'  # 蓝色
    warm_start_color = '#6ABC6A'  # 绿色
    
    # 位置计算
    num_groups = len(beta_values)
    group_width = len(methods) * bar_width + group_spacing
    x_positions = np.arange(0, num_groups * group_width, group_width)
    
    # 绘制堆叠柱状图
    for i, beta_idx in enumerate(range(len(beta_values))):
        beta = beta_values[beta_idx]
        for j, method_idx in enumerate(range(len(methods))):
            method = methods[method_idx]
            
            # 计算柱状图在x轴上的位置
            bar_position = x_positions[i] + j * bar_width
            
            # 获取冷启动比例
            cold_ratio = cold_start_ratios[beta][method]
            
            # 计算热启动比例
            warm_ratio = 1.0 - cold_ratio
            
            # 绘制冷启动部分（底部）- 使用全局边框线宽度
            cold_bar = ax.bar(bar_position, cold_ratio, bar_width, 
                            facecolor=cold_start_color, edgecolor='black', linewidth=BORDER_WIDTH)
            
            # 绘制热启动部分（顶部）- 添加虚线图案，使用浅灰色虚线，使用全局边框线宽度
            warm_bar = ax.bar(bar_position, warm_ratio, bar_width, 
                            bottom=cold_ratio, facecolor=warm_start_color, alpha=1,
                            edgecolor='black', linewidth=BORDER_WIDTH, hatch='/')
            
            # 在每个柱子下方添加方法标签
            ax.text(bar_position, -0.05, method, ha='center', va='top', 
                   transform=ax.get_xaxis_transform(), fontsize=TICK_SIZE)
    
    # 设置y轴范围
    ax.set_ylim(0, 1.0)
    
    # 设置y轴刻度和标签
    ax.set_yticks([0, 0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticklabels(['0', '20', '40', '60', '80', '100'])
    
    # 设置y轴标签
    ax.set_ylabel('Ratio (%)', fontsize=LABEL_SIZE)
    
    # 设置y轴刻度标签字体大小
    ax.tick_params(axis='y', labelsize=TICK_SIZE)
    
    # 隐藏x轴刻度和标签
    ax.set_xticks([])
    ax.set_xticklabels([])
    
    # 添加beta值标签在方法标签下方
    for i, beta in enumerate(beta_values):
        # 计算每组柱子的中心位置
        group_center = x_positions[i] + (len(methods) - 1) * bar_width / 2
        # 添加beta值标签
        ax.text(group_center, -0.15, f'β={beta:.2f}', ha='center', va='top',
               transform=ax.get_xaxis_transform(), fontsize=TICK_SIZE)
    
    # 创建图例 - 使用facecolor和edgecolor避免警告
    legend_elements = [
        plt.Rectangle((0, 0), 1, 1, facecolor=cold_start_color, edgecolor='black', linewidth=0.5, label='Cold start'),
        plt.Rectangle((0, 0), 1, 1, facecolor=warm_start_color, edgecolor='black', linewidth=0.5, label='Warm start'),
    ]
    
    ax.legend(handles=legend_elements, fontsize=LEGEND_SIZE, frameon=True, 
             edgecolor='black', borderpad=0.4, handletextpad=0.5,
             loc='upper right', bbox_to_anchor=(0.99, 0.99))
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 保存图表
    plt.tight_layout()
    plt.savefig(output_file, bbox_inches='tight')
    logging.info(f"冷启动/热启动比例图已保存为 '{output_file}'")

# 平均响应时间
def plot_avg_response_time_comparison(methods_data, output_file='fig/bar_response_time.png', show_plot=False):
    """绘制多种方法在不同beta值下的平均响应时间对比图"""
    # 获取所有方法和beta值
    method_names = list(methods_data.keys())
    all_beta_values = set()
    for method_data in methods_data.values():
        all_beta_values.update(method_data.keys())
    beta_values = sorted(list(all_beta_values))
    
    if not beta_values:
        logging.error("没有找到有效的beta值数据，无法绘制图表")
        return
    
    # 创建图形和坐标轴
    fig, ax = plt.subplots(figsize=(12, 7), dpi=300)
    set_border_width(ax)
    
    # 设置柱状图的宽度和组内的位置
    bar_width = 0.8 / len(method_names)
    
    # 调整方法顺序，确保LayerCache在最左边，OpenWhisk在最右边
    ordered_method_names = []
    # 首先添加LayerCache
    if 'LayerCache' in method_names:
        ordered_method_names.append('LayerCache')
    
    # 添加CrossEdge
    if 'CrossEdge' in method_names:
        ordered_method_names.append('CrossEdge')
    
    # 添加FaaSCache
    if 'FaaSCache' in method_names:
        ordered_method_names.append('FaaSCache')
    
    # 最后添加OpenWhisk
    if 'OpenWhisk' in method_names:
        ordered_method_names.append('OpenWhisk')
    
    # 用于在x轴上定位柱状图，这里x是beta值索引
    x = np.arange(len(beta_values))
    
    # 不同方法使用不同颜色
    # 红色(LayerCache)、黄色(CrossEdge)、绿色(FaaSCache)、蓝色(OpenWhisk)
    method_colors = {
        'LayerCache': '#C03D3E',  # 红色
        'CrossEdge': '#E1812C',  # 黄色
        'FaaSCache': '#3A923A',   # 绿色
        'OpenWhisk': '#3274A1'    # 蓝色
    }
    
    # 为每种方法绘制柱状图组
    for i, method_name in enumerate(ordered_method_names):
        # 获取当前方法在不同beta值下的平均响应时间
        response_times = []
        for beta in beta_values:
            if method_name in methods_data and beta in methods_data[method_name] and 'weighted_response_time' in methods_data[method_name][beta]:
                response_times.append(methods_data[method_name][beta]['weighted_response_time'])
            else:
                response_times.append(0)
        
        # 计算柱状图在x轴上的位置
        bar_positions = x - (len(ordered_method_names) - 1) * bar_width / 2 + i * bar_width
        
        # 绘制柱状图
        bars = ax.bar(bar_positions, response_times, bar_width, 
                     label=method_name, color=method_colors[method_name])
    
    # 获取y轴的最大值
    response_times_list = []
    for method in ordered_method_names:
        if method in methods_data:
            method_response_times = []
            for beta in beta_values:
                if beta in methods_data[method] and 'weighted_response_time' in methods_data[method][beta]:
                    method_response_times.append(methods_data[method][beta]['weighted_response_time'])
            if method_response_times:  # 确保列表非空
                response_times_list.append(method_response_times)
    
    if response_times_list:  # 确保列表非空
        y_max = max(max(response_times) for response_times in response_times_list)
    else:
        y_max = 1.0  # 如果没有数据，设置一个默认值
    
    # 设置y轴范围，增加20%的顶部边距
    ax.set_ylim(0, y_max * 1.2)
    
    # 设置轴标签
    ax.set_ylabel('Average Response Time (s)')
    
    # 设置x轴刻度标签
    ax.set_xticks(x)
    ax.set_xticklabels([f'β={beta:.2f}' for beta in beta_values])
    
    # 设置刻度标签字体大小
    ax.tick_params(axis='both', labelsize=TICK_SIZE)
    
    # 添加图例 - 调整到右上角，并设置更紧凑的边距
    ax.legend(fontsize=LEGEND_SIZE, frameon=True, edgecolor='black', borderpad=0.4, handletextpad=0.5, 
              loc='upper right', bbox_to_anchor=(0.99, 0.99))
    
    # 添加网格线，提高可读性
    ax.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 保存图表
    plt.tight_layout()
    plt.savefig(output_file, bbox_inches='tight')
    logging.info(f"平均响应时间图已保存为 '{output_file}'")
    
    # 显示图表（如果需要）
    if show_plot:
        plt.show()
    else:
        plt.close()

# 平均系统成本
def plot_avg_cost_comparison(methods_data, output_file='fig/bar_cost.png', show_plot=False):
    """绘制多种方法在不同beta值下的平均成本对比图"""
    # 确保所有方法都有相同的beta值集合
    beta_values = sorted(list(next(iter(methods_data.values())).keys()))
    x_labels = [f'β={beta:.2f}' for beta in beta_values]
    
    # 创建图形和坐标轴
    fig, ax = plt.subplots(figsize=(12, 7), dpi=300)
    set_border_width(ax)
    
    # 设置柱状图的宽度和组内的位置
    bar_width = 0.8 / len(methods_data)
    
    # 调整方法顺序，确保LayerCache在最左边，OpenWhisk在最右边
    ordered_method_names = []
    # 首先添加LayerCache
    if 'LayerCache' in methods_data:
        ordered_method_names.append('LayerCache')
    
    # 添加CrossEdge
    if 'CrossEdge' in methods_data:
        ordered_method_names.append('CrossEdge')
    
    # 添加FaaSCache
    if 'FaaSCache' in methods_data:
        ordered_method_names.append('FaaSCache')
    
    # 最后添加OpenWhisk
    if 'OpenWhisk' in methods_data:
        ordered_method_names.append('OpenWhisk')
    
    # 用于在x轴上定位柱状图
    x = np.arange(len(beta_values))
    
    # 不同方法使用不同颜色
    # 红色(LayerCache)、黄色(CrossEdge)、绿色(FaaSCache)、蓝色(OpenWhisk)
    method_colors = {
        'LayerCache': '#C03D3E',  # 红色
        'CrossEdge': '#E1812C',  # 黄色
        'FaaSCache': '#3A923A',   # 绿色
        'OpenWhisk': '#3274A1'    # 蓝色
    }
    
    # 绘制每种方法的柱状图
    for i, method_name in enumerate(ordered_method_names):
        data = methods_data[method_name]
        # 获取方法在不同beta值下的平均成本
        costs = []
        for beta in beta_values:
            if beta in data and 'avg_cost' in data[beta]:
                costs.append(data[beta]['avg_cost'])
            else:
                costs.append(0)
        
        # 计算柱状图在x轴上的位置
        bar_positions = x - (len(methods_data) - 1) * bar_width / 2 + i * bar_width
        
        # 绘制柱状图
        bars = ax.bar(bar_positions, costs, bar_width, 
                     label=method_name, color=method_colors[method_name])

    # 获取y轴的最大值
    costs_list = []
    for method in ordered_method_names:
        if method in methods_data:
            method_costs = []
            for beta in beta_values:
                if beta in methods_data[method] and 'avg_cost' in methods_data[method][beta]:
                    method_costs.append(methods_data[method][beta]['avg_cost'])
            if method_costs:  # 确保列表非空
                costs_list.append(method_costs)
    
    if costs_list:  # 确保列表非空
        y_max = max(max(costs) for costs in costs_list)
    else:
        y_max = 1.0  # 如果没有数据，设置一个默认值
    
    # 设置y轴范围，增加20%的顶部边距
    ax.set_ylim(0, y_max * 1.2)
    
    # 设置轴标签
    ax.set_ylabel('Average Cost')
    
    # 设置x轴刻度标签
    ax.set_xticks(x)
    ax.set_xticklabels(x_labels)
    
    # 设置刻度标签字体大小
    ax.tick_params(axis='both', labelsize=TICK_SIZE)
    
    # 添加图例 - 调整到右上角，并设置更紧凑的边距
    ax.legend(fontsize=LEGEND_SIZE, frameon=True, edgecolor='black', borderpad=0.4, handletextpad=0.5, 
              loc='upper right', bbox_to_anchor=(0.99, 0.99))
    
    # 添加网格线，提高可读性
    ax.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 保存图表
    plt.tight_layout()
    plt.savefig(output_file, bbox_inches='tight')
    logging.info(f"平均成本图已保存为 '{output_file}'")

# 加载内存总大小
def plot_memory_instant_comparison(methods_data, output_file='fig/bar_memory_instant.png', show_plot=False):
    """绘制多种方法在不同beta值下的平均内存使用对比图"""
    # 获取所有方法和beta值
    method_names = list(methods_data.keys())
    all_beta_values = set()
    for method_data in methods_data.values():
        all_beta_values.update(method_data.keys())
    beta_values = sorted(list(all_beta_values))
    
    if not beta_values:
        logging.error("没有找到有效的beta值数据，无法绘制图表")
        return
    
    # 创建图形和坐标轴
    fig, ax = plt.subplots(figsize=(12, 7), dpi=300)
    set_border_width(ax)
    
    # 设置柱状图的宽度和组内的位置
    bar_width = 0.8 / len(method_names)
    
    # 调整方法顺序，确保LayerCache在最左边，OpenWhisk在最右边
    ordered_method_names = []
    # 首先添加LayerCache
    if 'LayerCache' in method_names:
        ordered_method_names.append('LayerCache')
    
    # 添加CrossEdge
    if 'CrossEdge' in method_names:
        ordered_method_names.append('CrossEdge')
    
    # 添加FaaSCache
    if 'FaaSCache' in method_names:
        ordered_method_names.append('FaaSCache')
    
    # 最后添加OpenWhisk
    if 'OpenWhisk' in method_names:
        ordered_method_names.append('OpenWhisk')
    
    # 用于在x轴上定位柱状图，这里x是beta值索引
    x = np.arange(len(beta_values))
    
    # 不同方法使用不同颜色
    # 红色(LayerCache)、黄色(CrossEdge)、绿色(FaaSCache)、蓝色(OpenWhisk)
    method_colors = {
        'LayerCache': '#C03D3E',  # 红色
        'CrossEdge': '#E1812C',  # 黄色
        'FaaSCache': '#3A923A',   # 绿色
        'OpenWhisk': '#3274A1'    # 蓝色
    }
    
    # 为每种方法绘制柱状图组
    for i, method_name in enumerate(ordered_method_names):
        # 获取当前方法在不同beta值下的平均内存使用量
        memory_usages = []
        for beta in beta_values:
            if method_name in methods_data and beta in methods_data[method_name] and 'avg_total_memory' in methods_data[method_name][beta]:
                memory_usages.append(methods_data[method_name][beta]['avg_total_memory'])
            else:
                memory_usages.append(0)
        
        # 计算柱状图在x轴上的位置
        bar_positions = x - (len(ordered_method_names) - 1) * bar_width / 2 + i * bar_width
        
        # 绘制柱状图
        bars = ax.bar(bar_positions, memory_usages, bar_width, 
                     label=method_name, color=method_colors[method_name])
    
    # 获取y轴的最大值
    memory_list = []
    for method in ordered_method_names:
        if method in methods_data:
            method_memories = []
            for beta in beta_values:
                if beta in methods_data[method] and 'avg_total_memory' in methods_data[method][beta]:
                    method_memories.append(methods_data[method][beta]['avg_total_memory'])
            if method_memories:  # 确保列表非空
                memory_list.append(method_memories)
    
    if memory_list:  # 确保列表非空
        y_max = max(max(memories) for memories in memory_list)
    else:
        y_max = 1.0  # 如果没有数据，设置一个默认值
    
    # 设置y轴范围，增加20%的顶部边距
    ax.set_ylim(0, y_max * 1.2)
    
    # 设置轴标签
    ax.set_ylabel('Average instant memory (MB)')
    
    # 设置x轴刻度标签
    ax.set_xticks(x)
    ax.set_xticklabels([f'β={beta:.2f}' for beta in beta_values])
    
    # 设置刻度标签字体大小
    ax.tick_params(axis='both', labelsize=TICK_SIZE)
    
    # 添加图例 - 调整到右上角，并设置更紧凑的边距
    ax.legend(fontsize=LEGEND_SIZE, frameon=True, edgecolor='black', borderpad=0.4, handletextpad=0.5, 
              loc='upper right', bbox_to_anchor=(0.99, 0.99))
    
    # 添加网格线，提高可读性
    ax.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 保存图表
    plt.tight_layout()
    plt.savefig(output_file, bbox_inches='tight')
    logging.info(f"平均内存使用图已保存为 '{output_file}'")
    
    # 显示图表（如果需要）
    if show_plot:
        plt.show()
    else:
        plt.close()

# 横向加载平均内存
def plot_horizontal_avg_memory_bar_comparison(methods_data, output_file='fig/avg_instant_memory.png', show_plot=False):
    """绘制多种方法在 beta=0.5 下的平均内存使用对比横向柱状图"""
    # 获取所有方法
    method_names = list(methods_data.keys())
    
    # 只使用 beta=0.5
    beta = 0.5
    
    # 创建图形和坐标轴 - 减小高度
    fig, ax = plt.subplots(figsize=(10, 6), dpi=300)
    set_border_width(ax)
    
    # 调整方法顺序，确保LayerCache在最上方，OpenWhisk在最下方
    ordered_method_names = []
    # 首先添加LayerCache
    if 'LayerCache' in method_names:
        ordered_method_names.append('LayerCache')
    
    # 添加CrossEdge
    if 'CrossEdge' in method_names:
        ordered_method_names.append('CrossEdge')
    
    # 添加FaaSCache
    if 'FaaSCache' in method_names:
        ordered_method_names.append('FaaSCache')
    
    # 最后添加OpenWhisk
    if 'OpenWhisk' in method_names:
        ordered_method_names.append('OpenWhisk')
    
    # 用于在y轴上定位柱状图
    y = np.arange(len(ordered_method_names))
    
    # 不同方法使用不同颜色
    # 红色(LayerCache)、黄色(CrossEdge)、绿色(FaaSCache)、蓝色(OpenWhisk)
    method_colors = {
        'LayerCache': '#C03D3E',  # 红色
        'CrossEdge': '#E1812C',  # 黄色
        'FaaSCache': '#3A923A',   # 绿色
        'OpenWhisk': '#3274A1'    # 蓝色
    }   
    
    # 获取当前beta值下各方法的内存使用量
    memory_values = []
    for method_name in ordered_method_names:
        if method_name in methods_data and beta in methods_data[method_name] and 'avg_total_memory' in methods_data[method_name][beta]:
            memory_values.append(methods_data[method_name][beta]['avg_total_memory'])
        else:
            memory_values.append(0)
    
    # 获取最大内存使用量用于设置x轴范围
    max_memory = max(memory_values) if memory_values else 100
    
    # 为每种方法绘制横向柱状图
    for i, (method_name, memory_value) in enumerate(zip(ordered_method_names, memory_values)):
        # 绘制横向柱状图，去掉虚线图案
        bar = ax.barh(i, memory_value, 0.6, 
                    label=method_name, 
                    color=method_colors[method_name],
                    edgecolor='black', linewidth=0.5)

    # 设置y轴刻度标签（方法名称）
    ax.set_yticks(y)
    ax.set_yticklabels(ordered_method_names)
    
    # 设置x轴范围
    ax.set_xlim(0, max_memory * 1.2)
    
    # 设置刻度标签字体大小
    ax.tick_params(axis='both', labelsize=TICK_SIZE)
    
    # 添加x轴标签
    ax.set_xlabel('Average instant memory (MB)')
    
    # 添加网格线
    ax.grid(axis='x', linestyle='--', alpha=0.7)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 保存图表
    plt.tight_layout()
    plt.savefig(output_file, bbox_inches='tight')
    logging.info(f"横向内存使用柱状图(β=0.5)已保存为 '{output_file}'")

# 横向加载内存总大小
def plot_horizontal_total_memory_bar_comparison(methods_data, output_file='fig/total_instant_memory.png', show_plot=False):
    """绘制多种方法在 beta=0.5 下的新内存使用对比横向柱状图"""
    # 获取所有方法
    method_names = list(methods_data.keys())
    
    # 只使用 beta=0.5
    beta = 0.5
    
    # 创建图形和坐标轴 - 减小高度
    fig, ax = plt.subplots(figsize=(10, 6), dpi=300)
    set_border_width(ax)
    
    # 调整方法顺序，确保LayerCache在最上方，OpenWhisk在最下方
    ordered_method_names = []
    # 首先添加LayerCache
    if 'LayerCache' in method_names:
        ordered_method_names.append('LayerCache')
    # 添加CrossEdge
    if 'CrossEdge' in method_names:
        ordered_method_names.append('CrossEdge')
    # 添加FaaSCache
    if 'FaaSCache' in method_names:
        ordered_method_names.append('FaaSCache')
    # 最后添加OpenWhisk
    if 'OpenWhisk' in method_names:
        ordered_method_names.append('OpenWhisk')

    # 用于在y轴上定位柱状图
    y = np.arange(len(ordered_method_names))
    
    # 不同方法使用不同颜色
    # 红色(LayerCache)、黄色(CrossEdge)、绿色(FaaSCache)、蓝色(OpenWhisk)
    method_colors = {
        'LayerCache': '#C03D3E',  # 红色
        'CrossEdge': '#E1812C',  # 黄色
        'FaaSCache': '#3A923A',   # 绿色
        'OpenWhisk': '#3274A1'    # 蓝色
    }   
    
    # 获取当前beta值下各方法的新内存使用量
    memory_values = []
    for method_name in ordered_method_names:
        if method_name in methods_data and beta in methods_data[method_name] and 'new_memory_usage' in methods_data[method_name][beta]:
            memory_values.append(methods_data[method_name][beta]['new_memory_usage'])
        else:
            memory_values.append(0)
    
    # 基础上限根据实际数据的最大值来确定
    base_max = max(memory_values) if memory_values else 10000
    # 在基础上限上添加20%的边距
    max_memory = base_max * 1.2
    
    # 为每种方法绘制横向柱状图
    bars = []
    for i, (method_name, memory_value) in enumerate(zip(ordered_method_names, memory_values)):
        # 绘制横向柱状图，去掉虚线图案
        bar = ax.barh(i, memory_value, 0.6, 
                    color=method_colors[method_name],
                    edgecolor='black', linewidth=0.5)
        bars.append(bar)
    
    # 隐藏y轴刻度和标签
    ax.set_yticks([])
    ax.set_yticklabels([])
    
    # 设置x轴范围，使用计算出的最大值
    ax.set_xlim(0, max_memory)
    
    # 设置x轴刻度标签 - 根据实际数据范围自动调整
    # 将最大值四舍五入到最接近的千位或万位，便于设置刻度
    if base_max < 10000:  # 如果小于10K，按千位取整
        step = 1000
        max_rounded = ((int(max_memory) + step - 1) // step) * step
    else:  # 否则按万位取整
        step = 10000
        max_rounded = ((int(max_memory) + step - 1) // step) * step
    
    # 生成刻度点，大约5-6个点
    num_ticks = 6
    x_step = max(step, max_rounded // (num_ticks - 1))
    x_ticks = list(range(0, max_rounded + x_step, x_step))
    
    # 生成刻度标签，将单位从K改为G
    x_tick_labels = []
    for tick in x_ticks:
        if tick >= 1000000:  # 如果大于等于1GB (1,000,000KB)
            x_tick_labels.append(f'{tick//1000000:.0f}G')
        elif tick >= 1000:  # 如果大于等于1MB (1,000KB)
            x_tick_labels.append(f'{tick//1000:.0f}G')
        else:
            x_tick_labels.append(f'{tick/1000:.0f}G')  # 小于1MB的也转换为G单位
    
    ax.set_xticks(x_ticks)
    ax.set_xticklabels(x_tick_labels)
    
    # 设置刻度标签字体大小
    ax.tick_params(axis='both', labelsize=TICK_SIZE)
    
    # 添加网格线
    ax.grid(axis='x', linestyle='--', alpha=0.7)
    
    # 添加图例到右下角
    ax.legend(bars, ordered_method_names, 
              fontsize=LEGEND_SIZE, 
              frameon=True, 
              edgecolor='black', 
              borderpad=0.4, 
              handletextpad=0.5,
              loc='lower right')
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 保存图表
    fig.subplots_adjust(left=0.1, right=0.95, top=0.95, bottom=0.15)
    plt.savefig(output_file)
    plt.close(fig)  # 显式关闭图形以释放内存
    logging.info(f"横向新内存使用柱状图(β=0.5)已保存为 '{output_file}'")

# ---------------------折线图版本---------------------

# 冷启动频率
def plot_cold_start_line_comparison(methods_data, output_file='fig/line_cold_start_frequency.png', show_plot=False):
    """绘制多种方法在不同beta值下的冷启动频率折线对比图"""
    # 获取所有方法和beta值
    method_names = list(methods_data.keys())
    all_beta_values = set()
    for method_data in methods_data.values():
        all_beta_values.update(method_data.keys())
    beta_values = sorted(list(all_beta_values))
    
    if not beta_values:
        logging.error("没有找到有效的beta值数据，无法绘制图表")
        return
    
    # 创建图形和坐标轴
    fig, ax = plt.subplots(figsize=(12, 7), dpi=300)
    set_border_width(ax)
    
    # 调整方法顺序，确保LayerCache在最左边，OpenWhisk在最右边
    ordered_method_names = []
    # 首先添加LayerCache
    if 'LayerCache' in method_names:
        ordered_method_names.append('LayerCache')
    
    # 添加CrossEdge
    if 'CrossEdge' in method_names:
        ordered_method_names.append('CrossEdge')
    
    # 添加FaaSCache
    if 'FaaSCache' in method_names:
        ordered_method_names.append('FaaSCache')
    
    # 最后添加OpenWhisk
    if 'OpenWhisk' in method_names:
        ordered_method_names.append('OpenWhisk')
    
    # 不同方法使用不同颜色和标记
    # 红色(LayerCache)、黄色(CrossEdge)、绿色(FaaSCache)、蓝色(OpenWhisk)
    method_colors = {
        'LayerCache': '#C03D3E',  # 红色
        'CrossEdge': '#E1812C',  # 黄色
        'FaaSCache': '#3A923A',   # 绿色
        'OpenWhisk': '#3274A1'    # 蓝色
    }
    markers = ['o', 's', '^', 'D', 'x']
    
    # 为每种方法绘制折线
    for i, method_name in enumerate(ordered_method_names):
        # 获取当前方法在不同beta值下的冷启动频率
        frequencies = []
        for beta in beta_values:
            if method_name in methods_data and beta in methods_data[method_name] and 'cold_start_frequency' in methods_data[method_name][beta]:
                frequencies.append(methods_data[method_name][beta]['cold_start_frequency'])
            else:
                frequencies.append(0)
        
        # 绘制折线图
        ax.plot(beta_values, frequencies, 
                label=method_name, 
                color=method_colors[method_name],
                marker=markers[i % len(markers)],
                markersize=10,
                linewidth=2.5)
    
    # 设置轴标签
    ax.set_xlabel('')  # 移除 β Values 标签
    ax.set_ylabel('Cold-start Frequency')
    
    # 设置x轴刻度标签
    ax.set_xticks(beta_values)
    ax.set_xticklabels([f'β={beta:.2f}' for beta in beta_values])
    
    # 设置刻度标签字体大小
    ax.tick_params(axis='both', labelsize=TICK_SIZE)
    
    # 添加图例 - 调整到右上角，并设置更紧凑的边距
    ax.legend(fontsize=LEGEND_SIZE, frameon=True, edgecolor='black', borderpad=0.4, handletextpad=0.5, 
              loc='upper right', bbox_to_anchor=(0.99, 0.99))
    
    # 添加网格线，提高可读性
    ax.grid(True, linestyle='--', alpha=0.7)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 保存图表
    plt.tight_layout()
    plt.savefig(output_file, bbox_inches='tight')
    logging.info(f"冷启动频率折线图已保存为 '{output_file}'")
    
    # 显示图表（如果需要）
    if show_plot:
        plt.show()
    else:
        plt.close()

# 平均响应时间
def plot_avg_response_time_line_comparison(methods_data, output_file='fig/line_response_time.png', show_plot=False):
    """绘制多种方法在不同beta值下的平均响应时间折线对比图"""
    # 获取所有方法和beta值
    method_names = list(methods_data.keys())
    all_beta_values = set()
    for method_data in methods_data.values():
        all_beta_values.update(method_data.keys())
    beta_values = sorted(list(all_beta_values))
    
    if not beta_values:
        logging.error("没有找到有效的beta值数据，无法绘制图表")
        return
    
    # 创建图形和坐标轴
    fig, ax = plt.subplots(figsize=(12, 7), dpi=300)
    set_border_width(ax)
    
    # 调整方法顺序，确保LayerCache在最左边，OpenWhisk在最右边
    ordered_method_names = []
    # 首先添加LayerCache
    if 'LayerCache' in method_names:
        ordered_method_names.append('LayerCache')
    
    # 添加CrossEdge
    if 'CrossEdge' in method_names:
        ordered_method_names.append('CrossEdge')
    
    # 添加FaaSCache
    if 'FaaSCache' in method_names:
        ordered_method_names.append('FaaSCache')
    
    # 最后添加OpenWhisk
    if 'OpenWhisk' in method_names:
        ordered_method_names.append('OpenWhisk')
    
    # 不同方法使用不同颜色和标记
    # 红色(LayerCache)、黄色(CrossEdge)、绿色(FaaSCache)、蓝色(OpenWhisk)
    method_colors = {
        'LayerCache': '#C03D3E',  # 红色
        'CrossEdge': '#E1812C',  # 黄色
        'FaaSCache': '#3A923A',   # 绿色
        'OpenWhisk': '#3274A1'    # 蓝色
    }
    markers = ['o', 's', '^', 'D', 'x']
    
    # 为每种方法绘制折线
    for i, method_name in enumerate(ordered_method_names):
        # 获取当前方法在不同beta值下的平均响应时间
        response_times = []
        for beta in beta_values:
            if method_name in methods_data and beta in methods_data[method_name] and 'weighted_response_time' in methods_data[method_name][beta]:
                response_times.append(methods_data[method_name][beta]['weighted_response_time'])
            else:
                response_times.append(0)
        
        # 绘制折线图
        ax.plot(beta_values, response_times, 
                label=method_name, 
                color=method_colors[method_name],
                marker=markers[i % len(markers)],
                markersize=10,
                linewidth=2.5)
    
    # 设置轴标签
    ax.set_xlabel('')  # 移除 β Values 标签
    ax.set_ylabel('Average Response Time (s)')
    
    # 设置x轴刻度标签
    ax.set_xticks(beta_values)
    ax.set_xticklabels([f'β={beta:.2f}' for beta in beta_values])
    
    # 设置刻度标签字体大小
    ax.tick_params(axis='both', labelsize=TICK_SIZE)
    
    # 添加图例 - 调整到右上角，并设置更紧凑的边距
    ax.legend(fontsize=LEGEND_SIZE, frameon=True, edgecolor='black', borderpad=0.4, handletextpad=0.5, 
              loc='upper right', bbox_to_anchor=(0.99, 0.99))
    
    # 添加网格线，提高可读性
    ax.grid(True, linestyle='--', alpha=0.7)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 保存图表
    plt.tight_layout()
    plt.savefig(output_file, bbox_inches='tight')
    logging.info(f"平均响应时间折线图已保存为 '{output_file}'")
    
    # 显示图表（如果需要）
    if show_plot:
        plt.show()
    else:
        plt.close()

# 平均系统成本
def plot_avg_cost_line_comparison(methods_data, output_file='fig/line_cost.png', show_plot=False):
    """绘制多种方法在不同beta值下的平均成本折线对比图"""
    # 确保所有方法都有相同的beta值集合
    beta_values = sorted(list(next(iter(methods_data.values())).keys()))
    
    # 创建图形和坐标轴
    fig, ax = plt.subplots(figsize=(12, 7), dpi=300)
    set_border_width(ax)
    
    # 调整方法顺序，确保LayerCache在最左边，OpenWhisk在最右边
    ordered_method_names = []
    # 首先添加LayerCache
    if 'LayerCache' in methods_data:
        ordered_method_names.append('LayerCache')
    
    # 添加CrossEdge
    if 'CrossEdge' in methods_data:
        ordered_method_names.append('CrossEdge')
    
    # 添加FaaSCache
    if 'FaaSCache' in methods_data:
        ordered_method_names.append('FaaSCache')
    
    # 最后添加OpenWhisk
    if 'OpenWhisk' in methods_data:
        ordered_method_names.append('OpenWhisk')
    
    # 不同方法使用不同颜色和标记
    # 红色(LayerCache)、黄色(CrossEdge)、绿色(FaaSCache)、蓝色(OpenWhisk)
    method_colors = {
        'LayerCache': '#C03D3E',  # 红色
        'CrossEdge': '#E1812C',  # 黄色
        'FaaSCache': '#3A923A',   # 绿色
        'OpenWhisk': '#3274A1'    # 蓝色
    }
    markers = ['o', 's', '^', 'D', 'x']
    
    # 为每种方法绘制折线
    for i, method_name in enumerate(ordered_method_names):
        data = methods_data[method_name]
        # 获取方法在不同beta值下的平均成本
        costs = []
        for beta in beta_values:
            if beta in data and 'avg_cost' in data[beta]:
                costs.append(data[beta]['avg_cost'])
            else:
                costs.append(0)
        
        # 绘制折线图
        ax.plot(beta_values, costs, 
                label=method_name, 
                color=method_colors[method_name],
                marker=markers[i % len(markers)],
                markersize=10,
                linewidth=2.5)
    
    # 设置轴标签
    ax.set_xlabel('')  # 移除 β Values 标签
    ax.set_ylabel('Average Cost')
    
    # 设置x轴刻度标签
    ax.set_xticks(beta_values)
    ax.set_xticklabels([f'β={beta:.2f}' for beta in beta_values])
    
    # 设置刻度标签字体大小
    ax.tick_params(axis='both', labelsize=TICK_SIZE)
    
    # 添加图例 - 调整到右上角，并设置更紧凑的边距
    ax.legend(fontsize=LEGEND_SIZE, frameon=True, edgecolor='black', borderpad=0.4, handletextpad=0.5, 
              loc='upper right', bbox_to_anchor=(0.99, 0.99))
    
    # 添加网格线，提高可读性
    ax.grid(True, linestyle='--', alpha=0.7)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 保存图表
    plt.tight_layout()
    plt.savefig(output_file, bbox_inches='tight')
    logging.info(f"平均成本折线图已保存为 '{output_file}'")
    
    # 显示图表（如果需要）
    if show_plot:
        plt.show()
    else:
        plt.close()

# 加载内存总大小
def plot_memory_instant_line_comparison(methods_data, output_file='fig/line_memory_instant.png', show_plot=False):
    """绘制多种方法在不同beta值下的平均内存使用折线对比图"""
    # 获取所有方法和beta值
    method_names = list(methods_data.keys())
    all_beta_values = set()
    for method_data in methods_data.values():
        all_beta_values.update(method_data.keys())
    beta_values = sorted(list(all_beta_values))
    
    if not beta_values:
        logging.error("没有找到有效的beta值数据，无法绘制图表")
        return
    
    # 创建图形和坐标轴
    fig, ax = plt.subplots(figsize=(12, 7), dpi=300)
    set_border_width(ax)
    
    # 调整方法顺序，确保LayerCache在最左边，OpenWhisk在最右边
    ordered_method_names = []
    # 首先添加LayerCache
    if 'LayerCache' in method_names:
        ordered_method_names.append('LayerCache')
    
    # 添加CrossEdge
    if 'CrossEdge' in method_names:
        ordered_method_names.append('CrossEdge')
    
    # 添加FaaSCache
    if 'FaaSCache' in method_names:
        ordered_method_names.append('FaaSCache')
    
    # 最后添加OpenWhisk
    if 'OpenWhisk' in method_names:
        ordered_method_names.append('OpenWhisk')
    
    # 不同方法使用不同颜色和标记
    # 红色(LayerCache)、黄色(CrossEdge)、绿色(FaaSCache)、蓝色(OpenWhisk)
    method_colors = {
        'LayerCache': '#C03D3E',  # 红色
        'CrossEdge': '#E1812C',  # 黄色
        'FaaSCache': '#3A923A',   # 绿色
        'OpenWhisk': '#3274A1'    # 蓝色
    }
    markers = ['o', 's', '^', 'D', 'x']
    
    # 为每种方法绘制折线
    for i, method_name in enumerate(ordered_method_names):
        # 获取当前方法在不同beta值下的平均内存使用量
        memory_usages = []
        for beta in beta_values:
            if method_name in methods_data and beta in methods_data[method_name] and 'avg_total_memory' in methods_data[method_name][beta]:
                memory_usages.append(methods_data[method_name][beta]['avg_total_memory'])
            else:
                memory_usages.append(0)
        
        # 绘制折线图
        ax.plot(beta_values, memory_usages, 
                label=method_name, 
                color=method_colors[method_name],
                marker=markers[i % len(markers)],
                markersize=10,
                linewidth=2.5)
    
    # 设置轴标签
    ax.set_xlabel('')  # 移除 β Values 标签
    ax.set_ylabel('Average instant memory (MB)')
    
    # 设置x轴刻度标签
    ax.set_xticks(beta_values)
    ax.set_xticklabels([f'β={beta:.2f}' for beta in beta_values])
    
    # 设置刻度标签字体大小
    ax.tick_params(axis='both', labelsize=TICK_SIZE)
    
    # 添加图例 - 调整到右上角，并设置更紧凑的边距
    ax.legend(fontsize=LEGEND_SIZE, frameon=True, edgecolor='black', borderpad=0.4, handletextpad=0.5, 
              loc='upper right', bbox_to_anchor=(0.99, 0.99))
    
    # 添加网格线，提高可读性
    ax.grid(True, linestyle='--', alpha=0.7)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 保存图表
    plt.tight_layout()
    plt.savefig(output_file, bbox_inches='tight')
    logging.info(f"平均内存使用折线图已保存为 '{output_file}'")
    
    # 显示图表（如果需要）
    if show_plot:
        plt.show()
    else:
        plt.close()

# ---------------------Alpha版本---------------------
def plot_alpha_cost_comparison(methods_data, alpha_values, beta=1.00, output_file=None, show_plot=False):
    """绘制固定beta值下多种方法在不同alpha值下的平均成本对比柱状图
    
    Args:
        methods_data (dict): 包含各方法在不同alpha值下的数据
        alpha_values (list): alpha值列表
        beta (float): 当前使用的beta值，用于标题显示，默认为1.00
        output_file (str): 输出文件路径，如果为None则根据beta值自动生成
        show_plot (bool): 是否显示图表
    """
    # 获取所有方法
    method_names = list(methods_data.keys())
    
    if not alpha_values:
        logging.error("没有找到有效的alpha值数据，无法绘制图表")
        return
    
    # 如果未提供输出文件路径，则自动生成
    if output_file is None:
        output_file = f'fig/bar_alpha_cost_beta_{beta:.2f}.png'
    
    # 创建图形和坐标轴 - 增大图形宽度以容纳一排图例
    fig, ax = plt.subplots(figsize=(18.5, 9), dpi=300)
    set_border_width(ax)
    
    # 设置柱状图的宽度和组内的位置
    bar_width = 0.8 / len(method_names)
    
    # 调整方法顺序，确保LayerCache在最左边，OpenWhisk在最右边
    ordered_method_names = []
    # 首先添加LayerCache
    if 'LayerCache' in method_names:
        ordered_method_names.append('LayerCache')
    
    # 添加CrossEdge
    if 'CrossEdge' in method_names:
        ordered_method_names.append('CrossEdge')
    
    # 添加FaaSCache
    if 'FaaSCache' in method_names:
        ordered_method_names.append('FaaSCache')
    
    # 最后添加OpenWhisk
    if 'OpenWhisk' in method_names:
        ordered_method_names.append('OpenWhisk')
    
    # 用于在x轴上定位柱状图，这里x是alpha值索引
    x = np.arange(len(alpha_values))
    
    # 不同方法使用不同颜色
    # 红色(LayerCache)、黄色(CrossEdge)、绿色(FaaSCache)、蓝色(OpenWhisk)
    method_colors = {
        'LayerCache': '#C03D3E',  # 红色
        'CrossEdge': '#E1812C',  # 黄色
        'FaaSCache': '#3A923A',   # 绿色
        'OpenWhisk': '#3274A1'    # 蓝色
    }
    
    # 为每种方法绘制柱状图组
    for i, method_name in enumerate(ordered_method_names):
        # 获取当前方法在不同alpha值下的平均成本
        costs = []
        for alpha in alpha_values:
            if method_name in methods_data and alpha in methods_data[method_name] and 'avg_cost' in methods_data[method_name][alpha]:
                costs.append(methods_data[method_name][alpha]['avg_cost'])
            else:
                costs.append(0)
        
        # 计算柱状图在x轴上的位置
        bar_positions = x - (len(ordered_method_names) - 1) * bar_width / 2 + i * bar_width
        
        # 绘制柱状图
        bars = ax.bar(bar_positions, costs, bar_width, 
                     label=method_name, color=method_colors[method_name])
    
    # 获取y轴的最大值
    costs_list = []
    for method in ordered_method_names:
        if method in methods_data:
            method_costs = []
            for alpha in alpha_values:
                if alpha in methods_data[method] and 'avg_cost' in methods_data[method][alpha]:
                    method_costs.append(methods_data[method][alpha]['avg_cost'])
            if method_costs:  # 确保列表非空
                costs_list.append(method_costs)
    
    if costs_list:  # 确保列表非空
        y_max = max(max(costs) for costs in costs_list)
    else:
        y_max = 1.0  # 如果没有数据，设置一个默认值
    
    # 增加坐标轴边框粗度
    for spine in ax.spines.values():
        spine.set_linewidth(1.5)
    
    # 设置y轴范围，增加20%的顶部边距
    ax.set_ylim(0, y_max * 1.2)
    
    # 设置轴标签
    ax.set_ylabel('Average Cost')
    
    # 设置x轴刻度标签
    ax.set_xticks(x)
    ax.set_xticklabels([f'α={alpha:.3f}' for alpha in alpha_values])
    
    # 设置刻度线和刻度标签 - 增大粗度
    ax.tick_params(axis='both', width=2.0, length=6)  # 设置刻度线粗度和长度
    ax.tick_params(axis='both', labelsize=TICK_SIZE)  # 设置刻度标签字体大小
    
    # 添加图例 - 修改为一排，并放置在顶部中央，添加透明效果，缩小元素间距
    ax.legend(fontsize=LEGEND_SIZE, frameon=True, edgecolor='black', borderpad=0.4, handletextpad=0.3, 
              loc='upper center', bbox_to_anchor=(0.5, 1.01), ncol=4, framealpha=0.7, columnspacing=0.8)
    
    # 添加网格线，提高可读性
    ax.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 保存图表
    plt.tight_layout()
    plt.savefig(output_file, bbox_inches='tight')
    logging.info(f"β={beta:.2f}下的alpha-成本柱状图已保存为 '{output_file}'")

    return output_file

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='绘制模拟结果比较图')
    
    # 添加输入目录参数
    parser.add_argument('--input-dir', type=str, default='./result',
                        help='指定输入数据的目录 (默认: ./result)，如 ./result/EUA 或 ./result/Telecom')
    
    # 添加beta值相关参数
    parser.add_argument('--beta', type=float, nargs='+', default=[0.50, 0.75, 1.00, 1.25, 1.50],
                        help='要比较的beta值列表，默认为 [0.50, 0.75, 1.00, 1.25, 1.50]')
    
    parser.add_argument('--num-betas', type=int, default=5,
                        help='要使用的beta值数量，从提供的beta列表中选择前N个，默认为5')
    
    # 添加alpha值相关参数
    parser.add_argument('--alpha', type=float, nargs='+', default=[0.001, 0.003, 0.005, 0.010, 0.015],
                        help='要比较的alpha值列表，默认为 [0.001, 0.003, 0.005, 0.010, 0.015]')
    
    parser.add_argument('--num-alphas', type=int, default=5,
                        help='要使用的alpha值数量，从提供的alpha列表中选择前N个，默认为5')
    
    parser.add_argument('--fixed-beta', type=float, default=1.00,
                        help='固定beta值用于alpha变化图，默认为1.00')
    
    parser.add_argument('--output-dir', type=str, default='fig',
                        help='输出图表的目录 (默认: fig)')
    
    parser.add_argument('--show', action='store_true',
                        help='设置此参数才会显示图表窗口，默认仅保存图片')
    
    parser.add_argument('--verbose', '-v', action='store_true',
                        help='显示详细日志信息')
    
    parser.add_argument('--log-file', type=str, default=None,
                        help='指定日志输出文件路径 (默认: logs/plot_metrics.log)')
    
    parser.add_argument('--clean-logs', action='store_true',
                        help='清理logs目录下的旧日志文件')
    
    parser.add_argument('--only-cold-start', action='store_true',
                        help='仅绘制冷启动频率图')
    
    parser.add_argument('--only-response-time', action='store_true',
                        help='仅绘制平均响应时间图')
    
    parser.add_argument('--only-cost', action='store_true',
                        help='仅绘制平均成本图')
    
    parser.add_argument('--only-alpha-cost', action='store_true',
                        help='仅绘制固定beta值下随alpha变化的成本图')
    
    parser.add_argument('--only-memory-usage', action='store_true',
                        help='仅绘制平均内存使用图')
    
    parser.add_argument('--horizontal-memory-bar', action='store_true',
                        help='绘制横向内存使用柱状图')
    
    parser.add_argument('--horizontal-new-memory-bar', action='store_true',
                        help='绘制横向新内存使用柱状图')

    parser.add_argument('--cold-start-ratio', action='store_true',
                        help='绘制冷启动/热启动比例堆叠柱状图')
    
    parser.add_argument('--selected-methods', type=str, nargs='+',
                        help='指定要比较的方法，默认为所有方法 [LayerCache, FaaSCache, OpenWhisk, CrossEdge]')
    
    parser.add_argument('--max-rows', type=int, default=100000,
                        help='读取每个CSV文件的最大行数，用于限制数据量 (默认: 100000)')
    
    parser.add_argument('--use-line', action='store_true',
                        help='使用折线图而不是柱状图来展示数据')
    
    return parser.parse_args()

def main():
    # 解析命令行参数
    args = parse_args()
    
    # 设置日志
    if args.log_file is None:
        args.log_file = 'logs/plot_metrics.log'
    setup_logging(verbose=args.verbose, log_file=args.log_file, clean_logs=args.clean_logs)
    
    logging.info("开始运行性能指标绘图脚本")
    logging.info(f"使用输入目录: {args.input_dir}")
    
    # 从输入目录提取后缀
    suffix = extract_suffix_from_path(args.input_dir)
    logging.info(f"提取的文件名后缀: {suffix}")
    
    # 决定是否显示图表，默认不显示
    show_plot = args.show
    
    # 确保图表输出目录存在
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir, exist_ok=True)
        logging.info(f"创建图表输出目录: {args.output_dir}")
    
    # 初始化生成的文件列表
    generated_files = []
    
    # 使用指定数量的beta值（如果提供了num_betas参数）
    if args.num_betas > 0 and args.num_betas < len(args.beta):
        beta_values = args.beta[:args.num_betas]
        logging.info(f"使用前 {args.num_betas} 个beta值: {beta_values}")
    else:
        beta_values = args.beta
        if args.num_betas > len(args.beta):
            logging.warning(f"请求的beta值数量 ({args.num_betas}) 大于提供的beta值列表长度 ({len(args.beta)})，使用所有可用beta值")
        logging.info(f"使用所有提供的beta值: {beta_values}")
    
    # 如果需要绘制横向内存柱状图
    if args.horizontal_memory_bar:
        # 添加后缀到输出文件名
        file_name = f"total_instant_memory{suffix}.png"
        horizontal_memory_bar_output = os.path.join(args.output_dir, file_name)
        logging.info(f"将绘制横向内存使用柱状图: {horizontal_memory_bar_output}")
        
        # 只加载beta=0.5的数据
        beta_values = [0.5]
        logging.info(f"将加载beta值 {beta_values} 的数据")
        methods_data, beta_values = load_data_for_beta_values(beta_values=beta_values, input_dir=args.input_dir)
        
        # 过滤选定的方法
        if args.selected_methods:
            filtered_data = {}
            for method in args.selected_methods:
                if method in methods_data:
                    filtered_data[method] = methods_data[method]
                else:
                    logging.warning(f"方法 '{method}' 在数据集中不存在，将被忽略")
            methods_data = filtered_data
        
        # 调用函数绘制图表
        plot_horizontal_total_memory_bar_comparison(
            methods_data=methods_data,
            output_file=horizontal_memory_bar_output,
            show_plot=show_plot
        )
        
        # 添加到生成的文件列表
        generated_files.append(("横向内存使用柱状图", horizontal_memory_bar_output))
        
        # 在控制台输出汇总信息
        print("\n已生成以下图表:")
        for file_type, file_path in generated_files:
            print(f"  {file_type}: {file_path}")
            
        logging.info("绘图脚本运行完成")
        return
    
        # 如果需要绘制横向新内存使用柱状图
    
    if args.horizontal_new_memory_bar:
        # 添加后缀到输出文件名
        file_name = f"avg_instant_memory{suffix}.png"
        horizontal_new_memory_bar_output = os.path.join(args.output_dir, file_name)
        logging.info(f"将绘制横向新内存使用柱状图: {horizontal_new_memory_bar_output}")
        
        # 只加载beta=0.5的数据
        beta_values = [0.5]
        logging.info(f"将加载beta值 {beta_values} 的数据")
        methods_data, beta_values = load_data_for_beta_values(beta_values=beta_values, input_dir=args.input_dir)
        
        # 过滤选定的方法
        if args.selected_methods:
            filtered_data = {}
            for method in args.selected_methods:
                if method in methods_data:
                    filtered_data[method] = methods_data[method]
                else:
                    logging.warning(f"方法 '{method}' 在数据集中不存在，将被忽略")
            methods_data = filtered_data
        
        # 调用函数绘制图表
        plot_horizontal_avg_memory_bar_comparison(
            methods_data=methods_data,
            output_file=horizontal_new_memory_bar_output,
            show_plot=show_plot
        )
        
        # 添加到生成的文件列表
        generated_files.append(("横向新内存使用柱状图", horizontal_new_memory_bar_output))
        
        # 在控制台输出汇总信息
        print("\n已生成以下图表:")
        for file_type, file_path in generated_files:
            print(f"  {file_type}: {file_path}")
            
        logging.info("绘图脚本运行完成")
        return

    # 如果需要绘制冷启动/热启动比例图
    if args.cold_start_ratio:
        # 添加后缀到输出文件名
        file_name = f"cold_start_ratio{suffix}.png"
        cold_start_ratio_output = os.path.join(args.output_dir, file_name)
        logging.info(f"将绘制冷启动/热启动比例图: {cold_start_ratio_output}")
        
        # 加载数据
        logging.info(f"将加载beta值 {beta_values} 的数据")
        methods_data, beta_values = load_data_for_beta_values(beta_values=beta_values, input_dir=args.input_dir)
        
        # 过滤选定的方法
        if args.selected_methods:
            filtered_data = {}
            for method in args.selected_methods:
                if method in methods_data:
                    filtered_data[method] = methods_data[method]
                else:
                    logging.warning(f"方法 '{method}' 在数据集中不存在，将被忽略")
            methods_data = filtered_data
        
        # 调用函数绘制图表
        plot_cold_start_ratio(
            methods_data=methods_data,
            output_file=cold_start_ratio_output,
            show_plot=show_plot
        )
        
        # 添加到生成的文件列表
        generated_files.append(("冷启动/热启动比例图", cold_start_ratio_output))
        
        # 在控制台输出汇总信息
        print("\n已生成以下图表:")
        for file_type, file_path in generated_files:
            print(f"  {file_type}: {file_path}")
            
        logging.info("绘图脚本运行完成")
        return
    
    # 仅绘制请求的图表，默认绘制所有图表
    draw_all = not (args.only_cold_start or args.only_response_time or 
                    args.only_cost or args.only_alpha_cost or args.only_memory_usage)
    
    # 设置输出文件名，根据图表类型选择前缀
    bar_prefix = "bar"
    line_prefix = "line"
    
    # 柱状图输出文件路径，添加后缀
    bar_cold_start_output = os.path.join(args.output_dir, f"{bar_prefix}_cold_start_frequency{suffix}.png")
    bar_response_time_output = os.path.join(args.output_dir, f"{bar_prefix}_response_time{suffix}.png")
    bar_cost_output = os.path.join(args.output_dir, f"{bar_prefix}_cost{suffix}.png")
    bar_memory_usage_output = os.path.join(args.output_dir, f"{bar_prefix}_memory_usage{suffix}.png")
    
    # 折线图输出文件路径，添加后缀
    line_cold_start_output = os.path.join(args.output_dir, f"{line_prefix}_cold_start_frequency{suffix}.png")
    line_response_time_output = os.path.join(args.output_dir, f"{line_prefix}_response_time{suffix}.png")
    line_cost_output = os.path.join(args.output_dir, f"{line_prefix}_cost{suffix}.png")
    line_memory_usage_output = os.path.join(args.output_dir, f"{line_prefix}_memory_usage{suffix}.png")
    
    # 冷启动/热启动比例图输出文件路径，添加后缀
    cold_start_ratio_output = os.path.join(args.output_dir, f"cold_start_ratio{suffix}.png")
    
    # alpha成本图输出文件路径，添加后缀
    alpha_cost_bar_output = os.path.join(args.output_dir, f"bar_alpha_cost_{args.fixed_beta:.2f}{suffix}.png")
    
    # 日志输出文件路径信息
    logging.info(f"冷启动比例图将保存为: {cold_start_ratio_output}")
    logging.info(f"冷启动频率柱状图将保存为: {bar_cold_start_output}")
    logging.info(f"冷启动频率折线图将保存为: {line_cold_start_output}")
    logging.info(f"平均响应时间柱状图将保存为: {bar_response_time_output}")
    logging.info(f"平均响应时间折线图将保存为: {line_response_time_output}")
    logging.info(f"平均成本柱状图将保存为: {bar_cost_output}")
    logging.info(f"平均成本折线图将保存为: {line_cost_output}")
    logging.info(f"平均内存使用柱状图将保存为: {bar_memory_usage_output}")
    logging.info(f"平均内存使用折线图将保存为: {line_memory_usage_output}")
    
    if args.only_alpha_cost:
        logging.info(f"固定beta值下的alpha-成本柱状图将保存为: {alpha_cost_bar_output}")
    
    if args.only_memory_usage:
        # 加载数据
        logging.info(f"将加载beta值 {beta_values} 的数据")
        methods_data, beta_values = load_data_for_beta_values(beta_values=beta_values, input_dir=args.input_dir)
        
        # 过滤选定的方法
        if args.selected_methods:
            filtered_data = {}
            for method in args.selected_methods:
                if method in methods_data:
                    filtered_data[method] = methods_data[method]
                else:
                    logging.warning(f"方法 '{method}' 在数据集中不存在，将被忽略")
            methods_data = filtered_data
        
        if not methods_data:
            logging.error("没有找到有效的方法数据，无法绘制图表")
            return
        
        # 检查是否有有效的beta值数据
        has_valid_data = False
        for method_data in methods_data.values():
            if method_data:
                has_valid_data = True
                break
                
        if not has_valid_data:
            logging.error("没有找到有效的beta值数据，无法绘制图表")
            return
            
        logging.info(f"将绘制以下方法的平均内存使用对比图: {list(methods_data.keys())}")
        
        # 绘制平均内存使用柱状图
        plot_memory_instant_comparison(methods_data, 
                                    output_file=bar_memory_usage_output,
                                    show_plot=show_plot)
        logging.info(f"已绘制平均内存使用柱状图: {bar_memory_usage_output}")
        generated_files.append(("平均内存使用柱状图", bar_memory_usage_output))
        
        # 绘制平均内存使用折线图
        plot_memory_instant_line_comparison(methods_data, 
                                         output_file=line_memory_usage_output,
                                         show_plot=show_plot)
        logging.info(f"已绘制平均内存使用折线图: {line_memory_usage_output}")
        generated_files.append(("平均内存使用折线图", line_memory_usage_output))
        
        # 绘制横向总内存使用柱状图（只针对 beta=0.5 的数据）
        # 过滤出 beta=0.5 的数据
        beta05_data = {}
        for method, method_data in methods_data.items():
            if 0.5 in method_data:
                beta05_data[method] = {0.5: method_data[0.5]}
        
        # 添加后缀到输出文件名
        file_name = f"total_instant_memory{suffix}.png"
        horizontal_memory_bar_output = os.path.join(args.output_dir, file_name)
        plot_horizontal_total_memory_bar_comparison(
            methods_data=beta05_data,
            output_file=horizontal_memory_bar_output,
            show_plot=show_plot
        )
        logging.info(f"已绘制横向总内存使用柱状图(β=0.5): {horizontal_memory_bar_output}")
        generated_files.append(("横向总内存使用柱状图(β=0.5)", horizontal_memory_bar_output))
        
        # 在控制台输出汇总信息
        print("\n已生成以下图表:")
        for file_type, file_path in generated_files:
            print(f"  {file_type}: {file_path}")
        
        logging.info("绘图脚本运行完成")
        return
    
    if args.only_alpha_cost:
        # 使用指定数量的alpha值
        if args.num_alphas > 0 and args.num_alphas < len(args.alpha):
            alpha_values = args.alpha[:args.num_alphas]
            logging.info(f"使用前 {args.num_alphas} 个alpha值: {alpha_values}")
        else:
            alpha_values = args.alpha
            if args.num_alphas > len(args.alpha):
                logging.warning(f"请求的alpha值数量 ({args.num_alphas}) 大于提供的alpha值列表长度 ({len(args.alpha)})，使用所有可用alpha值")
            logging.info(f"使用所有提供的alpha值: {alpha_values}")
        
        # 使用固定的三个beta值: 0.50, 1.00, 1.50
        fixed_beta_values = [0.50, 1.00, 1.50]
        logging.info(f"将为以下beta值绘制alpha-成本图: {fixed_beta_values}")
        
        # 为每个beta值绘制图表
        for beta in fixed_beta_values:
            # 加载固定beta值下不同alpha值的数据
            methods_data, alpha_values = load_data_for_alpha_values(beta=beta, alpha_values=alpha_values, input_dir=args.input_dir)
            
            # 过滤选定的方法
            if args.selected_methods:
                filtered_data = {}
                for method in args.selected_methods:
                    if method in methods_data:
                        filtered_data[method] = methods_data[method]
                    else:
                        logging.warning(f"方法 '{method}' 在数据集中不存在，将被忽略")
                methods_data = filtered_data
            
            if not methods_data:
                logging.error(f"beta={beta:.2f}下没有找到有效的方法数据，跳过此beta值")
                continue
            
            # 检查是否有有效的alpha值数据
            has_valid_data = False
            for method_data in methods_data.values():
                if method_data:
                    has_valid_data = True
                    break
                    
            if not has_valid_data:
                logging.error(f"beta={beta:.2f}下没有找到有效的alpha值数据，跳过此beta值")
                continue
            
            # 为当前beta值生成输出文件路径，添加后缀
            beta_output_file = os.path.join(args.output_dir, f"bar_alpha_cost_beta_{beta:.2f}{suffix}.png")
            
            # 绘制alpha-成本柱状图
            output_file = plot_alpha_cost_comparison(methods_data, alpha_values, beta=beta,
                                      output_file=beta_output_file,
                                      show_plot=show_plot)
            logging.info(f"已绘制β={beta:.2f}下的alpha-成本柱状图: {output_file}")
            
            # 添加到生成的文件列表
            generated_files.append((f"β={beta:.2f}下的alpha-成本柱状图", output_file))
        
        # 在控制台输出汇总信息
        print("\n已生成以下图表:")
        for file_type, file_path in generated_files:
            print(f"  {file_type}: {file_path}")
        
        logging.info("绘图脚本运行完成")
        return
    
    # 加载数据 - 这部分代码在某些条件分支中缺失，导致methods_data未定义
    logging.info(f"将加载beta值 {beta_values} 的数据")
    methods_data, beta_values = load_data_for_beta_values(beta_values=beta_values, input_dir=args.input_dir)
    
    # 过滤选定的方法
    if args.selected_methods:
        filtered_data = {}
        for method in args.selected_methods:
            if method in methods_data:
                filtered_data[method] = methods_data[method]
            else:
                logging.warning(f"方法 '{method}' 在数据集中不存在，将被忽略")
        methods_data = filtered_data
    
    # 默认情况下绘制所有图表
    if draw_all or args.only_cold_start:
        # 绘制冷启动频率柱状图
        plot_cold_start_comparison(methods_data, 
                                  output_file=bar_cold_start_output,
                                  show_plot=show_plot)
        logging.info(f"已绘制冷启动频率柱状图: {bar_cold_start_output}")
        generated_files.append(("冷启动频率柱状图", bar_cold_start_output))
        
        # 折线图
        plot_cold_start_line_comparison(methods_data, 
                                        output_file=line_cold_start_output,
                                        show_plot=show_plot)
        logging.info(f"已绘制冷启动频率折线图: {line_cold_start_output}")
        generated_files.append(("冷启动频率折线图", line_cold_start_output))
    
    if draw_all or args.only_response_time:
        # 绘制平均响应时间柱状图
        plot_avg_response_time_comparison(methods_data, 
                                        output_file=bar_response_time_output,
                                        show_plot=show_plot)
        logging.info(f"已绘制平均响应时间柱状图: {bar_response_time_output}")
        generated_files.append(("平均响应时间柱状图", bar_response_time_output))
        
        # 折线图
        plot_avg_response_time_line_comparison(methods_data, 
                                            output_file=line_response_time_output,
                                            show_plot=show_plot)
        logging.info(f"已绘制平均响应时间折线图: {line_response_time_output}")
        generated_files.append(("平均响应时间折线图", line_response_time_output))
    
    if draw_all or args.only_cost:
        # 绘制平均成本柱状图
        plot_avg_cost_comparison(methods_data, 
                               output_file=bar_cost_output,
                               show_plot=show_plot)
        logging.info(f"已绘制平均成本柱状图: {bar_cost_output}")
        generated_files.append(("平均成本柱状图", bar_cost_output))
        
        # 折线图
        plot_avg_cost_line_comparison(methods_data, 
                                     output_file=line_cost_output,
                                     show_plot=show_plot)
        logging.info(f"已绘制平均成本折线图: {line_cost_output}")
        generated_files.append(("平均成本折线图", line_cost_output))
    
    if draw_all or args.only_memory_usage:
        # 绘制平均内存使用柱状图
        plot_memory_instant_comparison(methods_data, 
                                     output_file=bar_memory_usage_output,
                                     show_plot=show_plot)
        logging.info(f"已绘制平均内存使用柱状图: {bar_memory_usage_output}")
        generated_files.append(("平均内存使用柱状图", bar_memory_usage_output))
        
        # 折线图
        plot_memory_instant_line_comparison(methods_data, 
                                            output_file=line_memory_usage_output,
                                            show_plot=show_plot)
        logging.info(f"已绘制平均内存使用折线图: {line_memory_usage_output}")
        generated_files.append(("平均内存使用折线图", line_memory_usage_output))
    
    # 默认情况下也绘制冷启动/热启动比例图
    if draw_all:
        # 绘制冷启动/热启动比例图
        plot_cold_start_ratio(
            methods_data=methods_data,
            output_file=cold_start_ratio_output,
            show_plot=show_plot
        )
        logging.info(f"已绘制冷启动/热启动比例图: {cold_start_ratio_output}")
        generated_files.append(("冷启动/热启动比例图", cold_start_ratio_output))
    
    # 在控制台输出汇总信息
    print("\n已生成以下图表:")
    for file_type, file_path in generated_files:
        print(f"  {file_type}: {file_path}")
    
    # 如果是默认执行所有图表，也绘制横向内存使用柱状图（只针对 beta=0.5 的数据）
    if draw_all:
        # 过滤出 beta=0.5 的数据
        beta05_data = {}
        for method, method_data in methods_data.items():
            if 0.5 in method_data:
                beta05_data[method] = {0.5: method_data[0.5]}
        
        if beta05_data:
            # 添加后缀到输出文件名
            file_name = f"total_instant_memory{suffix}.png"
            horizontal_memory_bar_output = os.path.join(args.output_dir, file_name)
            plot_horizontal_total_memory_bar_comparison(
                methods_data=beta05_data,
                output_file=horizontal_memory_bar_output,
                show_plot=show_plot
            )
            logging.info(f"已绘制横向总内存使用柱状图(β=0.5): {horizontal_memory_bar_output}")
            print(f"  横向总内存使用柱状图(β=0.5): {horizontal_memory_bar_output}")

            # 绘制横向平均内存使用柱状图
            file_name = f"avg_instant_memory{suffix}.png"
            horizontal_new_memory_bar_output = os.path.join(args.output_dir, file_name)
            plot_horizontal_avg_memory_bar_comparison(
                methods_data=beta05_data,
                output_file=horizontal_new_memory_bar_output,
                show_plot=show_plot
            )
            logging.info(f"已绘制横向平均内存使用柱状图(β=0.5): {horizontal_new_memory_bar_output}")
            print(f"  横向平均内存使用柱状图(β=0.5): {horizontal_new_memory_bar_output}")
    
    logging.info("绘图脚本运行完成")

if __name__ == "__main__":
    main() 

    # python plot_metrics.py --input-dir ./result/EUA --output-dir ./fig/EUA
    # python plot_metrics.py --input-dir ./result/Telecom --output-dir ./fig/Telecom