// Copyright 2022 Google Inc. All Rights Reserved.
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package main

import (
	"log"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"net/http"
    "crypto/tls"

)


const schedulerName = "my-scheduler"

func startSchedule() {
    doneChan := make(chan struct{})
	var wg sync.WaitGroup

	wg.Add(1)
	go monitorUnscheduledPods(doneChan, &wg)
	log.Printf("monitorUnscheduledPods finish")


	wg.Add(1)
	go reconcileUnscheduledPods(30, doneChan, &wg)
	log.Printf("reconcileUnscheduledPods finish")



	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)
	for {
		select {
		case <-signalChan:
			log.Printf("Shutdown signal received, exiting...")
			close(doneChan)
			wg.Wait()
			os.Exit(0)
		}
	}
}

func main() {
	// 打开或创建日志文件
	file, err := os.OpenFile("./custom.log", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	log := log.New(file, "Custom Log", log.LstdFlags)

	if err != nil {
        log.Fatal(err) // 如果打开文件出错，记录错误并退出
    }
    defer file.Close() // 确保在函数结束时关闭文件

	// 加载配置文件
	LoadConfig("./config/my-config.json")

	log.Printf("Starting custom scheduler...") // 记录调度器启动信息
	// 设置 HTTP 默认传输的 TLS 配置为不验证证书
	http.DefaultTransport.(*http.Transport).TLSClientConfig = &tls.Config{InsecureSkipVerify: true}

	doneChan := make(chan struct{}) // 创建一个通道用于通知协程完成
	var wg sync.WaitGroup // 创建一个 WaitGroup 用于等待协程完成

	// 启动监控未调度的 Pod 的协程
	wg.Add(1)
	go monitorUnscheduledPods(doneChan, &wg)
	log.Printf("monitorUnscheduledPods finish") // 记录监控协程启动信息

	// 启动调和未调度的 Pod 的协程
	wg.Add(1)
	go reconcileUnscheduledPods(50, doneChan, &wg)
	log.Printf("reconcileUnscheduledPods finish") // 记录调和协程启动信息
	
	// ----------------调度请求----------------
	scheduleRequests()

	// 处理系统信号，关闭程序
	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)
	for {
		select {
		case <-signalChan: // 接收到关闭信号
			log.Printf("Shutdown signal received, exiting...") // 记录关闭信号信息
			close(doneChan) // 关闭通道，通知协程退出
			wg.Wait() // 等待所有协程完成
			os.Exit(0) // 退出程序
		}
	}
    
	file.Close() // 关闭日志文件
}


func main_test() {
	// LoadRequest("./config/prediction.csv", 10000)

	// genZipf(10, 100, 2)
    LoadConfig("./config/my-config.json")
	loadTopo(config_G.TopoName)
	initGlobal()
	genReqZipf("./config/prediction.csv", 10000)
	

	// for k, v := range requestsMap_G.Map { 
	// 	// log.Printf("time slot [%d] \n", k)
		
	// 		// log.Println(len(v.Requests))
		
	// }

	log.Println("map length", len(requestsMap_G.Map))
	log.Println(requestCount_G)
	getRequestsMapSum()
	// log.Println(requestsMap_G.Map)
}

// type testvalue struct {
//     value int
// }

// func (tv *testvalue) add(value int){
// 	tv.value = value
// }

// type testmap struct {
// 	Map map[int]testvalue  // <time_slot, requests>
// }

// func (tm *testmap) init(){
// 	tm.Map = make(map[int]testvalue)
// }

// //check empty case
// func (tm *testmap) add(v int, key int){ 
	
// 	tv, ok := tm.Map[key]


// 	if ok {
		
// 		(&tv).add(v)
// 		tm.Map[key] = tv
		

// 	}else{
        
// 		tv := testvalue{}
// 		(&tv).add(v)
// 		tm.Map[key] = tv
		
// 	}
    
// }

// func main() {

// 	tm := testmap{}
// 	tm.init()
// 	tm.add(999,1)
//     log.Printf("%d", tm.Map[1].value)
// 	tm.add(200,1)
// 	log.Printf("%d", tm.Map[1].value)

// }