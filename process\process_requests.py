# 节点ID,函数类型1,到达时间1,函数类型2,到达时间2,...
import csv
import random
import argparse
import os
import time
import logging
from typing import List, Dict, Tuple
from collections import defaultdict

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("process_requests.log"),
        logging.StreamHandler()
    ]
)

def process_request_file(input_file: str, output_file: str):
    """
    处理请求文件，将每分钟的请求数平均分布到这一分钟内
    
    Args:
        input_file: 输入文件路径，格式为每行表示一分钟，每列表示一个节点的请求数
        output_file: 输出文件路径，格式为每个时间槽的请求按节点和到达时间组织
    """
    start_time = time.time()
    logging.info(f"开始处理请求文件: {input_file}")
    
    # 读取输入文件
    with open(input_file, 'r') as f:
        reader = csv.reader(f)
        lines = list(reader)
    
    # 检查第一行是否为标题行
    if lines and lines[0] and lines[0][0].lower() == 'functype':
        # 如果是标题行，跳过它，从第二行开始读取函数类型
        func_types_line = lines[1]
        data_start_line = 2
    else:
        # 否则假设第一行是函数类型
        func_types_line = lines[0]
        data_start_line = 1
    
    # 解析函数类型，确保在1-10范围内
    func_types = []
    for ft in func_types_line:
        if ft:
            try:
                ft_value = int(ft)
                # 确保函数类型在1-10范围内
                if 1 <= ft_value <= 10:
                    func_types.append(ft_value)
            except ValueError:
                # 如果无法转换为整数，跳过
                continue
    
    if not func_types:
        # 如果没有找到有效的函数类型，使用默认值1-10
        logging.warning("未找到有效的函数类型，使用默认值1-10")
        func_types = list(range(1, 11))
    
    logging.info(f"使用的函数类型: {func_types}")
    
    # 第一次扫描：找出每个节点的最大请求数
    max_requests_per_node = defaultdict(int)
    time_slot_count = len(lines) - data_start_line
    
    logging.info("第一次扫描：计算每个节点的最大请求数...")
    for line in lines[data_start_line:]:
        for node_id, req_count_str in enumerate(line, 1):
            if req_count_str:
                try:
                    req_count = int(req_count_str)
                    if req_count > max_requests_per_node[node_id]:
                        max_requests_per_node[node_id] = req_count
                except ValueError:
                    continue
    
    # 为每个节点生成基准时间分布
    node_base_distributions = {}
    for node_id, max_count in max_requests_per_node.items():
        if max_count > 0:
            # 生成基准时间分布
            node_base_distributions[node_id] = distribute_requests_evenly(max_count, 60)
            # 确保时间点是升序排列的
            node_base_distributions[node_id].sort()
    
    logging.info(f"已为 {len(node_base_distributions)} 个节点生成基准时间分布")
    
    # 使用新的数据结构来组织输出
    # 格式: time_slot -> node_id -> [(func_type, second), ...]
    output_data_by_slot = {}
    
    # 第二次扫描：处理每个时间槽的请求
    logging.info("第二次扫描：处理每个时间槽的请求...")
    for time_slot_idx, line in enumerate(lines[data_start_line:], 1):
        time_slot = time_slot_idx
        
        # 初始化当前时间槽的数据结构
        if time_slot not in output_data_by_slot:
            output_data_by_slot[time_slot] = defaultdict(list)
        
        # 处理每个节点的请求
        for node_id, req_count_str in enumerate(line, 1):
            if req_count_str:
                try:
                    req_count = int(req_count_str)
                    if req_count <= 0:
                        continue
                        
                    # 获取该节点的基准时间分布
                    if node_id in node_base_distributions:
                        base_distribution = node_base_distributions[node_id]
                        max_count = max_requests_per_node[node_id]
                        
                        # 如果当前请求数小于最大请求数，随机选择时间点
                        if req_count < max_count:
                            # 随机选择req_count个时间点
                            selected_times = random.sample(base_distribution, req_count)
                            selected_times.sort()  # 确保时间点是升序的
                        else:
                            # 使用全部基准时间分布
                            selected_times = base_distribution
                        
                        # 为每个请求分配函数类型和时间
                        for second in selected_times:
                            func_type = random.choice(func_types)
                            output_data_by_slot[time_slot][node_id].append((func_type, second))
                except ValueError:
                    continue
        
        # 每10个时间槽记录一次进度
        if time_slot_idx % 10 == 0 or time_slot_idx == time_slot_count:
            logging.info(f"进度: {time_slot_idx}/{time_slot_count} 时间槽")
    
    # 写入输出文件
    logging.info("写入输出文件...")
    with open(output_file, 'w', newline='') as f:
        for time_slot in sorted(output_data_by_slot.keys()):
            # 写入时间槽标题行
            f.write(f"time_slot {time_slot}\n")
            
            # 按节点ID排序
            for node_id in sorted(output_data_by_slot[time_slot].keys()):
                requests = output_data_by_slot[time_slot][node_id]
                if not requests:
                    continue
                
                # 对请求按到达时间排序
                requests.sort(key=lambda x: x[1])
                
                # 构建行数据：节点ID后跟(func_type, second)对
                row_data = [str(node_id)]
                for func_type, second in requests:
                    row_data.extend([str(func_type), str(second)])
                
                # 写入行
                f.write(",".join(row_data) + "\n")
    
    total_time = time.time() - start_time
    logging.info(f"处理完成，输出文件: {output_file}，总耗时: {total_time:.2f}秒")

def distribute_requests_evenly(count: int, interval: int) -> List[int]:
    """
    将count个请求平均分布在interval秒内
    
    Args:
        count: 请求数量
        interval: 时间间隔（秒）
        
    Returns:
        包含请求时间点的列表
    """
    if count <= 0:
        return []
    
    if count == 1:
        return [random.randint(0, interval-1)]
    
    # 计算平均间隔
    avg_gap = interval / count
    
    # 生成基础时间点（均匀分布）
    base_times = [int(i * avg_gap) for i in range(count)]
    
    # 添加小的随机偏移，但确保不超出区间且不重复
    times = []
    for base_time in base_times:
        # 计算可能的偏移范围
        max_offset = min(int(avg_gap / 2), 5)  # 最大偏移不超过间隔的一半或5秒
        offset = random.randint(-max_offset, max_offset)
        time_point = max(0, min(interval-1, base_time + offset))
        times.append(time_point)
    
    # 如果请求数量大于区间长度，直接返回（可能有重复）
    if count > interval:
        return times
    
    # 确保没有重复的时间点
    loop_count = 0
    while len(set(times)) < count:
        loop_count += 1
        if loop_count > 100:  # 防止无限循环
            # 强制生成不重复的点
            times = list(set(times))
            while len(times) < count:
                new_t = random.randint(0, interval-1)
                if new_t not in times:
                    times.append(new_t)
            break
            
        # 找出重复的时间点
        seen = set()
        duplicates = set()
        for t in times:
            if t in seen:
                duplicates.add(t)
            else:
                seen.add(t)
        
        # 调整重复的时间点
        for i, t in enumerate(times):
            if t in duplicates:
                # 尝试找一个未使用的时间点
                new_t = t
                attempts = 0
                while new_t in seen and attempts < 10:  # 限制尝试次数
                    new_t = random.randint(0, interval-1)
                    attempts += 1
                times[i] = new_t
                seen.add(new_t)
                duplicates.remove(t)
                if not duplicates:
                    break
    
    return times

def process_all_beta_files():
    """处理所有beta值对应的请求文件"""
    beta_values = ["0.50", "0.75", "1.00", "1.25", "1.50"]
    
    # 获取当前脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    if not script_dir:
        script_dir = "."
    
    logging.info(f"开始处理所有beta值的文件")
    
    for beta in beta_values:
        input_file = f"./data/request/requests-{beta}.csv"
        output_file = os.path.join(script_dir, f"requests-{beta}.csv")
        
        try:
            process_request_file(input_file, output_file)
        except Exception as e:
            logging.error(f"处理文件 {input_file} 时出错: {e}")
            import traceback
            logging.error(traceback.format_exc())

def main():
    start_time = time.time()
    
    parser = argparse.ArgumentParser(description='处理请求文件，将每分钟的请求数平均分布到秒级')
    parser.add_argument('--input_file', '-i', help='输入文件路径')
    parser.add_argument('--output_file', '-o', help='输出文件路径')
    parser.add_argument('--all_beta', '-a', action='store_true', help='处理所有beta值的文件')
    
    args = parser.parse_args()
    
    if args.all_beta:
        process_all_beta_files()
    elif args.input_file:
        # 如果未指定输出文件，则保存到脚本同目录下
        output_file = args.output_file
        if not output_file:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            if not script_dir:
                script_dir = "."
            input_name = os.path.basename(args.input_file)
            base_name = os.path.splitext(input_name)[0]
            output_file = os.path.join(script_dir, f"{base_name}-processed.csv")
        
        process_request_file(args.input_file, output_file)
    else:
        process_all_beta_files()
    
    total_time = time.time() - start_time
    logging.info(f"脚本执行完成，总耗时: {total_time:.2f}秒")

if __name__ == "__main__":# 
    # 默认处理所有beta值的文件
    main()
    # 处理单个文件
    # python process_requests.py --input_file ./data/request/requests-0.50.csv