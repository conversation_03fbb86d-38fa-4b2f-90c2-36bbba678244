import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import csv

# 从azure中筛选特定Function的数据
def read_azure_2019(file_name, function_id):
   
    with open(file_name,'r') as f:
        contents = f.readlines()
    owner = []
    app = []
    function = []
    trigger = []
    time_slot = []

    for index in range(1,len(contents)):
        parameters = contents[index].split(",")
        if parameters[2] == function_id:
        
            owner.append(parameters[0])
            app.append(parameters[1])
            function.append(parameters[2])
            trigger.append(parameters[3])
            time_slot = parameters[4:]
            break
        
    data = pd.DataFrame(np.array([time_slot], dtype = np.float32))
#test_data_size = 120960
    data = data.transpose()
    data.columns = ['Invocation']

    return data

# 从sortedd01中筛选特定APP的数据
def read_sortedd01(file_name, app_id):
   
    with open(file_name,'r') as f:
        contents = f.readlines()
    app = []
    total = []
    time_slot = []

    for index in range(1,len(contents)):
        parameters = contents[index].split(",")
        if parameters[0] == app_id:
        
            app.append(parameters[0])
            total.append(parameters[1])
            time_slot = parameters[2:]
            break
        
    data = pd.DataFrame(np.array([time_slot], dtype = np.float32))
#test_data_size = 120960
    data = data.transpose()
    data.columns = ['Invocation']

    return data

# 根据频率对数据进行排序
def consolidate_data(file_names, output_name):

    for file_name in file_names:
        with open(file_name,'r') as f:
            contents = f.readlines()

        data_map = {}
        time_slot = [1440]
        
        for index in range(1,len(contents)):
            parameters = contents[index].split(",")
            app_id = parameters[1]
            time = parameters[4:]
            #convert to int
            time_int = list(map(int, time))
            time_slot = np.array(time_int)
                
            if data_map.get(app_id) is None:
            #new key
                data_map.update({app_id : time_slot})
            
            else:
            #old key
                current_time_slot = data_map[app_id]
                update_time_slot = np.add(time_slot, current_time_slot)
                data_map.update({app_id : update_time_slot})
    
    ## write data_map to excel
    f = open(output_name, 'w')

    # create the csv writer
    writer = csv.writer(f)

    f.write('App')
    f.write(',')
    f.write('totalNumber')
    f.write(',')
    time_slot_index = list(np.arange(1,1440+1))
    writer.writerow(time_slot_index)

    for key, value in data_map.items():
        f.write(key)
        f.write(',')
        f.write("%s" % np.sum(value))
        f.write(',')
        writer.writerow(value)
    f.close()

# 根据调用数量对文件进行排序
def sort_data(file_name, output_name):
    # DataFrame to read our input CS file
    dataFrame = pd.read_csv(file_name)

    print(dataFrame.head(3))

    # sorting according to totalNumber column
    dataFrame = dataFrame.sort_values(by = ['totalNumber'], ascending=False, na_position = 'last')
   
    dataFrame.head(4).to_csv(output_name, index = False)
    
    print('after sorting')
    print(dataFrame.head(3))

# 获取调用最多的4个apps
def create_top_4():
    filenames = []
    filename = './data/invocations_per_function_md.anon.d01.csv'
    filenames.append(filename)
    consolidate_data(filenames, './data/final/mergedd01.csv')
    #sort the files
    sort_data('./data/final/mergedd01.csv', './data/final/sortedd01.csv')

# 处理csv文件 
def remove(file_name, output_name):
    # 读取CSV文件
    df = pd.read_csv(file_name)

    # 删除第一列和第二列
    df = df.iloc[:, 2:]  # 从第三列开始选择所有列

    # 保存到新的CSV文件
    df.to_csv(output_name, index=False)

# 加载请求文件
def LoadRequest(file_name, factor):
    # Load a csv file.
    try:
        with open(file_name, 'r') as csv_file:
            print("Successfully Opened request file")
            csv_reader = csv.reader(csv_file)
            csv_lines = list(csv_reader)
    except FileNotFoundError:
        print(f"cannot find request file {file_name}")
        return None

    data = {
        'Time': [],
        'App1': [],
        'App2': [],
        'App3': [],
        'App4': []
    }

    for index, line in enumerate(csv_lines):
        if index == 0:
            for value in line:
                data['Time'].append(int(float(value)))
        elif index == 1:
            for value in line:
                data['App1'].append(int(float(value) / factor))
        elif index == 3:
            for value in line:
                data['App2'].append(int(float(value) / factor))
        elif index == 5:
            for value in line:
                data['App3'].append(int(float(value) / factor))
        elif index == 7:
            for value in line:
                data['App4'].append(int(float(value) / factor))

    return data

if __name__ == "__main__":
    # 4 apps
    file_name = './data/final/sortedd01.csv'
    
    # Web Server
    app_id1 = '94409f2485ebd997a61cbd06906595e4f3ef1846ed7406f9e3fa03cfa4d5060a'
    # File Processing
    app_id2 = '734bb9a04a1d2e6917f75e46c093a6c95d114970e624b9975b98bad86c12f14b'
    # Supermarket Checkout
    app_id3 = '4b75f2532af503341fd11268d543ad0119dd2e8950a022f2851b4a5d0c8a5e0a'
    # Image Recognition
    app_id4 = '228ef3c9f06cde19b4382e39c9339cc9b62b521ae71d70ed02d775df20529a43'
    
    '''画单个函数
    # azure_2019
    #file_name = './data/invocations_per_function_md.anon.d01.csv'
    # lstm预测训练的函数
    #function_id0 = '5e98666f0af3fee4a98e670ab893ddf57046816b30775e3decd77a098d317e98'
 
    #in_data = read_azure_2019(file_name, function_id1)    
    plt.title('Time vs Invocations')
    plt.ylabel('Invocations')
    plt.xlabel('Time (minute)')
    plt.grid(True)
    plt.autoscale(axis='x',tight=True)
    plt.plot(in_data['Invocation'])
    plt.show()
    '''
    
    # 画多个APP
    in_data1 = read_sortedd01(file_name, app_id1)
    in_data2 = read_sortedd01(file_name, app_id2)
    in_data3 = read_sortedd01(file_name, app_id3)
    in_data4 = read_sortedd01(file_name, app_id4)

    plt.title('Time vs Invocations')
    plt.ylabel('Invocations')
    plt.xlabel('Time (minute)')
    plt.grid(True)
    plt.autoscale(axis='x', tight=True)
    
    plt.plot(in_data1['Invocation'], label='Web Server', color='blue')
    plt.plot(in_data2['Invocation'], label='File Processing', color='green')
    plt.plot(in_data3['Invocation'], label='Supermarket Checkout', color='red')
    plt.plot(in_data4['Invocation'], label='Image Recognition', color='purple')
    
    plt.legend()
    plt.show()
    #create_top_4()
    #remove('./data/final/sortedd01.csv', './data/final/invocations.csv')


