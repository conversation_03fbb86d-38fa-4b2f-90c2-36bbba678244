�

    ]��h�6 �            
       ��  � U d dl Zd dlmZ d dlmZmZmZm	Z	m
Z
 d dlZd dlZd dl
Z
d dlZd dlZd dlZd dlZd dlZd dlZ G d� d�      Z G d� d�      Z eddd	d
ddd
ddd��
       eddd	d
ddd
ddd��
      d�Zed   ad� Zd� Zej4                  dfd�Z G d� d�      Z G d� d�      Z G d� d �      Z G d!� d"�      Z G d#� d$�      Z  G d%� d&�      Z! G d'� d(�      Z" G d)� d*�      Z# G d+� d,�      Z$ G d-� d.�      Z% G d/� d0�      Z& G d1� d2�      Z' G d3� d4�      Z( G d5� d6�      Z) G d7� d8�      Z* G d9� d:�      Z+ G d;� d<�      Z, G d=� d>�      Z- G d?� d@�      Z. G dA� dB�      Z/ G dC� dD�      Z0 G dE� dF�      Z1 G dG� dHe1�      Z2 G dI� dJe1�      Z3 G dK� dL�      Z4 G dM� dN�      Z5 G dO� dP�      Z6 G dQ� dR�      Z7dS� Z8dTe9dUe9fdV�Z:dTe9dUe;fdW�Z<dX� Z=dY� Z>dZe?d[e9d\e;fd]�Z@d^e9fd_�ZAd`edUe'fda�ZBd�dbe#dce#dde?dUe;fde�ZCdf� ZDdge?fdh�ZEdie9dje9d^e9dke9fdl�ZFd�d[e9dme;dne;doe;fdp�ZGd[e9dTe9dUe;fdq�ZHd[e9dTe9dUe;fdr�ZId[e9dUe;fds�ZJd�dte1dje9dUe9fdu�ZKdv� ZLdw� ZMdx� ZNdy� ZOd`efdz�ZPd{� ZQd`efd|�ZRd`ed}e(d~e9fd�ZSd�\  aTaUd�e'd`efd��ZVd`ed}e(d~e9d[e9d�e;f
d��ZWd�� ZXd`ed�e9fd��ZYd�� ZZd�e9fd��Z[d�e9d�e9fd��Z\d`efd��Z]d`efd��Z^d�e9dTe9dUe;fd��Z_d�e9d�e9dUe9fd��Z`d`efd��Zad}e(dUebfd��Zcd�� Zdd`efd��Zed`edUebfd��Zfd�\  aTaUd�e'd`edUebfd��Zgd`efd��Zhd`edUebfd��Zid`edUebfd��Zjd`ed�e4dUe;fd��Zkd�e?d�e9d�e4dUebfd��Zld�� Zmd�e?fd��Zn e$�       aoi Zpee9e%f   eqd�<   i aree9e4f   eqd�<   d�� Zsd�� Zteud�k(  r* et�         evd��        evd�t�        �        evd�t�        �       yy)��    N)�List�Dict�Tuple�Optional�Unionc                   �Z   � e Zd Zdefd�Zdd�Zded   fd�Zdedd	fd
�Zde	fd�Z
defd�Zy
)�WaitingQueue�node_idc                 �    � || _         g | _        y �N)r
   �queue��selfr
   s     �9D:\Users\19925\Desktop\Code\LayerCache\simulation-7.23.py�__init__zWaitingQueue.__init__   s   � ����$&��
�    �Requestc                 �:   � | j                   j                  |�       y)u   将请求添加到等待队列N)r
   �append�r   �requests     r   �add_requestzWaitingQueue.add_request   s   � ��
�
���'�"r   �returnc                 �Z   � | j                  �       ry| j                  j                  d�      S )uC   获取队列中的下一个请求，如果队列为空则返回NoneNr   )�is_emptyr
   �pop�r   s    r   �get_next_requestzWaitingQueue.get_next_request   s!   � ��=�=�?���z�z�~�~�a� � r   �	next_slot�requests_map�RequestsMapc                 �~   � | j                   D ]  }|j                  ||�       � | j                   j                  �        y)u*   将队列中的请求移至下一时间槽N)r
   �add�clear)r   r   r    r   s       r   �move_to_next_slotzWaitingQueue.move_to_next_slot   s0   � ��z�z�G����W�i�0� "��
�
���r   c                 �2   � t        | j                  �      dk(  S )u   检查队列是否为空r   ��lenr
   r   s    r   r   zWaitingQueue.is_empty$   s   � ��4�:�:��!�#�#r   c                 �,   � t        | j                  �      S )u   返回队列中的请求数量r'   r   s    r   �sizezWaitingQueue.size(   �   � ��4�:�:��r   N)r   r   )�__name__�
__module__�__qualname__�intr   r   r   r   r%   �boolr   r*   � r   r   r	   r	      sO   � �'�� '�#�!�(�9�"5� !��3� �m� �$�$� $��c� r   r	   c                   �@   � e Zd Z	 d
dedededededededed	ed
efd�Zy)�Config�	topo_file�latency_para�mem_cap�node_num�alpha�beta�slot_num�redu_factor�cpu_Freq�cache_methodc                 �   � || _         || _        || _        || _        || _        || _        || _        || _        |	| _        |
| _	        y r   )
r4   r5   r6   r7   r8   r9   r:   r;   r<   r=   )r   r4   r5   r6   r7   r8   r9   r:   r;   r<   r=   s              r   r   zConfig.__init__-   sM   � � #���(������ ��
���
���	� ��
�&��� ��
�(��r   N)�
LayerCache)r,   r-   r.   �str�floatr/   r   r1   r   r   r3   r3   ,   s[   � � &2�
)�#� 
)�U� 
)�S� 
)��
)�',�
)�49�
)��
)�-0�
)�<?�
)�  #�
)r   r3   z"./data/topo/site-optus-melbCBD.csv�}   �   r?   �   �   �      �?g���Q��?i�a  �d   )
r4   r7   r:   r=   r<   r5   r9   r8   r6   r;   z%./data/topo/shanghai-data-sampled.csv皙�����?�   )�EUA�TelecomrJ   c                  ��   � t        �       at        �       at	        �       at
        �       at        �       a	t        �       adada
dadadadadadadadadadadadadadadadadadada i a!da"da#i a$y )NrE   r   �        )%�ActiveFunctions�activeFunctions_G�CacheMap�
cacheMap_G�FunctionFreq�functionfreq_Gr!   �
requestsMap_G�FunctionInfoMap�
funcInfoMap_G�Req_on_Nodes_Time_Type�rontt_G�clock_G�count_G�req_count_G�total_req_count_G�served_req_count_G�cold_req_count_G�total_cold_req_G�deploy_current_req_G�deploy_neighbor_req_G�total_response_time_G�total_cost_G�weighted_response_time_G�new_total_memory_G�new_bare_memory_G�new_lang_memory_G�new_user_memory_G�new_bare_counter_G�new_lang_counter_G�new_user_counter_G�new_container_counter_G�total_wait_time_G�waiting_queues_G�wait_count_G�second_clock_G�node_execution_end_times_Gr1   r   r   �
initGlobalrr   [   s�   � � (�)����J�!�^�N��M�M�#�%�M�$�&�G��G��G��K����������������L� �� �������� �������� �����L��N�!#�r   c            
      �h  � t        j                  d��      } | j                  dt        dddj	                  t
        j
                  �       �      z   ��       | j                  dt        t        j                  d	t        j                  � d
���       | j                  dt        t        j                  g d�d
t        j                  � d
���       | j                  dt        t        j                  dt        j                  � d
���       | j                  dt        t        j                  dt        j                  � d
���       | j                  dt        t        j                  dt        j                  � d
���       | j                  dt        dg d�d��       | j                  ddd��       | j                  �       S )u`   
    解析命令行参数
    
    Returns:
        argparse.Namespace: 解析后的参数
    z'Simulation in Serverless Edge Computing)�descriptionz--topo-fileNu    拓扑文件路径，可选值: z, )�type�default�helpz--betau   Zipf分布β参数 (默认: �)z--cache-method)r?   �	FaaSCache�	CrossEdge�	OpenWhisku   缓存方法 (默认: )ru   rv   �choicesrw   z
--slot-numu   时间槽数量 (默认: z
--redu-factoru   冗余因子 (默认: z--alphau   成本参数 (默认: z--log-level�INFO��DEBUGr}   �WARNING�ERROR�CRITICALu   日志级别 (默认: INFO)z
--console-log�
store_trueu   启用控制台日志输出)�actionrw   )�argparse�ArgumentParser�add_argumentr@   �join�topo_configs�keysrA   �config_Gr9   r=   r/   r:   r;   r8   �
parse_args)�parsers    r   r�   r�   �   s�  � � �
$�
$�1Z�
[�F� ���
�C��?�$�)�)�L�L]�L]�L_�B`�`� � b� ����u�h�m�m�;�H�M�M�?�!�L� � N� ���(�s�H�<Q�<Q� U�5�h�6K�6K�5L�A�N� � P� ����3��8I�8I�8��9J�9J�8K�1�M� � O� ����c�8�;O�;O�5�h�6J�6J�5K�1�M� � O� ���	��x�~�~�5�h�n�n�5E�Q�G� � I� ���
�C�� Q�:� � <� �����:� � <� ����r   Tc                 �  � t         j                  j                  | �      }|r4t         j                  j                  |�      st        j                  |�       t        j                  �       }|j                  |�       |j                  dd D ]  }|j                  |�       � t        j                  | dd��      }|j                  |�       t        j                  d�      }|j                  |�       |j                  |�       y)u�   
    设置日志配置
    
    Args:
        log_file (str): 日志文件名
        level (int): 日志级别，默认为INFO
    N�wzutf-8)�mode�encodingz)%(asctime)s - %(levelname)s - %(message)s)�os�path�dirname�exists�makedirs�logging�	getLogger�setLevel�handlers�
removeHandler�FileHandler�	Formatter�setFormatter�
addHandler)�log_file�level�console_output�log_dir�root_logger�handler�file_handler�file_formatters           r   �
setup_loggingr�   �   s�   � � �g�g�o�o�h�'�G��r�w�w�~�~�g�.�
���G�� �#�#�%�K������ �'�'��*���!�!�'�*� +� �&�&�x�c�G�L�L����%� ��&�&�'R�S�N����n�-����<�(r   c                   �$   � e Zd Zd� Zdefd�Zd� Zy)�Req_on_Nodesc                 �   � g | _         y r   ��	numVectorr   s    r   r   zReq_on_Nodes.__init__�   s	   � �$&��r   �numc                 �:   � | j                   j                  |�       y�u   添加请求数量N�r�   r   )r   r�   s     r   r#   zReq_on_Nodes.add�   �   � ������c�"r   c                 �8   � | j                   j                  �        y�u   清空请求数量N�r�   r$   r   s    r   r$   zReq_on_Nodes.clear�   �   � ������r   N)r,   r-   r.   r   r/   r#   r$   r1   r   r   r�   r�   �   s   � �'�#�s� #�r   r�   c                   �$   � e Zd Zd� Zdefd�Zd� Zy)�Req_on_Nodes_Timec                 �   � g | _         y r   r�   r   s    r   r   zReq_on_Nodes_Time.__init__�   s	   � �-/��r   �ronc                 �:   � | j                   j                  |�       yr�   r�   )r   r�   s     r   r#   zReq_on_Nodes_Time.add�   r�   r   c                 �8   � | j                   j                  �        yr�   r�   r   s    r   r$   zReq_on_Nodes_Time.clear�   r�   r   N)r,   r-   r.   r   r�   r#   r$   r1   r   r   r�   r�   �   s   � �0�#�|� #�r   r�   c                   �"   � e Zd Zd� Zdedefd�Zy)rW   c                 �   � i | _         y r   ��numMapr   s    r   r   zReq_on_Nodes_Time_Type.__init__�   s	   � �46��r   �funcType�rontc                 �"   � || j                   |<   y)u!   添加函数类型的请求数量Nr�   )r   r�   r�   s      r   r#   zReq_on_Nodes_Time_Type.add�   s   � � $����H�r   N)r,   r-   r.   r   r/   r�   r#   r1   r   r   rW   rW   �   s   � �7�%�C� %�'8� %r   rW   c                   �T   � e Zd Z	 	 	 ddedddddeded	ed
ddefd�Zddd
dd	edefd
�Zy)r   N�id�function�Function�ingress�PhyNode�
arriveTime�served�isColdStart�
deployNode�	linkDelayc	                 ��   � || _         || _        || _        || _        || _        || _        || _        || _        d| _        d| _	        d| _
        d| _        d| _        d| _
        y )NrM   r   )r�   r�   r�   r�   r�   r�   r�   r�   �instan_cost�waitTime�
startExecTime�endExecTime�executionDuration�arrival_second)	r   r�   r�   r�   r�   r�   r�   r�   r�   s	            r   r   zRequest.__init__�   sn   � � ��� ��
����$������&���$���"������ ��
� ������!$�����r   c                 �J   � || _         d| _        || _        || _        || _        y)u6   更新请求的函数、部署节点和冷启动状态TN)r�   r�   r�   r�   r�   )r   r�   r�   r�   r�   s        r   �updatezRequest.update  s&   � � ��
����$���&���"��r   )r   NNr   FFNrM   )r,   r-   r.   r/   r0   rA   r   r�   r1   r   r   r   r   �   so   � �VZ�PU�BE� �3�  �j�  �)�  � � �.2� �IM� �&� �:?� �(#�z� #�y� #�t� #�`e� #r   r   c                   �   � e Zd Zd� Zdefd�Zy)�Requestsc                 �   � g | _         y r   )�requestsr   s    r   r   zRequests.__init__  s	   � �')��
r   r   c                 �:   � | j                   j                  |�       y r   )r�   r   r   s     r   r#   zRequests.add  s   � ��
�
���W�%r   N)r,   r-   r.   r   r   r#   r1   r   r   r�   r�     s   � �*�&�7� &r   r�   c                   �H   � e Zd Zd� Zdedefd�Zdedeee   e	f   fd�Z
d� Zy)	r!   c                 �   � i | _         y r   ��mapr   s    r   r   zRequestsMap.__init__#  s	   � �(*��r   r   �	time_slotc                 �   � || j                   vrt        �       | j                   |<   | j                   |   j                  |�       y r   )r�   r�   r#   )r   r   r�   s      r   r#   zRequestsMap.add&  s5   � ��D�H�H�$�"*�*�D�H�H�Y����������(r   r   c                 �\   � || j                   v r| j                   |   j                  dfS g dfS �NTF)r�   r�   )r   r�   s     r   �getzRequestsMap.get+  s2   � ����� ��8�8�I�&�/�/��5�5��5�y�r   c                 �,   � t        | j                  �      S r   )r(   r�   r   s    r   r*   zRequestsMap.size0  s   � ��4�8�8�}�r   N)r,   r-   r.   r   r   r/   r#   r   r   r0   r�   r*   r1   r   r   r!   r!   "  s@   � �+�)�7� )�s� )�
�S� �U�4��=�$�+>�%?� �
r   r!   c                   �   � e Zd Zd� Zy)�RequestFilec                 �   � g | _         g | _        g | _        g | _        g | _        g | _        g | _        g | _        g | _        y r   )	�time�app1�app2�app3�app4�app5�app6�app7�app8r   s    r   r   zRequestFile.__init__5  sA   � �!��	�!��	�!��	�!��	�!��	�!��	�!��	�!��	�!��	r   N�r,   r-   r.   r   r1   r   r   r�   r�   4  s   � �	"r   r�   c                   �   � e Zd Z	 	 ddededededef
d�Zdedefd	�Zdedefd
�Zdedefd�Zded
efd�Z	defd�Z
defd�Zdefd�Zy)r�   r�   �lat�long�mem�cpuFreqc                 �f   � || _         || _        || _        || _        || _        i | _        i | _        y r   )r�   r�   r�   r�   r�   �funcFreq�recency)r   r�   r�   r�   r�   r�   s         r   r   zPhyNode.__init__D  s3   � ���������	�������*,��
�)+��r   r�   r   c                 �   � || j                   vs| j                   |   dk  rd| j                   |<   y| j                   |   S �Nr   rF   �r�   �r   r�   s     r   �getFreqzPhyNode.getFreqN  s>   � ��4�=�=�(�D�M�M�(�,C�q�,H�&)�D�M�M�(�#���}�}�X�&�&r   c                 �   � || j                   vs| j                   |   dk  rd| j                   |<   y| j                   |   S r�   �r�   r�   s     r   �
getRecencyzPhyNode.getRecencyT  s>   � ��4�<�<�'�4�<�<��+A�Q�+F�%(�D�L�L��"���|�|�H�%�%r   r�   c                 �"   � || j                   |<   y r   r  )r   r�   r�   s      r   �
setRecencyzPhyNode.setRecencyZ  s   � �!(����X�r   �freqc                 �"   � || j                   |<   y r   r�   )r   r�   r  s      r   �setFreqzPhyNode.setFreq]  s   � �"&��
�
�h�r   c                 �n   � || j                   vrd| j                   |<   y | j                   |xx   dz
  cc<   y �NrF   r�   r�   s     r   �addFreqzPhyNode.addFreq`  s0   � ��4�=�=�(�&)�D�M�M�(�#��M�M�(�#�s�*�#r   c                 �P   � || j                   v r| j                   |xx   dz  cc<   y y r
  r�   r�   s     r   �	minusFreqzPhyNode.minusFreqf  s'   � ��t�}�}�$��M�M�(�#�s�*�#� %r   c                 �   � | j                   S r   )r�   r   s    r   �getMemzPhyNode.getMemj  s   � ��x�x�r   N)r   rM   rM   rM   rM   )
r,   r-   r.   r/   rA   r   r   r  r  r  r  r
  r  r1   r   r   r�   r�   C  s�   � �DG�47�,�3� ,�� ,�E� ,��,�,1�,�'�� '�� '�&�3� &�5� &�)�3� )�� )�'�� '�5� '�+�� +�+�#� +��� r   r�   c                   �   � e Zd Zd� Zdedefd�Zdedefd�Zdedede	fd	�Z
defd
�Zdedefd�Zdedefd
�Z
dedede	fd�Zdefd�Zdede	fd�Zy)�Topologyc                 �   � i | _         y r   ��nodesr   s    r   r   zTopology.__init__o  s	   � �)+��
r   �	phyNodeIDr   c           	      �R   � | j                   j                  |t        dddd�      �      S )u   获取物理节点r   rM   )r  r�   r�   �r   r  s     r   r�   zTopology.getr  s"   � ��z�z�~�~�i���C��c�)B�C�Cr   �pc                 �"   � || j                   |<   y)u!   添加物理节点到拓扑结构Nr  )r   r  r  s      r   �add_nodezTopology.add_nodev  s   � � !��
�
�9�r   �	operationr*   c                 �   � |dk(  r#| j                   |   xj                  |z
  c_        y|dk(  r#| j                   |   xj                  |z  c_        yy)u   更新物理节点的内存r#   �minusN)r  r�   )r   r  r  r*   s       r   r�   zTopology.updatez  sK   � �����J�J�y�!�%�%��-�%�
�'�
!��J�J�y�!�%�%��-�%� "r   c                 �,   � t        | j                  �      S )u   返回节点数量)r(   r  r   s    r   r*   z
Topology.size�  r+   r   r�   c                 �f   � | j                  |�      }|j                  |�       || j                  |<   y)u   减少函数类型的频率N)r�   r
  r  �r   r  r�   r  s       r   r
  zTopology.minusFreq�  s*   � ��H�H�Y���	���H�� !��
�
�9�r   c                 �f   � | j                  |�      }|j                  |�       || j                  |<   y)u   增加函数类型的频率N)r�   r  r  r   s       r   r  zTopology.addFreq�  s*   � ��H�H�Y���	�	�	�(�� !��
�
�9�r   �recentc                 �h   � | j                  |�      }|j                  ||�       || j                  |<   y)u'   设置函数类型的最近使用时间N)r�   r  r  )r   r  r�   r"  r  s        r   r  zTopology.setRecency�  s,   � ��H�H�Y���	���X�v�&� !��
�
�9�r   c                 �d   � | j                   j                  �       D ]  }|j                  |�       � y)u0   对所有物理节点增加函数类型的频率N)r  �valuesr  )r   r�   r  s      r   �
addFreqAllzTopology.addFreqAll�  s%   � ����"�"�$�A�
�I�I�h�� %r   c                 �f   � | j                   j                  �       D ]  }|j                  ||�       � y)u<   对所有物理节点设置函数类型的最近使用时间N)r  r%  r  )r   r�   r"  r  s       r   �
setRecencyAllzTopology.setRecencyAll�  s'   � ����"�"�$�A�
�L�L��6�*� %r   N)r,   r-   r.   r   r/   r�   r�   r  r@   rA   r�   r*   r
  r  r  r&  r(  r1   r   r   r  r  n  s�   � �,�D�S� D�W� D�"�#� "�'� "�.�� .�� .�5� .��c� �"�3� "�#� "�"�� "�� "�"�C� "�3� "�� "� �3�  �
+�c� +�5� +r   r  c                   �.   � e Zd Zddedefd�Zdedefd�Zy)	�Location�latitude�	longitudec                 �    � || _         || _        y r   �r+  r,  )r   r+  r,  s      r   r   zLocation.__init__�  s   � � ��
�"��r   �lat1�long1c                 �    � || _         || _        y r   r.  )r   r/  r0  s      r   �initz
Location.init�  s   � ���
���r   N)rM   rM   )r,   r-   r.   rA   r   r2  r1   r   r   r*  r*  �  s(   � �#�� #�� #��� �u� r   r*  c                   �"   � e Zd Zdedefd�Zd� Zy)�Distancer  �distancec                 �    � || _         || _        y r   �r  r5  )r   r  r5  s      r   r   zDistance.__init__�  s   � �"��� ��
r   c                 �   � | j                   S r   )r  r   s    r   �get_idzDistance.get_id�  s   � ��~�~�r   N)r,   r-   r.   r/   rA   r   r9  r1   r   r   r4  r4  �  s   � �!�#� !�� !�r   r4  c                   �   � e Zd Zd� Zy)�	DistSlicec                 �   � g | _         y r   )�slicer   s    r   r   zDistSlice.__init__�  s	   � �%'��
r   Nr�   r1   r   r   r;  r;  �  s   � �(r   r;  c            %       �   � e Zd Z	 	 	 	 	 ddededededededed	ed
ededed
ededededededef$d�Zd� Zd� Z	d� Z
d� Zdefd�Zy)r�   N�nameru   r*   �clock�
coldStartTime�priority�
executionTime�phyNode�
creation_time�lastUseTime�lifeTime�	lang_type�	bare_size�	lang_size�	user_size�
bare_delay�
lang_delay�
user_delayc                 �  � || _         || _        || _        || _        || _        || _        || _        || _        |	| _        |
| _	        || _
        || _        |
| _        || _
        || _        || _        || _        || _        d | _        d | _        y r   )r?  ru   r*   r@  rA  rB  rC  rD  rE  rF  rG  rH  rI  rJ  rK  rL  rM  rN  �lang_layer_uuid�bare_layer_uuid)r   r?  ru   r*   r@  rA  rB  rC  rD  rE  rF  rG  rH  rI  rJ  rK  rL  rM  rN  s                      r   r   zFunction.__init__�  s�   � �
 ��	���	���	���
�*��� ��
�*������*���&��� ��
� #���"���"���"��� %���$���$���  $���#��r   c                 ��   � t         j                  | j                  �      }t        | _        | j                  t        |�      | j                  z  | j                  z  z   | _        y)u   计算活动优先级N)	rS   r�   ru   rY   r@  rA   rA  r*   rB  �r   r  s     r   �active_priorityzFunction.active_priority�  sE   � ��!�!�$�)�)�,����
��
�
�e�D�k�D�4F�4F�&F�4�9�9�%U�U��
r   c                 �   � t         j                  | j                  �      }| j                  t	        |�      | j
                  z  | j                  z  z   | _        y)u5   计算缓存优先级 如果cache不使用当前clockN)rS   r�   ru   r@  rA   rA  r*   rB  rS  s     r   �cache_priorityzFunction.cache_priority�  s>   � ��!�!�$�)�)�,���
�
�e�D�k�D�4F�4F�&F�4�9�9�%U�U��
r   c                 �N   � t        d| j                  � d| j                  � ��       y)u   显示函数优先级�	Function �
 Priority N)�printr?  rB  r   s    r   �
show_priorityzFunction.show_priority�  s   � �
�	�$�)�)��J�t�}�}�o�>�?r   c                 �J   � | xj                   dz  c_         | j                   dk  S )u9   减少函数容器的生命周期，用于OpenWhisk策略rE   r   �rG  r   s    r   �	minusLifezFunction.minusLife�  s   � ��
�
���
��}�}��!�!r   r�   c                 �   � || _         y)u9   设置函数容器的生命周期，用于OpenWhisk策略Nr]  )r   r�   s     r   �activeLifeTimezFunction.activeLifeTime�  s	   � ���
r   )Nr   rM   rM   rM   rF   rM   Nr   r   �
   r   rM   rM   rM   rM   rM   rM   )
r,   r-   r.   r@   r/   rA   r�   r   rT  rV  r[  r^  r`  r1   r   r   r�   r�   �  s�   � �Z]�[^�hj�hk�WZ�	$�S� $�s� $�e� $�RW� $� %�$�7<�$�SX�$�!�$�9<�$�OR�$�be�$�  �$� 16�$� IN�$� af�$� #�	$� 7<�	$� PU�	$�@V�V�
@�"�
�3� r   r�   c                   �6   � e Zd Zdefd�Zdefd�Zdefd�Zd� Zy)	�	Functions�	func_typec                 �    � || _         g | _        y r   )ru   r=  �r   rd  s     r   r   zFunctions.__init__�  s   � ���	�%'��
r   r�   c                 �:   � | j                   j                  |�       y r   )r=  r   �r   r�   s     r   r#   z
Functions.add�  s   � ��
�
���(�#r   �indexc                 �~   � d|cxk  rt        | j                  �      k  rn y | j                  j                  |�       y y �Nr   )r(   r=  r   )r   ri  s     r   �deletezFunctions.delete�  s,   � ���'��D�J�J��'��J�J�N�N�5�!� (r   c                 �p   � | j                   D ]'  }t        d|j                  � d|j                  � ��       �) y )NrX  rY  )r=  rZ  r?  rB  )r   �funcs     r   r[  zFunctions.show_priority  s-   � ��J�J�D��I�d�i�i�[�
�4�=�=�/�B�C� r   N)	r,   r-   r.   r/   r   r�   r#   rl  r[  r1   r   r   rc  rc  �  s,   � �(�#� (�$�H� $�"�C� "�Dr   rc  c                   �H   � e Zd ZdZ	 	 	 ddededededededed	ed
edefd�Zy
)�FunctionInfou-   函数信息类，存储函数的基本信息rA  rL  rM  rN  rC  rH  r*   rI  rJ  rK  c                 �   � || _         || _        || _        || _        || _        || _        || _        || _        |	| _        |
| _	        y r   )
rA  rL  rM  rN  rC  rH  r*   rI  rJ  rK  )r   rA  rL  rM  rN  rC  rH  r*   rI  rJ  rK  s              r   r   zFunctionInfo.__init__  sN   � �
 +���$���$���$���*���"��� ��	�"���"���"��r   N)
rM   rM   rM   rM   rM   r   rM   rM   rM   rM   )r,   r-   r.   �__doc__rA   r/   r   r1   r   r   rp  rp    sa   � �7�_b�WX�gj�#�e� #�e� #�W\� #�"�#�9>�#�QT�#��#�/4�#�GL�#�_d�#r   rp  c                   �2   � e Zd Zd� Zdedefd�Zdedefd�Zy)rU   c                 �   � i | _         y r   ��func_mapr   s    r   r   zFunctionInfoMap.__init__  s	   � �13��
r   rd  �fic                 �"   � || j                   |<   y)u   添加函数信息)NFru  )r   rd  rw  s      r   r#   zFunctionInfoMap.add   s   � � $&��
�
�i� �r   r   c                 �R   � || j                   v r| j                   |   j                  S y)u   获取函数大小r   )rv  r*   rf  s     r   �get_sizezFunctionInfoMap.get_size'  s&   � ���
�
�%��=�=��+�0�0�0�r   N)r,   r-   r.   r   r/   rp  r#   rz  r1   r   r   rU   rU     s-   � �4��S� �l� ��#� �#� r   rU   c                   �:   � e Zd Zd� Zdedefd�Zdefd�Zdefd�Zy)rR   c                 �   � i | _         y r   r�   r   s    r   r   zFunctionFreq.__init__/  s	   � �#%��r   r�   r   c                 �:   � | j                   j                  |d�      S r
  )r�   r�   r�   s     r   r�   zFunctionFreq.get2  s   � ��x�x�|�|�H�c�*�*r   c                 �n   � || j                   v r| j                   |xx   dz
  cc<   y d| j                   |<   y �NrE   r�   r�   s     r   r#   zFunctionFreq.add5  s0   � ��t�x�x���H�H�X��!�#��!"�D�H�H�X�r   c                 �P   � || j                   v r| j                   |xx   dz  cc<   y y r  r�   r�   s     r   r  zFunctionFreq.minus;  s'   � ��t�x�x���H�H�X��!�#��  r   N)r,   r-   r.   r   r/   r�   r#   r  r1   r   r   rR   rR   .  s2   � �&�+�C� +�C� +�#�C� #�$�c� $r   rR   c                   �F   � e Zd Zdefd�Zdefd�Zd� Zdefd�Zded	efd
�Z	y)�
NodeFunctions�phy_node_idc                 �    � || _         i | _        y r   )r  �	functions)r   r�  s     r   r   zNodeFunctions.__init__A  s   � �$���/1��r   r
   c                 �   � | j                   j                  �       D ],  \  }}t        d|� d|� dt        |j                  �      � ��       �. y )NzNode id z funcType: z => active container num: )r�  �itemsrZ  r(   r=  )r   r
   rd  r�  s       r   �showzNodeFunctions.showE  sH   � �$(�N�N�$8�$8�$:� �I�y��H�W�I�[���;U�VY�Zc�Zi�Zi�Vj�Uk�l�m� %;r   c                 �b   � | j                   j                  �       D ]  }|j                  �        � y r   )r�  r%  r[  )r   r�  s     r   r[  zNodeFunctions.show_priorityI  s%   � ����.�.�0�I��#�#�%� 1r   r�   c                 ��   � |j                   | j                  vr,t        |j                   �      | j                  |j                   <   | j                  |j                      j                  |�       y r   )ru   r�  rc  r#   rh  s     r   r#   zNodeFunctions.addM  sH   � ��=�=����.�,5�h�m�m�,D�D�N�N�8�=�=�)����x�}�}�%�)�)�(�3r   rd  �ic                 �^   � || j                   v r| j                   |   j                  |�       y y r   )r�  rl  )r   rd  r�  s      r   rl  zNodeFunctions.deleteR  s*   � �����&��N�N�9�%�,�,�Q�/� 'r   N)
r,   r-   r.   r/   r   r�  r[  r�   r#   rl  r1   r   r   r�  r�  @  s@   � �2�C� 2�n�C� n�&�4�H� 4�
0�� 0�� 0r   r�  c                   �B   � e Zd Zd� Zd� Zd� Zdedefd�Zdededefd	�Z	y
)rN   c                 �   � i | _         y r   r�   r   s    r   r   zActiveFunctions.__init__X  s	   � �-/��r   c                 �   � | j                   st        d�       y t        d�       | j                   j                  �       D ]  \  }}|j                  |�       � y )N�ActiveFunctions is emptyzshow ActiveFunctions....)r�   rZ  r�  r�  )r   �nodeID�nfs      r   r�  zActiveFunctions.show[  sB   � ��x�x��,�-��
�(�)��(�(�.�.�*�J�F�B��G�G�F�O� +r   c                 �   � | j                   st        d�       y t        d�       | j                   j                  �       D ]  }|j                  �        � y )Nr�  zshow priority....)r�   rZ  r%  r[  )r   r�  s     r   r[  zActiveFunctions.show_priorityd  s?   � ��x�x��,�-��
�!�"��(�(�/�/�#�B����� $r   r�   r  c                 ��   � || j                   vrt        |�      | j                   |<   t        j                  |j                  �       |j                  �        | j                   |   j                  |�       y r   )r�   r�  rS   r#   ru   rT  )r   r�   r  s      r   r#   zActiveFunctions.addm  sX   � ��D�H�H�$�"/�	�":�D�H�H�Y�� 	���8�=�=�)�� � �"���������)r   r�   r�  c                 �   � || j                   v r1| j                   |   }|j                  ||�       || j                   |<   y y r   )r�   rl  )r   r  r�   r�  r�  s        r   rl  zActiveFunctions.deletev  s=   � ����� ����)�$�B��I�I�h��"�"$�D�H�H�Y�� !r   N)
r,   r-   r.   r   r�  r[  r�   r/   r#   rl  r1   r   r   rN   rN   W  s>   � �0���*�H� *�� *�%�� %�s� %�s� %r   rN   c                   �~   � e Zd Zdefd�Zd� Zd� Zd� Zd� Zd� Z	de
d	eeee
   f   fd
�Z
defd�Zd
efd�Zded	efd�Zy)�Cacher  c                 �    � || _         g | _        y r   )r  �functionListr  s     r   r   zCache.__init__~  s   � �"���,.��r   c                 �`   � t        d| j                  � dt        | j                  �      � ��       y )N�node z cache size : )rZ  r  r(   r�  r   s    r   r�  z
Cache.show�  s(   � �
��d�n�n�%�^�C��8I�8I�4J�3K�L�Mr   c                 �p   � | j                   D ]'  }t        d|j                  � d|j                  � ��       �) y )N� z Priority : )r�  rZ  r?  rB  rh  s     r   r[  zCache.show_priority�  s2   � ��)�)�H��A�h�m�m�_�L��1B�1B�0C�D�E� *r   c                 �>   � | j                   j                  d� ��       y )Nc                 �   � | j                   S r   )rB  ��fs    r   �<lambda>z!Cache.sort_list.<locals>.<lambda>�  �   � �Q�Z�Zr   ��key�r�  �sortr   s    r   �	sort_listzCache.sort_list�  s   � ������#7��8r   c                 �>   � | j                   j                  d� ��       y )Nc                 �   � | j                   S r   )rF  r�  s    r   r�  z-Cache.sort_by_last_use_time.<locals>.<lambda>�  s   � �Q�]�]r   r�  r�  r   s    r   �sort_by_last_use_timezCache.sort_by_last_use_time�  s   � ������#:��;r   c                 �>   � | j                   j                  d� ��       y)u*   按生命周期排序（OpenWhisk策略）c                 �   � | j                   S r   r]  r�  s    r   r�  z)Cache.sort_by_life_time.<locals>.<lambda>�  r�  r   r�  Nr�  r   s    r   �sort_by_life_timezCache.sort_by_life_time�  s   � ������#7��8r   �functionNamer   c                 �   � t        t        | j                  �      �      D ]$  }|| j                  |   j                  k(  s� |d fc S  y)N)�����z can't find this function by name)�ranger(   r�  r?  )r   r�  r�  s      r   �findz
Cache.find�  sC   � ��s�4�,�,�-�.�A��t�0�0��3�8�8�8��$�w�� /� 6r   r�   c                 ��   � t         j                  |j                  �       |j                  �        t        |_        | j                  j                  |�       | j                  �        y r   )	rS   r  ru   rV  rY   rF  r�  r   r�  rh  s     r   r#   z	Cache.add�  sF   � ����X�]�]�+����!�&������ � ��*����r   r�  c                 �~   � d|cxk  rt        | j                  �      k  rn y | j                  j                  |�       y y rk  )r(   r�  r   )r   r�  s     r   rl  zCache.delete�  s3   � ���*�C��)�)�*�*����!�!�!�$� +r   r�   c                 �z   � t        | j                  �      D ]#  \  }}|j                  |k(  s�| j                  |=  y yr�   )�	enumerater�  ru   )r   r�   r�  r�   s       r   �
removeTypezCache.removeType�  s=   � �$�T�%6�%6�7�K�A�x��}�}��(��%�%�a�(�� 8� r   N)r,   r-   r.   r/   r   r�  r[  r�  r�  r�  r@   r   r   r�  r�   r#   rl  r0   r�  r1   r   r   r�  r�  }  su   � �/�#� /�N�F�9�<�9�6�� 6��s�H�S�M�/A�)B� 6��H� �%�� %�
�3� �4� r   r�  c                   ��   � e Zd Zd� Zd� Zd� Zdefd�Zdede	fd�Z
ded	edeeef   fd
�Zdede
fd�Zd� Zd
� Zdedefd�Zded	edefd�Zdedeeef   fd�Zdedeeef   fd�Zy)rP   c                 �   � i | _         y r   )�cachesr   s    r   r   zCacheMap.__init__�  s	   � �(*��r   c                 �   � | j                   st        d�       y t        d�       | j                   j                  �       D ]  }|j                  �        � y )N�CacheMap is emptyz--------CacheMap--------)r�  rZ  r%  r�  �r   �caches     r   r�  z
CacheMap.show�  s>   � ��{�{��%�&��
�(�)��[�[�'�'�)�E��J�J�L� *r   c                 �   � | j                   st        d�       y t        d�       | j                   j                  �       D ]  }|j                  �        � y )Nr�  z!--------CacheMap Priority--------)r�  rZ  r%  r[  r�  s     r   r[  zCacheMap.show_priority�  sA   � ��{�{��%�&��
�1�2��[�[�'�'�)�E����!� *r   r�   c                 �T  � |j                   j                  | j                  v r3| j                  |j                   j                     j                  |�       y t	        |j                   j                  �      }|j                  |�       || j                  |j                   j                  <   y r   )rD  r�   r�  r#   r�  )r   r�   �	new_caches      r   r#   zCacheMap.add�  sy   � ������$�+�+�-��K�K��(�(�+�+�,�0�0��:��h�.�.�1�1�2�I��M�M�(�#�/8�D�K�K��(�(�+�+�,r   r  r   c                 �L   � | j                   j                  |t        |�      �      S r   )r�  r�   r�  r  s     r   r�   zCacheMap.get�  s   � ��{�{���y�%�	�*:�;�;r   r�   c                 ��   � || j                   v r[| j                   |   }t        |j                  �      D ]4  \  }}|j                  |k(  s�t        |_        ||j                  |<   ||fc S  t
        �       dfS )Nr�  )r�  r�  r�  ru   rY   rF  r�   )r   r  r�   r�  r�  r�   s         r   �get_idle_functionzCacheMap.get_idle_function�  sq   � �����#��K�K�	�*�E�(��);�);�<���8��=�=�H�,�+2�H�(�,4�E�&�&�q�)�#�Q�;�&�  =� �z�2�~�r   c                 �   � || j                   v r?| j                   |   j                  r&| j                   |   j                  d   j                  S y)Nr   rM   )r�  r�  rB  r  s     r   �get_lowest_priorityzCacheMap.get_lowest_priority�  sB   � �����#����I�(>�(K�(K��;�;�y�)�6�6�q�9�B�B�B�r   c                 �b   � | j                   j                  �       D ]  }|j                  �        � y)u   对所有缓存进行排序N)r�  r%  r�  r�  s     r   r�  z
CacheMap.sort�  s#   � ��[�[�'�'�)�E��O�O�� *r   c                 �b   � | j                   j                  �       D ]  }|j                  �        � y)uW   按固定缓存（OpenWhisk）策略的生命周期对所有节点的缓存进行排序N)r�  r%  r�  r�  s     r   �sort_life_timezCacheMap.sort_life_time�  s%   � ��[�[�'�'�)�E��#�#�%� *r   �	funcIndexc                 �^   � || j                   v r| j                   |   j                  |�       y y r   )r�  rl  )r   r  r�  s      r   rl  zCacheMap.delete�  s*   � �����#��K�K�	�"�)�)�)�4� $r   c                 �   � | j                  |�      }|j                  |�      r&|| j                  |<   t        j	                  ||�       yyr�   )r�   r�  r�  �topo_Gr
  )r   r  r�   r�  s       r   �delete_ProbzCacheMap.delete_Prob�  sB   � �����#�����H�%�%*�D�K�K�	�"����Y��1��r   c                 ��   � || j                   v rW| j                   |   j                  r>| j                   |   j                  d   }| j                   |   j                  d�       |dfS t        �       dfS )Nr   TF)r�  r�  rl  r�   �r   r  r�   s      r   �delete_low_functionzCacheMap.delete_low_function�  sf   � �����#����I�(>�(K�(K��{�{�9�-�:�:�1�=�H��K�K�	�"�)�)�!�,��T�>�!��z�5� � r   c                 �  � || j                   v rg| j                   |   j                  rN| j                  �        | j                   |   j                  d   }| j                   |   j                  d�       |dfS t	        �       dfS )u6   删除生命周期最低的函数（OpenWhisk策略）r   TF)r�  r�  r�  rl  r�   r�  s      r   �delete_low_lifetime_functionz%CacheMap.delete_low_lifetime_function�  sr   � �����#����I�(>�(K�(K����!��{�{�9�-�:�:�1�=�H��K�K�	�"�)�)�!�,��T�>�!��z�5� � r   N)r,   r-   r.   r   r�  r[  r�   r#   r/   r�  r�   r   r�  rA   r�  r�  r�  rl  r0   r�  r�  r�  r1   r   r   rP   rP   �  s�   � �+��"�9�H� 9�<�S� <�U� <�	�3� 	�#� 	�%��RU�
�BV� 	��S� �U� �
�
&�
5�� 5�� 5��S� �C� �D� �!�S� !�U�8�T�>�5J� !�	!�c� 	!�e�H�d�N�>S� 	!r   rP   c                   �2   � e Zd ZdZd	dededefd�Zdefd�Zy)
�ContainerLayeru   容器层的基类r*   rE  �last_used_timec                 �r   � t        t        j                  �       �      | _        || _        || _        || _        y r   )r@   �uuid�uuid4r*   rE  r�  )r   r*   rE  r�  s       r   r   zContainerLayer.__init__
  s+   � ���
�
��%��	���	�*���+��r   �current_timec                 �   � || _         y)u   更新层的最后使用时间N)r�  )r   r�  s     r   �update_usagezContainerLayer.update_usage  s
   � �*��r   N�r   r   )r,   r-   r.   rr  rA   r/   r   r�  r1   r   r   r�  r�    s+   � ��,�U� ,�3� ,�C� ,�+�� +r   r�  c                   �2   � � e Zd ZdZddededef� fd�
Z� xZS )�	BareLayeru0   基础容器层，只包含基础环境和工具r*   rH  rE  c                 �D   �� t         �| �  |||�       d| _        || _        y )N�bare)�superr   �
layer_typerH  )r   r*   rH  rE  �	__class__s       �r   r   zBareLayer.__init__  s"   �� �
����}�m�<� ���"��r   �r   )r,   r-   r.   rr  rA   r/   r   �
__classcell__�r�  s   @r   r�  r�    s%   �� �:�#�U� #�s� #�3� #� #r   r�  c            	       �6   � � e Zd ZdZddedededef� fd�
Z� xZS )�	LangLayeru3   语言运行时层，包含特定语言的运行时r*   rH  rQ  rE  c                 �`   �� t         �| �  |||�       d| _        || _        d | _        || _        y )N�lang)r�  r   r�  rH  �
bare_layerrQ  )r   r*   rH  rQ  rE  r�  s        �r   r   zLangLayer.__init__"  s1   �� �
����}�m�<� ���"������.��r   r�  )	r,   r-   r.   rr  rA   r/   r@   r   r�  r�  s   @r   r�  r�     s-   �� �=�/�U� /�s� /�S� /�Y\� /� /r   r�  c                   �  � e Zd Zdefd�Zdefd�Zdefd�Zdefd�Z	defd�Z
d	efd
�Zd	efd�Z
d	efd�Zd	efd
�Zd	edefd�Zd	edefd�Zdefd�Zdefd�Zd	efd�Zd	efd�Zd� Zdefd�Zdefd�Zd	edee   fd�Zd	edee   fd�Zy)�Layer_Cacher
   c                 �   � || _         i | _        i | _        i | _        i | _        i | _        i | _        i | _        i | _        d| _	        y )NrM   )
r
   �active_bare_layers�bare_cache_layers�active_lang_layers�lang_cache_layers�active_bare_index�bare_cache_index�active_lang_index�lang_cache_index�total_memory_usager   s     r   r   zLayer_Cache.__init__+  sS   � ����8:���79���8:���79��� 8:���68���79���68���"%��r   r�  c                 ��   � || j                   |j                  <   |j                  | j                  vrg | j                  |j                  <   | j                  |j                     j	                  |j                  �       y)u   添加活动Bare层N)r�  r�  rH  r�  r   �r   r�  s     r   �add_active_bare_layerz!Layer_Cache.add_active_bare_layer;  �c   � �3=����
���0����t�'=�'=�=�;=�D�"�"�:�#7�#7�8����z�3�3�4�;�;�J�O�O�Lr   c                 ��   � || j                   |j                  <   |j                  | j                  vrg | j                  |j                  <   | j                  |j                     j	                  |j                  �       y)u   添加缓存Bare层N)r�  r�  rH  r�  r   r�  s     r   �add_bare_cache_layerz Layer_Cache.add_bare_cache_layerB  �c   � �2<����z���/����t�'<�'<�<�:<�D�!�!�*�"6�"6�7����j�2�2�3�:�:�:�?�?�Kr   �
lang_layerc                 ��   � || j                   |j                  <   |j                  | j                  vrg | j                  |j                  <   | j                  |j                     j	                  |j                  �       y)u   添加活动Lang层N)r�  r�  rH  r�  r   �r   r  s     r   �add_active_lang_layerz!Layer_Cache.add_active_lang_layerI  r�  r   c                 ��   � || j                   |j                  <   |j                  | j                  vrg | j                  |j                  <   | j                  |j                     j	                  |j                  �       y)u   添加缓存Lang层N)r�  r�  rH  r�  r   r  s     r   �add_lang_cache_layerz Layer_Cache.add_lang_cache_layerP  r   r   r�  c                 �  � || j                   v rz| j                   |   }| j                   |= |j                  | j                  v rE|| j                  |j                     v r)| j                  |j                     j                  |�       yyyy)u   移除活动Bare层N)r�  rH  r�  �remove�r   r�  r�  s      r   �remove_active_bare_layerz$Layer_Cache.remove_active_bare_layerW  �   � ��4�*�*�*��0�0��6�J��'�'��-��#�#�t�'=�'=�=��4�1�1�*�2F�2F�G�G��*�*�:�+?�+?�@�G�G��M� H� >� +r   c                 �  � || j                   v rz| j                   |   }| j                   |= |j                  | j                  v rE|| j                  |j                     v r)| j                  |j                     j                  |�       yyyy)u   移除缓存Bare层N)r�  rH  r�  r  r	  s      r   �remove_bare_cache_layerz#Layer_Cache.remove_bare_cache_layer`  �   � ��4�)�)�)��/�/��5�J��&�&�t�,��#�#�t�'<�'<�<��4�0�0��1E�1E�F�F��)�)�*�*>�*>�?�F�F�t�L� G� =� *r   c                 �  � || j                   v rz| j                   |   }| j                   |= |j                  | j                  v rE|| j                  |j                     v r)| j                  |j                     j                  |�       yyyy)u   移除活动Lang层N)r�  rH  r�  r  �r   r�  r  s      r   �remove_active_lang_layerz$Layer_Cache.remove_active_lang_layeri  r  r   c                 �  � || j                   v rz| j                   |   }| j                   |= |j                  | j                  v rE|| j                  |j                     v r)| j                  |j                     j                  |�       yyyy)u   移除缓存Lang层N)r�  rH  r�  r  r  s      r   �remove_lang_cache_layerz#Layer_Cache.remove_lang_cache_layerr  r  r   r   c                 �   � || j                   v S r   )r�  �r   r�  s     r   �has_bare_cache_layerz Layer_Cache.has_bare_cache_layer{  �   � ��t�-�-�-�-r   c                 �   � || j                   v S r   )r�  r  s     r   �has_lang_cache_layerz Layer_Cache.has_lang_cache_layer~  r  r   rH  c                 �X   � || j                   v xr t        | j                   |   �      dkD  S )u+   检查是否有指定类型的缓存Lang层r   )r�  r(   �r   rH  s     r   �has_lang_cache_layer_by_typez(Layer_Cache.has_lang_cache_layer_by_type�  �/   � ��D�1�1�1�_�c�$�:O�:O�PY�:Z�6[�^_�6_�_r   c                 �X   � || j                   v xr t        | j                   |   �      dkD  S )u1   检查是否有指定语言类型的缓存Bare层r   )r�  r(   r  s     r   �has_bare_cache_layer_by_typez(Layer_Cache.has_bare_cache_layer_by_type�  r  r   c                 �   � || j                   v r3| j                   |   }| j                  |�       | j                  |�       |S y)u"   将Bare层从缓存移动到活动N)r�  r�  r
  r	  s      r   �move_bare_layer_to_activez%Layer_Cache.move_bare_layer_to_active�  �E   � ��4�)�)�)��/�/��5�J��&�&�z�2��(�(��.���r   c                 �   � || j                   v r3| j                   |   }| j                  |�       | j                  |�       |S y)u"   将Lang层从缓存移动到活动N)r�  r  r  r  s      r   �move_lang_layer_to_activez%Layer_Cache.move_lang_layer_to_active�  r"  r   c                 �.   � t        j                  d�       y)u7   获取一个缓存Bare层（按创建时间最新的）zLget_bare_cache_layer is deprecated, use get_bare_cache_layer_by_type insteadN)r�   �warningr   s    r   �get_bare_cache_layerz Layer_Cache.get_bare_cache_layer�  s   � � 	���f�g�r   c                 �   � � � j                  |�      syt        � j                  |   � fd���      }� j                  |   S )uF   获取指定语言类型的缓存Bare层（按创建时间最新的）Nc                 �6   �� �j                   |    j                  S r   )r�  rE  �r�  r   s    �r   r�  z:Layer_Cache.get_bare_cache_layer_by_type.<locals>.<lambda>�  �   �� �$�*@�*@��*F�*T�*Tr   r�  )r  �maxr�  r�  �r   rH  �newest_uuids   `  r   �get_bare_cache_layer_by_typez(Layer_Cache.get_bare_cache_layer_by_type�  �B   �� ��0�0��;�� �$�/�/�	�:�T�V���%�%�k�2�2r   c                 �   � � � j                  |�      syt        � j                  |   � fd���      }� j                  |   S )u@   获取指定类型的缓存Lang层（按创建时间最新的）Nc                 �6   �� �j                   |    j                  S r   )r�  rE  r*  s    �r   r�  z:Layer_Cache.get_lang_cache_layer_by_type.<locals>.<lambda>�  r+  r   r�  )r  r,  r�  r�  r-  s   `  r   �get_lang_cache_layer_by_typez(Layer_Cache.get_lang_cache_layer_by_type�  r0  r   c                 �8   � | j                   j                  |�      S r   )r�  r�   r  s     r   �get_bare_cache_layer_by_uuidz(Layer_Cache.get_bare_cache_layer_by_uuid�  �   � ��%�%�)�)�$�/�/r   c                 �8   � | j                   j                  |�      S r   )r�  r�   r  s     r   �get_lang_cache_layer_by_uuidz(Layer_Cache.get_lang_cache_layer_by_uuid�  r6  r   N)r,   r-   r.   r/   r   r�  r�  r�  r�  r  r  r@   r
  r
  r  r  r0   r  r  r  r  r!  r$  r'  r/  r3  r   r5  r8  r1   r   r   r�  r�  *  s%  � �&�� &� M�	� M�L�y� L�M�	� M�L�y� L�N�S� N�M�C� M�N�S� N�M�C� M�.�� .�� .�.�� .�� .�`�c� `�`�c� `��c� ��c� ��3�c� 3�3�c� 3�0�� 0��)�9L� 0�0�� 0��)�9L� 0r   r�  c                   �   � e Zd Zdedefd�Zy)�ProbPairrd  �probabilityc                 �    � || _         || _        y r   )rd  r;  )r   rd  r;  s      r   r   zProbPair.__init__�  s   � �"���&��r   N)r,   r-   r.   r/   rA   r   r1   r   r   r:  r:  �  s   � �'�#� '�E� 'r   r:  c                   �$   � e Zd Zd� Zd� Zdefd�Zy)�ProbPairVecc                 �   � g | _         y r   )�prob_pair_vr   s    r   r   zProbPairVec.__init__�  s
   � �+-��r   c                 �>   � | j                   j                  d� ��       y )Nc                 �   � | j                   S r   )r;  )�pps    r   r�  z&ProbPairVec.sort_vec.<locals>.<lambda>�  s   � �R�^�^r   r�  )r@  r�  r   s    r   �sort_veczProbPairVec.sort_vec�  s   � ������";��<r   rC  c                 �:   � | j                   j                  |�       y r   )r@  r   )r   rC  s     r   �	push_backzProbPairVec.push_back�  s   � �������#r   N)r,   r-   r.   r   rD  r:  rF  r1   r   r   r>  r>  �  s   � �.�=�$�H� $r   r>  c                   �   � e Zd Zd� Zd� Zy)�Zipfc                 �.  � d|z
  }d|z  | _         t        j                  t        j                  |�      |z  �      | _        t        j                  t        j                  |�      |z  �      }||| j                  z
  z  | _        || j
                  z  | _        y r  )�qInv�math�exp�log�aPowQ�c�qDivC)r   �a�b�s�q�bPowQs         r   r   z
Zipf.__init__�  sp   � �
��E����E��	��X�X�d�h�h�q�k�A�o�.��
�������!��q��)���e�d�j�j�(�)�������Z��
r   c                 �   � t        j                  || j                  z  | j                  z   �      }t        j                  || j
                  z  �      }|S r   )rK  rM  rP  rN  rL  rJ  )r   �u�ln�ts       r   �float64zZipf.float64�  s<   � �
�X�X�a�$�*�*�n�t�z�z�1�
2���H�H�R�$�)�)�^�$���r   N)r,   r-   r.   r   rZ  r1   r   r   rH  rH  �  s   � � �r   rH  c                  �  � g d�g d�g d�g d�g d�g d�g d�g d�g d	�g d
�d�
} d}| d
   d   |z  }| d
   d   |z  }d}d}| d
   d   |z  }| d
   d   |z  }||z   |z   }t        |||||dd||d�
      }	t        j                  d|	�       | d   d   |z  }| d   d   |z  }||z   |z   }t        |||||dd||d�
      }	t        j                  d|	�       | d   d   |z  }| d   d   |z  }||z   |z   }t        |||||dd||d�
      }	t        j                  d|	�       | d   d   |z  }| d   d   |z  }||z   |z   }t        |||||dd||d�
      }	t        j                  d|	�       | d    d   |z  }
| d    d   |z  }d!}d}
| d    d   |z  }| d    d   |z  }|
|z   |z   }t        ||
|||dd"||
d#�
      }	t        j                  d$|	�       | d%   d   |z  }| d%   d   |z  }|
|z   |z   }t        ||
|||dd&||
d'�
      }	t        j                  d(|	�       | d)   d   |z  }| d)   d   |z  }|
|z   |z   }t        ||
|||dd*||
d�
      }	t        j                  d+|	�       | d,   d   |z  }| d,   d   |z  }|
|z   |z   }t        ||
|||dd||
d-�
      }	t        j                  d.|	�       | d/   d   |z  }| d/   d   |z  }d}d0}| d/   d   |z  }| d/   d   |z  }||z   |z   }t        |||||dd1||d2�
      }	t        j                  d3|	�       | d4   d   |z  }| d4   d   |z  }||z   |z   }t        |||||dd5||d-�
      }	t        j                  d6|	�       y )7N)��   �^  i  i4  )r\  r]  ��  i�
  )r\  r]  il  r^  )r\  r]  r^  i  )�,  r_  i�  i�  )r_  r_  ��   ix  )r_  r_  �   i�  )r_  r_  i�  i�  )r]  ��  r`  ra  )r]  rb  r`  iX  )
�AC-Js�IS-Js�UL-Js�TN-Js�IR-Py�DV-Py�FC-Py�MD-Py�DG-Java�DS-Javag����MbP?rc  r   rE   rC   �x   �   rD   r`  �2   rd  i  �A   re  ��   �<   rf  �   �(   �   rg  �#   rb  ih  �   rh  �   �F   �   ri  �   �   rj  rG   �   rk  �   r_  �Z   �	   rl  i6  ra  )rp  rV   r#   )�latency_data�	ms_to_sec�
js_bare_delay�
js_lang_delay�js_bare_size�js_lang_sizerN  �execution_time�cold_start_timerw  �
py_bare_delay�
py_lang_delay�py_bare_size�py_lang_size�java_bare_delay�java_lang_delay�java_bare_size�java_lang_sizes                     r   �
init_func_mapr�  �  sh  � � (�'�'�'�'�&�%�%�'�'��L�" �I� !��)�!�,�y�8�M� ��)�!�,�y�8�M��L��L� �g�&�q�)�I�5�J�!�'�*�1�-�	�9�N�#�m�3�j�@�O�	�o�}�m�Z�Q_�ab�dg�iu�  xD�  FH�  
I�B����a��� �g�&�q�)�I�5�J�!�'�*�1�-�	�9�N�#�m�3�j�@�O�	�o�}�m�Z�Q_�ab�dg�iu�  xD�  FH�  
I�B����a��� �g�&�q�)�I�5�J�!�'�*�1�-�	�9�N�#�m�3�j�@�O�	�o�}�m�Z�Q_�ab�dg�iu�  xD�  FH�  
I�B����a��� �g�&�q�)�I�5�J�!�'�*�1�-�	�9�N�#�m�3�j�@�O�	�o�}�m�Z�Q_�ab�dg�iu�  xD�  FH�  
I�B����a��� !��)�!�,�y�8�M� ��)�!�,�y�8�M��L��L� �g�&�q�)�I�5�J�!�'�*�1�-�	�9�N�#�m�3�j�@�O�	�o�}�m�Z�Q_�ab�dg�iu�  xD�  FI�  
J�B����a��� �g�&�q�)�I�5�J�!�'�*�1�-�	�9�N�#�m�3�j�@�O�	�o�}�m�Z�Q_�ab�dg�iu�  xD�  FH�  
I�B����a��� �g�&�q�)�I�5�J�!�'�*�1�-�	�9�N�#�m�3�j�@�O�	�o�}�m�Z�Q_�ab�dg�iu�  xD�  FI�  
J�B����a��� �g�&�q�)�I�5�J�!�'�*�1�-�	�9�N�#�m�3�j�@�O�	�o�}�m�Z�Q_�ab�dg�iu�  xD�  FI�  
J�B����a��� #�9�-�a�0�9�<�O�"�9�-�a�0�9�<�O��N��N� �i�(��+�i�7�J�!�)�,�Q�/�)�;�N�%��7�*�D�O�	�o����Uc�ef�hk�m{�  ~L�  NP�  
Q�B����a��� �i�(��+�i�7�J�!�)�,�Q�/�)�;�N�%��7�*�D�O�	�o����Uc�ef�hk�m{�  ~L�  NQ�  
R�B����b�"�r   r�   r   c                 �,   � t         j                  | �      S r   )rV   rz  �r�   s    r   �get_container_sizer�  V  s   � ��!�!�(�+�+r   c                 �b   � | t         j                  v rt         j                  |    j                  S y)u=   
    获取某个函数类型的总冷启动延迟时间
    rM   )rV   rv  rA  r�  s    r   �get_container_delayr�  Z  s,   � � �=�)�)�)��%�%�h�/�=�=�=�r   c                  �R   � t         j                  D ]  } t        | �      t        | <   � y r   )r�  r  r�  �layer_map_G�r
   s    r   �init_layer_mapr�  c  s   � ��<�<��*�7�3��G��  r   c            	      �  � 	 t        t        j                  d�      } t        j                  d�       t        j                  | �      }d}t        |�      D ]�  \  }}|dk(  r�|d   dk(  r�t        |d   �      }t        |d   �      }t        �       }|j                  ||�       |t        |<   t        |||t        j                  t        j                   ��      }t        j                  d	|� d
|� d|� ��       t"        j%                  ||�       |dz
  }�� | j'                  �        y # t
        $ r t
        dt        j                  � ��       Y y w xY w)N�rz!open topo file succeessfully.....u   无法找到拓扑文件 rE   r   � rn  �r�   r�   r�   r�   r�   r�  z lat z long )�openr�   r4   r�   �info�FileNotFoundErrorrZ  �csv�readerr�  rA   r*  r2  �
node_map_Gr�   r6   r<   r�  r  �close)	r�  �
csv_readerr
   ri  �rowr�   r�   �loc�phynodes	            r   �	load_topor�  j  s6  � ����#�#�S�)�����8�9�
 ���A��J��G��
�+�
��s��A�:���q�6�R�<�� �C��F�m���S��V�}���j������d��!�
�7������� � ��%�%�
�� 	���u�W�I�U�3�%�v�d�V�<�=������)��1���/ ,�0 �G�G�I�
��? � �
�)�(�*<�*<�)=�>�?���s   �/D! �!%E	�E	�operatorr  r�   c                 �x   � | dk(  rt         j                  d||�       y | dk(  rt         j                  d||�       y y )Nr#   r  )r�  r�   )r�  r  r�   s      r   �update_topor�  �  s7   � ��5���
�
�e�Y��,�	�W�	��
�
�g�y�#�.� 
r   �
ingress_idc                 �   � t         j                  j                  �       D ]  }|j                  | k(  s�|dfc S  t	        j
                  d| � d��       dd| � �fS )u(   根据入口节点 ID 获取物理节点NzphyNode z does not exist.....�Non-exist phyNode )r�  r  r%  r�   r�   �error)r�  �nodes     r   �get_phy_noder�  �  sZ   � ����#�#�%���7�7�j� ���:�� &� �M�M�H�Z�L�(<�=�>��%�j�\�2�2�2r   r   c                 �*  � t        �       }t        j                  j                  �       D ]J  }t	        | j
                  |�      }t
        |j                  |��      }|j                  j                  |�       �L |j                  j                  d� ��       |S )uC   
    根据请求到物理节点的距离对节点进行排序
    r7  c                 �   � | j                   S r   )r5  )�ds    r   r�  zsort_phynodes.<locals>.<lambda>�  s   � ��
�
r   r�  )r;  r�  r  r%  �calculate_distancer�   r4  r�   r=  r   r�  )r   �dsr�  �distr�  s        r   �
sort_phynodesr�  �  sl   � � 
��B����#�#�%��!�'�/�/�4�8���t�w�w��6��
������� &� �H�H�M�M�*�M�+�
�Ir   �phyNode1�phyNode2�unitc                 �|  � | j                   }| j                  }|j                   }|j                  }||k(  r||k(  ryt        j                  |�      }t        j                  |�      }||z
  }	t        j                  |	�      }
t        j                  |�      t        j                  |�      z  t        j
                  |�      t        j
                  |�      z  t        j
                  |
�      z  z   }|dkD  rd}t        j                  |�      }t        j                  |�      }|dz  dz  }|dk(  r|dz  }|S |dk(  r|dz  }|S )	u1   
    计算两个物理节点之间的距离
    rM   rE   rr  g��C�l�?�Kg�8�~߿�?�Ng������?)r�   r�   rK  �radians�sin�cos�acos�degrees)r�  r�  r�  r/  �lng1�lat2�lng2�radlat1�radlat2�theta�radthetar�  s               r   r�  r�  �  s  � � �<�<�D��=�=�D��<�<�D��=�=�D� �t�|������l�l�4� �G��l�l�4� �G��4�K�E��|�|�E�"�H��8�8�G��t�x�x��0�0�4�8�8�G�3D�t�x�x�PW�GX�3X�[_�[c�[c�dl�[m�3m�m�D��a�x����9�9�T�?�D��<�<���D��"�9�v��D��s�{��h��� �K� 
����f�}���Kr   c                  ��   � t        j                  d�       t        j                  D ]9  } t        j                  d| � dt        j                  |    j                  � d��       �; y )Nz----node memomry----r�  z : z MB)r�   r�  r�  r  r�   r�  s    r   �show_nodes_memoryr�  �  sH   � ����+�,��|�|�G��L�L�5��	��V�\�\�'�-B�-F�-F�,G�s�K�L� $r   �request_filec           	      ��  � 	 t        | d��      5 }t        j                  d�       d}|D �]�  }|j                  �       }|s�|j	                  d�      rt        |j
                  �       d   �      }�E|j
                  d�      }t        |�      dk  r�e	 t        |d   �      }d}d}|t        |�      dz
  k  r<	 t        ||   �      }t        ||dz      �      }	|dz
  }|dz
  }|t        |�      dz
  k  r�<t        dt        |t        j                  z  �      �      }
|
dk(  r��d}d}g }|t        |�      dz
  k  rY||k  rT	 t        ||   �      }t        ||dz      �      }	|j                  ||	f�       |dz
  }|dz
  }|t        |�      dz
  k  r||k  r�Tt        |�      dkD  rWt        dt        |�      |
z  �      }
|d d |
�   d |
 }|D ]/  \  }}	t        ||||	�      \  }}|r�t        j!                  ||�       �1 ��� |j#                  �        d}t%        d|dz   �      D ]+  }t        j'                  |�      \  }}|s�|t        |�      z
  }�- t        j                  d	|� d
��       t        j                  dt        j                  � d|� ��       	 d d d �       y # t        t        f$ r Y ���w xY w# t        t        f$ r Y ��+w xY w# t        $ r Y ���w xY w# 1 sw Y   y xY w# t(        $ r t+        d
| � ��       Y y w xY w)Nr�  )r�   z$open request file succeessfully.....r   r�   rE   �,rn  u%   成功加载请求文件，共处理 u
    个时间槽u   应用redu_factor=u   后，总请求数量: u   无法打开请求文件: )r�  r�   r�  �strip�
startswithr/   �splitr(   �
ValueError�
IndexErrorr,  r�   r;   r   �create_request_with_timerT   r#   r�  r�  r�   r�  rZ  )r�  �file�current_time_slot�line�partsr
   r�  �
request_countrd  �arrival_time�reduced_request_count�created_requests�requests_to_create�step�sampled_requestsr   �err�total_requestsr�   r�   �founds                        r   �
read_requestsr�  �  sN  � �[�
�,�S�
)�T��L�L�?�@� !"�����z�z�|���� �?�?�;�/�(+�D�J�J�L��O�(<�%�� �
�
�3����u�:��>��4�!�%��(�m�G� �A�$%�M��c�%�j�1�n�,�"�(+�E�!�H�
�I�+.�u�Q��U�|�+<�L�)�Q�.�M���F�A� �c�%�j�1�n�,� -0��3�}�x�G[�G[�7[�3\�,]�)� -��1� � �A�'(�$�)+�&� �c�%�j�1�n�,�1A�M�1Q�"�(+�E�!�H�
�I�+.�u�Q��U�|�+<�L�.�5�5�y�,�6O�P�,��1�,���F�A�
 �c�%�j�1�n�,�1A�M�1Q� �-�.��2�"�1�c�*<�&=�AV�&V�W��+=�f��f�+E�F\�G\�+]�(� 8H�3�I�|�+C�DU�W`�bi�kw�+x�L�G�S�#&� -� 1� 1�'�;L� M� 8H��{ �J 
�J�J�L� �N�"�1�&7�!�&;�<�	�"/�"3�"3�I�">���%��"�c�(�m�3�N� =�
 
�L�L�@�AR�@S�S`�a�b��L�L�-�h�.B�.B�-C�CZ�[i�Zj�k�l��mV	� V	��D !+�J�7� "�!�"��. !+�J�7� "�!�"�� "� ����Q *�
)��p � �
�*�<�.�9�:���s�   �
K �A?K	�#J9�3)J
�J9�.+J9�K	�J9�7<J#�3J9�
AJ9�J9�/AK	�1AK	�K �
J �J9�J � J9�#J6�2J9�5J6�6J9�9	K�K	�K�K	�	K�K �K �K/�.K/r�   rd  r�  c                 �  � t         j                  |�      }|�r%t        ||j                  |j                  t
        j                  t
        j                  ��      }t        j                  j                  |�      }|r�t        ||j                  |j                  |j                  | |j                  |j                  |j                   |j"                  |j$                  |j&                  |j(                  ��      }t+        t,        ||| ddd��      }||_        t,        dz
  a|dfS t1        j2                  d|� ��       dd|� �fS t1        j2                  d|� ��       dd|� �fS )	u�   
    创建带有具体到达时间的请求

    Args:
        time_slot: 时间槽
        func_type: 函数类型
        ingress_id: 入口节点ID
        arrival_time: 在时间槽内的具体到达时间（秒）
    r�  )ru   rC  rA  r*   rE  rH  rI  rJ  rK  rL  rM  rN  FN)r�   r�   r�   r�   r�   r�   r�   rE   zCannot find funcType r�  )r�  r�   r�   r+  r,  r�   r6   r<   rV   rv  r�   rC  rA  r*   rH  rI  rJ  rK  rL  rM  rN  r   r[   r�   r�   r�  )	r�   rd  r�  r�  r�  r�   �
function_infor�   r   s	            r   r�  r�  8  sT  � � �.�.��
$�C�
���������� � ��%�%�
�� &�.�.�2�2�9�=�
����+�9�9�+�9�9�"�'�'�'�'�1�1�'�1�1�'�1�1�'�1�1�(�3�3�(�3�3�(�3�3�
�H�  ��!��$��!���G� &2�G�"��1��K��D�=� ��M�M�1�)��=�>��0���<�<�<��
�
�*�:�,�7�8��)�*��6�6�6r   rI  rJ  rK  c                 �|   � t        | �      }|dk(  ry||z   |z   }||z  dz  }|dk(  rt        j                  d�       |S )uR   
    计算分层容器的实例化成本，根据实际创建的层来计算
    r   rH   zlayered instant cost is 0)�get_cpur�   r�  )r  rI  rJ  rK  �cpu_freq�
total_sizer�   s          r   �get_layered_instan_costr�  z  sU   � � �y�!�H��1�}�� �Y�&��2�J� �x�'�#�-�K��a�����0�1��r   c                 �   � t        | �      }t        |�      }|dk(  ry||z  dz  }|dk(  rt        j                  d�       |S )uI   
    获取某个物理节点中某个函数类型的实例化成本
    r   rH   zinstant cost is 0)r�  r�  r�   r�  )r  r�   r�  r*   r�   s        r   �get_instan_costr�  �  sK   � � �y�!�H��h�'�D��1�}����/�C�'�K��a�����(�)��r   c                 �   � t        | �      }t        |�      }|dk(  rt        j                  d�       y||z  t        j
                  z  dz  }|S )uF   
    获取某个物理节点中某个函数类型的运行成本
    r   zrun cost cpuFreq is 0g{�G�z�?)r�  r�  r�   r�  r�   r8   )r  r�   r�  r*   �run_costs        r   �get_run_costr�  �  sK   � � �y�!�H��h�'�D��1�}����,�-���h�����/�$�6�H��Or   c                 �   � t         j                  | �      }|j                  dk(  rt        j                  d�       y|j
                  S )u.   
    获取某个物理节点的CPU频率
    r   zCannot find the node 0)r�  r�   r�   r�   r�  r�   )r  �phy_nodes     r   r�  r�  �  s:   � � �z�z�)�$�H��{�{�a�����-�.�����r   �container_layerc                 ��  � | j                   }d}|�n|t        j                  v r\t        | t        �      rt        j                  |   j
                  }n�t        | t        �      r�t        j                  |   j                  }nkt        | t        �      r[t        | d�      rO| j                  }t        j                  j                  �       D ]"  \  }}|j                  |k(  s�|j                  } n t        j                  }||z  d|z
  |z  z  }d}	|�H|t        j                  v r6t        j                  |   dkD  r t        ddt        j                  |   z  �      }	t!        |	|dz  �      }
y)ui   
    计算容器的保活时间（TTL）
    TTL = min(IAT, β)，其中 β = (α * t)/((1-α) * m)
    r   rH  rE   ra  rw  rG   i'  )r*   rV   rv  �
isinstancer�  rM  r�  rL  �hasattrrH  r�  r�   r8   rS   r�   r,  �min)r�  rd  �memory_usage�
startup_delayrH  �f_type�f_infor8   r9   �iat�ttls              r   �calculate_keep_alive_timer   �  sI  � � #�'�'�L� �M� ���m�.D�.D�!D��o�y�1�)�2�2�9�=�H�H�M�
���
3�)�2�2�9�=�H�H�M� �o�y�1�g�o�{�6[�'�1�1�I�"/�"8�"8�">�">�"@�����#�#�y�0�$*�$5�$5�M�� #A�
 
�N�N�E��M�!�q�5�y�L�&@�A�D� 
�C����n�.@�.@�!@�^�EW�EW�Xa�Eb�ef�Ef��!�S�>�-�-�i�8�8�9�� �c�4�%�<�
 �C� r   c                  �  � i } t         j                  j                  �       D ]
  \  }}|| |<   � t        | j                  �       d� d��      }t        j
                  j                  �       D �]   \  }}|j
                  �       }|dk  rt        j                  d|� d|� d��       �9|}|t        vrt        |�      t        |<   t        |   }|D �]�  \  }}	|j
                  �       }|dk  rt        j                  d|� d|� d��        ��t        j                  ||�      \  }
}|dk7  r�Y|t        j                  vr�lt        j                  |   }|j                  }
|j                   }|j"                  }|j$                  }d}t'        d	||�      }|s||z
  }t'        d
||�      }|s||z
  }||
z
  }||k  r��|sJt)        ||t*        �      }|j-                  |�       |xj.                  |z
  c_        ||z  }t1        d||�       nW||j2                  v rI|j2                  |   r:|j2                  |   d   }|j5                  |�      }|�|j7                  t*        �       |sUt9        ||j:                  t*        �      }|j=                  |�       |xj.                  |z
  c_        ||z  }t1        d||�       nH||j>                  v r:|j>                  |   d   }|jA                  |�      }|�|j7                  t*        �       tC        ||jD                  |jF                  |jH                  |||||
|jJ                  |jL                  |jN                  �
�      }
j:                  |
_(        |jR                  |
_)        tT        jW                  |
|�       t1        d||
�       ��� ��# y)u�   
    根据函数调用频率预热容器
    预热高频函数的User层（必要时会创建依赖的Bare层和Lang层）
    c                 �   � | d   S r  r1   ��xs    r   r�  z,prewarm_containers_layered.<locals>.<lambda>�  �   � �!�A�$r   T�r�  �reverser   r�  z memory empty (z MB), skip prewarmr�  r�  r�  r  N�ru   rC  rA  r*   rD  rH  rI  rJ  rK  rL  rM  rN  ),rS   r�   r�  �sortedr�  r  r  r�   r&  r�  r�  rQ   r�  rV   rv  rK  rH  rJ  rI  �check_layer_existsr�  rY   r�  r�  r�  r�  r!  r�  r�  r�  r  r�  r$  r�   rC  rA  r*   rL  rM  rN  rP  rQ  rO   r#   )�function_frequenciesrd  r  �sorted_functionsr
   r�  �node_memory�available_memory�
layered_cache�_r�  r�  r�  rK  rH  rJ  rI  �required_memory�bare_exists�lang_existsr�  �	bare_uuidr  �	lang_uuids                           r   �prewarm_containers_layeredr  �  s�  � � ��)�-�-�3�3�5��	�4�*.��Y�'� 6� �2�8�8�:��X\�]��  ���+�+�-�
����k�k�m�� �!���O�O�e�G�9�O�K�=�HZ�[�\��&�� �+�%�#.�w�#7�K�� �#�G�,�
� -�L�I�q�#�{�{�}���1�$����%��y��@P�?Q�Qc� d�e�� �/�/���C�D�A�q��B�w�� �
� 6� 6�6��)�2�2�9�=�M�%�/�/�I�%�/�/�I�%�/�/�I�%�/�/�I�  �O� -�V�Y�
�N�K���9�,�� -�V�Y�
�N�K���9�,�� 
�y�(�O�  �/�1�� �&�y�)�W�E�
��3�3�J�?��0�0�I�=�0� �I�-� ��G�W�i�8� �
� >� >�>�=�Ca�Ca�bk�Cl� -� >� >�y� I�!� L�I�!.�!H�!H��!S�J�!�-�"�/�/��8� �&��y��O�O���
�
 �3�3�J�?��0�0�I�=�0� �I�-� ��G�W�i�8� �
� >� >�>� -� >� >�y� I�!� L�I�!.�!H�!H��!S�J�!�-�"�/�/��8� ��+�9�9�+�9�9�"�'�'��#�#�#�#�(�3�3�(�3�3�(�3�3�
�A�  !+���A�� *� :� :�A�� 
�!�!�!�W�-� 
���)�4�Q -�% .r   c                  ��  � i } t         j                  j                  �       D ]
  \  }}|| |<   � t        | j                  �       d� d��      }t        j
                  j                  �       D �]  \  }}|j
                  �       }|dk  r�|t        j                  vrt        |�      t        j                  |<   t        j                  |   }|dd D ]�  \  }}|dk  r �p||j                  D �	cg c]  }	|	j                  �� c}	v r�4|t        j                  vr�Gt        j                  |   }
|
j                  }||k  r�lt        |||��      }t        j!                  |�       ||z  }t#        d||�       �� �� yc c}	w )	uN   
    对比方法的预热机制
    根据函数调用频率预热容器
    c                 �   � | d   S r  r1   r  s    r   r�  z$prewarm_containers.<locals>.<lambda>�  r  r   Tr  r   Nrw  )ru   r*   rD  r  )rS   r�   r�  r	  r�  r  r  rQ   r�  r�  r�  ru   rV   rv  r*   r�   r#   r�  )
r  rd  r  r  r
   r�  r
  r�  r  r�  r�  r*   r�   s
                r   �prewarm_containersr  }  s{  � � ��)�-�-�3�3�5��	�4�*.��Y�'� 6� �2�8�8�:��X\�]��  ���+�+�-�
����k�k�m�� �!��� �*�+�+�+�).�w��J���g�&��!�!�'�*�� -�R�a�0�L�I�q��a��� �U�-?�-?�@�-?��Q�V�V�-?�@�@�� �
� 6� 6�6��)�2�2�9�=�M� �%�%�D� �T�!��  �����H� 
�N�N�8�$��4��K����$�/�= 1� .��* As   �(E3
c                  �^  � t         j                  j                  �       D ]n  \  } }|j                  j                  �       D ]L  \  }}t	        t        |j                  �      �      D ]&  }|j                  |   }t        j                  |�       �( �N �p t        j                  �        t         j                  j                  �        t        j                  �       D �]�  \  }}t        |j                  j                  �       �      D ]<  \  }}	|j                  |	�       |	j                  t         �       |j#                  |�       �> t        |j$                  j                  �       �      D ]<  \  }
}|j'                  |�       |j                  t         �       |j)                  |
�       �> t        |j*                  j                  �       �      D ]�  \  }
}d}|t        j,                  v rNt        j,                  |   }
|
j.                  D ],  }t1        |d�      s�|j2                  |j4                  k(  s�*d} n |r�kt7        |�      }t         |j8                  z
  |kD  s��|j:                  }|xj<                  |z  c_        t?        d||�       |jA                  |
�       �� t        |jB                  jE                  �       �      D ]�  }t        |jB                  jG                  |g �      �      D ]�  }d}|j*                  j                  �       D ]%  \  }
}t1        |d�      s�|jH                  |k(  s�#d} n |r�J|jJ                  jG                  |�      }	|	s�ht7        |	d�      }t         |	j8                  z
  |kD  s��|	j:                  }|xj<                  |z  c_        t?        d||�       |jM                  |�       �� �� ��� y)u�   
    在每个时间间隔结束时，更新优先级，将活动函数移动到缓存函数，并更新分层容器的保活状态
    容器超时策略，Bare层直接销毁，User/Lang层降级处理并设置新的超时时间
    FrP  Tr#   rQ  N)'rO   r�   r�  r�  r�  r(   r=  rQ   r#   r�  r$   r�  �listr�  r�  r�  rY   r
  r�  r  r  r�  r�  r�  r�  rP  r�  r   r�  r*   r�  r�  r  r�  r�   r�   rQ  r�  r
  )r  �node_functionsr�   r�  r�  r�  r
   �layer_cacher  r�  r  r  �
has_referencer�  rn  r�  rJ  rH  rI  s                      r   �update_keep_aliver  �  s  � � &7�%:�%:�%@�%@�%B�!�	�>�#1�#;�#;�#A�#A�#C��H�i��3�y���/�0���O�O�A�&�����q�!� 1� $D� &C� �O�O�������!� !,� 1� 1� 3����%)�+�*H�*H�*N�*N�*P�%Q�!�I�z��,�,�Z�8��#�#�G�,��0�0��;�
 &R� &*�+�*H�*H�*N�*N�*P�%Q�!�I�z��,�,�Z�8��#�#�G�,��0�0��;�
 &R� &*�+�*G�*G�*M�*M�*O�%P�!�I�z�!�M��*�+�+�+�"�)�)�'�2��!�.�.�D��t�%6�7�D�<P�<P�T^�Tc�Tc�<c�(,�
�� /�
 !�/�
�;�� �Z�6�6�6��<� *���I��2�2�i�?�2���w�	�:��7�7�	�B�+ &Q�0 �k�:�:�?�?�A�B�I�!�+�">�">�"B�"B�9�b�"Q�R�	� %�
�-8�-J�-J�-P�-P�-R�)�I�z��z�+<�=�*�B\�B\�`i�Bi�(,�
�� .S�
 %�!,�!>�!>�!B�!B�9�!M�J�!�7�
�D�I�� #�Z�%>�%>�>��D�(2���I�'�:�:�i�G�:�'��w�	�B�'�?�?�	�J�- S� C�Y !4r   c                  ��  � t         j                  j                  �       D ]n  \  } }|j                  j                  �       D ]L  \  }}t	        t        |j                  �      �      D ]&  }|j                  |   }t        j                  |�       �( �N �p t        j                  dk(  rt        j                  �        nt        j                  �        t         j                  j                  �        t        j                  dk(  r�t        t        j                  j!                  �       �      D ]�  } | t        j                  v s�t        j                  |    }d}|t        |j"                  �      k  s�D|j"                  |   j%                  �       rB|j"                  |   }|j"                  j'                  |�       t)        d| |j*                  �       n|dz
  }|t        |j"                  �      k  r�}�� yy)u�   
    在每个时间间隔结束时，更新全容器/User层优先级，将活动函数移动到缓存函数
    对于OpenWhisk策略，在此处检查并删除过期容器
    r{   r   r#   rE   N)rO   r�   r�  r�  r�  r(   r=  rQ   r#   r�   r=   r�  r�  r$   r  r�  r�   r�  r^  r   r�  r*   )r  r  r�   r�  r�  r�  r�  r�   s           r   �update_cacher!    s�  � � &7�%:�%:�%@�%@�%B�!�	�>�#1�#;�#;�#A�#A�#C��H�i��3�y���/�0���O�O�A�&�����q�!� 1� $D� &C� ����+��!�!�#� 	���� �����!� ����+��j�/�/�4�4�6�7�I��J�-�-�-�"�)�)�)�4�����#�e�0�0�1�1��)�)�!�,�6�6�8�#(�#5�#5�a�#8���*�*�.�.�q�1�#�E�9�h�m�m�D��Q��� �#�e�0�0�1�1�	 8� ,r   c                 �R  � t        | �      }t        j                  dk(  rt        | �       yt	        | �      r
t
        dz
  ayt
        || �      r
t        dz
  ayt        dz
  at        j                  dk(  rt        | �       yt        j                  dk(  rt        | �       yt        | �       y)u   
    部署单个请求
    r?   rE   Nr{   rz   )r�  r�   r=   �deploy_request_layered�deploy_to_currentr`   �deploy_to_neighbourra   r_   �create_to_current_openwhisk�create_to_current_crossedge�create_to_current_faascache�r   r�  s     r   �deploy_requestr*  B  s�   � � 
�w�	�B� ����,��w�'� �W�%� �A�%� �� �r�7�+�!�Q�&�!�� 	�A���� � �K�/�'��0�
�
"�
"�k�
1�'��0�'��0r   c            	      �v  � t        dt        j                  dz   �      D �]�  } t        j	                  | �      \  }}|st        d| � d��       �.dat        j                  d| � dt        |�      � d��       t        |d� �	�      }i }|D ],  }t        |d
d�      }||vrg ||<   ||   j                  |�       �. t        dk  r�t        |v r�|t           }t        j                  dt        � d
t        |�      � d��       |D ]^  }t        |�       t        dz
  a|j                  s�$|j                   j"                  |_        t'        ||j(                  j*                  �       �` t-        �        t/        �        t        dk  r��| t        j                  k  rt1        | | dz   �       t2        dz
  at5        �        ��� t7        �        y)ux   
    部署所有时间槽的请求，按arrival_second顺序处理，实现秒级循环和容器执行完成检查
    rE   �--------cannot find time slot �--------r   �   --------处理时间槽 �    的请求，共 �    个--------c                 �   � t        | dd�      S �Nr�   r   ��getattr��reqs    r   r�  z!deploy_requests.<locals>.<lambda>y  �   � �7�3�HX�Z[�;\r   r�  r�   rr  �     秒 �	   : 处理 �
    个请求N)r�  r�   r:   rT   r�   rZ  rp   r�   r�  r(   r	  r4  r   r*  r\   r�   r�   rC  r�   �schedule_container_executionr�   r�   �check_finished_containers�update_second_clock�"move_waiting_requests_to_next_slotrY   r!  r�  �r�  r�   r�  �sorted_requests�requests_by_secondr   �arrival_sec�current_second_requestss           r   �deploy_requestsrD  c  s�  � � �1�h�'�'�!�+�
,��'�+�+�A�.���%���2�1�#�X�>�?�� �����/��s�2C�C��M�?�R^�_�`� !��/\�]��  ��&�G�!�'�+;�Q�?�K��"4�4�24�"�;�/��{�+�2�2�7�;�	 '� �r�!��!3�3�*<�^�*L�'����v�n�%5�Y�s�CZ�?[�>\�\f�g�h�6�G�"�7�+�%��*�%� �~�~�4;�4D�4D�4R�4R��1�4�W�g�>P�>P�>S�>S�T�  7� 
&�'� 
 �!�- �r�!�2 
�x� � � �.�q�!�A�#�6� 	�1�����q -�t �r   c                 �   � t         j                  | j                  j                  | j                  j
                  �      \  }}|dk7  rt
        | ||�       yy)u�   
    尝试在当前节点部署请求
    如果节点上有匹配的容器，则使用该容器处理请求
    返回True表示成功部署，False表示节点无匹配容器
    r�  TF)rQ   r�  r�   r�   r�   ru   �place_to_current)r   r�  r�  s      r   r$  r$  �  sI   � � �'�'����(:�(:�G�<L�<L�<Q�<Q�R�D�A�q��B�w���!�Q�'��r   r�   r�  c                 �  � t         j                  | j                  j                  �       t         j	                  | j                  j                  t        | j                  �      �       t        j                  || j                  j                  �       t        j                  | j                  j                  |�       | j                  || j                  dd�       y)u7   
    将请求分配给当前节点的缓存容器
    Fr   N)r�  r&  r�   ru   r(  rA   r�   rO   r#   r�   r�   rQ   rl  r�   )r   r�   r�  s      r   rF  rF  �  s�   � � ���g�&�&�+�+�,�
����)�)�.�.��g�6H�6H�0I�J����(�G�O�O�$6�$6�7����g�o�o�(�(�!�,��N�N�8�W�_�_�e�Q�7r   r�  �	distancesc                 �  � | j                   dd D ]�  }|j                  t        j                  z  }t	        |j
                  j                  �      }||kD  r	t        dz
  a||k  s�St        dz
  at        j                  |j                  |j
                  j                  �      \  }}|dk7  s��t        ||||j                  |�        y y)u7   
    尝试将请求分配给邻近的缓存函数
    rE   Nr�  TF)
r=  r5  r�   r5   r�  r�   ru   rQ  rR  rQ   r�  r  �place_to_neighbour)rH  r   r=  �
link_delayr�  r�  r�  s          r   r%  r%  �  s�   � �
 �����$���^�^�h�&;�&;�;�
�-�g�.>�.>�.C�.C�D����'�
��F�A���'�
��F�A��/�/�����AQ�AQ�AV�AV�W�D�A�q��B�w�"�7�A�q�%�/�/�:�N�� %� r   rK  c                 �r  � t         j                  |j                  �       t         j                  |j                  t	        | j
                  �      �       t        j                  ||�       t        j                  ||�       t         j                  |�      }|j                  dk(  ry| j                  ||d|�       y)u1   
    将请求分配给邻近的缓存函数
    r   NF)
r�  r&  ru   r(  rA   r�   rO   r#   rQ   rl  r�   r�   r�   )r   r�   r�  r  rK  �deploy_nodes         r   rJ  rJ  �  s�   � � ���h�m�m�$�
�������g�.@�.@�(A�B����(�I�.����i��#��*�*�Y�'�K��~�~�����N�N�8�[�%��<r   c                  �   � t         dz
  a y)u=   
    更新秒级时钟，用于跟踪容器执行进度
    rE   N)rp   r1   r   r   r=  r=  �  s   � �
 �a��Nr   r
   c                 �   � |t         vr	i t         |<   t        | _        t        | j                  z   }|| _        |t         |   | j
                  <   y)u�   
    安排容器执行，计算执行结束时间
    
    参数:
      - request: 请求对象
      - node_id: 节点ID
      
    返回值:
      - 无，但会更新node_execution_end_times_G字典
    N)rq   rp   r�   r�   r�   r�   )r   r
   �end_times      r   r;  r;  �  sR   � � �0�0�.0�"�7�+� +�G�� �� 9� 9�9�H�"�G�� 7?��w�'��
�
�3r   c                  ��   � d} t        t        j                  �       �      D ]U  }t        t        |   j                  �       �      D ]0  }t        |   |   t        k  s�t	        |�       t        |   |= | dz
  } �2 �W | S )u�   
    检查已完成执行的容器，处理等待队列中的请求
    
    返回值:
      - 已完成执行的容器数量
    r   rE   )r  rq   r�   rp   �process_finished_container)�finished_countr
   �
request_ids      r   r<  r<    s|   � � �N� �2�7�7�9�:���9�'�B�G�G�I�J�J�)�'�2�:�>�.�P�*�7�3�.�w�7�
�C��!�#�� K� ;� �r   c                 ��  � | t         vryt         |    }|j                  �       ryd}|j                  �       �s�|j                  �       }t        j                  dk(  r�t        |�      r3t        |j                  z
  |_        t        || �       t        dz
  a
|dz
  }�rt        |�       |j                  r3t        |j                  z
  |_        t        || �       t        dz
  a
|dz
  }��|j                  |�       	 |S t        |�      r4t        |j                  z
  |_        t        || �       t        dz
  a
|dz
  }��t        j                  dk(  rt        |�       n=t        j                  dk(  rt!        |�       nt        j                  dk(  rt#        |�       |j                  r4t        |j                  z
  |_        t        || �       t        dz
  a
|dz
  }���|j                  |�       	 |S |S )u�   
    处理节点上已完成容器的等待队列
    
    参数:
      - node_id: 节点ID
      
    返回值:
      - 处理的请求数量
    r   r?   rE   ry   rz   r{   )rn   r   r   r�   r=   �deploy_to_current_layeredrp   r�   r�   r;  r]   �create_to_current_layeredr�   r   r$  r(  r'  r&  )r
   r
   �processed_countr   s       r   rR  rR  !  s�  � � �&�&�� 
�W�%�E� 
�~�~����O� �n�n���(�(�*�� � � �L�0�(��1�#1�G�4F�4F�#F�� �,�W�g�>�"�a�'�"��1�$��� *�'�2��>�>�'5��8J�8J�'J�G�$�0��'�B�&�!�+�&�#�q�(�O�� �%�%�g�.��B ��= !��)�#1�G�4F�4F�#F�� �,�W�g�>�"�a�'�"��1�$��� �(�(�K�7�/��8��*�*�k�9�/��8��*�*�k�9�/��8� �>�>�'5��8J�8J�'J�G�$�0��'�B�&�!�+�&�#�q�(�O�� �%�%�g�.����?�r   �current_slotr   c                 �   � d}t         j                  �       D ]A  \  }}|j                  �       r�|j                  �       }||z
  }|j	                  |t
        �       �C |S )u�   
    将当前时间槽中未处理的请求移至下一时间槽
    
    参数:
      - current_slot: 当前时间槽
      - next_slot: 下一时间槽
      
    返回值:
      - 移动的请求数量
    r   )rn   r�  r   r*   r%   rT   )rY  r   �moved_countr
   r
   �
queue_sizes         r   r>  r>  {  s[   � � �K� +�0�0�2�����~�~������J��:�%�K� 
�#�#�I�}�=� 3� �r   c                 �  � | j                   j                  �        t        �       }d}| j                   j                  t        j
                  | j                  j                     j                  kD  �r�|�r�t        j                  | j                  j                  �      }| j                   j                  |kD  �r�t        j                  | j                  j                  �      \  }}|r,t        d| j                  j                  |j                  �       nm| j                  j                  }|t        vrt        |�      t        |<   | j                   j                   | _        t        |   j%                  | �       t&        dz
  ay | j                   j                  t        j
                  | j                  j                     j                  k  �r@| j                  | j                   _        t*        j-                  | j                   | j                  j                  �       t        d| j                  j                  | j                   j                  �       | j/                  | j                   | j                  dd�       t0        | j                   j                  z
  at2        dz
  ay | j                  j                  }|t        vrt        |�      t        |<   | j                   j                   | _        t        |   j%                  | �       t&        dz
  ay |r���y y t0        | j                   j                  z
  at2        dz
  a| j                  | j                   _        t*        j-                  | j                   | j                  j                  �       t        d| j                  j                  | j                   j                  �       | j/                  | j                   | j                  dd�       y )NTr#   rE   r  r   )r�   rT  r�   r*   r�  r  r�   r�   r�   rQ   r�  rB  r�  r�  rn   r	   rC  r�   r   ro   rD  rO   r#   r�   re   rl   )r   r�  �	succ_flag�lowest_priorityr
   s        r   r(  r(  �  s�  � � ���$�$�&��
�A��I������v�|�|�G�O�O�,>�,>�?�C�C�C��(�<�<�W�_�_�=O�=O�P�O����(�(�?�:�)�=�=�g�o�o�>P�>P�Q���9����w���'9�'9�1�6�6�B� &�o�o�0�0�G��&6�6�4@��4I�(��1� 18�0@�0@�0N�0N�G�-� %�W�-�9�9�'�B� �A�%�L���#�#�(�(�F�L�L����9K�9K�,L�,P�,P�P�/6���G�$�$�,�%�)�)�'�*:�*:�G�O�O�<N�<N�O������);�);�W�=M�=M�=R�=R�S��N�N�7�#3�#3�W�_�_�d�A�N� '�'�*:�*:�*?�*?�?�&�+�q�0�+�� "�/�/�,�,���"2�2�0<�W�0E�$�W�-� -4�,<�,<�,J�,J��)� !��)�5�5�g�>���!���Y �` 	�g�.�.�3�3�3���1�$��#*�?�?���� ����g�.�.����0B�0B�C��G�W�_�_�/�/��1A�1A�1F�1F�G����w�'�'����$��Br   c                 �@	  � d}d}| j                   j                  t        j                  | j                  j
                     j                  kD  �r|dk  �r�|dz
  }t        | j                  j
                  | j                   j                  �      }|| j                   j                  k(  rm| j                  j
                  }|t        vrt        |�      t        |<   | j                   j                  | _        t        |   j                  | �       t        dz
  ay t        j!                  | j                  j
                  |�      }t#        |�      }|r!t%        d| j                  j
                  |�       | j                   j                  t        j'                  | j                  j
                  �      j                  k  �r9t(        | j                   j                  z
  at*        dz
  a| j                  | j                   _        t.        j1                  | j                   | j                  j
                  �       t        j3                  | j                   j                  �       t        j5                  | j                   j                  t7        | j8                  �      �       t%        d| j                  j
                  | j                   j                  �       | j;                  | j                   | j                  dd�       y |s���|dk  r���| j                  j
                  }|t        vrt        |�      t        |<   | j                   j                  | _        t        |   j                  | �       t        dz
  ay t(        | j                   j                  z
  at*        dz
  a| j                  | j                   _        t.        j1                  | j                   | j                  j
                  �       t        j3                  | j                   j                  �       t        j5                  | j                   j                  t7        | j8                  �      �       t%        d| j                  j
                  | j                   j                  �       | j;                  | j                   | j                  dd�       y )NTr   ��  rE   r#   r  )r�   r*   r�  r  r�   r�   r�   �get_evicted_containerru   rn   r	   rC  r�   r   ro   rQ   r�  r�  r�  r�   re   rl   rD  rO   r#   r&  r(  rA   r�   r�   )r   r^  �countrd  r
   �
function_sizes         r   r'  r'  �  sv  � � �I�
�E������v�|�|�G�O�O�,>�,>�?�C�C�C��d�l��Q�J�E�-�g�o�o�.@�.@�'�BR�BR�BW�BW�X�I� �G�,�,�1�1�1�!�/�/�,�,���"2�2�0<�W�0E�$�W�-� -4�,<�,<�,J�,J��)� !��)�5�5�g�>���!���"�.�.�w���/A�/A�9�M�I�.�y�9�M� ��E�7�?�?�#5�#5�}�E� ���$�$��
�
�7�?�?�3E�3E�(F�(J�(J�J�"�g�&6�&6�&;�&;�;�"�'�1�,�'�+2�?�?�� � �(�!�%�%�g�&6�&6����8J�8J�K��!�!�'�"2�"2�"7�"7�8��$�$�W�%5�%5�%:�%:�E�'�BT�BT�<U�V��G�W�_�_�%7�%7��9I�9I�9N�9N�O����w�/�/����$��J�� ��W �d�l�\ �/�/�$�$���*�*�(4�W�(=��W�%� %,�$4�$4�$B�$B��!� 	��!�-�-�g�6������ 	�g�.�.�3�3�3���1�$��#*�?�?���� ����g�.�.����0B�0B�C����'�*�*�/�/�0����W�-�-�2�2�E�'�:L�:L�4M�N��G�W�_�_�/�/��1A�1A�1F�1F�G����w�'�'����$��B�r   r�  c                 �z  � t         j                  | �      }t        | |�      }|j                  |�      }|j	                  |�      }t        |�      }|dk(  rt
        j                  d|� d| � ��       y|dk(  rt
        j                  d|� d| � ��       y|dk(  rt
        j                  d|� d| � ��       y|||z   z  dz  S )Nr   zcannot find freq for funcType z	 at node z cannot find recent for funcType zcannot find size for funcType ra  )r�  r�   r�  r   r  r�  r�   r�  )r�  r�   r�  r�   r  r�   r*   s          r   �get_probrf  +	  s�   � ��z�z�&�!�H�!�&�(�3�K����H�%�D��!�!�(�+�G��h�'�D��q�y��
�
�6�x�j�	�&��R�S���!�|��
�
�8��
�)�F�8�T�U���q�y��
�
�6�x�j�	�&��R�S�� �4�'�>�"�T�)�)r   �reqFuncTypec                 �N  � t        | |�      }i }d}t        j                  j                  �       D ]  }t        | |�      }|||<   ||z
  }� ||z  }t	        �       }|j                  �       D ]'  \  }}||z  }t
        ||�      }	|j                  |	�       �) |j                  �        t        j                  j                  dd�      }
d}|j                  D ]R  }	||	j                  z
  }t        |
�      |dz  k  s�$t        j                   d|	j"                  � ��       |	j"                  c S  y)Nr   rG   zevict type )rf  rV   rv  r�   r>  r�  r:  rF  rD  �np�random�randintr@  r;  rA   r�   r�  rd  )r�  rg  �	threshold�prob_map�
total_probr�   �prob�
prob_pair_vec�normalized_prob�	prob_pair�val�
accum_probs               r   rb  rb  B	  s,  � ����-�I� �H��J� "�*�*�/�/�1�����)��!�����d��
� 2� �J�&�I�  �M�M�"�.�.�*���$���+���X��7�	����	�*� +� ���� 
�)�)�
�
�A�s�
#�C� �J�"�.�.�	��i�+�+�+�
���:��c�)�*��L�L�;�y�':�':�&;�<�=��&�&�&�	 /� 
r   c                 ��  � d}d}| j                   j                  t        j                  | j                  j
                  �      j                  kD  �r%|dk  �r�|dz
  }t        j                  | j                  j
                  �      \  }}|r+t        d| j                  j
                  |j                  �       |j                  | j                   j                  k(  rm| j                  j
                  }|t        vrt        |�      t        |<   | j                   j                  | _        t        |   j                  | �       t         dz
  ay| j                   j                  t        j                  | j                  j
                  �      j                  k  �rt"        | j                   j                  z
  at$        dz
  a| j                  | j                   _        t(        | j                   _        | j                   j-                  d�       t.        j1                  | j                   | j                  j
                  �       t        d| j                  j
                  | j                   j                  �       | j3                  | j                   | j                  dd�       y|sm| j                  j
                  }|t        vrt        |�      t        |<   | j                   j                  | _        t        |   j                  | �       t         dz
  ay|dk  r���| j                  j
                  }|t        vrt        |�      t        |<   | j                   j                  | _        t        |   j                  | �       t         dz
  ayt"        | j                   j                  z
  at$        dz
  a| j                  | j                   _        t(        | j                   _        | j                   j-                  d�       t.        j1                  | j                   | j                  j
                  �       t        d| j                  j
                  | j                   j                  �       | j3                  | j                   | j                  dd�       y)	u5  
    使用固定缓存（OpenWhisk）策略在当前节点创建新容器
    容器将保持固定时间（lifeTime）的活动状态
    1. 如果内存不足，尝试删除生命周期最低的容器，直到有足够空间
    2. 如果仍然无法腾出足够空间，将请求添加到等待队列
    Tr   ra  rE   r#   Nra  r  )r�   r*   r�  r�   r�   r�   r�   rQ   r�  r�  ru   rn   r	   rC  r�   r   ro   re   rl   rD  rY   rE  r`  rO   r#   r�   )r   r^  rc  r�   r
   s        r   r&  r&  l	  sk  � � �I�
�E������v�z�z�'�/�/�*<�*<�=�A�A�A��d�l��Q�J�E�",�"I�"I�'�/�/�J\�J\�"]��H�i���E�7�?�?�#5�#5�x�}�}�E��}�}�� 0� 0� 5� 5�5�!�/�/�,�,���"2�2�0<�W�0E�$�W�-� -4�,<�,<�,J�,J��)� !��)�5�5�g�>���!������$�$��
�
�7�?�?�3E�3E�(F�(J�(J�J� #�g�&6�&6�&;�&;�;�"�'�1�,�'�+2�?�?�� � �(�18�� � �.�� � �/�/��3�!�%�%�g�&6�&6����8J�8J�K��G�W�_�_�%7�%7��9I�9I�9N�9N�O����w�/�/����$��J���!�/�/�,�,���"2�2�0<�W�0E�$�W�-� -4�,<�,<�,J�,J��)� !��)�5�5�g�>���!���a �d�l�f �/�/�$�$���*�*�(4�W�(=��W�%� %,�$4�$4�$B�$B��!� 	��!�-�-�g�6������ 	�g�.�.�3�3�3���1�$��#*�?�?���� �)0����&����'�'��+����g�.�.����0B�0B�C��G�W�_�_�/�/��1A�1A�1F�1F�G����w�'�'����$��Br   c                 �    � | j                   dk  S )ub   
    根据容器的lifeTime判断是否过期
    lifeTime小于等于0表示容器已过期
    r   r]  )r�   s    r   �is_container_expiredrw  �	  s   � �
 ����!�!r   c            	      �v  � t        dt        j                  dz   �      D �]�  } t        j	                  | �      \  }}|st        d| � d��       �.dat        j                  d| � dt        |�      � d��       t        |d� �	�      }i }|D ],  }t        |d
d�      }||vrg ||<   ||   j                  |�       �. t        dk  r�t        |v r�|t           }t        j                  dt        � d
t        |�      � d��       |D ]^  }t        |�       t        dz
  a|j                  s�$|j                   j"                  |_        t'        ||j(                  j*                  �       �` t-        �        t/        �        t        dk  r��| t        j                  k  rt1        | | dz   �       t2        dz
  at5        �        ��� t7        �        y)u�   
    使用RainbowCake的分层容器缓存和共享机制部署请求，按arrival_second顺序处理，实现秒级循环和容器执行完成检查
    rE   r,  r-  r   r.  r/  r0  c                 �   � t        | dd�      S r2  r3  r5  s    r   r�  z)deploy_requests_layered.<locals>.<lambda>�	  r7  r   r�  r�   rr  r8  r9  r:  N)r�  r�   r:   rT   r�   rZ  rp   r�   r�  r(   r	  r4  r   r#  r\   r�   r�   rC  r�   r;  r�   r�   r<  r=  r>  rY   r  r�  r?  s           r   �deploy_requests_layeredrz  �	  s�  � � �1�h�'�'�!�+�
,��'�+�+�A�.���%���2�1�#�X�>�?�� �����/��s�2C�C��M�?�R^�_�`� !��/\�]��  ��&�G�!�'�+;�Q�?�K��"4�4�24�"�;�/��{�+�2�2�7�;�	 '� �r�!��!3�3�*<�^�*L�'����v�n�%5�Y�s�CZ�?[�>\�\f�g�h�6�G�*�7�3�%��*�%� �~�~�4;�4D�4D�4R�4R��1�4�W�g�>P�>P�>S�>S�T�  7� 
&�'� 
 �!�- �r�!�2 
�x� � � �.�q�!�A�#�6� 	�1�����u -�x �r   c                 �   � t        | �      }t        | �      r
t        dz
  ayt        || �      r
t        dz
  ayt
        dz
  at
        | �       y)uH   
    使用RainbowCake的分层容器缓存机制部署单个请求
    rE   N)r�  rV  r`   �deploy_to_neighbour_layeredra   r_   rW  r)  s     r   r#  r#  
  sU   � � 
�w�	�B� !��)���!��� #�2�w�/���"��� �����g�&r   c                 �  � | j                   j                  }|t        vr+t        |�      t        |<   t	        j
                  d|� d��       t        |   }| j                  }t        j                  || j                  j                  �      \  }}|dk7  rDt        | ||�       |j                  |j                  �       |j                  |j                  �       yy)u=   
    尝试在当前节点使用分层容器部署请求
    �
   为节点 �    创建新的分层缓存r�  TF)r�   r�   r�  r�  r�   r�  r�   rQ   r�  ru   rF  r!  rQ  r$  rP  )r   r
   r  r�   r�  r�  s         r   rV  rV  (
  s�   � � �o�o� � �G� �k�!�*�7�3��G�����z�'��*C�D�E���(�M����H� �'�'���1A�1A�1F�1F�G�D�A�q��B�w� 	��!�Q�'��/�/��0A�0A�B��/�/��0A�0A�B��r   c                 �8  � | j                   dd D �]  }|j                  �       }|j                  t        j                  z  }t        |j                  j                  �      }||kD  r	t        dz
  a||k  s�dt        dz
  a	|t        vrt        |�      t        |<   t        |   }t        j                  ||j                  j                  �      \  }}|dk7  s��t        |||||�       |j                  |j                   �       |j#                  |j$                  �        y y)u=   
    尝试在邻近节点使用分层容器部署请求
    rE   Nr�  TF)r=  r9  r5  r�   r5   r�  r�   ru   rQ  rR  r�  r�  rQ   r�  rJ  r!  rQ  r$  rP  )	rH  r   r=  r
   rK  r�  r  r�  r�  s	            r   r|  r|  G
  s�   � �
 �����$���,�,�.���^�^�h�&;�&;�;�
�-�g�.>�.>�.C�.C�D����'�
��F�A���'�
��F�A��k�)�'2�7�';��G�$�'��0�M� �/�/���9I�9I�9N�9N�O�D�A�q��B�w�"�7�A�q�'�:�F��7�7��8I�8I�J��7�7��8I�8I�J��1 %�4 r   c                 �
  � | j                   j                  }|t        vr+t        |�      t        |<   t	        j
                  d|� d��       t        |   }| j                  }t        | |�      }t        j                  |   j                  }t        d|j                  |�      }t        d|j                  |�      }||kD  r$t        | �      rt	        j
                  d|� d��       t        d|j                  |�      }t        d|j                  |�      }d}d}	d}
|j                  }|s�t        |j                   |j                  t"        �      }|j%                  |�       |xj&                  |j(                  z
  c_        |j*                  }t-        d||j(                  �       |j                   }	t.        |	z
  at0        d	z
  ans|j                  |j2                  v r[|j2                  |j                     rB|j2                  |j                     d
   }
|j5                  |
�      }|j7                  t"        �       |s�t9        |j:                  |j                  j<                  t"        �      }|j?                  |�       |xj&                  |j(                  z
  c_        ||j@                  z
  }t-        d||j(                  �       |j:                  }
tB        |
z
  a!tD        d	z
  a"�n|j                  |jF                  v r\|jF                  |j                     rC|jF                  |j                     d
   }|jI                  |�      }|j7                  t"        �       n�t9        |j:                  |j                  j<                  t"        �      }|j?                  |�       |xj&                  |j(                  z
  c_        ||j@                  z
  }t-        d||j(                  �       |j:                  }
tB        |
z
  a!tD        d	z
  a"||jJ                  z
  }tL        |j                  z
  a&tN        d	z
  a't.        tB        z   tL        z   a(tS        ||	|
|�      }|| _*        tW        |jX                  |jZ                  ||j(                  | j                   |j                  |j                   |j:                  |j                  |j*                  |j@                  |jJ                  ��      }|j<                  |_.        |j^                  |_/        | j                   | j                  _0        tb        je                  ||�       | jg                  || j                   dd
�       t-        d||j                  �       y
)u�   
    在当前节点创建新容器，使用RainbowCake的分层容器缓存机制
    # 1. 计算所需内存
    # 2. 检查各层是否存在
    # 3. 如果内存不足，调用evict_user_layer
    # 4. 创建和部署容器
    r~  r  r�  r�  r�  z evict successrM   r  rE   r   r  TN)4r�   r�   r�  r�  r�   r�  r�   �calculate_required_memoryr�  r  r�   r
  rH  �evict_user_layerrK  r�  rI  rY   r�  r�  r*   rL  r�  rf   ri   r�  r!  r�  r�  rJ  r�  r  rM  rg   rj   r�  r$  rN  rh   rk   re   r�  r�   r�   ru   rC  rP  rQ  rD  rO   r#   r�   )r   r
   r  r�   r  �current_memoryr  r  �coldrI  rJ  rK  r�  r  r  r  r�   r�  s                     r   rW  rW  h
  s�  � � �o�o� � �G� �k�!�*�7�3��G�����z�'��*C�D�E���(�M� ���H�/���G�O��\�\�'�*�.�.�N� %�V�X�-?�-?��O�K�$�V�X�-?�-?��O�K� ��'��G�$��L�L�5��	��8�9� %�V�X�-?�-?��O�K�$�V�X�-?�-?��O�K��D��I��I��"�"�I� ��x�1�1�8�3E�3E�w�O�
��+�+�J�7��(�(�J�O�O�;�(��"�"���G�W�j�o�o�6��&�&�	��Y�&���a��� ����!?�!?�?�M�Db�Db�ck�cu�cu�Dv�%�6�6�x�7I�7I�J�1�M�I�&�@�@��K�J��#�#�G�,� ���������O�O��	�
�
 	�+�+�J�7��(�(�J�O�O�;�(���#�#�#���G�W�j�o�o�6��&�&�	��Y�&���a��� ����!?�!?�?�M�Db�Db�ck�cu�cu�Dv�%�6�6�x�7I�7I�J�1�M�I�&�@�@��K�J��#�#�G�,� #��"�"��"�"�����	�J�
 
�/�/�
�;��,�,�
���?�,��H�'�'�'�D����*�/�/�:� �*�*�I� 
��*���!�#�� 	�H����D���+�+�+���!��� +�->�>�AR�R�� *�'�9�i��S�K� &�G�� 	�
�]�]��,�,��
�]�]�����$�$��$�$��$�$��$�$��&�&��&�&��&�&�
	�A�  #���A��"�2�2�A��  '���G�������!�W�%��N�N�8�W�_�_�d�A�6����(�"4�"4�5r   c                 �  � | j                   j                  }| j                  j                  �        |t        vrt        |�      t        |<   t        |   }t
        | |�      }t        j                  |   j                  }d}||kD  �r;t        j                  |�      }|| j                  j                  k  r�t        j                  |�      \  }}|r>t        d||j                  �       t        j                  |   j                  }||k  r�d}	 |S |t         vrt#        |�      t         |<   | j                  j$                  | _        t         |   j)                  | �       t*        dz
  ay|t         vrt#        |�      t         |<   | j                  j$                  | _        t         |   j)                  | �       t*        dz
  ay||kD  r��;|S )u  
    当内存不足时，尝试驱逐低优先级容器来为新请求腾出空间
    如果无法驱逐，将请求添加到等待队列
    
    参数:
      - request: 请求对象
      
    返回值:
      - 如果成功腾出足够空间则返回True，否则返回False
    Fr#   TrE   )r�   r�   r�   rT  r�  r�  r�  r�  r  r�   rQ   r�  rB  r�  r�  r*   rn   r	   rC  r�   r   ro   )	r   r
   r  r  r�  �successr_  r�  r^  s	            r   r�  r�  �
  s�  � � �o�o� � �G����$�$�&� �k�!�*�7�3��G����(�M� 0���G�O��\�\�'�*�.�.�N��G� �N�
*�$�8�8��A�� �W�-�-�6�6�6�%�9�9�'�B�L�A�y���E�7�A�F�F�3�!'���g�!6�!:�!:��"�n�4�"�G��4 �N�/ �"2�2�0<�W�0E�$�W�-� -4�,<�,<�,J�,J��)� !��)�5�5�g�>���!��� �.�.�,8��,A� ��)� )0�(8�(8�(F�(F�G�%� 
�W�%�1�1�'�:��A��L��M �N�
*�P �Nr   c                 ��  � | j                   j                  }| j                  j                  �        |t        vrt        |�      t        |<   t        |   }t
        | |�      }t        j                  |   j                  }d}g }||kD  �rJt        j                  |�      }|| j                  j                  k  r�t        j                  |�      \  }}	|	rM|j                  |�       t        d||j                   �       t        j                  |   j                  }||k  r�d}n�|t"        vrt%        |�      t"        |<   | j                  j&                  | _        t"        |   j+                  | �       t,        dz
  ay|t"        vrt%        |�      t"        |<   | j                  j&                  | _        t"        |   j+                  | �       t,        dz
  ay||kD  r��J|�r
|�r
|D �]  }t/        |d�      rs|j0                  rg|j0                  |j2                  v r|j5                  |j0                  �       n3|j0                  |j6                  v r|j9                  |j0                  �       t/        |d�      s��|j:                  s��|j:                  |j<                  v r|j?                  |j:                  �       ��|j:                  |j@                  v s��|jC                  |j:                  �       �� |S )u1  
    当内存不足时，尝试驱逐完整容器（包括Lang和Bare层）来为新请求腾出空间
    如果无法驱逐，将请求添加到等待队列
    
    参数:
      - request: 请求对象
      
    返回值:
      - 如果成功腾出足够空间则返回True，否则返回False
    Fr#   TrE   rP  rQ  )"r�   r�   r�   rT  r�  r�  r�  r�  r  r�   rQ   r�  rB  r�  r   r�  r*   rn   r	   rC  r�   r   ro   r�  rP  �lang_active_layersr  r�  r  rQ  �bare_active_layersr
  r�  r
  )
r   r
   r  r  r�  r�  �evicted_functionsr_  r�  r^  s
             r   �evict_full_containerr�  @  s�  � � �o�o� � �G����$�$�&� �k�!�*�7�3��G����(�M� 0���G�O��\�\�'�*�.�.�N��G��� �N�
*�$�8�8��A�� �W�-�-�6�6�6�%�9�9�'�B�L�A�y��!�(�(��+��E�7�A�F�F�3�!'���g�!6�!:�!:�� #�n�4�"�G�� �"2�2�0<�W�0E�$�W�-� -4�,<�,<�,J�,J��)� !��)�5�5�g�>���!��� �.�.�,8��,A� ��)� )0�(8�(8�(F�(F�G�%� 
�W�%�1�1�'�:��A��L��Q �N�
*�V �$�"�A��q�+�,��1B�1B��$�$�
�(H�(H�H�!�:�:�1�;L�;L�M��&�&�-�*I�*I�I�!�9�9�!�:K�:K�L��q�+�,��1B�1B��$�$�
�(H�(H�H�!�:�:�1�;L�;L�M��&�&�-�*I�*I�I�!�9�9�!�:K�:K�L� #�  �Nr   r  c                 ��   � | j                   }d}t        d|j                  |�      }|s||j                  z
  }t        d|j                  |�      }|s||j                  z
  }||j
                  z
  }|S )u�   
    计算在分层缓存中部署请求所需的内存
    考虑已经存在的层（Bare和Lang）
    
    参数:
      - request: 请求对象
      - layered_cache: 节点的分层缓存
      
    返回值:
      - 所需内存总量
    rM   r�  r�  )r�   r
  rH  rI  rJ  rK  )r   r  r�   r  r  r  s         r   r�  r�  �  s~   � � ���H��O� %�V�X�-?�-?��O�K���8�-�-�-�� %�V�X�-?�-?��O�K���8�-�-�-�� �x�)�)�)�O��r   r�  �layer_idc                 ��   � | dk(  r+||j                   v xr t        |j                   |   �      dkD  S | dk(  r+||j                  v xr t        |j                  |   �      dkD  S y)uL  
    检查指定类型和ID的层是否存在(活动或缓存)
    
    参数:
      - layer_type: 层类型("bare"或"lang")
      - layer_id: 层ID(bare层为语言类型，lang层为语言类型)
      - layered_cache: 节点的分层缓存
      
    返回值:
      - 如果层缓存存在返回True，否则返回False
    r�  r   r�  F)r�  r(   r�  )r�  r�  r  s      r   r
  r
  �  s{   � � �V���M�:�:�:� B��M�2�2�8�<�=��A�	C�	�v�	��M�:�:�:� B��M�2�2�8�<�=��A�	C�r   c            	      �Z	  � t        d�       t        j                  d�       t        dt        �       t        dt        �       t        dt
        �       t        dt        �       t        d�       d} d}t        d	t        j                  �       d	z   �      D �]~  }|t        j                  v s�t        j                  |   j                  }|D �]E  }|j                  s�t        d	z
  a
t        |j                  j                   |j"                  j$                  �      }||z
  }|j&                  }d}|j(                  r�t*        d	z
  at,        j.                  d
k(  rt1        |d�      r
|j2                  }n4t5        |j                  j                   |j"                  j$                  �      }| |z
  } ||j"                  j6                  z
  }t1        |d�      r1|j8                  dkD  r"||j8                  z
  }t:        |j8                  z
  at<        |z
  at>        ||z   z
  a��H ��� t        t@        z  }t>        t        z  }	| t        z  }
|t        z  }t<        t        z  }t:        dkD  rt:        t        z  }
nd}
t@        t        z
  }t
        t@        z  }t        d
|d���       t        dt@        � ��       t        dt        � ��       t        dt*        � ��       t        d|� ��       t        d|d���       t        d�       t        d|
d���       t        d|d���       t        dt>        d���       t        d|	d���       t        d�       t        dt<        d�d��       t        d|d�d��       t        dt:        d�d��       t        d|
d�d��       t        d�       t,        j.                  d
k(  r@tB        tD        tF        z   tH        z   z  }t        dtB        d�d��       t        d |d�d��       n1tB        tJ        z  }t        d!tB        d�d��       t        d |d�d��       t        d�       t,        tL        d"   k(  rBtO        d#t,        j.                  � d$t,        jP                  d�d$t,        jR                  d%�d&��       y tO        d't,        j.                  � d$t,        jP                  d�d$t,        jR                  d%�d&��       y )(Nz--------Schedule End--------z-------- Schedule End --------u   部署在当前节点的请求:u   部署在邻居节点的请求:u%   当前节点创建新容器的请求:u   存在过等待时间的请求:z------------------------r   rE   r?   r�   r�   u   服务成功率: �.2fu   实际请求总数: u   服务请求总数: u   冷启动请求(已服务): u   冷启动请求(未服务): u   冷启动频率: u   平均实例化成本: u   平均运行成本: u   总成本: u   平均总成本: u   总响应时间: rS  u   平均响应时间: u   总等待时间: u   平均等待时间: u   新创建总内存: �.0f�MBu   平均加载内存: u   新创建全容器: rJ   z
./result/EUA/�-�.3f�.csvz./result/Telecom/)*rZ  r�   r�  r`   ra   r_   ro   r�  rT   r*   r�   r�   r�   r]   r�  r�   r�   r�   ru   r�   r�   r^   r�   r=   r�  r�   r�  rA  r�   rm   rb   rc   r\   re   rk   rj   ri   rl   r�   �
result_to_csvr9   r8   )�total_instan_cost�total_run_costr�  r�   r   r�  �
response_timer�   �success_rate�avg_cost�avg_instan_cost�avg_run_cost�avg_response_time�
avg_wait_time�unserved_req_count�cold_start_frequency�avg_total_memorys                    r   �print_resultr�  �  s�  � �	�
(�)��L�L�1�2� 
�
+�-A�B�	�
+�-B�C�	�
1�3C�D�	�
+�\�:�	�
$�%����N�
�1�m�(�(�*�Q�.�
/���
�!�!�!�$�(�(��+�4�4�H�#���~�~��"�a�'�"� (��(:�(:�(=�(=�w�?O�?O�?T�?T�U���(�*�� !(� 1� 1�
�  ���&�&�$��)�$��,�,��<���R_�A`�&-�&9�&9�� '6�g�6H�6H�6K�6K�W�M]�M]�Mb�Mb�&c��%��4�%�!�W�%5�%5�%C�%C�C�M� �7�J�/�G�4D�4D�q�4H�!�W�%5�%5�5�M�%��)9�)9�9�%� &��6�%� ��;� 6�6��I $� 0�T &�(9�9�L� �0�0�H�'�*<�<�O�!�$6�6�L� .�0B�B�� �1��)�L�8�
��
� +�-?�?��+�.?�?��	��l�3�/�
0�1�	� �!2� 3�
4�5�	� �!3� 4�
5�6�	�(�)9�(:�
;�<�	�(�);�(<�
=�>�	��2�3�7�
8�9�	�
$�%�	�#�O�C�#8�
9�:�	� ��c� 2�
3�4�	�K��S�)�
*�+�	��h�s�^�
,�-�	�
$�%�	��3�C�8��
:�;�	� �!2�3� 7�q�
9�:�	��/��4�A�
6�7�	� ��s� 3�1�
5�6�	�
$�%� ����,�-�1C�FX�1X�[m�1m�n��
�$�%7��$<�B�?�@�
�$�%5�c�$:�"�=�>�-�0G�G��
�$�%7��$<�B�?�@�
�$�%5�c�$:�"�=�>�	�
$�%� �<��&�&��
�h�&;�&;�%<�A�h�m�m�C�=P�PQ�RZ�R`�R`�ad�Qe�ei�j�k��)�(�*?�*?�)@��(�-�-�PS�AT�TU�V^�Vd�Vd�eh�Ui�im�n�or   �output_filec                 �  � t         j                  j                  | �      }|rNt         j                  j                  |�      s/t        j                  |d��       t        j                  d|� ��       t        | dd��      5 }t        j                  |�      }t        t        z  }t        t        z  }t        t        z  }t        dkD  rt        t         z  }nd}t"        j$                  dk(  rt&        t(        t*        z   t,        z   z  }n
t&        t.        z  }|j1                  g d	��       |j1                  t        t        t        t         |d
�|d
�|d
�|d
�|d�t&        d�t2        d�t4        d�t6        d�g
�       |j1                  g d
��       t9        dt:        j=                  �       dz   �      D �]�  }	|	t:        j>                  v s�t:        j>                  |	   j@                  }
|
D �]�  }|jB                  s�tE        |jF                  jH                  |jJ                  jL                  �      }d}
|jN                  }d}|jP                  ryt"        j$                  dk(  rtS        |d�      r
|jT                  }
n4tW        |jF                  jH                  |jJ                  jL                  �      }
||jJ                  jX                  z
  }tS        |d�      r |jZ                  dkD  r|jZ                  }||z
  }||
z   }|j1                  |j\                  |jH                  |jJ                  jL                  |j^                  jH                  |jF                  jH                  |jP                  |d�|
d�|d�|d�|d�g�       ��� ��� 	 ddd�       y# 1 sw Y   yxY w)u�   
    将结果写入CSV文件，包括性能指标和内存使用情况
    
    参数:
      - output_file: 输出文件路径
    T��exist_oku   创建输出目录: r�   r�  )r�   �newliner   r?   )�total_req_count�served_req_count�cold_req_count�wait_req_countr�  r�  r�  �weighted_response_timer�  r�  �new_memory_usage�new_bare_memory�new_lang_memory�new_user_memoryr�  r�  r�  )�Time�IDr�   �Ingress�
DeployNode�IsColdStart�RunCost�
InstanCost�	TotalCost�ResponseTime�WaitTimerE   r�   r�   N)0r�   r�   r�   r�   r�   r�   r�  r�  r�  �writerrb   r]   rc   r_   r\   rm   ro   r�   r=   re   rk   rj   ri   rl   �writerowrf   rg   rh   r�  rT   r*   r�   r�   r�   r�  r�   r�   r�   ru   r�   r�   r�  r�   r�  rA  r�   r�   r�   )r�  �
output_dirr�  r�  r�  r�  r�  r�  r�  r�  r�   r   r�  r�   r�  �	wait_time�
total_costs                    r   r�  r�  K  s  � � ������-�J��"�'�'�.�.��4�
���J��.����+�J�<�8�9� 
�k��R�	0�D����D�!��1�4F�F���"4�4��/�2C�C���q� �-��<�M��M� � � �L�0�1�5G�J\�5\�_q�5q�r��1�4K�K�� 	��� 
� 	�  	�������#�C�(� ��%��S�!���n���$�!�#�&� ��%� ��%� ��%�
� 	�" 	��� 
� 	� �q�-�,�,�.��2�3�A��M�%�%�%�(�,�,�Q�/�8�8��'�G��~�~�#/��0B�0B�0E�0E�w�GW�GW�G\�G\�#]��&'��(/�(9�(9�
�$%�	�"�.�.�'�4�4��D��QX�Zg�Ih�.5�.A�.A��.=�g�>P�>P�>S�>S�U\�Ue�Ue�Uj�Uj�.k��)�W�-=�-=�-K�-K�K�M� #�7�J�7�G�<L�<L�q�<P�(/�(8�(8�I�)�Y�6�M�%-��%;�
����#�.�.�#�J�J�#�,�,�1�1�#�O�O�.�.�#�.�.�1�1�#�/�/�'��n�*�3�/�)�#�.�,�S�1�(��o�)� �+  (� 4�E 
1�	0�	0�s   �>D?M8�>/M8�.F M8�8Nr�  r�  c            
      �	  � t        �        t        �        t        �        t        j                  dk(  r
t        �        dt        j                  d�d�} t        | �       t        d�       t        j                  d�       t        j                  }t        j                  dk(  rt        �        n
t        �        t        d� t        j!                  �       D �       �      }d}|dkD  �r�|dk  �r�|d	z
  }||z   }t        j                  d
|� d|� d��       t        j#                  �       D ]M  \  }}|j%                  �       r�|j'                  �       }t(        j+                  ||�       |j%                  �       s�7�O |t        _        t        j                  dk(  �r�t(        j-                  |�      \  }}	|	�r�d}
t        j                  d
|� dt/        |�      � d��       |D ]U  }t1        |�       |j2                  s�|j4                  j6                  |_        t;        ||j<                  j>                  �       �W d}d}||k  r�tA        �        |d	z
  }tC        �       }
t/        tD        �      dk(  r2d}t        j!                  �       D ]  }|j%                  �       r�d} n |rnm|dz  dk(  r_t        d� tD        j!                  �       D �       �      }t        d� t        j!                  �       D �       �      }t        d|� d|
� d|� d|� ��       ||k  r��d	z
  }tG        �        �n~t(        j-                  |�      \  }}	|	�rcd}
t        j                  d
|� dt/        |�      � d��       |D ]U  }tI        |�       |j2                  s�|j4                  j6                  |_        t;        ||j<                  j>                  �       �W d}d}||k  r�tA        �        |d	z
  }tC        �       }
t/        tD        �      dk(  r2d}t        j!                  �       D ]  }|j%                  �       r�d} n |rnm|dz  dk(  r_t        d� tD        j!                  �       D �       �      }t        d� t        j!                  �       D �       �      }t        d|� d|
� d|� d|� ��       ||k  r��d	z
  }tK        �        t        d� t        j!                  �       D �       �      }|dkD  r|dk  r���|t        _        tM        �        y)u�   
    根据配置的缓存方法选择合适的调度策略，
    并在所有时间槽处理完后，如果仍有未处理的请求，添加额外时间槽直到所有请求处理完成
    r?   z./data/request/requests-r�  r�  z--------Schedule Start--------c              3   �<   K  � | ]  }|j                  �       �� � y �wr   �r*   ��.0r
   s     r   �	<genexpr>z$schedule_requests.<locals>.<genexpr>�  s   � �� � U�;T�%�����;T��   �r   ra  rE   u   --------添加额外时间槽 u   ，处理剩余 u    个请求--------r.  r/  r0  i  TFrG   c              3   �2   K  � | ]  }t        |�      �� � y �wr   �r(   �r�  �
containerss     r   r�  z$schedule_requests.<locals>.<genexpr>-
  �   � �� �2y�Ux�z�3�z�?�Ux��   �c              3   �<   K  � | ]  }|j                  �       �� � y �wr   r�  �r�  rT  s     r   r�  z$schedule_requests.<locals>.<genexpr>.
  �   � �� �.[�AZ�A�q�v�v�x�AZ�r�  u
   时间槽 u   ，秒级时钟 u   ，执行中容器: u   ，等待请求: c              3   �2   K  � | ]  }t        |�      �� � y �wr   r�  r�  s     r   r�  z$schedule_requests.<locals>.<genexpr>b
  r�  r�  c              3   �<   K  � | ]  }|j                  �       �� � y �wr   r�  r�  s     r   r�  z$schedule_requests.<locals>.<genexpr>c
  r�  r�  c              3   �<   K  � | ]  }|j                  �       �� � y �wr   r�  r�  s     r   r�  z$schedule_requests.<locals>.<genexpr>k
  s   � �� �$Y�?X�e�U�Z�Z�\�?X�r�  N)'r�  rr   r�  r�   r=   r�  r9   r�  rZ  r�   r�  r:   rz  rD  �sumrn   r%  r�  r   r   rT   r#   r�   r(   r#  r�   r�   rC  r�   r;  r�   r�   r=  r<  rq   r  r*  r!  r�  )r�  �original_slot_num�total_waiting_requests�
extra_slotr   r
   r
   r   r�   r�  rp   �max_seconds�second_countrS  �all_queues_emptyrT  �executing_containers�waiting_requestsrY   s                      r   �schedule_requestsr�  �  s�  � � �K��L��O�����,���-�h�m�m�C�-@��E�L��,�� 
�
*�+��L�L�1�2� !�)�)�� ����,��!��� !� U�;K�;R�;R�;T� U�U�� �J�
 �1�
$��b���a��
�%�
�2�	����5�i�[�@P�Qg�Ph�hz�{�|� /�4�4�6�N�G�U��n�n�&��0�0�2���!�!�'�9�5� �n�n�&� 7� &��� � � �L�0�+�/�/�	�:�O�H�e��!"�����7�	�{�BS�TW�X`�Ta�Sb�bn�o�p�  (�G�*�7�3� �~�~�4;�4D�4D�4R�4R��1�4�W�g�>P�>P�>S�>S�T�  (� #�� ��"�[�0�'�)� �A�%�L� &?�%@�N� �5�6�!�;�+/�(�!1�!8�!8�!:�A�#$�:�:�<�38� 0� %� ";�
 ,�!� $�c�)�Q�.�/2�2y�Uo�Uv�Uv�Ux�2y�/y�,�+.�.[�AQ�AX�AX�AZ�.[�+[�(��
�9�+�5E�n�EU�Ui�j~�i�  @Q�  Rb�  Qc�  d�  e�/ #�[�0�4 �1���!�#� ,�/�/�	�:�O�H�e��!"�����7�	�{�BS�TW�X`�Ta�Sb�bn�o�p�  (�G�"�7�+� �~�~�4;�4D�4D�4R�4R��1�4�W�g�>P�>P�>S�>S�T�  (� #�� ��"�[�0�'�)� �A�%�L� &?�%@�N� �5�6�!�;�+/�(�!1�!8�!8�!:�A�#$�:�:�<�38� 0� %� ";�
 ,�!� $�c�)�Q�.�/2�2y�Uo�Uv�Uv�Ux�2y�/y�,�+.�.[�AQ�AX�AX�AZ�.[�+[�(��
�9�+�5E�n�EU�Ui�j~�i�  @Q�  Rb�  Qc�  d�  e�/ #�[�0�4 �1����� "%�$Y�?O�?V�?V�?X�$Y�!Y��y !�1�
$��b��~ *�H�� �Nr   c            	      �  � t        �       } t        j                  dd��       t        j                  dd��       t        j                  dd��       | j                  r?| j                  t        v r-t        | j                     at
        d| j                  � d��       nt
        dt
        j                  � ��       | j                  t
        _        | j                  t
        _        | j                  t
        _	        | j                  t
        _
        | j                  t
        _        | j                  r@| j                  t        vr.| j                  t
        _        t
        d	| j                  � d
��       dt
        j                  � dt
        j                  d
�dt
        j                  � dt
        j                  d�d�	}t        j                  t        j                  t        j                  t        j                   t        j"                  d�}|j%                  | j&                  t        j                  �      }| j(                  }t+        |||��       t        j,                  dt
        j                  � ��       t        j,                  dt
        j.                  � dt
        j0                  � dt
        j2                  � ��       t        j,                  dt
        j4                  � dt
        j                  � dt
        j                  � ��       t        j,                  dt
        j                  � dt
        j                  � dt
        j                  � ��       t7        �        y )Nz./result/EUATr�  z./result/Telecomz./result/logu   使用 u    拓扑的配置参数z
EUA Dataset: u'   警告: 使用未预设的拓扑文件 u   ，使用默认配置参数z
./result/log/r�  r�  r�  z.logr~   )r�   r�   ztopo_file: z
node_num: z, mem_cap: z, cpu_Freq: zlatency_para: z, redu_factor: z, slot_num: zcache_method: z,beta: z	, alpha: )r�   r�   r�   r4   r�   r�   rZ  r9   r=   r;   r:   r8   r�   r   r}   r�   r�   r�   r�   �	log_level�console_logr�   r�  r7   r6   r<   r5   r�  )�argsr�   �
log_level_mapr�  r�   s        r   �mainr�  s
  si  � ��<�D� �K�K���.��K�K�"�T�2��K�K���.� �~�~�$�.�.�L�8�����/��
�����'�'=�>�?� 	�
�h�0�0�1�2�3� �I�I�H�M� �-�-�H���+�+�H���
�
�H���Z�Z�H�N� �~�~�$�.�.��<�!�^�^���
�7����7G�Gb�c�d� �x�4�4�5�Q�x�}�}�S�6I��8�K_�K_�J`�`a�bj�bp�bp�qt�au�uy�z�H� �������?�?�����$�$��M� �!�!�$�.�.�'�,�,�?�I� �%�%�N� �(�)�N�K� �L�L�;�x�1�1�2�3�4��L�L�:�h�/�/�0��H�<L�<L�;M�\�Zb�Zk�Zk�Yl�m�n��L�L�>�(�"7�"7�!8���H\�H\�G]�]i�jr�j{�j{�i|�}�~��L�L�>�(�"7�"7�!8���
�
��i�X`�Xf�Xf�Wg�h�i� �r   �__main__u$   请求转到邻居节点的延迟：u   传输延迟更高：u   创建延迟更高：)r�  )rM   rM   rM   r   )w�numpyri  �matplotlib.pyplot�pyplot�plt�typingr   r   r   r   r   r�  rK  r�   r�   r�   r�  rj  r�   �sysr	   r3   r�   r�   rr   r�   r}   r�   r�   r�   rW   r   r�   r!   r�   r�   r  r*  r4  r;  r�   rc  rp  rU   rR   r�  rN   r�  rP   r�  r�  r�  r�  r:  r>  rH  r�  r/   r�  rA   r�  r�  r�  r@   r�  r�  r�  r�  r�  r�  r�  r�  r�  r�  r�  r   r  r  r  r!  r*  rD  r$  rF  rQ  rR  r%  rJ  r=  r;  r<  rR  r>  r(  r'  rf  rb  r&  r0   rw  rz  r#  rV  r|  rW  r�  r�  r�  r
  r�  r�  r�  r�  �__annotations__r�  r�  r�  r,   rZ  r1   r   r   �<module>r�     s�  �� � � 5� 5� 
� � 	� � � � 
� � 
�� �:)� )�$ �6���!���
����� �9���!���
�������6 ����2$�j&�P #*�,�,�t� )�@
� 
�
� 
�%� %�#� #�>&� &�� �$
"� 
"�(� (�V1+� 1+�h� �� �(� (�7� 7�tD� D� #� #�,� �$$� $�$0� 0�.#%� #%�L-� -�`X!� X!�|
+� 
+�#�� #�/�� /�O0� O0�h'� '�
$� $�
� 
�*p�f,�� ,�� ,��#� �%� �4�$�L/�#� /�#� /�E� /�3�S� 3�
�7� 
�y� 
�"�� "�G� "�3� "�QV� "�HM�\�� \�|=7�� =7�� =7�� =7�\_� =7�D�s� �u� �u� �gl� �*�s� �c� �e� �$
�C� 
�3� 
�5� 
� 	 �s� 	 �u� 	 �/�~� /�#� /�Y\� /�bJ5�X=0�~WK�r(�Z1�G� 1�BA�F�w� � 8�g� 8�� 8�c� 8� ���1��9� �w� �(=�� =�8� =�� =�PS� =�af� =�"�?�'� ?�C� ?�6�2X�� X�t�S� �S� �B>C�� >C�BK�� K�\*�S� *�C� *�E� *�.&
�#� &
�C� &
�C� &
�TUC�� UC�p"�8� "�� "�B�H'�G� '�,�w� �4� �< 
���!��9� �w� �4� �BM6�w� M6�`E�g� E�$� E�PZ�'� Z�d� Z�z�w� �{� �u� �>�3� �#� �k� �VZ� �2tp�lx�s� x�x 
���"$�
�D��h��� $�%'��T�#�{�"�
#� '�f�P:�x �z��& 	�F�	�
0�1�	�
!�1�%�	�
!�1�%�- r   