import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

# 读取原始CSV文件
df = pd.read_csv('method_beta_metrics.csv')

# 创建新的DataFrame，只包含需要的列
result_df = df[['Method', 'Beta', 'Success_Rate', 'Avg_Response_Time']].copy()

# 使用新公式计算Weighted_Response_Time
# weighted_response_time = avg_response_time * 0.543 / (success_rate ** 1.5)
result_df['Weighted_Response_Time'] = result_df.apply(
    lambda row: row['Avg_Response_Time'] * 0.543 / (row['Success_Rate'] ** 1.5), 
    axis=1
)

# 保留3位小数
result_df['Weighted_Response_Time'] = result_df['Weighted_Response_Time'].round(3)

# 最终输出列
result_df = result_df[['Method', 'Beta', 'Avg_Response_Time', 'Weighted_Response_Time']]

# 保存到新的CSV文件
result_df.to_csv('method_response_time.csv', index=False)

print("计算完成，结果已保存到 method_response_time.csv")

# 打印结果以便查看
print("\n计算结果预览：")
print(result_df)

# 绘制加权响应时间折线图
def plot_weighted_response_time():
    """绘制多种方法在不同beta值下的加权响应时间折线对比图"""
    # 设置字体
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号
    
    # 创建图形和坐标轴
    fig, ax = plt.subplots(figsize=(12, 7), dpi=300)
    
    # 获取唯一的方法名和beta值
    methods = result_df['Method'].unique()
    beta_values = sorted(result_df['Beta'].unique())
    
    # 不同方法使用不同颜色和标记
    method_colors = {
        'LayerCache': '#C03D3E',  # 红色
        'CrossEdge': '#E1812C',   # 黄色
        'FaaSCache': '#3A923A',   # 绿色
        'OpenWhisk': '#3274A1'    # 蓝色
    }
    markers = ['o', 's', '^', 'D']
    
    # 调整方法顺序，确保LayerCache在最前面，OpenWhisk在最后面
    ordered_methods = []
    if 'LayerCache' in methods:
        ordered_methods.append('LayerCache')
    if 'CrossEdge' in methods:
        ordered_methods.append('CrossEdge')
    if 'FaaSCache' in methods:
        ordered_methods.append('FaaSCache')
    if 'OpenWhisk' in methods:
        ordered_methods.append('OpenWhisk')
    
    # 为每种方法绘制折线
    for i, method in enumerate(ordered_methods):
        # 获取当前方法的数据
        method_data = result_df[result_df['Method'] == method]
        
        # 确保数据按beta值排序
        method_data = method_data.sort_values('Beta')
        
        # 绘制折线图
        ax.plot(method_data['Beta'], method_data['Weighted_Response_Time'], 
                label=method, 
                color=method_colors.get(method, f'C{i}'),
                marker=markers[i % len(markers)],
                markersize=10,
                linewidth=2.5)
    
    # 设置轴标签
    ax.set_xlabel('', fontsize=28)  # 移除 β Values 标签
    ax.set_ylabel('Weighted Response Time (s)', fontsize=28)
    
    # 设置x轴刻度标签
    ax.set_xticks(beta_values)
    ax.set_xticklabels([f'β={beta:.2f}' for beta in beta_values], fontsize=26)
    
    # 设置y轴刻度标签字体大小
    ax.tick_params(axis='y', labelsize=26)
    
    # 添加图例 - 调整到右上角，并设置更紧凑的边距
    ax.legend(fontsize=24, frameon=True, edgecolor='black', borderpad=0.4, handletextpad=0.5, 
              loc='upper right', bbox_to_anchor=(0.99, 0.99))
    
    # 添加网格线，提高可读性
    ax.grid(True, linestyle='--', alpha=0.7)
    
    # 确保输出目录存在
    output_dir = 'fig'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 保存图表
    output_file = os.path.join(output_dir, 'weighted_response_time.png')
    plt.tight_layout()
    plt.savefig(output_file, bbox_inches='tight')
    print(f"加权响应时间折线图已保存为 '{output_file}'")

# 调用函数绘制图表
plot_weighted_response_time()