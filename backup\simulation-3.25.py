import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Optional
import csv
import math
import os
import logging
import argparse
#from data_struct import * 

class Config:
    def __init__(self, topo_file: str, latency_para: float, mem_cap: int, 
                 node_num: int, alpha: float, beta: float, 
                 slot_num: int, redu_factor: int, cpu_Freq: int, 
                 cache_method: str = "Layer-Cache"):
        self.topo_file = topo_file          # 拓扑文件路径
        self.latency_para = latency_para    # 时延参数
        self.mem_cap = mem_cap              # 内存容量
        self.node_num = node_num            # 节点数量
        self.alpha = alpha                  # α参数
        self.beta = beta                    # β参数
        self.slot_num = slot_num            # 插槽数量
        self.redu_factor = redu_factor      # 减少因子
        self.cpu_Freq = cpu_Freq            # cpu频率
        self.cache_method = cache_method    # 缓存方法
config_G = Config(
    node_num=10,                  # 节点数量
    slot_num=30,                  # 时间槽数量 slot_num=5满足Cross-Edge论文结果
    cache_method="Layer-Cache",   # 缓存方法 'Layer-Cache', 'S-Cache', 'Cross-Edge', 'LRU', 'FC'    
    cpu_Freq= 3,                  # CPU频率
    latency_para=1,               # 延迟参数
    mem_cap=10000,                # 节点内存容量
    alpha = 0.015,                # 运行成本参数
    beta = 0.5,                   # Zipf-β请求分布  0.5, 1, 1.5
    redu_factor=10,               # 请求数量减少参数  
    # topo_file='./data/topo/pi_nodes.csv',             # 10个节点
    topo_file='./data/topo/site-optus-melbCBD.csv'      # 125个节点
    )
def initGlobal():
    global clock_G, count_G, activeFunctions_G, cacheMap_G, functionfreq_G
    global requestsMap_G, funcInfoMap_G, rontt_G
    global total_req_count_G, cold_req_count_G, req_count_G, served_req_count_G
    global deploy_current_req_G, deploy_neighbor_req_G,  total_cold_req_G
    global total_response_time_G, total_cost_G

    activeFunctions_G = ActiveFunctions()   # 活动函数
    cacheMap_G = CacheMap()                 # 缓存函数
    functionfreq_G = FunctionFreq()         # 函数频率
    requestsMap_G = RequestsMap()           # 请求映射
    funcInfoMap_G = FunctionInfoMap()       # 函数属性
    rontt_G = Req_on_Nodes_Time_Type()      # funcType<->Req_Nodes_Time
    
    clock_G = 1                             # 每轮开始时更新时钟
    count_G = 0                             # 容器总数计数
    req_count_G = 0                         # 创建请求计数
    total_req_count_G = 0                   # 总请求计数
    served_req_count_G = 0                  # 服务请求计数
    cold_req_count_G = 0                    # 冷启动请求计数(已服务)
    total_cold_req_G = 0                    # 冷启动请求计数(总)
    deploy_current_req_G = 0                # 当前节点部署计数
    deploy_neighbor_req_G = 0               # 邻居节点部署计数
    total_response_time_G = 0               # 总响应时间
    total_cost_G = 0                        # 总成本

#region ---------------------命令行及日志---------------------
def parse_args():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='Simulation in Serverless Edge Computing')
    
    # 默认值保持与config_G初始化时相同
    parser.add_argument('--beta', type=float, default=config_G.beta,
                        help=f'Zipf分布β参数 (默认: {config_G.beta})')
    
    parser.add_argument('--cache-method', type=str, default=config_G.cache_method,
                        choices=['Layer-Cache', 'S-Cache', 'Cross-Edge', 'LRU', 'FC'],
                        help=f'缓存方法 (默认: {config_G.cache_method})')
    
    parser.add_argument('--slot-num', type=int, default=config_G.slot_num,
                        help=f'时间槽数量 (默认: {config_G.slot_num})')
    
    parser.add_argument('--redu-factor', type=int, default=config_G.redu_factor,
                        help=f'冗余因子 (默认: {config_G.redu_factor})')
    
    # 添加日志相关参数
    parser.add_argument('--log-level', type=str, default='INFO',
                        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                        help='日志级别 (默认: INFO)')
    
    parser.add_argument('--console-log', action='store_true',
                        help='启用控制台日志输出')
    
    return parser.parse_args()

def setup_logging(log_file, level=logging.INFO, console_output=True):
    """
    设置日志配置
    
    Args:
        log_file (str): 日志文件名
        level (int): 日志级别，默认为INFO
    """
    # 创建日志目录
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
    file_handler.setLevel(level)
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)

#endregion

#region ----------------------请求相关类----------------------
# ----------------Cross-Edge----------------
# 节点请求数量
class Req_on_Nodes:
    def __init__(self):
        self.numVector: List[int] = []  # 存储每个节点的请求数量

    def add(self, num: int):
        """添加请求数量"""
        self.numVector.append(num)

    def clear(self):
        """清空请求数量"""
        self.numVector.clear()

# 每个时间槽的请求数量(timeslot<->req_num) 
class Req_on_Nodes_Time:
    def __init__(self):
        self.numVector: List[Req_on_Nodes] = []  # 存储每个时间槽的请求数量

    def add(self, ron: Req_on_Nodes):
        """添加请求数量"""
        self.numVector.append(ron)

    def clear(self):
        """清空请求数量"""
        self.numVector.clear()

# 请求数量映射(funcType<->Req_Nodes_Time)
class Req_on_Nodes_Time_Type:
    def __init__(self):
        self.numMap: Dict[int, Req_on_Nodes_Time] = {}  # 存储函数类型与请求数量的映射

    def add(self, funcType: int, ront: Req_on_Nodes_Time):
        """添加函数类型的请求数量"""
        self.numMap[funcType] = ront
# ----------------Cross-Edge----------------

# 单个请求 request -> function -> phynode
class Request:
    # 使用字符串来引用尚未定义的类以实现嵌套 'Function' 'PhyNode'
    def __init__(self, id: int = 0, function: 'Function' = None, ingress: 'PhyNode' = None, 
                 arriveTime: int = 0, served: bool = False, isColdStart: bool = False, 
                 deployNode: 'PhyNode' = None, linkDelay: float = 0.0):
        self.id = id                    # 请求ID
        self.function = function        # 函数对象
        self.ingress = ingress          # 入口节点
        self.arriveTime = arriveTime    # 到达时间
        self.served = served            # 是否已服务
        self.isColdStart = isColdStart  # 是否冷启动
        self.deployNode = deployNode    # 部署节点
        self.linkDelay = linkDelay      # 传输延迟
       
    def update(self, function: 'Function', deployNode: 'PhyNode', isColdStart: bool, linkDelay: float):
        """更新请求的函数、部署节点和冷启动状态"""
        self.function = function
        self.served = True
        self.deployNode = deployNode
        self.isColdStart = isColdStart
        self.linkDelay = linkDelay

# 请求列表(List[Request])
class Requests:
    def __init__(self):
        self.requests: List[Request] = []  # 请求列表

    def add(self, request: Request):  # 添加请求
        self.requests.append(request)

# 请求映射(time slot<->request)
class RequestsMap:
    def __init__(self):
        self.map: Dict[int, Requests] = {}  # 时间槽到请求的映射

    def add(self, request: Request, time_slot: int):  # 添加请求到指定时间槽
        if time_slot not in self.map:
            self.map[time_slot] = Requests()  # 初始化请求列表
        self.map[time_slot].add(request)  # 添加请求

    def get(self, time_slot: int) -> Tuple[List[Request], bool]:  # 获取指定时间槽的请求
        if time_slot in self.map:
            return self.map[time_slot].requests, True  # 返回请求列表和找到的标志
        return [], False  # 返回空列表和未找到的标志

    def size(self):
        return len(self.map)

# 存储请求数据
class RequestFile:
    def __init__(self):
        self.time: List[int] = []  # 时间列表
        self.app1: List[int] = []  # 应用1请求列表
        self.app2: List[int] = []  # 应用2请求列表
        self.app3: List[int] = []  # 应用3请求列表
        self.app4: List[int] = []  # 应用4请求列表
# endregion

#region ---------------------节点相关类----------------------
# 物理节点
class PhyNode:
    def __init__(self, id: int = 0, lat: float = 0.0, long: float = 0.0, 
                 mem: float = 0.0, cpuFreq: float = 0.0):
        self.id = id
        self.lat = lat
        self.long = long
        self.mem = mem  
        self.cpuFreq = cpuFreq
        self.funcFreq: Dict[int, float] = {}  # <functype, freq>
        self.recency: Dict[int, float] = {}  # <functype, time>

    def getFreq(self, funcType: int) -> float:
        if funcType not in self.funcFreq or self.funcFreq[funcType] <= 0:
            self.funcFreq[funcType] = 1.0
            return 1.0
        return self.funcFreq[funcType]

    def getRecency(self, funcType: int) -> float:
        if funcType not in self.recency or self.recency[funcType] <= 0:
            self.recency[funcType] = 1.0
            return 1.0
        return self.recency[funcType]

    def setRecency(self, funcType: int, recency: float):
        self.recency[funcType] = recency

    def setFreq(self, funcType: int, freq: float):
        self.funcFreq[funcType] = freq

    def addFreq(self, funcType: int):
        if funcType not in self.funcFreq:
            self.funcFreq[funcType] = 1.0
        else:
            self.funcFreq[funcType] += 1.0

    def minusFreq(self, funcType: int):
        if funcType in self.funcFreq:
            self.funcFreq[funcType] -= 1.0

    def getMem(self) -> float:
        return self.mem

# 拓扑结构(node id, PhyNode)
class Topology:
    def __init__(self):
        self.nodes: Dict[int, PhyNode] = {}  

    def get(self, phyNodeID: int) -> PhyNode:
        """获取物理节点"""
        return self.nodes.get(phyNodeID, PhyNode(0, 0.0, 0.0, 0.0))  # 返回默认 PhyNode

    def add_node(self, phyNodeID: int, p: PhyNode):
        """添加物理节点到拓扑结构"""
        self.nodes[phyNodeID] = p  # 将节点添加到列表中

    def update(self, operation: str, phyNodeID: int, size: float):
        """更新物理节点的内存"""
        if operation == "add":
            self.nodes[phyNodeID].mem += size
        elif operation == "minus":
            self.nodes[phyNodeID].mem -= size

    def size(self) -> int:
        """返回节点数量"""
        return len(self.nodes)

    def minusFreq(self, phyNodeID: int, funcType: int):
        """减少函数类型的频率"""
        p = self.get(phyNodeID)
        p.minusFreq(funcType)
        self.nodes[phyNodeID] = p
    
    def addFreq(self, phyNodeID: int, funcType: int):
        """增加函数类型的频率"""
        p = self.get(phyNodeID)
        p.addFreq(funcType)
        self.nodes[phyNodeID] = p

    def setRecency(self, phyNodeID: int, funcType: int, recent: float):
        """设置函数类型的最近使用时间"""
        p = self.get(phyNodeID)
        p.setRecency(funcType, recent)
        self.nodes[phyNodeID] = p

    def addFreqAll(self, funcType: int):
        """对所有物理节点增加函数类型的频率"""
        for p in self.nodes.values():
            p.addFreq(funcType)

    def setRecencyAll(self, funcType: int, recent: float):
        """对所有物理节点设置函数类型的最近使用时间"""
        for p in self.nodes.values():
            p.setRecency(funcType, recent)

# 节点经纬度
class Location:
    def __init__(self, latitude: float = 0.0, longitude: float = 0.0):
        self.latitude = latitude
        self.longitude = longitude

    def init(self, lat1: float, long1: float):
        self.latitude = lat1
        self.longitude = long1

# 节点间距离(node id, distance)
class Distance:
    def __init__(self, phyNodeID: int, distance: float):
        self.phyNodeID = phyNodeID  # 物理节点ID
        self.distance = distance  # 距离
    
    def get_id(self):
        return self.phyNodeID

# 节点列表(List[Distance]距离排序)
class DistSlice:
    def __init__(self):
        self.slice: List[Distance] = []  # 距离切片列表
#endregion

#region ---------------------函数相关类----------------------
# 单个函数 function -> phynode
class Function:
    def __init__(self, name: str = None, type: int = 0, size: float = 0.0, clock: float = 0.0, 
                 coldStartTime: float = 0.0, priority: float = 1.0, processTime: float = 0.0,  
                 phyNode: PhyNode = None, creation_time: int = 0, lastUseTime: int = 0, lifeTime: int = 10,
                 lang_type: int = 0, bare_size: float = 0.0, lang_size: float = 0.0, user_size: float = 0.0,):
        self.name = name  # 函数名称
        self.type = type  # 函数类型 1-4
        self.size = size  # 函数整体大小
        self.clock = clock  # 函数时钟
        self.coldStartTime = coldStartTime  # 冷启动时间
        self.priority = priority  # 优先级
        self.processTime = processTime  # 处理时间
        self.phyNode = phyNode  # 物理节点
        self.creation_time = creation_time  # 创建时间
        self.lastUseTime = lastUseTime      # 最后使用时间
        self.lifeTime = lifeTime            # 容器生命周期，用于FC策略

        # 新增属性
        self.lang_type = lang_type  # 语言类型 1-2(node.js, python)
        self.bare_size = bare_size  # Bare层大小
        self.lang_size = lang_size  # Lang层大小
        self.user_size = user_size  # User层大小

    def active_priority(self):
        """计算活动优先级"""
        freq = functionfreq_G.get(self.type)
        self.clock = clock_G
        self.priority = self.clock + (float(freq) * self.coldStartTime) / (self.size)   # MB 单位

    def cache_priority(self):
        """计算缓存优先级 如果cache不使用当前clock"""
        freq = functionfreq_G.get(self.type)
        self.priority = self.clock + (float(freq) * self.coldStartTime) / (self.size)  # MB 单位

    def show_priority(self):
        """显示函数优先级"""
        print(f"Function {self.name} Priority {self.priority}")  # 使用 print 替代 log

    def minusLife(self):
        """减少函数容器的生命周期，用于FC策略"""
        self.lifeTime -= 1
        return self.lifeTime <= 0
        
    def activeLifeTime(self, time: int):
        """设置函数容器的生命周期，用于FC策略"""
        self.lifeTime = time

# 一种类型的活动函数(type, List[Function])
class Functions:
    def __init__(self, func_type: int):
        self.type = func_type  # 函数类型
        self.slice: List[Function] = []  # 存储函数的列表

    def add(self, function: Function):
        self.slice.append(function)  # 添加函数到列表

    def delete(self, index: int):
        if 0 <= index < len(self.slice):
            self.slice.pop(index)  # 从列表中删除指定索引的函数

    def show_priority(self):
        for func in self.slice:
            print(f"Function {func.name} Priority {func.priority}")  # 显示函数的优先级

# ----------------Cross-Edge----------------
class FunctionInfo:
    """函数信息类，存储函数的基本信息"""
    def __init__(self, processTime: float = 0.0, coldStartTime: float= 0.0, 
                 size: float = 0.0, lang_type: str = None,
                 bare_size: float = 0.0, lang_size: float = 0.0, user_size: float = 0.0):
        self.processTime = processTime      # 处理时间
        self.coldStartTime = coldStartTime  # 冷启动时间
        self.size = size                    # 函数大小
        self.lang_type = lang_type          # 语言类型
        
        # 三层容器的大小
        self.bare_size = bare_size          # Bare层大小
        self.lang_size = lang_size          # Lang层大小
        self.user_size = user_size          # User层大小

# (funcType<->FunctionInfo)
class FunctionInfoMap:
    def __init__(self):
        self.func_map: Dict[int, FunctionInfo] = {}  

    def add(self, func_type: int, fi: FunctionInfo):

        """添加函数信息"""
        self.func_map[func_type] = fi

        return None, False
    
    def get_size(self, func_type: int) -> int:
        """获取函数大小"""
        if func_type in self.func_map:
            return self.func_map[func_type].size
        return 0
# ----------------Cross-Edge----------------

# 函数频率(type<->freq)(active+,cache-)
class FunctionFreq:
    def __init__(self):
        self.map: Dict[int, int] = {}  # 函数类型到频率的映射

    def get(self, funcType: int) -> int:  # 获取函数类型的频率
        return self.map.get(funcType, 0)  # 返回频率，如果不存在则返回0    

    def add(self, funcType: int):  # 增加函数类型的频率
        if funcType in self.map:
            self.map[funcType] += 1  # 增加频率
        else:
            self.map[funcType] = 1  # 初始化频率

    def minus(self, funcType: int):  # 减少函数类型的频率
        if funcType in self.map:
            self.map[funcType] -= 1  # 减少频率

# 不同类型的活动函数(node id, type<->Functions)
class NodeFunctions:
    def __init__(self, phy_node_id: int):
        self.phyNodeID = phy_node_id  # 物理节点ID
        self.functions: Dict[int, Functions] = {}  # <funcType, FunctionSlice>

    def show(self, node_id: int):
        for func_type, functions in self.functions.items():
            print(f"Node id {node_id} funcType: {func_type} => active container num: {len(functions.slice)}")

    def show_priority(self):
        for functions in self.functions.values():
            functions.show_priority()  # 调用 Functions 的 show_priority 方法

    def add(self, function: Function):
        if function.type not in self.functions:
            self.functions[function.type] = Functions(function.type)  # 初始化 Functions
        self.functions[function.type].add(function)  # 将函数添加到对应的 Functions

    def delete(self, func_type: int, i: int):
        if func_type in self.functions:
            self.functions[func_type].delete(i)  # 从对应的 Functions 中删除函数

# 所有节点上的活动函数(node id<->NodeFunctions)
class ActiveFunctions:
    def __init__(self):
        self.map: Dict[int, NodeFunctions] = {}  # 节点ID到节点函数的映射

    def show(self):
        if not self.map:
            print("ActiveFunctions is empty")
            return

        print("show ActiveFunctions....")
        for nodeID, nf in self.map.items():
            nf.show(nodeID)  # 调用 NodeFunctions 的 show 方法

    def show_priority(self):
        if not self.map:
            print("ActiveFunctions is empty")
            return

        print("show priority....")
        for nf in self.map.values():
            nf.show_priority()  # 调用 NodeFunctions 的 showPriority 方法

    def add(self, function: Function, phyNodeID: int):
        if phyNodeID not in self.map:
            self.map[phyNodeID] = NodeFunctions(phyNodeID)  # 初始化 NodeFunctions

        # 更新函数频率
        functionfreq_G.add(function.type)
        function.active_priority()      
        self.map[phyNodeID].add(function)  # 将函数添加到对应的 NodeFunctions

    def delete(self, phyNodeID: int, funcType: int, i: int):
        if phyNodeID in self.map:
            nf = self.map[phyNodeID]
            nf.delete(funcType, i)    # 调用 NodeFunctions 的 delete 方法
            self.map[phyNodeID] = nf  # 更新映射

# 单个节点的缓存列表(node id, List[Function]优先级升序)
class Cache:
    def __init__(self, phyNodeID: int):
        self.phyNodeID = phyNodeID  # 物理节点ID
        self.functionList: List[Function] = []  # 优先级函数列表，升序

    def show(self):
        print(f"node {self.phyNodeID} cache size : {len(self.functionList)}")

    def show_priority(self):
        for function in self.functionList:
            print(f" {function.name} Priority : {function.priority}")

    def sort_list(self):
        self.functionList.sort(key=lambda f: f.priority)  # 按优先级排序
        
    def sort_by_last_use_time(self):
        self.functionList.sort(key=lambda f: f.lastUseTime)  # 按最后使用时间排序（LRU）
        
    def sort_by_creation_time(self):
        """按生命周期排序（FC策略）"""
        self.functionList.sort(key=lambda f: f.lifeTime)

    def find(self, functionName: str) -> Tuple[int, Optional[str]]:
        for i in range(len(self.functionList)):
            if functionName == self.functionList[i].name:
                return i, None  # 返回索引和无错误
        return -1, "can't find this function by name"  # 返回未找到的错误信息

    def add(self, function: Function):
        functionfreq_G.minus(function.type)  
        function.cache_priority()  
        function.lastUseTime = clock_G  # 设置最后使用时间为当前时钟
        self.functionList.append(function)
        self.sort_list()  

    def delete(self, i: int):
        # 检查索引是否存在
        if 0 <= i < len(self.functionList):
            self.functionList.pop(i)  # 从列表中删除指定索引的函数

    def removeType(self, funcType: int) -> bool:
        for i, function in enumerate(self.functionList):
            if function.type == funcType:
                del self.functionList[i]
                return True
        return False

# 多个节点的缓存映射(node id<->cache)
class CacheMap:
    def __init__(self):
        self.caches: Dict[int, Cache] = {}  # 物理节点ID到缓存的映射

    def show(self):
        if not self.caches:
            print("CacheMap is empty")
            return

        print("--------CacheMap--------")
        for cache in self.caches.values():
            cache.show()  # 调用 Cache 的 show 方法

    def show_priority(self):
        if not self.caches:
            print("CacheMap is empty")
            return

        print("--------CacheMap Priority--------")
        for cache in self.caches.values():
            cache.show_priority()  # 调用 Cache 的 show_priority 方法

    def add(self, function: Function):
        if function.phyNode.id in self.caches:
            self.caches[function.phyNode.id].add(function)  # 添加到现有缓存
        else:
            new_cache = Cache(function.phyNode.id)  # 创建新的 Cache 实例
            new_cache.add(function)  # 添加函数
            self.caches[function.phyNode.id] = new_cache  # 将新缓存添加到映射中

    # 寻找可用缓存容器
    def get_idle_function(self, phyNodeID: int, funcType: int) -> Tuple[Function, int]:
        if phyNodeID in self.caches:
            cache = self.caches[phyNodeID]
            for i, function in enumerate(cache.functionList):
                if function.type == funcType:
                    # 更新最后使用时间为当前时钟
                    function.lastUseTime = clock_G
                    cache.functionList[i] = function
                    return function, i  
        return Function(), -1 

    def delete(self, phyNodeID: int, funcIndex: int):
        if phyNodeID in self.caches:
            self.caches[phyNodeID].delete(funcIndex)  # 从缓存中删除函数

    def get_lowest_priority(self, phyNodeID: int) -> float:
        if phyNodeID in self.caches and self.caches[phyNodeID].functionList:
            return self.caches[phyNodeID].functionList[0].priority  # 返回最低优先级
        return 0.0  # 如果没有函数，返回0

    def delete_low_function(self, phyNodeID: int) -> Tuple[Function, bool]:
        if phyNodeID in self.caches and self.caches[phyNodeID].functionList:
            function = self.caches[phyNodeID].functionList[0]  # 获取最低优先级的函数
            self.caches[phyNodeID].delete(0)  # 删除该函数
            return function, True  # 返回函数和成功标志 
        return Function(), False  # 返回空函数和失败标志
        
    def sort_by_lru(self, phyNodeID: int):
        """按LRU（最近最少使用）策略对缓存进行排序"""
        if phyNodeID in self.caches:
            self.caches[phyNodeID].sort_by_last_use_time()
            
    def delete_lru_function(self, phyNodeID: int) -> Tuple[Function, bool]:
        """删除最近最少使用的函数（LRU策略）"""
        if phyNodeID in self.caches and self.caches[phyNodeID].functionList:
            # 先按最后使用时间排序
            self.sort_by_lru(phyNodeID)
            # 取出最久未使用的函数（列表头部）
            function = self.caches[phyNodeID].functionList[0]
            self.caches[phyNodeID].delete(0)
            return function, True
        return Function(), False
        
    def check_expired_functions(self, phyNodeID: int) -> List[Function]:
        """检查并返回过期的函数列表（FC策略）"""
        expired_functions = []
        if phyNodeID in self.caches:
            cache = self.caches[phyNodeID]
            i = 0
            while i < len(cache.functionList):
                function = cache.functionList[i]
                if is_container_expired(function):
                    expired_functions.append(function)
                    cache.functionList.pop(i)
                else:
                    i += 1
        return expired_functions
        
    def delete_expired_functions(self, phyNodeID: int) -> List[Function]:
        """删除并返回过期的函数（FC策略）"""
        expired_functions = self.check_expired_functions(phyNodeID)
        return expired_functions
    
    def sort(self):
        """对所有缓存进行排序"""
        for cache in self.caches.values():
            cache.sort_list()  # 调用 Cache 的 sort_list 方法

    def get(self, phyNodeID: int) -> Cache:
        return self.caches.get(phyNodeID, Cache(phyNodeID))

    def size(self) -> int:
        return sum(cache.size() for cache in self.caches.values())

    def delete_Prob(self, phyNodeID: int, funcType: int) -> bool:
        cache = self.get(phyNodeID)
        if cache.removeType(funcType):
            self.caches[phyNodeID] = cache
            topo_G.minusFreq(phyNodeID, funcType)
            return True
        return False
        
    def sort_by_lifetime(self, phyNodeID: int):
        """按固定缓存（FC）策略的生命周期对缓存进行排序"""
        if phyNodeID in self.caches:
            self.caches[phyNodeID].sort_by_creation_time()  # 实际上是按lifeTime排序
    
    def delete_low_lifetime_function(self, phyNodeID: int) -> Tuple[Function, bool]:
        """删除生命周期最低的函数（FC策略）"""
        if phyNodeID in self.caches and self.caches[phyNodeID].functionList:
            # 先按生命周期排序
            self.sort_by_lifetime(phyNodeID)
            # 取出生命周期最低的函数（列表头部）
            function = self.caches[phyNodeID].functionList[0]
            self.caches[phyNodeID].delete(0)
            return function, True
        return Function(), False
#endregion

#region ---------------------容器相关类----------------------
# 容器层基类
class ContainerLayer:
    """容器层的基类"""
    def __init__(self, size: float, creation_time: int = 0, last_used_time: int = 0):
        self.size = size  # 层大小
        self.creation_time = creation_time      # 创建时间
        self.last_used_time = creation_time     # 最后使用时间(初始化为创建时间)
        self.shared_count = 0  # 共享计数

    def update_usage(self, current_time: int):
        """更新层的最后使用时间"""
        self.last_used_time = current_time
        
    def get_age(self, current_time: int) -> int:
        """获取层的存活时间（当前时间 - 创建时间）"""
        return current_time - self.creation_time

# 基础容器层
class BareLayer(ContainerLayer):
    """基础容器层，只包含基础环境和工具"""
    def __init__(self, size: float, creation_time: int = 0):
        super().__init__(size, creation_time, creation_time)  # 最后使用时间初始化为创建时间
        self.layer_type = "bare"
        
# 语言运行层
class LangLayer(ContainerLayer):
    """语言运行时层，包含特定语言的运行时"""
    def __init__(self, size: float, lang_type: int, bare_layer: BareLayer, creation_time: int = 0):
        super().__init__(size, creation_time, creation_time)  # 最后使用时间初始化为创建时间
        self.layer_type = "lang"
        self.lang_type = lang_type      # 语言类型 1-2(python, node.js)
        self.bare_layer = bare_layer    # 引用基础层
        
# 用户代码层
class UserLayer(ContainerLayer):
    """用户代码层，包含特定函数的代码和依赖"""
    def __init__(self, size: float, func_type: int, lang_layer: LangLayer, creation_time: int = 0):
        super().__init__(size, creation_time, creation_time)  # 最后使用时间初始化为创建时间
        self.layer_type = "user"
        self.func_type = func_type      # 函数类型
        self.lang_layer = lang_layer    # 引用语言层

# 分层容器缓存管理
class Layer_Cache:
    def __init__(self, node_id: int):
        self.node_id = node_id
        self.bare_layers: Dict[int, BareLayer] = {}  # Bare层
        self.lang_layers: Dict[int, LangLayer] = {}  # Lang层
        self.user_layers: Dict[int, UserLayer] = {}  # User层
        self.total_memory_usage = 0.0  # 总内存使用量
        
    def evict_containers(self, required_memory: float) -> bool:
        """
        驱逐容器以腾出空间
        # 实现RainbowCake的共享感知驱逐策略
        # 1. 计算每个层的共享价值
        # 2. 优先驱逐共享价值低的层
        """
    
        # 首先尝试驱逐User层
        user_layers_values = [(func_type, self.calculate_sharing_value(layer)) 
                             for func_type, layer in self.user_layers.items()]
        user_layers_values.sort(key=lambda x: x[1])  # 按共享价值排序
        
        freed_memory = 0.0
        evicted_user_layers = []
        
        # 尝试驱逐User层
        for func_type, value in user_layers_values:
            if freed_memory >= required_memory:
                break
                
            layer = self.user_layers[func_type]
            freed_memory += layer.size
            evicted_user_layers.append(func_type)
            
        # 如果驱逐User层后仍然不够，尝试驱逐Lang层
        if freed_memory < required_memory:
            lang_layers_values = [(lang_type, self.calculate_sharing_value(layer)) 
                                for lang_type, layer in self.lang_layers.items()]
            lang_layers_values.sort(key=lambda x: x[1])  # 按共享价值排序
            
            evicted_lang_layers = []
            
            for lang_type, value in lang_layers_values:
                if freed_memory >= required_memory:
                    break
                    
                layer = self.lang_layers[lang_type]
                # 检查该Lang层是否被任何保留的User层引用
                if not any(self.user_layers[ut].lang_layer == layer for ut in self.user_layers 
                          if ut not in evicted_user_layers):
                    freed_memory += layer.size
                    evicted_lang_layers.append(lang_type)
        
        # 如果仍然不够，尝试驱逐Bare层（通常不会到这一步，因为Bare层通常是共享的）
        bare_layer_evicted = False
        if freed_memory < required_memory and self.bare_layers:
            # 检查Bare层是否被任何保留的Lang层引用
            if not any(self.lang_layers[l].bare_layer == self.bare_layers[0] for l in self.lang_layers 
                      if l not in evicted_lang_layers):
                freed_memory += self.bare_layers[0].size
                bare_layer_evicted = True
        
        # 执行实际驱逐
        for func_type in evicted_user_layers:
            layer_size = self.user_layers[func_type].size
            self.total_memory_usage -= layer_size
            del self.user_layers[func_type]
            update_topo("add", self.node_id, layer_size)
            
        for lang_type in evicted_lang_layers:
            layer_size = self.lang_layers[lang_type].size
            self.total_memory_usage -= layer_size
            del self.lang_layers[lang_type]
            update_topo("add", self.node_id, layer_size)
            
        if bare_layer_evicted:
            layer_size = self.bare_layers[0].size
            self.total_memory_usage -= layer_size
            self.bare_layers.clear()
            update_topo("add", self.node_id, layer_size)
            
        return freed_memory >= required_memory
    
    def calculate_sharing_value(self, layer: ContainerLayer) -> float:
        """计算层的共享价值"""
        global clock_G
        
        # 实现RainbowCake的共享价值计算
        # 考虑因素：层类型、共享计数、使用时间、大小等
        
        # 基础分值 - 根据层类型
        if isinstance(layer, BareLayer):
            base_value = 10  # Bare层基础价值最高
        elif isinstance(layer, LangLayer):
            base_value = 5.0   # Lang层次之
        else:  # UserLayer
            base_value = 1.0   # User层最低
            
        # 共享因子 - 根据共享计数
        sharing_factor = 1.0 + layer.shared_count * 0.5
        
        # 年龄因子 - 较新的层价值更高
        age = layer.get_age(clock_G)
        age_factor = max(0.1, 1.0 - age / 1000.0)  # 随着年龄增长，价值降低
        
        # 大小因子 - 较小的层价值更高
        size_factor = max(0.1, 1.0 - layer.size / 100.0)
        
        # 计算最终价值
        value = base_value * sharing_factor * age_factor * size_factor
        
        return value
    

#endregion

#region ---------------------方法相关类----------------------
# 概率计算类
class ProbPair:
    def __init__(self, func_type: int, probability: float):
        self.func_type = func_type  # <funcType>
        self.probability = probability  # <probability>

class ProbPairVec:
    def __init__(self):
        self.prob_pair_v: List[ProbPair] = []  # 存储 ProbPair 对象的列表

    def sort_vec(self):
        self.prob_pair_v.sort(key=lambda pp: pp.probability)  # 按概率排序

    def push_back(self, pp: ProbPair):
        self.prob_pair_v.append(pp)  # 添加 ProbPair 对象

# Zipf样本类
class Zipf:
    def __init__(self, a, b, s):
        q = 1 - s
        self.qInv = 1 / q
        self.aPowQ = math.exp(math.log(a) * q)
        bPowQ = math.exp(math.log(b) * q)

        self.c = q / (bPowQ - self.aPowQ)
        self.qDivC = q / self.c

    def float64(self, u):
        ln = math.log(u * self.qDivC + self.aPowQ)
        t = math.exp(ln * self.qInv)
        return t

#endregion

#############################################################

#region ---------------------初始化相关----------------------
# 创建函数实例 4个函数 2种语言
def create_func(func_type: int):
    """
    根据类型创建新的函数实例
    - func_type: 函数类型(1-4对应不同容器)
    """
    if func_type == 1:
        container_1 = Function(
            type=1,
            processTime=0.035,
            coldStartTime=5.31,
            size=55,
            lang_type=1,
            bare_size=10,
            lang_size=30,
            user_size=15
        )
        return container_1, None 
     
    elif func_type == 2:
        container_2 = Function(
            type=2,
            processTime=0.063,
            coldStartTime=5.33,
            size=158,
            lang_type=2,
            bare_size=10,
            lang_size=50,
            user_size=98
        )
        return container_2, None
    
    elif func_type == 3:
        container_3 = Function(
            type=3,
            processTime=0.020,
            coldStartTime=5.34,
            size=332,
            lang_type=1,
            bare_size=10,
            lang_size=170,
            user_size=152
        )
        return container_3, None
    
    elif func_type == 4:
        container_4 = Function(
            type=4,
            processTime=2.076,
            coldStartTime=4.89,
            size=92,
            lang_type=2,
            bare_size=10,
            lang_size=40,
            user_size=42
        )
        return container_4, None
    
    return None, "Fail to getFunc...."

def init_func_map():
    fi = FunctionInfo(0.035, 5.31, 55, 1, 10, 30, 15)
    funcInfoMap_G.add(1, fi)

    fi = FunctionInfo(0.063, 5.33, 158, 2, 10, 50, 98)
    funcInfoMap_G.add(2, fi)

    fi = FunctionInfo(0.020, 5.34, 332, 1, 10, 170, 152)
    funcInfoMap_G.add(3, fi)

    fi = FunctionInfo(2.076, 4.89, 92, 2, 10, 40, 42)
    funcInfoMap_G.add(4, fi)

# 获取容器大小
def get_container_size(funcType: int) -> int:

    return funcInfoMap_G.get_size(funcType)
# 初始化分层缓存
def init_layer_map():
    global layer_map_G, topo_G
    for node_id in topo_G.nodes:
        layer_map_G[node_id] = Layer_Cache(node_id)
#endregion

#region ----------------------拓扑相关-----------------------
def load_topo():
    try:
        f = open(config_G.topo_file, 'r')
        # print("open topo file succeessfully.....")
        logging.info("open topo file succeessfully.....")
    except FileNotFoundError:
        print(f"无法找到拓扑文件 {config_G.topo_file}")
        return  # 如果文件未找到，直接返回

    csv_reader = csv.reader(f)
    node_id = 1
    for index, row in enumerate(csv_reader):
        if index == 0:  # 跳过标题行
            continue
        if row[0] == "":  # 跳过空行
            continue

        # 解析 CSV 行并创建 PhyNode 实例
        lat = float(row[1])
        long = float(row[2])

        loc = Location()
        loc.init(lat, long)
        node_map_G[node_id] = loc
        # 创建 PhyNode 实例并添加到 topo_G.nodes
        phynode = PhyNode(
            id=node_id,
            lat=lat,
            long=long,
            mem=config_G.mem_cap,          
            cpuFreq=config_G.cpu_Freq      
        )
        logging.info(f"node {node_id} lat {lat} long {long}")
        topo_G.add_node(node_id, phynode)
        node_id += 1
    f.close()
    return

def update_topo(operator: str, phyNodeID: int, mem: float):
    if operator == "add":
        topo_G.update("add", phyNodeID, mem)  # 增加内存
    elif operator == "minus":
        topo_G.update("minus", phyNodeID, mem)  # 减少内存

def get_phy_node(ingress_id: int):
    """根据入口节点 ID 获取物理节点"""
    for node in topo_G.nodes.values():
        if node.id == ingress_id:
            return node, None
    logging.error(f"phyNode {ingress_id} does not exist.....")
    return None, f"Non-exist phyNode {ingress_id}"  

def sort_phynodes(request: Request) -> DistSlice:
    """
    根据请求到物理节点的距离对节点进行排序
    """
    ds = DistSlice() 
    for node in topo_G.nodes.values():
        dist = calculate_distance(request.ingress, node)  # 计算距离
        d = Distance(phyNodeID=node.id, distance=dist)  # 实例化 Distance 对象
        ds.slice.append(d)  # 将 Distance 对象添加到 DistSlice 的切片中

    # 按距离排序
    ds.slice.sort(key=lambda d: d.distance)  # 使用 Python 的 sort 方法进行排序

    return ds  # 返回排序后的 DistSlice 对象

def calculate_distance(phyNode1: PhyNode, phyNode2: PhyNode, unit: str = "K") -> float:
    """
    计算两个物理节点之间的距离
    """
    #print(f"phyNode1 type: {type(phyNode1)}")
    #print(f"phyNode2 type: {type(phyNode2)}")
    lat1 = phyNode1.lat
    lng1 = phyNode1.long
    lat2 = phyNode2.lat
    lng2 = phyNode2.long

    # 如果坐标相同，距离为0
    if lat1 == lat2 and lng1 == lng2:
        return 0.0

    radlat1 = math.radians(lat1)
    radlat2 = math.radians(lat2)

    theta = lng1 - lng2
    radtheta = math.radians(theta)

    dist = math.sin(radlat1) * math.sin(radlat2) + math.cos(radlat1) * math.cos(radlat2) * math.cos(radtheta)
    if dist > 1:
        dist = 1

    dist = math.acos(dist)
    dist = math.degrees(dist)
    dist = dist * 60 * 1.1515  # 转换为英里

    if unit == "K":
        dist = dist * 1.609344  # 转换为公里
    elif unit == "N":
        dist = dist * 0.8684  # 转换为海里

    return dist

def show_nodes_memory():
        # print("----node memomry----")
        logging.info("----node memomry----")
        for node_id in topo_G.nodes:        
            logging.info(f"node {node_id} : {topo_G.nodes[node_id].mem} MB")
        # for i in range(len(topo_G.nodes)):
        #     print(f"node {i} : {topo_G.nodes[i].mem} MB") 
#endregion

#region --------------------请求相关(S-Cache)----------------
""" # 使用Zipf分布生成请求
def gen_req_zipf(request_file: str):
    rFile = load_request(request_file, config_G.redu_factor)  # 加载请求数据
    # load_requests_to_csv(rFile, './result/load_requests.csv')

    for i in range(len(rFile.app1)):  # 遍历应用1请求
    # for i in range(1):  # 遍历应用1请求
        sample = generate_zipf_samples(config_G.node_num, rFile.app1[i], config_G.beta)  # 生成Zipf分布样本
        gen_requests(sample, 1, i)  # 生成请求

    for i in range(len(rFile.app2)):  
        sample = generate_zipf_samples(config_G.node_num, rFile.app2[i], config_G.beta)
        gen_requests(sample, 2, i)  

    for i in range(len(rFile.app3)): 
        sample = generate_zipf_samples(config_G.node_num, rFile.app3[i], config_G.beta)  
        gen_requests(sample, 3, i)  

    for i in range(len(rFile.app4)): 
        sample = generate_zipf_samples(config_G.node_num, rFile.app4[i], config_G.beta)  
        gen_requests(sample, 4, i) 

def load_requests_to_csv(rFile: RequestFile, output_file: str):
    with open(output_file, mode='w', newline='') as file:
        writer = csv.writer(file)
        
        # 写入CSV头
        writer.writerow(['Time', 'App1 Requests', 'App2 Requests', 'App3 Requests', 'App4 Requests'])
        
        # 找到最大请求数
        max_length = max(len(rFile.app1), len(rFile.app2), len(rFile.app3), len(rFile.app4))
        
        for i in range(max_length):
            row = [rFile.time[i] if i < len(rFile.time) else '']  # 时间
            row.append(rFile.app1[i] if i < len(rFile.app1) else 0)  # 应用1请求
            row.append(rFile.app2[i] if i < len(rFile.app2) else 0)  # 应用2请求
            row.append(rFile.app3[i] if i < len(rFile.app3) else 0)  # 应用3请求
            row.append(rFile.app4[i] if i < len(rFile.app4) else 0)  # 应用4请求
            writer.writerow(row)

def load_request(rFile: str, reduct_factor: float) -> RequestFile:
    data = RequestFile()  # 初始化请求数据对象
    try:
        with open(rFile, mode='r') as file:
            # print("-----open request file succeessfully-----")
            logging.info("-----open request file succeessfully-----")   
            reader = csv.reader(file)
            csv_lines = list(reader)
    except FileNotFoundError:
        print(f"无法找到请求文件 {rFile}")
        return data  # 返回空数据

    for index, line in enumerate(csv_lines):
        if index == 0:  # 第一行是时间
            for value in line:
                value = float(value)  # 解析为浮点数
                data.time.append(int(value))  # 添加到时间列表
        elif index == 1:
            for value in line:
                value = float(value)  # 解析为浮点数
                data.app1.append(int(value / reduct_factor))  # 添加到应用1请求列表
        elif index == 3:
            for value in line:
                value = float(value)  
                data.app2.append(int(value / reduct_factor))  # 添加到应用2请求列表
        elif index == 5:
            for value in line:
                value = float(value)  
                data.app3.append(int(value / reduct_factor))  # 添加到应用3请求列表
        elif index == 7:
            for value in line:
                value = float(value) 
                data.app4.append(int(value / reduct_factor))  # 添加到应用4请求列表
    file.close()
    return data  # 返回请求数据对象
# 生成符合Zipf分布的样本
def generate_zipf_samples(node_num: int, max_num: int, beta: float) -> List[int]:
    node_num += 1
    a = float(1)
    b = float(node_num)
    if beta == 1:
        beta += 0.01  
    s = float(beta)
    z = new_zipf(a,b,s)
    
    sample_cnt = float(max_num)
    sample = [0] * node_num  
    for u in np.arange(0, 1, 1 / sample_cnt):
        x = int(z.float64(u))
        if x < 0 or x >= node_num:
            continue
        sample[x] += 1

    sample = sample[1:]  

    if len(sample) > node_num - 1:
        sample = sample[:node_num - 1]

    return sample

def gen_request(type: int, IngressID: int, arriveTime: int):
    global req_count_G
    r = Request()  # 初始化请求对象

    # 创建函数实例
    function, err = create_func(type)
    if err:
        logging.error(f"Fail to create_func: {err}")
        return None, err 

    # 获取物理节点
    ingress, err = get_phy_node(IngressID)
    if err:
        logging.error(f"Fail to genRequest: {err}")
        return None, err  

    # 创建请求对象
    r = Request(
        id=req_count_G,
        function=function,
        ingress=ingress,
        arriveTime=arriveTime,
        served=False,
        isColdStart=False,
        deployNode=None
    )
    req_count_G += 1

    return r, None  # 返回请求对象和无错误信息

def gen_requests(sample: List[int], type: int, timeSlot: int):
    for i in range(1, len(sample)):  # 遍历样本从1开始
        for j in range(sample[i]):  # 根据样本值生成请求
            request, _ = gen_request(type, i, timeSlot)  # 生成请求
            requestsMap_G.add(request, timeSlot)  # 将请求添加到请求映射

def new_zipf(a, b, s):
    return Zipf(a, b, s)
 """
#endregion

#region ------------------请求相关(Cross-Edge)---------------
# 加载Zipf请求文件
def read_requests(request_file: str):
    try:
        with open(request_file, mode='r') as file:
            # print("open request file succeessfully.....")
            logging.info("open request file succeessfully.....")

            ront = Req_on_Nodes_Time()  # 存储每个时间槽的请求分布
            funcType = 0  # 函数类型计数器（1-4）

            for line in file:
                line = line.strip()
                if "funcType" in line:
                    if funcType == 0:
                        funcType += 1
                        continue  # 跳过第一个标题行
                    logging.info(f"add functype {funcType}")
                    # 将当前收集的请求数据存入全局变量
                    rontt_G.add(funcType, ront)
                    ront.clear    # 重置时间序列
                    funcType += 1
                    continue

                # 解析每个节点的请求数量（逗号分割）
                count = 1
                ron = Req_on_Nodes()
                for req_num in line.split(","):
                    logging.info(f"node {count} req_num: {req_num}")
                    count += 1
                    ron.add(int(req_num))
                ront.add(ron)  # 添加到当前时间槽

            # 所有结束时添加函数类型4
            if funcType > 0:
                logging.info(f"add functype {funcType}")
                rontt_G.add(funcType, ront)

            file.close()
            return

    except FileNotFoundError:
        print(f"无法打开请求文件: {request_file}")
        return    

# 通过rontt_G创建所有请求
def create_requests():
    # 遍历 rontt_G 中的每个函数类型
    for func_type, req_on_nodes_time in rontt_G.numMap.items():
        # 遍历每个时间槽
        for time_slot, req_on_nodes in enumerate(req_on_nodes_time.numVector):
            # 为每个时间槽创建请求
            create_request_in_slot(time_slot, func_type, req_on_nodes)

def create_request_in_slot(timeSlot: int, func_type: int, req_on_nodes: Req_on_Nodes):
    # 遍历每个节点的请求数量
    for node_index, req_num in enumerate(req_on_nodes.numVector):
        node_id = node_index + 1  # 假设节点ID从1开始
        req_num = int(req_num / config_G.redu_factor)  # 根据减少因子调整请求数量

        # 为每个请求数量生成请求
        for _ in range(req_num):
            request, err = create_request(timeSlot, func_type, node_id)
            if not err:
                requestsMap_G.add(request, timeSlot)

def create_request(time_slot: int, func_type: int, ingress_id: int):
    global req_count_G
    # 获取物理节点位置
    loc = node_map_G.get(ingress_id)
    if loc:
        # 创建 PhyNode 实例
        ingress = PhyNode(
            id=ingress_id,
            lat=loc.latitude,
            long=loc.longitude,
            mem=config_G.mem_cap,
            cpuFreq=config_G.cpu_Freq
        )

        # 获取函数信息
        function_info = funcInfoMap_G.func_map.get(func_type)
        if function_info:
            # 创建函数实例
            function = Function(
                type=func_type,
                processTime=function_info.processTime,
                coldStartTime=function_info.coldStartTime,
                size=function_info.size,
                creation_time=time_slot,
                lang_type=function_info.lang_type,
                bare_size=function_info.bare_size,
                lang_size=function_info.lang_size,
                user_size=function_info.user_size
            )

            # 创建请求对象
            request = Request(
                id=req_count_G,
                function=function,
                ingress=ingress,
                arriveTime=time_slot,
                served=False,
                isColdStart=False,
                deployNode=None
            )
            req_count_G += 1
            return request, None
        else:
            logging.error(f"Cannot find funcType {func_type}")
            return None, f"Cannot find funcType {func_type}"
    else:
        logging.error(f"Non-exist phyNode {ingress_id}")
        return None, f"Non-exist phyNode {ingress_id}"
#endregion

#region ----------------------系统成本----------------------
# 获取函数的实例化成本
def get_instan_cost(phyNodeID: int, funcType: int) -> float:
    """
    获取某个物理节点中某个函数类型的实例化成本
    """
    cpu_freq = get_cpu(phyNodeID)
    size = get_container_size(funcType)

    if cpu_freq == 0:
        return 0

    instan_cost = size / cpu_freq * 0.01

    if instan_cost == 0:
        logging.info("instant cost is 0")

    return instan_cost

# 获取节点的CPU频率
def get_cpu(phyNodeID: int) -> float:
    """
    获取某个物理节点的CPU频率
    """
    phy_node = topo_G.get(phyNodeID)
    if phy_node.id == 0:
        logging.info("Cannot find the node 0")
        return 0
    else:
        return phy_node.cpuFreq

# 获取运行成本
def get_run_cost(phyNodeID: int, funcType: int) -> float:
    """
    获取某个物理节点中某个函数类型的运行成本
    """
    cpu_freq = get_cpu(phyNodeID)
    size = get_container_size(funcType)

    if cpu_freq == 0:
        logging.info("run cost cpuFreq is 0")
        return 0

    run_cost = size * cpu_freq * config_G.alpha * 0.01

    return run_cost
#endregion

#region ----------------------预热保活----------------------
def prewarm_containers():
    """
    根据函数调用频率预热容器
    预热高频函数的User层（必要时会创建依赖的Bare层和Lang层）
    """
    global layer_map_G, topo_G, funcInfoMap_G, clock_G
    
    # 获取函数调用频率
    function_frequencies = {}
    for func_type, freq in functionfreq_G.map.items():
        function_frequencies[func_type] = freq
    
    # 按频率排序
    sorted_functions = sorted(function_frequencies.items(), key=lambda x: x[1], reverse=True)
    
    # 为每个节点预热容器
    for node_id, node in topo_G.nodes.items():
        # 获取节点的可用内存
        node_memory = node.getMem()
        
        # 确保节点内存足够
        if node_memory <= 0:
            logging.warning(f"node {node_id} memory empty ({node_memory} MB), skip prewarm")
            continue
            
        available_memory = node_memory
        
        # 获取或创建节点的分层缓存
        if node_id not in layer_map_G:
            layer_map_G[node_id] = Layer_Cache(node_id)
        
        layered_cache = layer_map_G[node_id]
        
        # 只预热User层（按照函数调用频率排序）
        for func_type, _ in sorted_functions:
            # 检查可用内存
            available_memory = node.getMem()
            if available_memory <= 0:
                logging.warning(f"node {node_id} memory empty ({available_memory} MB), skip prewarm")
                break
                
            # 如果该函数类型的User层已存在，则跳过
            if func_type in layered_cache.user_layers:
                continue
                
            # 获取函数信息
            if func_type not in funcInfoMap_G.func_map:
                continue
                
            function_info = funcInfoMap_G.func_map[func_type]
            user_size = function_info.user_size
            lang_type = function_info.lang_type
            lang_size = function_info.lang_size
            bare_size = 10  # 假设bare层大小固定为10
            
            # 计算所需总内存
            required_memory = 0
            
            # 如果没有Bare层，需要创建
            if not layered_cache.bare_layers:
                required_memory += bare_size
            
            # 如果没有对应的Lang层，需要创建
            if lang_type not in layered_cache.lang_layers:
                required_memory += lang_size
            
            # 加上User层所需内存
            required_memory += user_size
            
            # 检查总内存是否足够
            if available_memory < required_memory:
                continue
            
            # 创建必要的Bare层（如果不存在）
            if not layered_cache.bare_layers:
                bare_layer = BareLayer(bare_size, clock_G)
                layered_cache.bare_layers[0] = bare_layer
                layered_cache.total_memory_usage += bare_size
                available_memory -= bare_size
                update_topo("minus", node_id, bare_size)
            
            # 创建必要的Lang层（如果不存在）
            if lang_type not in layered_cache.lang_layers:
                lang_layer = LangLayer(
                    lang_size, lang_type, 
                    layered_cache.bare_layers[0], clock_G)
                layered_cache.lang_layers[lang_type] = lang_layer
                layered_cache.total_memory_usage += lang_size
                available_memory -= lang_size
                update_topo("minus", node_id, lang_size)
            
            # 创建User层
            user_layer = UserLayer(
                user_size, func_type,
                layered_cache.lang_layers[lang_type], clock_G
            )
            layered_cache.user_layers[func_type] = user_layer
            layered_cache.total_memory_usage += user_size
            update_topo("minus", node_id, user_size)

# 目前固定TTL=10
def calculate_keep_alive_time(container_layer: ContainerLayer, func_type: int = None) -> int:
    """
    计算容器的保活时间（TTL）
    TTL = min(IAT, β)，其中 β = (α * t)/((1-α) * m)
    """
    global config_G, funcInfoMap_G
    
    # 获取容器内存占用
    memory_usage = container_layer.size
    
    # 设置不同层冷启动时间的比例
    BARE_RATIO = 0.2   # Bare层占总冷启动时间的20%
    LANG_RATIO = 0.3   # Lang层占总冷启动时间的30%
    USER_RATIO = 0.5   # User层占总冷启动时间的50%
    
    # 获取启动延迟时间
    startup_delay = 0
    cold_start_time = 0
    
    # 获取函数的总冷启动时间
    if func_type is not None and func_type in funcInfoMap_G.func_map:
        cold_start_time = funcInfoMap_G.func_map[func_type].coldStartTime
    
    # 根据层类型分配启动延迟
    if isinstance(container_layer, UserLayer):
        startup_delay = cold_start_time * USER_RATIO
    elif isinstance(container_layer, LangLayer):
        startup_delay = cold_start_time * LANG_RATIO
    elif isinstance(container_layer, BareLayer):
        startup_delay = cold_start_time * BARE_RATIO
    
    alpha = config_G.alpha  
    beta = (alpha * startup_delay) / ((1 - alpha) * memory_usage)
    
    # 预测的请求间隔时间（IAT）
    # 这里可以基于历史数据使用泊松分布预测，简化实现可以使用函数调用频率的倒数
    iat = 10  # 默认IAT值
    if func_type is not None and func_type in functionfreq_G.map and functionfreq_G.map[func_type] > 0:
        iat = max(5, 100 / functionfreq_G.map[func_type])  # 防止频率过低导致IAT过大
        # 这里func_type为KeyError: None
        # print(f"freq:{functionfreq_G.map[func_type]}")
    
    # 返回 min(IAT, β)
    # IAT范围为5-10是否合理
    ttl = min(iat, beta * 10000)    # 乘以系数调整时间单位
    # print(f"iat: {iat}, beta:{beta*10000}")
    # return max(5, int(ttl))        
    return 10 

def update_keep_alive():
    """
    在每个时间间隔结束时，更新优先级，将活动函数移动到缓存函数，并更新分层容器的保活状态
    容器超时策略，Bare层直接销毁，User/Lang层降级处理并设置新的超时时间
    """
    global clock_G, activeFunctions_G, cacheMap_G, layer_map_G
    
    # 将活动函数添加到缓存，更新优先级，并从活动函数中删除
    # 遍历所有节点上的活动函数
    for phyNodeID, node_functions in activeFunctions_G.map.items():
        # 遍历节点上各种类型的函数
        for funcType, functions in node_functions.functions.items():
            for i in range(len(functions.slice)):
                f = functions.slice[i]
                cacheMap_G.add(f)

    cacheMap_G.sort()
    activeFunctions_G.map.clear()  # 清空活动函数列表

    # 更新分层容器的保活状态
    for node_id, layer_cache in layer_map_G.items():
        # 处理User层容器
        for func_type, user_layer in list(layer_cache.user_layers.items()):
            # 计算User层的保活时间
            ttl = calculate_keep_alive_time(user_layer, func_type)
            
            # 如果超过保活时间，考虑降级或销毁
            if clock_G - user_layer.last_used_time > ttl:
                # 释放User层占用的内存
                user_size = user_layer.size
                layer_cache.total_memory_usage -= user_size
                update_topo("add", node_id, user_size)
                # 从分层缓存中删除
                del layer_cache.user_layers[func_type]
                cacheMap_G.delete(node_id, func_type)                
                logging.info(f"node {node_id} func_type {func_type} User Layer downgrade")
        
        # 处理Lang层容器
        for lang_type, lang_layer in list(layer_cache.lang_layers.items()):
            # 检查是否有User层引用该Lang层
            if not any(ul.lang_layer == lang_layer for ul in layer_cache.user_layers.values()):
                # 计算Lang层的保活时间
                ttl = calculate_keep_alive_time(lang_layer)
                
                # 如果超过保活时间，考虑降级或销毁
                if clock_G - lang_layer.last_used_time > ttl:
                    # 释放Lang层占用的内存
                    lang_size = lang_layer.size
                    layer_cache.total_memory_usage -= lang_size
                    update_topo("add", node_id, lang_size)
                    
                    # 从Lang层缓存中删除
                    del layer_cache.lang_layers[lang_type]
                    
                    # 降级后不需要立即删除Bare层，会在Bare层的处理中检查
                    logging.info(f"node {node_id} lang_type {lang_type} Lang Layer downgrade")
        
        # 处理Bare层容器
        if layer_cache.bare_layers and not layer_cache.lang_layers:
            # 如果没有Lang层引用Bare层，可以考虑销毁
            if 0 in layer_cache.bare_layers:
                bare_layer = layer_cache.bare_layers[0]
                
                # 计算Bare层的保活时间
                ttl = calculate_keep_alive_time(bare_layer)
                
                # Bare层是最底层，超时后直接销毁
                if clock_G - bare_layer.last_used_time > ttl:
                    # 释放Bare层占用的内存
                    bare_size = bare_layer.size
                    layer_cache.total_memory_usage -= bare_size
                    update_topo("add", node_id, bare_size)
                    
                    # 清空Bare层缓存
                    layer_cache.bare_layers.clear()
                    logging.info(f"node {node_id} Bare Layer kill")

def update_cache():
    """
    在每个时间间隔结束时，更新全容器/User层优先级，将活动函数移动到缓存函数
    对于FC策略，在此处检查并删除过期容器
    """
    # 遍历所有节点上的活动函数
    for phyNodeID, node_functions in activeFunctions_G.map.items():
        # 遍历节点上各种类型的函数
        for funcType, functions in node_functions.functions.items():
            for i in range(len(functions.slice)):
                f = functions.slice[i]
                cacheMap_G.add(f)
    
    cacheMap_G.sort()
    activeFunctions_G.map.clear()
    
    # 如果使用FC策略，检查并删除过期容器
    if config_G.cache_method == "FC":
        # 遍历所有节点，处理缓存中的函数生命周期
        for phyNodeID in list(cacheMap_G.caches.keys()):
            if phyNodeID in cacheMap_G.caches:
                cache = cacheMap_G.caches[phyNodeID]
                i = 0
                while i < len(cache.functionList):
                    # 减少生命周期
                    if cache.functionList[i].minusLife():
                        # 如果生命周期结束，释放内存并删除函数
                        function = cache.functionList[i]
                        cache.functionList.pop(i)
                        # 更新节点内存
                        update_topo("add", phyNodeID, function.size)
                    else:
                        i += 1

#endregion

#region ----------------------请求分发----------------------
def deploy_request(request: Request):
    """
    部署单个请求
    """
    global deploy_current_req_G, deploy_neighbor_req_G, total_cold_req_G

    # 根据请求到物理节点的距离，对节点进行排序
    ds = sort_phynodes(request)

    # 使用不同的缓存策略
    if config_G.cache_method == "Layer-Cache":
        # 使用分层缓存策略部署请求
        deploy_request_layered(request)
    else:
        # 首先尝试在当前节点部署
        if deploy_to_current(request):
            deploy_current_req_G += 1
            return
        
        # 如果当前节点无法部署，尝试邻近节点
        if deploy_to_neighbour(ds, request):
            deploy_neighbor_req_G += 1
            return
        
        # 如果无法直接部署，尝试在当前节点创建容器
        total_cold_req_G += 1
        if config_G.cache_method == "LRU":
            create_to_current_lru(request)
        elif config_G.cache_method == "FC":
            create_to_current_fc(request)
        elif config_G.cache_method == "Cross-Edge":
            create_to_current_ce(request)
        else:   # S-Cache
            create_to_current(request)   

def deploy_requests():
    global total_req_count_G, clock_G   

    for i in range(1, config_G.slot_num + 1):
        requests, found = requestsMap_G.get(i)  # 从请求映射中获取当前时间槽的请求
        if not found:
            print(f"--------cannot find time slot {i}--------")
            continue

        # 获取当前时间槽的请求
        for request in requests:
            deploy_request(request)
            total_req_count_G += 1 
    
        clock_G += 1
        update_cache()
    
    show_nodes_memory()

def deploy_to_current(request: Request):
    """
    尝试在当前节点部署请求
    如果节点上有匹配的容器，则使用该容器处理请求
    返回True表示成功部署，False表示节点无匹配容器
    """
    # 寻找当前节点上可用的容器
    f, i = cacheMap_G.get_idle_function(request.ingress.id, request.function.type)
    
    if i != -1:
        # 当前节点有可用容器，直接部署
        place_to_current(request, f, i)
        return True
    
    return False  # 当前节点没有可用容器

def place_to_current(request: Request, function: Function, i: int):
    """
    将请求分配给当前节点的缓存容器
    """
    topo_G.addFreqAll(request.function.type)
    topo_G.setRecencyAll(request.function.type, float(request.arriveTime))
    activeFunctions_G.add(function, request.ingress.id)  
    cacheMap_G.delete(request.ingress.id, i)  
    request.update(function, request.ingress, False, 0) 

# 转发给邻居节点的条件可能需要修改
def deploy_to_neighbour(distances: DistSlice, request: Request):
    """
    尝试将请求分配给邻近的缓存函数
    """
    global b
    for slice in distances.slice[1:]:
        link_delay = slice.distance * config_G.latency_para
        if config_G.cache_method != "S-Cache": # Cross-Edge, FC, LRU
            node_id = slice.get_id()
            instanCost = get_instan_cost(node_id, request.function.type)
            # if slice.distance * config_G.latency_para > instanCost:
            #     logging.info(f"latency: {link_delay}, instanCost: {instanCost}")
            # Cross-Edge 传输延迟 < 实例化成本
            if link_delay < instanCost:
                f, i = cacheMap_G.get_idle_function(slice.phyNodeID, request.function.type)
                if i != -1:
                    place_to_neighbour(request, f, i, slice.phyNodeID, link_delay)
                    return True

        else: # S-Cache
            # if slice.distance * config_G.latency_para > request.function.coldStartTime:
                # logging.info(f"latency: {link_delay}, coldStartTime: {request.function.coldStartTime}")
            # S-Cache 传输延迟 < 冷启动时间
            if link_delay < request.function.coldStartTime:
                f, i = cacheMap_G.get_idle_function(slice.phyNodeID, request.function.type)
                if i != -1:
                    place_to_neighbour(request, f, i, slice.phyNodeID, link_delay)
                    return True
            
    return False

def place_to_neighbour(request: Request, function: Function, i: int, phyNodeID: int, link_delay: float):
    """
    将请求分配给邻近的缓存函数
    """
    topo_G.addFreqAll(function.type)
    topo_G.setRecencyAll(function.type, float(request.arriveTime))
    activeFunctions_G.add(function, phyNodeID) 
    cacheMap_G.delete(phyNodeID, i)  
    
    deploy_node = topo_G.get(phyNodeID)  # 获取物理节点
    if deploy_node.id == 0:
        return
    request.update(function, deploy_node, False, link_delay)  # 更新请求状态

#endregion

#region ----------------------缓存方法----------------------
# 基于优先级的User层驱逐策略
def evict_user_layer(request: Request):
    """
    当内存不足时，尝试驱逐低优先级容器来为新请求腾出空间
    参考create_to_current中的驱逐容器逻辑
    返回值: 如果成功腾出足够空间则返回True，否则返回False
    """
    node_id = request.ingress.id
    # 更新函数优先级，用于与现有容器比较
    request.function.active_priority()
    
    # 所需内存
    required_memory = request.function.size
    
    # 如果分层缓存需要的内存
    if hasattr(request.function, 'bare_size') and hasattr(request.function, 'lang_size') and hasattr(request.function, 'user_size'):
        if node_id in layer_map_G:
            layered_cache = layer_map_G[node_id]
            # 检查是否需要创建Bare层
            if not layered_cache.bare_layers:
                required_memory = request.function.bare_size
            
            # 检查是否需要创建Lang层
            if request.function.lang_type not in layered_cache.lang_layers:
                required_memory += request.function.lang_size
            
            # 需要创建User层
            required_memory += request.function.user_size
    
    success = False
    
    # 如果节点内存不足，开始驱逐过程
    while required_memory > topo_G.nodes[node_id].mem:
        # 获取最低优先级的容器
        lowest_priority = cacheMap_G.get_lowest_priority(node_id)
        
        # 如果请求优先级高于最低优先级容器，则驱逐该容器
        if lowest_priority < request.function.priority:
            f, succ_flag = cacheMap_G.delete_low_function(node_id)
            if succ_flag:
                # 如果驱逐成功，更新拓扑结构中的内存
                update_topo("add", node_id, f.size)
                # 如果已腾出足够空间，标记为成功并退出循环
                if required_memory <= topo_G.nodes[node_id].mem:
                    success = True
                    break
            else:
                # 如果驱逐失败，结束循环
                break
        else:
            # 如果没有优先级更低的容器，结束循环
            break
    
    return success

# 基于优先级的全容器驱逐策略
def create_to_current(request: Request):
    request.function.active_priority()  # 更新函数优先级
    f = Function() 
    succ_flag = True
    # 如果节点内存不足，先尝试删除低优先级容器
    if request.function.size > topo_G.nodes[request.ingress.id].mem:
        while succ_flag:
            # 获取最低优先级的容器
            lowest_priority = cacheMap_G.get_lowest_priority(request.ingress.id)
            # 且请求的优先级 > 优先级最低的容器
            if request.function.priority > lowest_priority:
                f, succ_flag = cacheMap_G.delete_low_function(request.ingress.id)
                if succ_flag:
                    update_topo("add", request.ingress.id, f.size)  # 更新内存
                # 如果销毁后的内存空间充足
                if request.function.size <= topo_G.nodes[request.ingress.id].mem:
                    request.function.phyNode = request.ingress  # 设置请求的物理节点为当前节点
                    activeFunctions_G.add(request.function, request.ingress.id)  # 更新活动函数列表
                    update_topo("minus", request.ingress.id, request.function.size)  # 更新内存
                    request.update(request.function, request.ingress, True, 0)  # 标记为冷启动
                    return
            else:
                return
    # 内存仍然不足，创建新容器
    else:
        request.function.phyNode = request.ingress  # 设置请求的物理节点为当前节点
        activeFunctions_G.add(request.function, request.ingress.id)  # 更新活动函数列表
        update_topo("minus", request.ingress.id, request.function.size)  # 更新内存
        request.update(request.function, request.ingress, True, 0)  # 标记为冷启动

# 基于LRU策略驱逐容器
def create_to_current_lru(request: Request):
    succ_flag = True
    count = 0
    # 检查请求的函数大小超过当前节点的内存容量，尝试驱逐容器
    if request.function.size > topo_G.nodes[request.ingress.id].mem:
        while count < 1000:
            count += 1
            
            # 应用LRU策略删除最近最少使用的容器
            function, succ_flag = cacheMap_G.delete_lru_function(request.ingress.id)
            
            # 如果没有可删除的容器，则退出
            if not succ_flag:
                return
                
            # 避免驱逐与当前请求相同类型的容器
            if function.type == request.function.type:
                continue
                
            # 更新节点内存信息
            update_topo("add", request.ingress.id, function.size)
            
            # 如果腾出足够空间，部署请求
            if request.function.size <= topo_G.get(request.ingress.id).mem:
                request.function.phyNode = request.ingress
                request.function.lastUseTime = clock_G  # 设置最后使用时间
                activeFunctions_G.add(request.function, request.ingress.id)
                update_topo("minus", request.ingress.id, request.function.size)
                request.update(request.function, request.ingress, True, 0)
                return
    else:
        # 直接部署请求
        request.function.phyNode = request.ingress
        request.function.lastUseTime = clock_G  # 设置最后使用时间
        activeFunctions_G.add(request.function, request.ingress.id)
        update_topo("minus", request.ingress.id, request.function.size)
        request.update(request.function, request.ingress, True, 0)

# 基于固定缓存周期策略
def create_to_current_fc(request: Request):
    """
    使用固定缓存（FC）策略在当前节点创建新容器
    容器将保持固定时间（lifeTime）的活动状态
    1. 如果内存不足，尝试删除生命周期最低的容器，直到有足够空间
    2. 如果仍然无法腾出足够空间，拒绝服务
    """
    # 检查节点内存是否足够
    if request.function.size > topo_G.get(request.ingress.id).mem:
        # 尝试删除生命周期最低的容器直到有足够空间
        count = 0  # 避免无限循环
        
        while count < 1000:
            count += 1
            
            # 获取并删除生命周期最低的函数
            function, success = cacheMap_G.delete_low_lifetime_function(request.ingress.id)
            
            if success:
                # 如果成功删除容器，增加可用内存
                update_topo("add", request.ingress.id, function.size)
            else:
                # 没有更多容器可删除
                return
            
            # 检查是否已有足够空间
            if request.function.size <= topo_G.get(request.ingress.id).mem:
                # 有足够空间，创建新容器
                request.function.phyNode = request.ingress
                request.function.creation_time = clock_G  # 记录创建时间
                request.function.activeLifeTime(10)  # 设置容器生命周期为10
                activeFunctions_G.add(request.function, request.ingress.id)
                update_topo("minus", request.ingress.id, request.function.size)
                request.update(request.function, request.ingress, True, 0)
                return
    else:
        # 内存足够，直接创建容器
        request.function.phyNode = request.ingress
        request.function.creation_time = clock_G  # 记录创建时间
        request.function.activeLifeTime(10)  # 设置容器生命周期为10
        activeFunctions_G.add(request.function, request.ingress.id)
        update_topo("minus", request.ingress.id, request.function.size)
        request.update(request.function, request.ingress, True, 0)

# 基于驱逐概率的驱逐策略
def create_to_current_ce(request: Request):
    succ_flag = True
    count = 0
    # 检查请求的函数大小超过当前节点的内存容量，尝试驱逐容器
    if request.function.size > topo_G.nodes[request.ingress.id].mem:  
        while count < 1000: 
            count += 1
            func_type = get_evicted_container(request.ingress.id, request.function.type)
            
            # 要驱逐的容器类型与请求的相同或没有可驱逐的容器，则退出
            if func_type == request.function.type or func_type == 0: 
                return

            succ_flag = cacheMap_G.delete_Prob(request.ingress.id, func_type)
            function_size = get_container_size(func_type)
            
            # 如果删除成功，更新节点的内存信息
            if succ_flag:  
                update_topo("add", request.ingress.id, function_size)  # 更新内存
            
            # 节点内存足够以创建新的请求容器，标记为冷启动，更新信息
            if request.function.size <= topo_G.get(request.ingress.id).mem:  
                request.function.phyNode = request.ingress                                         
                activeFunctions_G.add(request.function, request.ingress.id)   
                topo_G.addFreqAll(request.function.type)                                             
                topo_G.setRecencyAll(request.function.type, float(request.arriveTime))             
                update_topo("minus", request.ingress.id, request.function.size)                        
                request.update(request.function, request.ingress, True, 0)                                
                return
            
            # 如果删除不成功且没有足够空间，则退出
            if not succ_flag: 
                return
    else:
        request.function.phyNode = request.ingress                              # 更新部署节点为当前节点
        activeFunctions_G.add(request.function, request.ingress.id)             # 将请求添加到活动函数列表中
        topo_G.addFreqAll(request.function.type)                                # 更新所有节点的频率信息
        topo_G.setRecencyAll(request.function.type, float(request.arriveTime))  # 更新所有节点的最近使用时间
        update_topo("minus", request.ingress.id, request.function.size)         # 更新节点内存信息               
        request.update(request.function, request.ingress, True, 0)              # 更新请求为冷启动

# 计算容器的驱逐概率(CS策略)
def get_prob(nodeID: int, funcType: int) -> float:
    phy_node = topo_G.get(nodeID)
    instan_cost = get_instan_cost(nodeID, funcType)
    freq = phy_node.getFreq(funcType)
    recency = phy_node.getRecency(funcType)
    # logging.info(f"functype: {funcType}, freq: {freq}, recent: {recency}")

    size = get_container_size(funcType)

    if freq == 0:
        logging.error(f"cannot find freq for funcType {funcType} at node {nodeID}")
        return 0
    if recency == 0:
        logging.error(f"cannot find recent for funcType {funcType} at node {nodeID}")
        return 0
    if size == 0:
        logging.error(f"cannot find size for funcType {funcType} at node {nodeID}")
        return 0

    # 将单位从MB转换，这将减少size在概率计算中的影响
    # return (float)size / (freq*instanCost + recent)
    return size / (freq + recency) / 1000

# 获取要被驱逐的容器类型(CS策略)
def get_evicted_container(nodeID: int, reqFuncType: int) -> int:
    # 计算请求函数类型的驱逐概率
    threshold = get_prob(nodeID, reqFuncType)

    # 创建一个字典来存储概率
    prob_map = {}
    total_prob = 0

    # 计算每个函数类型的概率
    for funcType in funcInfoMap_G.func_map.keys():
        prob = get_prob(nodeID, funcType)
        prob_map[funcType] = prob
        total_prob += prob

    # 将阈值转换为概率
    threshold = threshold / total_prob

    # 归一化概率
    prob_pair_vec = ProbPairVec()
    for funcType, prob in prob_map.items():
        normalized_prob = prob / total_prob
        prob_pair = ProbPair(funcType, normalized_prob)
        prob_pair_vec.push_back(prob_pair)

    # 对概率进行排序
    prob_pair_vec.sort_vec()

    # 生成一个随机数
    random_value = np.random.rand()
    # logging.info(f"Random number: {random_value}, Threshold: {threshold}")

    # 累积概率
    accum_prob = 0
    for prob_pair in prob_pair_vec.prob_pair_v:
        accum_prob += prob_pair.probability
        if random_value < accum_prob:
            logging.info(f"Evicting function type: {prob_pair.func_type}")
            return prob_pair.func_type

    logging.info("No need to evict")
    return 0  # 如果没有需要驱逐的容器，返回 0

# 检查容器是否过期（FC策略）
def is_container_expired(function: Function) -> bool:
    """
    根据容器的lifeTime判断是否过期
    lifeTime小于等于0表示容器已过期
    """
    return function.lifeTime <= 0

#endregion

# region ------------------ Layer_Cache---------------------
def deploy_requests_layered():
    """
    使用RainbowCake的分层容器缓存和共享机制部署请求
    """
    global total_req_count_G, clock_G
    
    for i in range(1, config_G.slot_num + 1):
        requests, found = requestsMap_G.get(i)  
        if not found:
            print(f"--------cannot find time slot {i}--------")
            continue
            
        logging.info(f"----------------timslot {i} end, num: {len(requests)}----------------")

        # 每10个时间槽预热一次容器
        if i == 1 or i % 10 == 0:
            prewarm_containers()

        # 处理当前时间槽的所有请求
        for request in requests:
            # 部署请求
            deploy_request_layered(request)
            total_req_count_G += 1 
        
        # 更新时钟、分层容器保活状态、User层调用频率及优先级
        clock_G += 1
        update_keep_alive()
    
    show_nodes_memory()

def deploy_request_layered(request: Request):
    """
    使用RainbowCake的分层容器缓存机制部署单个请求
    """
    global deploy_current_req_G, deploy_neighbor_req_G, total_cold_req_G
    # 根据请求到物理节点的距离，对节点进行排序
    ds = sort_phynodes(request)

    # 首先尝试在当前节点部署
    if deploy_to_current_layered(request):
        deploy_current_req_G += 1
        return
    
    # 如果当前节点无法部署，尝试邻近节点
    if deploy_to_neighbour_layered(ds, request):
        deploy_neighbor_req_G += 1
        return
    
    # 如果都无法部署，尝试创建新容器
    total_cold_req_G += 1
    create_to_current_layered(request)

def deploy_to_current_layered(request: Request) -> bool:
    """
    尝试在当前节点使用分层容器部署请求
    """
    global layer_map_G
    
    node_id = request.ingress.id
    
    # 获取或创建节点的分层缓存
    if node_id not in layer_map_G:
        layer_map_G[node_id] = Layer_Cache(node_id)
        logging.info(f"为节点 {node_id} 创建新的分层缓存")
    layered_cache = layer_map_G[node_id]

    function = request.function

    # 检查是否有User层
    f, i = cacheMap_G.get_idle_function(node_id, request.function.type)
    if i != -1:
        # 找到完全匹配的全容器，热启动
        # logging.info(f"node {node_id} find User Layer, hot start!")
        place_to_current(request, f, i)
        # 更新User层的使用时间
        if request.function.type in layered_cache.user_layers:
            layered_cache.user_layers[request.function.type].update_usage(clock_G)
            layered_cache.user_layers[request.function.type].shared_count += 1
        return True
    
    return False  # 无法在当前节点部署
a,b = 0,0
def deploy_to_neighbour_layered(distances: DistSlice, request: Request) -> bool:
    """
    尝试在邻近节点使用分层容器部署请求
    """
    global a, b
    for slice in distances.slice[1:]:  # 跳过第一个（当前节点）        
        node_id = slice.get_id()
        instanCost = get_instan_cost(node_id, request.function.type)
        link_delay = slice.distance * config_G.latency_para

        # if link_delay > instanCost and b == 0:
        if link_delay > instanCost:
            b += 1
            # print(f"node {node_id}, link_delay: {link_delay}, instanCost: {instanCost}")
        # 如果传输延迟小于冷启动时间，考虑使用该节点
        if link_delay < instanCost:
        # if link_delay < instanCost and a == 0:
            a += 1
            # print(f"node {node_id}, link_delay: {link_delay}, instanCost: {instanCost}")

            # 获取或创建节点的分层缓存
            if node_id not in layer_map_G:
                layer_map_G[node_id] = Layer_Cache(node_id)
            layered_cache = layer_map_G[node_id]
            
            # 检查是否有User层
            f, i = cacheMap_G.get_idle_function(node_id, request.function.type)
            if i != -1:
                # 找到完全匹配的全容器，热启动
                place_to_neighbour(request, f, i, node_id, link_delay)
                # 更新User层的使用时间
                if request.function.type in layered_cache.user_layers:
                    layered_cache.user_layers[request.function.type].update_usage(clock_G)
                    layered_cache.user_layers[request.function.type].shared_count += 1
                return True
            
    return False  # 无法在邻近节点部署

def create_to_current_layered(request: Request):
    """
    在当前节点创建新容器，使用RainbowCake的分层容器缓存机制
    """
    node_id = request.ingress.id
    
    # 获取或创建节点的分层缓存
    if node_id not in layer_map_G:
        layer_map_G[node_id] = Layer_Cache(node_id)
        logging.info(f"为节点 {node_id} 创建新的分层缓存")
    layered_cache = layer_map_G[node_id]
    
    # 计算需要的总内存
    function = request.function
    required_memory = 0.0
    
    # 检查是否需要创建Bare层
    if not layered_cache.bare_layers:
        required_memory += function.bare_size
        logging.info(f"create Bare Layer, size: {function.bare_size} MB")
    
    # 检查是否需要创建Lang层
    if function.lang_type not in layered_cache.lang_layers:
        required_memory += function.lang_size
        # logging.info(f"create Lang Layer, Lang type: {function.lang_type}, size: {function.lang_size} MB")
    
    # 需要创建User层
    required_memory += function.user_size
    # logging.info(f"create User Layer, function type: {function.type}, size: {function.user_size} MB")
    
    current_memory = topo_G.nodes[node_id].mem
    # logging.info(f"required memory: {required_memory} MB, current memory: {current_memory} MB")
    
    # 尝试使用基于优先级的驱逐User层策略
    if required_memory > current_memory:
        # logging.warning(f"node {node_id} need {required_memory} MB, only has {current_memory} MB")
        if evict_user_layer(request):
            logging.info(f"node {node_id} evict success")
        else: 
            logging.error(f"node {node_id} evict fail")
            return
    
    cold = 0.0

    # 创建Bare层(冷启动)
    if not layered_cache.bare_layers:            
        bare_layer = BareLayer(function.bare_size, clock_G)
        layered_cache.bare_layers[0] = bare_layer
        layered_cache.total_memory_usage += bare_layer.size
        cold = function.coldStartTime * 0.7
        update_topo("minus", node_id, bare_layer.size)
    
    # 创建Lang层(半温启动)
    if function.lang_type not in layered_cache.lang_layers:            
        lang_layer = LangLayer(
            function.lang_size,
            function.lang_type,
            layered_cache.bare_layers[0],
            clock_G
        )
        layered_cache.lang_layers[function.lang_type] = lang_layer
        layered_cache.total_memory_usage += lang_layer.size
        cold = function.coldStartTime * 0.5
        update_topo("minus", node_id, lang_layer.size)
    
    # 创建User层(温启动)        
    user_layer = UserLayer(
        function.user_size,
        function.type,
        layered_cache.lang_layers[function.lang_type],
        clock_G
    )
    layered_cache.user_layers[function.type] = user_layer
    layered_cache.total_memory_usage += user_layer.size
    cold = function.coldStartTime
    update_topo("minus", node_id, user_layer.size)

    # 创建函数实例
    f = Function(
        type=function.type,
        processTime=function.processTime,
        coldStartTime=cold,
        size=function.size,
        phyNode=request.ingress,
        lang_type=function.lang_type
    )

    request.function.phyNode = request.ingress  
    activeFunctions_G.add(f, node_id)
    request.update(function, request.ingress, True, 0)

#endregion

#region ----------------------打印结果----------------------
def print_result():
    print("----------------------Schedule End----------------------")
    logging.info("----------------------Schedule End----------------------")
    
    global total_req_count_G, served_req_count_G, cold_req_count_G
    global total_response_time_G, total_cost_G
    
    logging.info("current node deploy count: %d", deploy_current_req_G)
    logging.info("neighbor node deploy count: %d", deploy_neighbor_req_G)
    logging.info("try to create on current node count: %d", total_cold_req_G)
    print("部署在当前节点计数:", deploy_current_req_G)
    print("部署在邻居节点计数:", deploy_neighbor_req_G)
    print("尝试在当前节点创建新容器计数:", total_cold_req_G)
    '''
    activeFunctions_G.show()            # 显示活动函数  
    activeFunctions_G.show_priority()   # 显示活动函数优先级
    cacheMap_G.show()                   # 显示缓存函数
    cacheMap_G.show_priority()          # 显示缓存函数优先级
    '''

    total_comm_cost = 0     # 总通信成本
    total_instan_cost = 0   # 总实例化成本
    total_run_cost = 0      # 总运行成本
    total_cold_start_time = 0   # 冷启动总时间
    total_hot_start_time = 0    # 热启动总时间

    for i in range(1, requestsMap_G.size() + 1):
        if i in requestsMap_G.map:
            requests = requestsMap_G.map[i].requests
            for request in requests:
                # 跳过非被服务的请求
                if not request.served:
                    continue
                served_req_count_G += 1
                
                # 计算通信成本
                dist = calculate_distance(request.ingress, request.deployNode)
                comm_cost = dist * config_G.latency_para
                total_comm_cost += comm_cost

                # 计算运行成本
                run_cost = get_run_cost(request.deployNode.id, request.function.type)
                total_run_cost += run_cost

                # 计算请求响应时间
                base_response_time = request.function.processTime + request.linkDelay
                
                # 如果是冷启动，计算实例化成本和额外的冷启动响应时间
                instan_cost = 0
                if request.isColdStart:
                    cold_req_count_G += 1
                    instan_cost = get_instan_cost(request.deployNode.id, request.function.type)
                    total_instan_cost += instan_cost
                    # 冷启动时间 = 处理时间 + 链接延迟 + 冷启动时间
                    cold_start_response_time = base_response_time + request.function.coldStartTime
                    total_cold_start_time += cold_start_response_time
                else:
                    # 热启动时间 = 处理时间 + 链接延迟
                    hot_start_response_time = base_response_time
                    total_hot_start_time += hot_start_response_time
                
                # 计算单个请求的总成本
                total_cost_G += comm_cost + run_cost + instan_cost
    
    total_response_time_G = total_hot_start_time + total_cold_start_time
    
    # 计算平均成本
    avg_cost = total_cost_G / served_req_count_G 
    avg_comm_cost = total_comm_cost / served_req_count_G
    avg_instan_cost = total_instan_cost / served_req_count_G
    avg_run_cost = total_run_cost / served_req_count_G
    
    # 计算平均响应时间
    avg_response_time = total_response_time_G / served_req_count_G
    # 计算服务成功率和加权响应时间
    success_rate = served_req_count_G / total_req_count_G
    # 1、使用二次惩罚
    # 当success_rate接近1时，惩罚较小；当success_rate较小时，惩罚显著增加
    weighted_response_time = avg_response_time / (success_rate * success_rate)
    # 2、使用指数惩罚
    # weighted_response_time = avg_response_time * (1 + 2 * (1 - success_rate))

    # 计算服务效率 - 越大越好
    service_efficiency = 1 / weighted_response_time

    avg_cold_start_time = total_cold_start_time / cold_req_count_G
    hot_req_count = served_req_count_G - cold_req_count_G
    avg_hot_start_time = total_hot_start_time / hot_req_count
    
    # 统计冷启动数
    unserved_req_count = total_req_count_G - served_req_count_G
    cold_start_frequency = total_cold_req_G / served_req_count_G

    logging.info(f"total_req_count_G: {total_req_count_G}, served_req_count_G: {served_req_count_G}")
    logging.info(f"cold_req_count(served): {cold_req_count_G}, cold_req_count(unserved): {unserved_req_count}")
    logging.info(f"cold_start_frequency: {cold_start_frequency:.2f}")
    logging.info(f"avg_comm_cost: {avg_comm_cost:.2f}, avg_instan_cost: {avg_instan_cost:.2f}")
    logging.info(f"avg_run_cost: {avg_run_cost:.2f}, avg_cost: {avg_cost:.2f}")
    logging.info(f"total_response_time: {total_response_time_G:.2f}s, avg_response_time: {avg_response_time:.2f}s")
 
    
    print(f"实际请求总数: {total_req_count_G}, 服务请求总数: {served_req_count_G}, "
          f"冷启动请求(已服务): {cold_req_count_G}, 冷启动请求(未服务): {unserved_req_count}, " 
          f"冷启动频率: {cold_start_frequency:.2f}")
    print(f"平均通信成本: {avg_comm_cost:.2f}, 平均实例化成本: {avg_instan_cost:.2f}, "
          f"平均运行成本: {avg_run_cost:.2f}, 总成本: {total_cost_G:.2f}, 平均总成本: {avg_cost:.2f}")
    print(f"总响应时间: {total_response_time_G:.2f}s, 平均响应时间: {avg_response_time:.2f}s, 加权响应时间: {weighted_response_time:.2f}s")
    # print(f"冷请求数: {cold_req_count_G}, 总冷启动时间: {total_cold_start_time:.2f}s, 平均冷启动时间: {avg_cold_start_time:.2f}s")
    # print(f"热请求数: {hot_req_count}, 总热启动时间: {total_hot_start_time:.2f}s, 平均热启动时间: {avg_hot_start_time:.2f}s")
    print(f"服务成功率: {success_rate:.4f}, 服务效率: {service_efficiency:.4f}")
    
    # 输出指标和请求情况
    result_to_csv(f'./result/{config_G.cache_method}-{config_G.beta}-{config_G.redu_factor}.csv')
    
    # 输出分层缓存情况
    # layer_cache_csv(f'./result/result-{config_G.beta}-{config_G.redu_factor}.csv')
    
def layer_cache_csv(output_file: str):
    # 打开CSV文件进行写入
    with open(output_file, mode='w', newline='') as file:
        writer = csv.writer(file)

        # 写入CSV头
        writer.writerow(['Node ID', 'Layer Type', 'Size', 'Creation Time', 'Last Used Time', 'Shared Count', 'Additional Info'])

        # 遍历每个节点的分层缓存
        for node_id, layer_cache in layer_map_G.items():
            # 输出Bare层信息
            for key, layer in layer_cache.bare_layers.items():
                writer.writerow([
                    node_id,
                    'Bare',
                    layer.size,
                    layer.creation_time,
                    layer.last_used_time,
                    layer.shared_count,
                    ''  # 没有额外信息
                ])
            
            # 输出Lang层信息
            for key, layer in layer_cache.lang_layers.items():
                writer.writerow([
                    node_id,
                    'Lang',
                    layer.size,
                    layer.creation_time,
                    layer.last_used_time,
                    layer.shared_count,
                    f'Language: {layer.lang_type}'  # 额外信息：语言类型
                ])
            
            # 输出User层信息
            for key, layer in layer_cache.user_layers.items():
                writer.writerow([
                    node_id,
                    'User',
                    layer.size,
                    layer.creation_time,
                    layer.last_used_time,
                    layer.shared_count,
                    f'Function Type: {layer.func_type}'  # 额外信息：函数类型
                ])
       
def result_to_csv(output_file: str):
    # 打开CSV文件进行写入
    with open(output_file, mode='w', newline='') as file:
        writer = csv.writer(file)

        avg_response_time = total_response_time_G / served_req_count_G
        success_rate = served_req_count_G / total_req_count_G
        # weighted_response_time = avg_response_time * (1 + 2 * (1 - success_rate))
        weighted_response_time = avg_response_time / (success_rate * success_rate)
        avg_cost = total_cost_G / served_req_count_G
        cold_start_frequency = total_cold_req_G / served_req_count_G

        # 性能指标 - 更新包含成本信息
        writer.writerow([
            "beta",
            "total_req_count",
            "served_req_count",
            "cold_req_count",
            "cold_start_frequency",
            "weighted_response_time",
            "avg_cost"
        ])
        writer.writerow([
            config_G.beta,
            total_req_count_G,
            served_req_count_G,
            total_cold_req_G,
            f"{cold_start_frequency:.2f}",
            f"{weighted_response_time:.2f}",
            f"{avg_cost:.2f}"
        ])

        # 写入请求详情CSV头
        writer.writerow([
            'Time', 
            'ID', 
            'Function', 
            'Ingress', 
            'DeployNode', 
            'IsColdStart', 
            'CommCost', 
            'RunCost', 
            'InstanCost', 
            'TotalCost',
            'ResponseTime'
        ])

        # 遍历请求映射中的每个时间槽
        for config_G.slot_num, requestsMap in requestsMap_G.map.items():
            # 遍历每个时间槽中的请求
            for request in requestsMap.requests:
                if request.served:
                    # 计算请求的各种成本
                    dist = calculate_distance(request.ingress, request.deployNode)
                    comm_cost = dist * config_G.latency_para
                    run_cost = get_run_cost(request.deployNode.id, request.function.type)
                    instan_cost = 0
                    response_time = request.function.processTime + request.linkDelay
                    if request.isColdStart:
                        instan_cost = get_instan_cost(request.deployNode.id, request.function.type)
                        response_time += request.function.coldStartTime
                    total_cost = comm_cost + run_cost + instan_cost
                    
                    writer.writerow([
                        request.arriveTime,
                        request.id,
                        request.function.type,
                        request.ingress.id,
                        request.deployNode.id,
                        request.isColdStart,
                        f"{comm_cost:.2f}",
                        f"{run_cost:.2f}",
                        f"{instan_cost:.2f}",
                        f"{total_cost:.2f}",
                        f"{response_time:.2f}"
                    ])

#endregion

topo_G = Topology()
node_map_G: Dict[int, Location] = {}        # nodeID, Location
layer_map_G: Dict[int, Layer_Cache]= {}     # nodeID, Layer_Cache

def schedule_requests():
    """
    根据配置的缓存方法选择合适的调度策略
    """
    load_topo()
    initGlobal()
    init_func_map()
    
    # 根据缓存方法决定是否初始化分层缓存
    if config_G.cache_method == "Layer-Cache":
        init_layer_map()  # 初始化分层缓存
    
    # Zipf分布生成请求
    # request_file = './data/request/prediction.csv'
    # gen_req_zipf(request_file)
    
    request_file = f'./data/request/requests-{config_G.beta}.csv' 
    read_requests(request_file)    
    create_requests()           
    # show_nodes_memory()  

    print("----------------------Schedule Start----------------------")
    logging.info("----------------------Schedule Start----------------------")
    
    # 根据配置的缓存方法选择调度策略
    if config_G.cache_method == "Layer-Cache":
        deploy_requests_layered()   # 使用分层容器调度逻辑
    else:
        deploy_requests()           # 使用全容器调度逻辑
    
    print_result()

def main():
    # 解析命令行参数
    args = parse_args()
    
    # 保留原始配置对象，使用命令行参数覆盖特定属性
    global config_G
    config_G.beta = args.beta
    config_G.cache_method = args.cache_method
    # config_G.slot_num = args.slot_num
    # config_G.redu_factor = args.redu_factor
    
    # 设置日志文件名
    log_file = f'./result/log/{config_G.cache_method}-{config_G.beta}-{config_G.redu_factor}.log'
    
    # 转换日志级别字符串为对应的常量
    log_level_map = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    log_level = log_level_map.get(args.log_level, logging.INFO)
    
    # 是否输出到控制台
    console_output = args.console_log
    
    # 设置日志配置
    setup_logging(log_file, level=log_level, console_output=console_output)
    
    # 记录配置信息
    logging.info(f"beta={config_G.beta}, cache_method={config_G.cache_method}")
    # logging.info(f"slot_num={config_G.slot_num}, redu_factor={config_G.redu_factor}")
    
    # 运行调度器
    schedule_requests()

if __name__ == "__main__":
    # python simulation-3.25.py --beta 0.5 --cache-method Layer-Cache
    main()  
    # print(f"链路延迟更小: {a}, 实例化成本更小: {b}")
