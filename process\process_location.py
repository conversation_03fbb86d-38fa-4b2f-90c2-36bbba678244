import pandas as pd
import os

# 文件路径
input_file = "data/dataset/data_6.1~6.30_ - 复制.xlsx"
output_excel = "data/dataset/data_6.1~6.30_processed.xlsx"
output_csv = "data/topo/shanghai-data.csv"  # 新增CSV输出路径

# 确保输出目录存在
os.makedirs(os.path.dirname(output_excel), exist_ok=True)
os.makedirs(os.path.dirname(output_csv), exist_ok=True)

# 读取Excel文件
print(f"正在读取文件: {input_file}")
df = pd.read_excel(input_file)

# 显示原始数据的前几行和总行数
print("原始数据预览:")
print(df.head())
print(f"原始数据总行数: {len(df)}")

# 拆分location列
location_col = 'location(latitude/lontitude)'
if location_col in df.columns:
    # 使用str.split拆分location列，并创建两个新列
    df[['latitude', 'longitude']] = df[location_col].str.split('/', expand=True)
    
    # 将新列转换为浮点数
    df['latitude'] = pd.to_numeric(df['latitude'], errors='coerce')
    df['longitude'] = pd.to_numeric(df['longitude'], errors='coerce')
    
    # 删除包含空值的行
    df_before_dropna = df.copy()
    df = df.dropna(subset=['latitude', 'longitude'])
    dropped_na_count = len(df_before_dropna) - len(df)
    print(f"删除了 {dropped_na_count} 行包含空值的数据")
    
    # 删除原始的location列
    df = df.drop(location_col, axis=1)
    
    # 删除经纬度同时重复的行
    df_before_drop_duplicates = df.copy()
    df = df.drop_duplicates(subset=['latitude', 'longitude'])
    dropped_duplicates_count = len(df_before_drop_duplicates) - len(df)
    print(f"删除了 {dropped_duplicates_count} 行经纬度重复的数据")
    
    # 重新排列列的顺序
    columns = ['date', 'latitude', 'longitude'] + [col for col in df.columns if col not in ['date', 'latitude', 'longitude']]
    df = df[columns]
    
    # 重置索引，确保索引连续
    df = df.reset_index(drop=True)
    
    # 显示处理后的数据
    print("\n处理后数据预览:")
    print(df.head())
    print(f"处理后数据总行数: {len(df)}")
    
    # 保存处理后的数据到Excel文件
    print(f"正在保存处理后的Excel文件: {output_excel}")
    df.to_excel(output_excel, index=False)
    
    # 创建与site-optus-melbCBD.csv格式兼容的DataFrame
    # 添加必要的列以匹配site-optus-melbCBD.csv的格式
    topo_df = pd.DataFrame()
    topo_df['SITE_ID'] = range(1, len(df) + 1)  # 生成从1开始的站点ID
    topo_df['LATITUDE'] = df['latitude'].values  # 使用.values确保正确复制
    topo_df['LONGITUDE'] = df['longitude'].values  # 使用.values确保正确复制
    topo_df['NAME'] = [f"Shanghai Node {i}" for i in range(1, len(df) + 1)]  # 生成站点名称
    topo_df['STATE'] = "SH"  # 设置为上海
    topo_df['LICENSING_AREA_ID'] = 1  # 默认许可区域ID
    topo_df['POSTCODE'] = "200000"  # 上海邮编
    topo_df['SITE_PRECISION'] = "Within 10 meters"  # 默认精度
    topo_df['ELEVATION'] = ""  # 空海拔
    topo_df['HCIS_L2'] = "SH1P"  # 默认区域代码
    
    # 检查是否有空值
    print(f"CSV文件中经纬度空值数量: 纬度={topo_df['LATITUDE'].isna().sum()}, 经度={topo_df['LONGITUDE'].isna().sum()}")
    
    # 保存为CSV格式
    print(f"正在保存处理后的CSV文件: {output_csv}")
    topo_df.to_csv(output_csv, index=False)
    
    print("处理完成!")
else:
    print(f"错误: 未找到'{location_col}'列。请检查Excel文件的列名。")
    # 显示实际的列名，以便调试
    print("文件中的列名:", df.columns.tolist()) 