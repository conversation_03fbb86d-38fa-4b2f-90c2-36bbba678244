import os
import subprocess
import multiprocessing
import time
import argparse  # 添加argparse模块

def ensure_dirs():
    """确保必要的目录存在"""
    # 创建基本结果目录
    os.makedirs('./result', exist_ok=True)
    os.makedirs('./result/log', exist_ok=True)
    
    # 创建其他可能需要的目录
    os.makedirs('./data/request', exist_ok=True)  # 请求数据目录

def run_simulation(beta, method, alpha=0.015):
    """运行单个模拟实例"""
    # 检查结果文件是否已存在
    result_file = f'./result/{method}-{beta:.2f}-{alpha:.3f}.csv'
    log_file = f'./result/log/{method}-{beta:.2f}-{alpha:.3f}.log'
    
    if os.path.exists(result_file) and os.path.getsize(result_file) > 0:
        print(f"跳过执行: beta={beta:.2f}, method={method}, alpha={alpha:.3f} - 结果文件已存在")
        return beta, method, alpha, 0  # 返回成功码，表示"已完成"
    
    cmd = f"python simulation-7.30.py --beta {beta:.2f} --cache-method {method} --alpha {alpha:.3f}"
    print(f"开始执行: {cmd}")
    
    # 执行命令
    process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
    
    # 输出进程ID
    print(f"进程已启动 [PID={process.pid}]: beta={beta:.2f}, method={method}, alpha={alpha:.3f}")
    
    # 等待进程完成
    stdout, _ = process.communicate()
    exit_code = process.returncode
    
    # 输出完成信息
    print(f"任务完成 [PID={process.pid}]: beta={beta:.2f}, method={method}, alpha={alpha:.3f}, 退出代码={exit_code}")
    
    return beta, method, alpha, exit_code

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='并行运行模拟任务')
    parser.add_argument('--max-workers', type=int, default=9,
                        help='最大并行工作进程数 (默认: 9)')
    parser.add_argument('--max-tasks', type=int, default=None,
                        help='每批次最大执行任务数 (默认: 不限制)')
    parser.add_argument('--alpha-mode', action='store_true', default=False,
                        help='启用alpha变化模式，使用不同alpha值运行模拟')
    parser.add_argument('--redu-factor', type=int, default=None,
                        help='已废弃: reduction factor参数 (现在不再使用)')
    parser.add_argument('--batch-mode', action='store_true', default=True,
                        help='启用批处理模式，分批执行任务 (默认: 启用)')
    parser.add_argument('--no-analyze', action='store_true', default=False,
                        help='禁用执行完成后的冷启动频率分析 (默认: 启用分析)')
    return parser.parse_args()

def execute_batch(tasks_batch, max_workers):
    """执行一批任务"""
    if not tasks_batch:
        return []
    
    # 确定使用的进程数
    batch_workers = min(max_workers, len(tasks_batch))
    
    # 创建进程池
    pool = multiprocessing.Pool(processes=batch_workers)
    
    # 提交任务
    results = []
    for beta, method, alpha in tasks_batch:
        result = pool.apply_async(run_simulation, (beta, method, alpha))
        results.append(result)
    
    # 关闭进程池，不再接受新任务
    pool.close()
    
    # 定期检查和报告进度
    completed = 0
    total = len(tasks_batch)
    
    print(f"\n正在并行执行当前批次的 {total} 个任务...")
    
    while completed < total:
        # 检查完成数量
        completed = sum(1 for r in results if r.ready())
        print(f"当前批次进度: {completed}/{total} 完成")
        
        if completed < total:
            time.sleep(10)  # 每10秒检查一次
    
    # 等待所有任务完成
    pool.join()
    
    # 收集并返回结果
    return [r.get() for r in results]

def process_cold_start():
    """执行冷启动频率分析脚本"""
    print("\n正在分析LayerCache方法的冷启动频率...")
    
    # 检查分析脚本是否存在
    if not os.path.exists("process_cold_start.py"):
        print("错误: 未找到分析脚本 process_cold_start.py")
        return
    
    # 执行分析脚本
    subprocess.run(["python", "process_cold_start.py"], check=True)

def main():
    # 记录开始时间
    start_time = time.time()
    
    # 解析命令行参数
    args = parse_args()
    
    # 确保目录存在
    ensure_dirs()
    
    # 定义alpha值列表
    alphas = [0.001, 0.003, 0.005, 0.010, 0.015]
    
    # 准备参数组合
    if args.alpha_mode:
        # Alpha变化模式: 固定beta=1.00，使用不同alpha值
        # beta = 0.5
        # beta = 1.0
        beta = 1.5
        methods = ["LayerCache", "FaaSCache", "CrossEdge", "OpenWhisk"]
        combinations = [(beta, method, alpha) for method in methods for alpha in alphas]
    else:
        # 标准模式: beta变化，固定alpha=0.015
        alpha = 0.015
        combinations = [
            # LayerCache方法
            (1.50, "LayerCache", alpha),
            (1.25, "LayerCache", alpha),
            (1.00, "LayerCache", alpha),
            (0.75, "LayerCache", alpha),
            (0.50, "LayerCache", alpha),
            
            # FaaSCache方法
            (1.50, "FaaSCache", alpha),
            (1.25, "FaaSCache", alpha),
            (1.00, "FaaSCache", alpha),
            (0.75, "FaaSCache", alpha),
            (0.50, "FaaSCache", alpha),

            # CrossEdge方法
            (1.50, "CrossEdge", alpha),
            (1.25, "CrossEdge", alpha),
            (1.00, "CrossEdge", alpha),
            (0.75, "CrossEdge", alpha),
            (0.50, "CrossEdge", alpha),

            # OpenWhisk方法
            (1.50, "OpenWhisk", alpha),
            (1.25, "OpenWhisk", alpha),
            (1.00, "OpenWhisk", alpha),
            (0.75, "OpenWhisk", alpha),
            (0.50, "OpenWhisk", alpha),

            # LRU方法
            # (1.50, "LRU", alpha),
            # (1.25, "LRU", alpha),
            # (1.00, "LRU", alpha),
            # (0.75, "LRU", alpha),
            # (0.50, "LRU", alpha)
        ]
    
    # 预检查哪些任务需要执行
    tasks_to_run = []
    tasks_to_skip = []
    
    for beta, method, alpha in combinations:
        result_file = f'./result/{method}-{beta:.2f}-{alpha:.3f}.csv'
        if os.path.exists(result_file) and os.path.getsize(result_file) > 0:
            tasks_to_skip.append((beta, method, alpha))
        else:
            tasks_to_run.append((beta, method, alpha))
    
    print(f"\n总任务数: {len(combinations)}")
    print(f"已完成任务数(将跳过): {len(tasks_to_skip)}")
    print(f"待执行任务数: {len(tasks_to_run)}")
    
    if tasks_to_skip:
        print("\n将跳过以下任务:")
        for beta, method, alpha in tasks_to_skip:
            print(f"  - beta={beta:.2f}, method={method}, alpha={alpha:.3f}")
    
    if not tasks_to_run:
        print("\n所有任务已完成，无需执行新任务。")
        # 即使没有新任务，也执行分析
        if not args.no_analyze:
            process_cold_start()
        return
    
    print("\n待执行的任务:")
    for beta, method, alpha in tasks_to_run:
        print(f"  - beta={beta:.2f}, method={method}, alpha={alpha:.3f}")
    
    # 确定CPU核心数和最大工作进程数
    cpu_count = multiprocessing.cpu_count()
    max_workers = min(args.max_workers, cpu_count)
    print(f"\n系统有 {cpu_count} 个CPU核心")
    print(f"将使用最多 {max_workers} 个工作进程并行执行任务")
    
    # 执行任务
    all_results = []
    
    # 如果设置了批处理模式和最大任务数
    if args.batch_mode and args.max_tasks is not None and len(tasks_to_run) > args.max_tasks:
        # 计算需要的批次数
        total_tasks = len(tasks_to_run)
        batch_size = args.max_tasks
        num_batches = (total_tasks + batch_size - 1) // batch_size  # 向上取整
        
        print(f"\n任务将分 {num_batches} 批执行，每批最多 {batch_size} 个任务")
        
        # 分批执行任务
        for batch_idx in range(num_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, total_tasks)
            current_batch = tasks_to_run[start_idx:end_idx]
            
            print(f"\n开始执行第 {batch_idx + 1}/{num_batches} 批任务 (任务 {start_idx + 1} 至 {end_idx}，共 {total_tasks} 个)")
            
            # 执行当前批次
            batch_results = execute_batch(current_batch, max_workers)
            all_results.extend(batch_results)
            
            print(f"第 {batch_idx + 1}/{num_batches} 批任务完成")
            
            # 显示总体进度
            completed_so_far = len(all_results)
            print(f"总体进度: {completed_so_far}/{total_tasks} 完成 ({completed_so_far/total_tasks*100:.1f}%)")
    else:
        # 不分批，一次性执行所有任务
        all_results = execute_batch(tasks_to_run, max_workers)
    
    # 将跳过的任务添加到结果中
    for beta, method, alpha in tasks_to_skip:
        all_results.append((beta, method, alpha, 0))  # 0表示成功码
    
    # 打印最终结果
    print("\n所有任务已完成:")
    for beta, method, alpha, exit_code in all_results:
        status = "成功" if exit_code == 0 else f"失败(代码={exit_code})"
        print(f"beta={beta:.2f}, method={method}, alpha={alpha:.3f}: {status}")
    
    # 计算并显示总用时
    elapsed_time = time.time() - start_time
    minutes = int(elapsed_time // 60)
    seconds = elapsed_time % 60
    
    print("\n" + "=" * 80)
    print(f"所有任务执行完毕！总用时: {minutes} 分 {seconds:.2f} 秒 (共 {elapsed_time:.2f} 秒)")
    print("=" * 80)
    
    # 执行完成后分析冷启动频率
    if not args.no_analyze:
        process_cold_start()

if __name__ == "__main__":
    # python run_parallel.py --max-tasks 5    # 每批次最多执行5个任务
    # python run_parallel.py --alpha-mode     # 启用alpha变化模式，固定beta=1.00，使用不同alpha值
    # python run_parallel.py --max-tasks 5 --batch-mode  # 启用批处理模式，每批次最多执行5个任务
    # python run_parallel.py --no-analyze     # 禁用执行完成后的冷启动频率分析
    main() 