import heapq

import numpy as np
import dijkstra as dijk
import sys

"""
input: container information: <PERSON>(αin)(二维矩阵-执行延迟), <PERSON>(βin)(二维矩阵-数据可用性), 
                              Win1n1(三维矩阵-容器到各节点的传输延迟), <PERSON><PERSON>(一维数组-容器i的资源消耗)
       network condition: Cn(一维数组-节点n的资源容量), Yn(γn)(一维数组-无服务运营成本), p(常量-云中运行单位成本)

output: <PERSON><PERSON>(执行的节点), <PERSON>(取数据的节点), Fin1n2(流量路由情况), <PERSON>n(开启无服务的节点)

decision parameter: I, <PERSON><PERSON><PERSON>(数据可用节点), <PERSON><PERSON>(最短路径的长度), <PERSON><PERSON>(容器i到数据的传输延迟), 
                    <PERSON><PERSON>(α'in-更新后的执行延迟), <PERSON>(δin-按升序对节点排序), <PERSON><PERSON>(节点的当前资源使用量), Zn
"""

# 测试

# --------------------------输入--------------------------

# 节点数量N和容器数量I的规模
# 假设3个节点(1个云+2个边缘)，9个容器
scaleI = 1 + 9  # 0 useless
scaleN = 1 + 3  # 0 is useless , 1 represent cloud , other represent edge servers

# 初始执行延迟，此处默认为0
Ain = np.zeros((scaleI, scaleN))

# i数据在哪个节点上  0表示在
a = [[0, 0, 0, 0], [1, 0, 1, 0], [1, 1, 0, 1], [1, 0, 0, 1], [1, 1, 1, 0], 
     [1, 1, 1, 0], [1, 0, 0, 1], [1, 0, 1, 0], [1, 0, 0, 0], [1, 1, 0, 1]]
Bin = np.array(a)
a.clear()

# 传输延迟，第i行表示第几个i，每一行表示一个图Gi，每一列表示一个节点N
# 第0行没用，第0个结点没用
a = [[[0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]],
     [[0, 0, 0, 0], [0, 0, 1, 1], [0, 1, 0, 3], [0, 1, 3, 0]],
     [[0, 0, 0, 0], [0, 0, 10, 3], [0, 10, 0, 3], [0, 3, 3, 0]],
     [[0, 0, 0, 0], [0, 0, 1, 4], [0, 1, 0, 2], [0, 4, 2, 0]],
     [[0, 0, 0, 0], [0, 0, 2, 3], [0, 2, 0, 3], [0, 3, 3, 0]],
     [[0, 0, 0, 0], [0, 0, 2, 3], [0, 2, 0, 4], [0, 3, 4, 0]],
     [[0, 0, 0, 0], [0, 0, 4, 2], [0, 4, 0, 1], [0, 2, 1, 0]],
     [[0, 0, 0, 0], [0, 0, 2, 0], [0, 2, 0, 1], [0, 0, 1, 0]],
     [[0, 0, 0, 0], [0, 0, 3, 1], [0, 3, 0, 2], [0, 1, 2, 0]],
     [[0, 0, 0, 0], [0, 0, 1, 1], [0, 1, 0, 3], [0, 1, 3, 0]]]
Win1n2 = np.array(a)
a.clear()

Ci = np.array([0, 1.3, 0.5, 1.1, 0.7, 1.4, 0.4, 1.0, 2.2, 0.9])
Cn = np.array([0,1,10,8])
Yn = np.array([0,2,4,3])
p = 0.5

# output初始化
Xin = np.zeros((scaleI, scaleN))
Yin = np.zeros((scaleI, scaleN))
Fin1n2 = np.zeros((scaleI, scaleN, scaleN))
Zn = np.zeros(scaleN)

# 决策变量
Linkn = np.ones((scaleI, scaleN,scaleN))
Hin = np.zeros((scaleI, scaleN))
AAin = np.zeros((scaleI, scaleN))
Din = np.zeros((scaleI, scaleN))
Ln = np.zeros(scaleN)

# N = scaleN - 1

# --------------------------算法--------------------------
for i in range(1, scaleI):
    # 遍历每个容器i
    """
    一旦 Win1n2 有输入，Gi 就已构建
    所有 Bin == 0 的节点都属于集合 Ndatai(数据可用节点)

    Gi has been constructed once Win1n2 has input
    all nodes with Bin == 0 belong to set Ndatai
    """
    a.clear()
    for j in range(1, scaleN):
        if Bin[i][j] == 0:
            a.append(j)
    Ndatai = np.array(a)
    """
    对Ndatai节点进行单源最短路径算法
    并得到 Gi 中的每个节点 n 的最短路径和对应的长度Linkn

    Conduct the single-sourced shortest path algorithm for node
    of Ndatai and get the shortest path and corresponding length
    Linkn to each node n in Gi.
    """
    # 对Gi构建邻接表，节省空间
    dijk.makeAdjList(i, Win1n2)
    # 遍历每个数据可用节点Nk
    for Nk in Ndatai:
        Linkn[i][Nk] = dijk.dij(Nk,scaleN)
        # print(i)
        # print(Linkn[i][Nk])

    # 对于每一个结点N 找到最近的拥有i的数据的节点的距离
    for index in range(1, scaleN):
        # 找出在i中，结点n与存放i数据结点 之间最近的距离
        target = sys.maxsize
        for k in Ndatai:
            if Linkn[i][k][index] < target:
                target = Linkn[i][k][index]
        
        Hin[i][index] = target 
        # print(Hin[i][index])
        AAin[i][index] = Ain[i][index] + Hin[i][index]
        # print(AAin[i][index])
        # print('----')

    # 假设将容器 i 放置在节点 n 上对 P2 的目标函数的增量为 Din(δin)
    # Suppose the increment to the objective function of P2 by placing container i on node n is δin.
    for index in range(1, scaleN):
        # 云服务器
        if index == 1:
            Din[i][1] = AAin[i][1] + Ci[i] * p
        # 未启动无服务的节点
        elif Zn[index] == 0:
            Din[i][index] = AAin[i][index] + Yn[index]
        # 其他节点
        else:
            Din[i][index] = AAin[i][index]

    # sort Din[i]
    sortList = []
    for index in range(1, scaleN):
        heapq.heappush(sortList,(Din[i][index], index))
    for index in range(1, scaleN):
        # 取出Din中最小的节点
        node = heapq.heappop(sortList)
        first = node[0]
        second = node[1]
        # 若节点资源充足，确定执行节点
        if Ln[second] + Ci[i] <= Cn[second]:
            Xin[i][second] = 1
            Ln[second] = Ln[second] + Ci[i]
            # 找数据节点Nk
            target = 0
            for k in Ndatai:
                if Hin[i][index] == Linkn[i][k][index]:
                    target = k
                    # print(target)
                    break
            Yin[i][target] = 1
            if Zn[index] == 0:
                Zn[index] = 1
            Fin1n2[i][target][index] = 1
            break

# --------------------------输出--------------------------
for i in range(1, scaleI):
    print('container', i, ':')
    print('在哪个结点执行(1)：',Xin[i])
    print('在哪个结点取数(1)：',Yin[i])
    print('在哪个结点有数据(0)：',Bin[i])

