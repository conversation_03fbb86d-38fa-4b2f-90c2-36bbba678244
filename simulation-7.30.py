import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Optional, Union
import csv
import math
import os
import logging
import argparse
import uuid
import random
import time
import sys

class Config:
    def __init__(self, topo_file: str, latency_para: float, mem_cap: int, 
                 node_num: int, alpha: float, beta: float, 
                 slot_num: int, redu_factor: int, cpu_Freq: int, 
                 cache_method: str = "LayerCache"):
        self.topo_file = topo_file          # 拓扑文件
        self.latency_para = latency_para    # 时延参数
        self.mem_cap = mem_cap              # 内存容量
        self.node_num = node_num            # 节点数量
        self.alpha = alpha                  # 成本参数
        self.beta = beta                    # 请求参数
        self.slot_num = slot_num            # 插槽数量
        self.redu_factor = redu_factor      # 减少因子
        self.cpu_Freq = cpu_Freq            # CPU频率
        self.cache_method = cache_method    # 缓存方法

# 不同拓扑数据集的配置映射
topo_configs = {
    'EUA': Config(
        topo_file='./data/topo/site-optus-melbCBD.csv',
        node_num=125,                 # 节点数量
        slot_num=30,                  # 时间槽数量
        cache_method="LayerCache",    # 缓存方法
        cpu_Freq=3,                   # CPU频率
        latency_para=1,               # 延迟参数
        beta=1.00,                    # Zipf分布
        alpha=0.015,                  # 成本参数               
        mem_cap=11000,                # 内存容量
        redu_factor=30                # 减少因子
    ),   
    'Telecom': Config(
        topo_file='./data/topo/shanghai-data-sampled.csv',
        node_num=125,                 # 节点数量
        slot_num=30,                  # 时间槽数量
        cache_method="LayerCache",    # 缓存方法
        cpu_Freq=3,                   # CPU频率
        latency_para=0.1,             # 延迟参数
        beta=1.0,                     # Zipf分布
        alpha=0.015,                  # 成本参数
        mem_cap=25000,                # 内存容量
        redu_factor=15                # 减少因子
    )
}

config_G = topo_configs['EUA']
# config_G = topo_configs['Telecom']

CONTAINER_LIFETIME = 10             # 默认容器生命周期
SECONDS_PER_SLOT = 60               # 每个时间槽的秒数
MAX_EXTRA_SLOTS = 10                # 最大额外时间槽数量
MAX_RETRIES_PER_SECOND = 10         # 每秒最大重试次数
MAX_REQUESTS_PER_SECOND = 100       # 每秒最多处理的等待请求数
MAX_EVICTION_ATTEMPTS = 1000        # 最大驱逐尝试次数

# 调试变量
a, b = 0, 0  
def initGlobal():
    global clock_G, count_G, activeFunctions_G, cacheMap_G, functionfreq_G
    global requestsMap_G, funcInfoMap_G, rontt_G
    global total_req_count_G, cold_req_count_G, req_count_G, served_req_count_G
    global deploy_current_req_G, deploy_neighbor_req_G, create_current_req_G
    global total_response_time_G, total_cost_G, weighted_response_time_G
    # 新增用于统计新创建容器的内存变量
    global new_total_memory_G, new_bare_memory_G, new_lang_memory_G, new_user_memory_G
    # 新增用于统计新创建容器的计数器
    global new_bare_counter_G, new_lang_counter_G, new_user_counter_G, new_container_counter_G
    # 新增等待队列相关全局变量
    global waiting_queues_G, wait_count_G, waiting_queue_served_G, second_clock_G, node_execution_end_times_G, total_wait_time_G, is_extra_slot_G, is_processing_waiting_queue_G

    activeFunctions_G = ActiveFunctions()   # 活动函数
    cacheMap_G = CacheMap()                 # 缓存函数
    functionfreq_G = FunctionFreq()         # 函数频率
    requestsMap_G = RequestsMap()           # 请求映射
    funcInfoMap_G = FunctionInfoMap()       # 函数属性
    rontt_G = Req_on_Nodes_Time_Type()      # funcType<->Req_Nodes_Time
    
    clock_G = 1                             # 每轮开始时更新时钟
    count_G = 0                             # 容器总数计数
    req_count_G = 0                         # 创建请求计数
    total_req_count_G = 0                   # 总请求计数
    served_req_count_G = 0                  # 服务请求计数
    cold_req_count_G = 0                    # 冷启动请求计数(最后统计检查)
    create_current_req_G = 0                # 冷启动请求计数(请求分发统计)
    deploy_current_req_G = 0                # 当前节点部署计数
    deploy_neighbor_req_G = 0               # 邻居节点部署计数
    total_response_time_G = 0               # 总响应时间
    total_cost_G = 0                        # 总成本
    weighted_response_time_G = 0            # 加权响应时间
    
    # 新创建容器内存统计变量
    new_total_memory_G = 0.0      # 新创建总内存
    new_bare_memory_G = 0.0       # 新创建Bare层内存
    new_lang_memory_G = 0.0       # 新创建Lang层内存
    new_user_memory_G = 0.0       # 新创建User层内存
    
    # 新创建容器计数器
    new_bare_counter_G = 0        # 新创建Bare层计数
    new_lang_counter_G = 0        # 新创建Lang层计数
    new_user_counter_G = 0        # 新创建User层计数
    new_container_counter_G = 0   # 新创建全容器计数
    
    # 初始化等待队列相关全局变量
    total_wait_time_G = 0         # 总等待时间
    waiting_queues_G = {}         # 节点ID -> WaitingQueue
    wait_count_G = 0              # 等待请求计数
    waiting_queue_served_G = 0    # 等待队列中成功处理的请求数量
    second_clock_G = 0            # 秒级时钟
    is_extra_slot_G = False       # 标识当前是否在额外时间槽中
    is_processing_waiting_queue_G = False  # 标识当前是否在处理等待队列
    node_execution_end_times_G = {}  # 节点ID -> {容器ID -> 执行结束时间}

#region ---------------------命令行及日志--------------------
def parse_args():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='Simulation in Serverless Edge Computing')
    
    # 添加拓扑文件参数
    parser.add_argument('--topo-file', type=str, default=None,
                        help='拓扑文件路径，可选值: ' + ', '.join(topo_configs.keys()))
    
    # 默认值保持与config_G初始化时相同
    parser.add_argument('--mem-cap', type=int, default=config_G.mem_cap,
                        help=f'内存容量 (默认: {config_G.mem_cap})')

    parser.add_argument('--beta', type=float, default=config_G.beta,
                        help=f'Zipf分布β参数 (默认: {config_G.beta})')
    
    parser.add_argument('--cache-method', type=str, default=config_G.cache_method,
                        choices=['LayerCache', 'FaaSCache', 'CrossEdge', 'OpenWhisk'],
                        help=f'缓存方法 (默认: {config_G.cache_method})')
    
    parser.add_argument('--slot-num', type=int, default=config_G.slot_num,
                        help=f'时间槽数量 (默认: {config_G.slot_num})')
    
    parser.add_argument('--redu-factor', type=int, default=config_G.redu_factor,
                        help=f'冗余因子 (默认: {config_G.redu_factor})')
    
    parser.add_argument('--alpha', type=float, default=config_G.alpha,
                        help=f'成本参数 (默认: {config_G.alpha})')
    
    # 添加日志相关参数
    parser.add_argument('--log-level', type=str, default='INFO',
                        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                        help='日志级别 (默认: INFO)')
    
    parser.add_argument('--console-log', action='store_true',
                        help='启用控制台日志输出')
    
    return parser.parse_args()

def setup_logging(log_file, level=logging.INFO, console_output=True):
    """
    设置日志配置
    
    Args:
        log_file (str): 日志文件名
        level (int): 日志级别，默认为INFO
    """
    # 创建日志目录
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
    file_handler.setLevel(level)
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)

#endregion

#region ---------------------请求相关类----------------------
# 节点请求数量
class Req_on_Nodes:
    def __init__(self):
        self.numVector: List[int] = []  # 存储每个节点的请求数量

    def add(self, num: int):
        """添加请求数量"""
        self.numVector.append(num)

    def clear(self):
        """清空请求数量"""
        self.numVector.clear()

# 每个时间槽的请求数量(timeslot<->req_num) 
class Req_on_Nodes_Time:
    def __init__(self):
        self.numVector: List[Req_on_Nodes] = []  # 存储每个时间槽的请求数量

    def add(self, ron: Req_on_Nodes):
        """添加请求数量"""
        self.numVector.append(ron)

    def clear(self):
        """清空请求数量"""
        self.numVector.clear()

# 请求数量映射(funcType<->Req_Nodes_Time)
class Req_on_Nodes_Time_Type:
    def __init__(self):
        self.numMap: Dict[int, Req_on_Nodes_Time] = {}  # 存储函数类型与请求数量的映射

    def add(self, funcType: int, ront: Req_on_Nodes_Time):
        """添加函数类型的请求数量"""
        self.numMap[funcType] = ront

# 单个请求 request -> function -> phynode
class Request:
    # 使用字符串来引用尚未定义的类以实现嵌套 'Function' 'PhyNode'
    def __init__(self, id: int = 0, function: 'Function' = None, ingress: 'PhyNode' = None,
                 arriveTime: int = 0, served: bool = False, isColdStart: bool = False,
                 deployNode: 'PhyNode' = None, linkDelay: float = 0.0):
        self.id = id                    # 请求ID
        self.function = function        # 函数对象
        self.ingress = ingress          # 入口节点
        self.arriveTime = arriveTime    # 到达时间
        self.served = served            # 是否已服务
        self.isColdStart = isColdStart  # 是否冷启动
        self.deployNode = deployNode    # 部署节点
        self.linkDelay = linkDelay      # 传输延迟
        self.instan_cost = 0.0          # 实例化成本

        # 新增属性
        self.waitTime = 0.0             # 等待时间
        self.startExecTime = 0.0        # 执行开始时间
        self.endExecTime = 0.0          # 执行结束时间
        self.executionDuration = 0.0    # 执行所需时间（秒）
        self.arrival_second = 0         # 在时间槽内的具体到达秒数
       
    def update(self, function: 'Function', deployNode: 'PhyNode', isColdStart: bool, linkDelay: float):
        """更新请求的函数、部署节点和冷启动状态"""
        self.function = function
        self.served = True
        self.deployNode = deployNode
        self.isColdStart = isColdStart
        self.linkDelay = linkDelay

# 请求列表(List[Request])
class Requests:
    def __init__(self):
        self.requests: List[Request] = []  # 请求列表

    def add(self, request: Request):  # 添加请求
        self.requests.append(request)

# 请求映射(time slot<->request)
class RequestsMap:
    def __init__(self):
        self.map: Dict[int, Requests] = {}  # 时间槽到请求的映射

    def add(self, request: Request, time_slot: int):  # 添加请求到指定时间槽
        if time_slot not in self.map:
            self.map[time_slot] = Requests()  # 初始化请求列表
        self.map[time_slot].add(request)  # 添加请求

    def get(self, time_slot: int) -> Tuple[List[Request], bool]:  # 获取指定时间槽的请求
        if time_slot in self.map:
            return self.map[time_slot].requests, True  # 返回请求列表和找到的标志
        return [], False  # 返回空列表和未找到的标志

    def size(self):
        return len(self.map)

# 存储请求数据
class RequestFile:
    def __init__(self):
        self.time: List[int] = []  # 时间列表
        self.app1: List[int] = []  # 应用1请求列表
        self.app2: List[int] = []  # 应用2请求列表
        self.app3: List[int] = []  # 应用3请求列表
        self.app4: List[int] = []  # 应用4请求列表
        self.app5: List[int] = []  # 应用5请求列表
        self.app6: List[int] = []  # 应用6请求列表
        self.app7: List[int] = []  # 应用7请求列表
        self.app8: List[int] = []  # 应用8请求列表
# endregion

#region ---------------------节点相关类----------------------
# 每个节点的等待队列
class WaitingQueue:
    def __init__(self, node_id: int):
        self.node_id = node_id
        self.queue: List[Request] = []  # 等待队列
        self.retry_counts: Dict[int, int] = {}  # 请求ID -> 当前秒的重试次数
        self.last_second = -1  # 上次重置重试计数的秒数

    def add_request(self, request: 'Request'):
        """将请求添加到等待队列"""
        self.queue.append(request)
        # 记录当前秒的重试次数
        current_second = second_clock_G
        if current_second != self.last_second:
            # 新的秒开始，重置所有重试计数
            self.retry_counts.clear()
            self.last_second = current_second

        if request.id not in self.retry_counts:
            self.retry_counts[request.id] = 0
        self.retry_counts[request.id] += 1

    def get_next_request(self) -> Optional['Request']:
        """获取队列中的下一个请求，如果队列为空则返回None"""
        if self.is_empty():
            return None
        return self.queue.pop(0)

    def get_retry_count(self, request_id: int) -> int:
        """获取请求在当前秒的重试次数"""
        current_second = second_clock_G
        if current_second != self.last_second:
            # 新的秒开始，重置所有重试计数
            self.retry_counts.clear()
            self.last_second = current_second
            return 0
        return self.retry_counts.get(request_id, 0)
    
    def move_to_next_slot(self, next_slot: int, requests_map: 'RequestsMap'):
        """将队列中的请求移至下一时间槽"""
        for request in self.queue:
            requests_map.add(request, next_slot)
        self.queue.clear()
    
    def is_empty(self) -> bool:
        """检查队列是否为空"""
        return len(self.queue) == 0
    
    def size(self) -> int:
        """返回队列中的请求数量"""
        return len(self.queue)

    def peek_requests(self, max_count: int) -> List['Request']:
        """查看队列前面的请求，但不取出"""
        return self.queue[:max_count]

    def peek_next_request(self) -> Optional['Request']:
        """查看队列中的下一个请求，但不取出"""
        if self.is_empty():
            return None
        return self.queue[0]

    def remove_request(self, request: 'Request') -> bool:
        """从队列中移除指定的请求"""
        if request in self.queue:
            self.queue.remove(request)
            return True
        return False

# 物理节点
class PhyNode:
    def __init__(self, id: int = 0, lat: float = 0.0, long: float = 0.0, 
                 mem: float = 0.0, cpuFreq: float = 0.0):
        self.id = id
        self.lat = lat
        self.long = long
        self.mem = mem  
        self.cpuFreq = cpuFreq
        self.funcFreq: Dict[int, float] = {}  # <functype, freq>
        self.recency: Dict[int, float] = {}  # <functype, time>

    def getFreq(self, funcType: int) -> float:
        if funcType not in self.funcFreq or self.funcFreq[funcType] <= 0:
            self.funcFreq[funcType] = 1.0
            return 1.0
        return self.funcFreq[funcType]

    def getRecency(self, funcType: int) -> float:
        if funcType not in self.recency or self.recency[funcType] <= 0:
            self.recency[funcType] = 1.0
            return 1.0
        return self.recency[funcType]

    def setRecency(self, funcType: int, recency: float):
        self.recency[funcType] = recency

    def setFreq(self, funcType: int, freq: float):
        self.funcFreq[funcType] = freq

    def addFreq(self, funcType: int):
        if funcType not in self.funcFreq:
            self.funcFreq[funcType] = 1.0
        else:
            self.funcFreq[funcType] += 1.0

    def minusFreq(self, funcType: int):
        if funcType in self.funcFreq:
            self.funcFreq[funcType] -= 1.0

    def getMem(self) -> float:
        return self.mem

# 拓扑结构(node id, PhyNode)
class Topology:
    def __init__(self):
        self.nodes: Dict[int, PhyNode] = {}  

    def get(self, phyNodeID: int) -> PhyNode:
        """获取物理节点"""
        return self.nodes.get(phyNodeID, PhyNode(0, 0.0, 0.0, 0.0))  # 返回默认 PhyNode

    def add_node(self, phyNodeID: int, p: PhyNode):
        """添加物理节点到拓扑结构"""
        self.nodes[phyNodeID] = p  # 将节点添加到列表中

    def update(self, operation: str, phyNodeID: int, size: float):
        """更新物理节点的内存"""
        if operation == "add":
            self.nodes[phyNodeID].mem += size
        elif operation == "minus":
            self.nodes[phyNodeID].mem -= size

    def size(self) -> int:
        """返回节点数量"""
        return len(self.nodes)

    def minusFreq(self, phyNodeID: int, funcType: int):
        """减少函数类型的频率"""
        p = self.get(phyNodeID)
        p.minusFreq(funcType)
        self.nodes[phyNodeID] = p
    
    def addFreq(self, phyNodeID: int, funcType: int):
        """增加函数类型的频率"""
        p = self.get(phyNodeID)
        p.addFreq(funcType)
        self.nodes[phyNodeID] = p

    def setRecency(self, phyNodeID: int, funcType: int, recent: float):
        """设置函数类型的最近使用时间"""
        p = self.get(phyNodeID)
        p.setRecency(funcType, recent)
        self.nodes[phyNodeID] = p

    def addFreqAll(self, funcType: int):
        """对所有物理节点增加函数类型的频率"""
        for p in self.nodes.values():
            p.addFreq(funcType)

    def setRecencyAll(self, funcType: int, recent: float):
        """对所有物理节点设置函数类型的最近使用时间"""
        for p in self.nodes.values():
            p.setRecency(funcType, recent)

# 节点经纬度
class Location:
    def __init__(self, latitude: float = 0.0, longitude: float = 0.0):
        self.latitude = latitude
        self.longitude = longitude

    def init(self, lat1: float, long1: float):
        self.latitude = lat1
        self.longitude = long1

# 节点间距离(node id, distance)
class Distance:
    def __init__(self, phyNodeID: int, distance: float):
        self.phyNodeID = phyNodeID  # 物理节点ID
        self.distance = distance  # 距离
    
    def get_id(self):
        return self.phyNodeID

# 节点列表(List[Distance]距离排序)
class DistSlice:
    def __init__(self):
        self.slice: List[Distance] = []  # 距离切片列表
#endregion

#region ---------------------函数相关类----------------------
# 单个函数 function -> phynode
class Function:
    def __init__(self, name: str = None, type: int = 0, size: float = 0.0, clock: float = 0.0, 
                 coldStartTime: float = 0.0, priority: float = 1.0, executionTime: float = 0.0,  
                 phyNode: PhyNode = None, creation_time: int = 0, lastUseTime: int = 0, lifeTime: int = 10,
                 lang_type: int = 0, bare_size: float = 0.0, lang_size: float = 0.0, user_size: float = 0.0,
                 bare_delay: float = 0.0, lang_delay: float = 0.0, user_delay: float = 0.0):
        self.name = name  # 函数名称
        self.type = type  # 函数类型 1-9
        self.size = size  # 函数整体大小
        self.clock = clock  # 函数时钟
        self.coldStartTime = coldStartTime  # 冷启动时间
        self.priority = priority  # 优先级
        self.executionTime = executionTime  # 处理时间
        self.phyNode = phyNode  # 物理节点
        self.creation_time = creation_time  # 创建时间
        self.lastUseTime = lastUseTime      # 最后使用时间
        self.lifeTime = lifeTime            # 容器生命周期

        # 新增大小属性
        self.lang_type = lang_type  # 语言类型 1-3(node.js, python, java)
        self.bare_size = bare_size  # Bare层大小
        self.lang_size = lang_size  # Lang层大小
        self.user_size = user_size  # User层大小
        
        # 新增延迟属性
        self.bare_delay = bare_delay  # Bare层冷启动延迟
        self.lang_delay = lang_delay  # Lang层冷启动延迟
        self.user_delay = user_delay  # User层冷启动延迟
        
        # 使用UUID引用层，而不是直接对象引用
        self.lang_layer_uuid = None  # 存储Lang层的UUID
        self.bare_layer_uuid = None  # 存储Bare层的UUID

        # 执行状态
        self.is_executing = False    # 是否正在执行中

    def active_priority(self):
        """计算活动优先级"""
        freq = functionfreq_G.get(self.type)
        self.clock = clock_G
        self.priority = self.clock + (float(freq) * self.coldStartTime) / (self.size)   # MB 单位

    def cache_priority(self):
        """计算缓存优先级 如果cache不使用当前clock"""
        freq = functionfreq_G.get(self.type)
        self.priority = self.clock + (float(freq) * self.coldStartTime) / (self.size)  # MB 单位

    def show_priority(self):
        """显示函数优先级"""
        print(f"Function {self.name} Priority {self.priority}")  # 使用 print 替代 log

    def minusLife(self):
        """减少函数容器的生命周期，用于OpenWhisk策略"""
        self.lifeTime -= 1
        return self.lifeTime <= 0
        
    def activeLifeTime(self, time: int):
        """设置函数容器的生命周期，用于OpenWhisk策略"""
        self.lifeTime = time

# 一种类型的活动函数(type, List[Function])
class Functions:
    def __init__(self, func_type: int):
        self.type = func_type  # 函数类型
        self.slice: List[Function] = []  # 存储函数的列表

    def add(self, function: Function):
        self.slice.append(function)  # 添加函数到列表

    def delete(self, index: int):
        if 0 <= index < len(self.slice):
            self.slice.pop(index)  # 从列表中删除指定索引的函数

    def show_priority(self):
        for func in self.slice:
            print(f"Function {func.name} Priority {func.priority}")  # 显示函数的优先级

class FunctionInfo:
    """函数信息类，存储函数的基本信息"""
    def __init__(self, coldStartTime: float= 0.0, bare_delay: float = 0.0, lang_delay: float = 0.0, 
                 user_delay: float = 0.0, executionTime: float = 0.0, lang_type: int = 0, 
                 size: float = 0.0, bare_size: float = 0.0, lang_size: float = 0.0, user_size: float = 0.0):
        
        # 三层容器的加载延迟
        self.coldStartTime = coldStartTime  # 总冷启动时间
        self.bare_delay = bare_delay        # Bare层冷启动延迟
        self.lang_delay = lang_delay        # Lang层冷启动延迟
        self.user_delay = user_delay        # User层冷启动延迟
        self.executionTime = executionTime  # 执行时间
        
        self.lang_type = lang_type          # 语言类型 1-3(node.js, python, java)
        
        # 三层容器的内存大小
        self.size = size                    # 函数总大小
        self.bare_size = bare_size          # Bare层大小
        self.lang_size = lang_size          # Lang层大小
        self.user_size = user_size          # User层大小

# (funcType<->FunctionInfo)
class FunctionInfoMap:
    def __init__(self):
        self.func_map: Dict[int, FunctionInfo] = {}  

    def add(self, func_type: int, fi: FunctionInfo):

        """添加函数信息"""
        self.func_map[func_type] = fi

        return None, False
    
    def get_size(self, func_type: int) -> int:
        """获取函数大小"""
        if func_type in self.func_map:
            return self.func_map[func_type].size
        return 0

# 函数频率(type<->freq)(active+,cache-)
class FunctionFreq:
    def __init__(self):
        self.map: Dict[int, int] = {}  # 函数类型到频率的映射

    def get(self, funcType: int) -> int:  # 获取函数类型的频率
        return self.map.get(funcType, 1.0)  # 返回频率，如果不存在则返回1   

    def add(self, funcType: int):  # 增加函数类型的频率
        if funcType in self.map:
            self.map[funcType] += 1  # 增加频率
        else:
            self.map[funcType] = 1  # 初始化频率

    def minus(self, funcType: int):  # 减少函数类型的频率
        if funcType in self.map:
            self.map[funcType] -= 1  # 减少频率

# 不同类型的活动函数(node id, type<->Functions)
class NodeFunctions:
    def __init__(self, phy_node_id: int):
        self.phyNodeID = phy_node_id  # 物理节点ID
        self.functions: Dict[int, Functions] = {}  # <funcType, FunctionSlice>

    def show(self, node_id: int):
        for func_type, functions in self.functions.items():
            print(f"Node id {node_id} funcType: {func_type} => active container num: {len(functions.slice)}")

    def show_priority(self):
        for functions in self.functions.values():
            functions.show_priority()  # 调用 Functions 的 show_priority 方法

    def add(self, function: Function):
        if function.type not in self.functions:
            self.functions[function.type] = Functions(function.type)  # 初始化 Functions
        self.functions[function.type].add(function)  # 将函数添加到对应的 Functions

    def delete(self, func_type: int, i: int):
        if func_type in self.functions:
            self.functions[func_type].delete(i)  # 从对应的 Functions 中删除函数

# 所有节点上的活动函数(node id<->NodeFunctions)
class ActiveFunctions:
    def __init__(self):
        self.map: Dict[int, NodeFunctions] = {}  # 节点ID到节点函数的映射

    def show(self):
        if not self.map:
            print("ActiveFunctions is empty")
            return

        print("show ActiveFunctions....")
        for nodeID, nf in self.map.items():
            nf.show(nodeID)  # 调用 NodeFunctions 的 show 方法

    def show_priority(self):
        if not self.map:
            print("ActiveFunctions is empty")
            return

        print("show priority....")
        for nf in self.map.values():
            nf.show_priority()  # 调用 NodeFunctions 的 showPriority 方法

    def add(self, function: Function, phyNodeID: int):
        if phyNodeID not in self.map:
            self.map[phyNodeID] = NodeFunctions(phyNodeID)  # 初始化 NodeFunctions

        # 更新函数频率
        functionfreq_G.add(function.type)
        function.active_priority()      
        self.map[phyNodeID].add(function)  # 将函数添加到对应的 NodeFunctions

    def delete(self, phyNodeID: int, funcType: int, i: int):
        if phyNodeID in self.map:
            nf = self.map[phyNodeID]
            nf.delete(funcType, i)    # 调用 NodeFunctions 的 delete 方法
            self.map[phyNodeID] = nf  # 更新映射

# 单个节点的缓存列表(node id, List[Function]优先级升序)
class Cache:
    def __init__(self, phyNodeID: int):
        self.phyNodeID = phyNodeID  # 物理节点ID
        self.functionList: List[Function] = []  # 优先级函数列表，升序

    def show(self):
        print(f"node {self.phyNodeID} cache size : {len(self.functionList)}")

    def show_priority(self):
        for function in self.functionList:
            print(f" {function.name} Priority : {function.priority}")

    def sort_list(self):
        self.functionList.sort(key=lambda f: f.priority)  # 按优先级排序
        
    def sort_by_last_use_time(self):
        self.functionList.sort(key=lambda f: f.lastUseTime)  # 按最后使用时间排序（LRU）
        
    def sort_by_life_time(self):
        """按生命周期排序（OpenWhisk策略）"""
        self.functionList.sort(key=lambda f: f.lifeTime)

    def find(self, functionName: str) -> Tuple[int, Optional[str]]:
        for i in range(len(self.functionList)):
            if functionName == self.functionList[i].name:
                return i, None  # 返回索引和无错误
        return -1, "can't find this function by name"  # 返回未找到的错误信息

    def add(self, function: Function):
        functionfreq_G.minus(function.type)  
        function.cache_priority()  
        function.lastUseTime = clock_G  # 设置最后使用时间为当前时钟
        self.functionList.append(function)
        self.sort_list()  

    def delete(self, i: int):
        # 检查索引是否存在
        if 0 <= i < len(self.functionList):
            self.functionList.pop(i)  # 从列表中删除指定索引的函数

    def removeType(self, funcType: int) -> bool:
        for i, function in enumerate(self.functionList):
            if function.type == funcType:
                del self.functionList[i]
                return True
        return False

# 多个节点的缓存映射(node id<->cache)
class CacheMap:
    def __init__(self):
        self.caches: Dict[int, Cache] = {}  # 物理节点ID到缓存的映射

    def show(self):
        if not self.caches:
            print("CacheMap is empty")
            return

        print("--------CacheMap--------")
        for cache in self.caches.values():
            cache.show()  # 调用 Cache 的 show 方法

    def show_priority(self):
        if not self.caches:
            print("CacheMap is empty")
            return

        print("--------CacheMap Priority--------")
        for cache in self.caches.values():
            cache.show_priority()  # 调用 Cache 的 show_priority 方法

    def add(self, function: Function):
        if function.phyNode.id in self.caches:
            self.caches[function.phyNode.id].add(function)  # 添加到现有缓存
        else:
            new_cache = Cache(function.phyNode.id)  # 创建新的 Cache 实例
            new_cache.add(function)  # 添加函数
            self.caches[function.phyNode.id] = new_cache  # 将新缓存添加到映射中
 
    def get(self, phyNodeID: int) -> Cache:
        return self.caches.get(phyNodeID, Cache(phyNodeID))

    # 寻找可用缓存容器
    def get_idle_function(self, phyNodeID: int, funcType: int) -> Tuple[Function, int]:
        if phyNodeID in self.caches:
            cache = self.caches[phyNodeID]
            for i, function in enumerate(cache.functionList):
                if function.type == funcType:
                    # 检查容器是否正在执行中
                    if self.is_container_executing(phyNodeID, function):
                        continue  # 容器正在执行，跳过

                    # 更新最后使用时间为当前时钟
                    function.lastUseTime = clock_G
                    cache.functionList[i] = function
                    return function, i
        return Function(), -1

    def is_container_executing(self, phyNodeID: int, function: Function) -> bool:
        """
        检查容器是否正在执行中

        参数:
            phyNodeID: 物理节点ID
            function: 函数对象

        返回:
            True: 容器正在执行中
            False: 容器空闲可用
        """
        # 检查函数对象是否有执行状态属性
        if hasattr(function, 'is_executing') and function.is_executing:
            return True
        return False

    def get_lowest_priority(self, phyNodeID: int) -> float:
        if phyNodeID in self.caches and self.caches[phyNodeID].functionList:
            return self.caches[phyNodeID].functionList[0].priority  # 返回最低优先级
        return 0.0  # 如果没有函数，返回0

    def sort(self):
        """对所有缓存进行排序"""
        for cache in self.caches.values():
            cache.sort_list()  # 调用 Cache 的 sort_list 方法
        
    def sort_life_time(self):
        """按固定缓存（OpenWhisk）策略的生命周期对所有节点的缓存进行排序"""
        for cache in self.caches.values():
            cache.sort_by_life_time()  # 对每个节点的缓存调用sort_by_life_time方法

    def delete(self, phyNodeID: int, funcIndex: int):
        if phyNodeID in self.caches:
            self.caches[phyNodeID].delete(funcIndex)  # 从缓存中删除函数

    def delete_Prob(self, phyNodeID: int, funcType: int) -> bool:
        cache = self.get(phyNodeID)
        if cache.removeType(funcType):
            self.caches[phyNodeID] = cache
            topo_G.minusFreq(phyNodeID, funcType)
            return True
        return False

    def delete_low_function(self, phyNodeID: int) -> Tuple[Function, bool]:
        if phyNodeID in self.caches and self.caches[phyNodeID].functionList:
            function = self.caches[phyNodeID].functionList[0]  # 获取最低优先级的函数
            self.caches[phyNodeID].delete(0)  # 删除该函数
            return function, True  # 返回函数和成功标志 
        return Function(), False  # 返回空函数和失败标志    

    def delete_low_lifetime_function(self, phyNodeID: int) -> Tuple[Function, bool]:
        """删除生命周期最低的函数（OpenWhisk策略）"""
        if phyNodeID in self.caches and self.caches[phyNodeID].functionList:
            # 先按生命周期排序
            self.sort_life_time()
            # 取出生命周期最低的函数（列表头部）
            function = self.caches[phyNodeID].functionList[0]
            self.caches[phyNodeID].delete(0)
            return function, True
        return Function(), False

#endregion

#region ---------------------容器相关类----------------------
# 容器层基类
class ContainerLayer:
    """容器层的基类"""
    def __init__(self, size: float, creation_time: int = 0, last_used_time: int = 0, node_id: int = None):
        self.uuid = str(uuid.uuid4())  # 生成唯一标识符
        self.size = size  # 层大小
        self.creation_time = creation_time      # 创建时间
        self.last_used_time = creation_time     # 最后使用时间(初始化为创建时间)
        self.node_id = node_id  # 记录创建该层的节点ID

    def update_usage(self, current_time: int):
        """更新层的最后使用时间"""
        self.last_used_time = current_time
        
# 基础容器层
class BareLayer(ContainerLayer):
    """基础容器层，只包含基础环境和工具"""
    def __init__(self, size: float, lang_type: int, creation_time: int = 0, node_id: int = None):
        super().__init__(size, creation_time, creation_time, node_id)  # 最后使用时间初始化为创建时间
        self.layer_type = "bare"
        self.lang_type = lang_type  # 添加语言类型属性，使bare层与特定语言类型关联
        
# 语言运行层
class LangLayer(ContainerLayer):
    """语言运行时层，包含特定语言的运行时"""
    def __init__(self, size: float, lang_type: int, bare_layer_uuid: str, creation_time: int = 0, node_id: int = None):
        super().__init__(size, creation_time, creation_time, node_id)  # 最后使用时间初始化为创建时间
        self.layer_type = "lang"
        self.lang_type = lang_type      # 语言类型 1-3(node.js, python, java)
        self.bare_layer = None          # 移除直接对象引用
        self.bare_layer_uuid = bare_layer_uuid  # 直接存储Bare层的UUID

# 分层容器缓存管理
class Layer_Cache:
    def __init__(self, node_id: int):
        self.node_id = node_id
        # 使用UUID作为键的字典
        self.active_bare_layers: Dict[str, BareLayer] = {}  # 活动状态的Bare层，键为UUID
        self.bare_cache_layers: Dict[str, BareLayer] = {}   # 缓存状态的Bare层，键为UUID
        self.active_lang_layers: Dict[str, LangLayer] = {}  # 活动状态的Lang层，键为UUID
        self.lang_cache_layers: Dict[str, LangLayer] = {}   # 缓存状态的Lang层，键为UUID
        
        # 添加辅助索引，用于按类型快速查找
        self.active_bare_index: Dict[int, List[str]] = {}  # 按语言类型索引活动Bare层，值为UUID列表
        self.bare_cache_index: Dict[int, List[str]] = {}   # 按语言类型索引缓存Bare层，值为UUID列表
        self.active_lang_index: Dict[int, List[str]] = {}  # 按语言类型索引活动Lang层，值为UUID列表
        self.lang_cache_index: Dict[int, List[str]] = {}   # 按语言类型索引缓存Lang层，值为UUID列表
        
        self.total_memory_usage = 0.0  # 总内存使用量
    
    def add_active_bare_layer(self, bare_layer: BareLayer):
        """添加活动Bare层"""
        self.active_bare_layers[bare_layer.uuid] = bare_layer
        if bare_layer.lang_type not in self.active_bare_index:
            self.active_bare_index[bare_layer.lang_type] = []
        self.active_bare_index[bare_layer.lang_type].append(bare_layer.uuid)
    
    def add_bare_cache_layer(self, bare_layer: BareLayer):
        """添加缓存Bare层"""
        self.bare_cache_layers[bare_layer.uuid] = bare_layer
        if bare_layer.lang_type not in self.bare_cache_index:
            self.bare_cache_index[bare_layer.lang_type] = []
        self.bare_cache_index[bare_layer.lang_type].append(bare_layer.uuid)
    
    def add_active_lang_layer(self, lang_layer: LangLayer):
        """添加活动Lang层"""
        self.active_lang_layers[lang_layer.uuid] = lang_layer
        if lang_layer.lang_type not in self.active_lang_index:
            self.active_lang_index[lang_layer.lang_type] = []
        self.active_lang_index[lang_layer.lang_type].append(lang_layer.uuid)
    
    def add_lang_cache_layer(self, lang_layer: LangLayer):
        """添加缓存Lang层"""
        self.lang_cache_layers[lang_layer.uuid] = lang_layer
        if lang_layer.lang_type not in self.lang_cache_index:
            self.lang_cache_index[lang_layer.lang_type] = []
        self.lang_cache_index[lang_layer.lang_type].append(lang_layer.uuid)
    
    def remove_active_bare_layer(self, uuid: str):
        """移除活动Bare层"""
        if uuid in self.active_bare_layers:
            bare_layer = self.active_bare_layers[uuid]
            del self.active_bare_layers[uuid]
            if bare_layer.lang_type in self.active_bare_index:
                if uuid in self.active_bare_index[bare_layer.lang_type]:
                    self.active_bare_index[bare_layer.lang_type].remove(uuid)
    
    def remove_bare_cache_layer(self, uuid: str):
        """移除缓存Bare层"""
        if uuid in self.bare_cache_layers:
            bare_layer = self.bare_cache_layers[uuid]
            del self.bare_cache_layers[uuid]
            if bare_layer.lang_type in self.bare_cache_index:
                if uuid in self.bare_cache_index[bare_layer.lang_type]:
                    self.bare_cache_index[bare_layer.lang_type].remove(uuid)
    
    def remove_active_lang_layer(self, uuid: str):
        """移除活动Lang层"""
        if uuid in self.active_lang_layers:
            lang_layer = self.active_lang_layers[uuid]
            del self.active_lang_layers[uuid]
            if lang_layer.lang_type in self.active_lang_index:
                if uuid in self.active_lang_index[lang_layer.lang_type]:
                    self.active_lang_index[lang_layer.lang_type].remove(uuid)
    
    def remove_lang_cache_layer(self, uuid: str):
        """移除缓存Lang层"""
        if uuid in self.lang_cache_layers:
            lang_layer = self.lang_cache_layers[uuid]
            del self.lang_cache_layers[uuid]
            if lang_layer.lang_type in self.lang_cache_index:
                if uuid in self.lang_cache_index[lang_layer.lang_type]:
                    self.lang_cache_index[lang_layer.lang_type].remove(uuid)
  
    def has_bare_cache_layer(self, uuid: str) -> bool:
        return uuid in self.bare_cache_layers

    def has_lang_cache_layer(self, uuid: str) -> bool:
        return uuid in self.lang_cache_layers

    def has_lang_cache_layer_by_type(self, lang_type: int):
        """检查是否有指定类型的缓存Lang层"""
        return lang_type in self.lang_cache_index and len(self.lang_cache_index[lang_type]) > 0

    def has_bare_cache_layer_by_type(self, lang_type: int):
        """检查是否有指定语言类型的缓存Bare层"""
        return lang_type in self.bare_cache_index and len(self.bare_cache_index[lang_type]) > 0

    def move_bare_layer_to_active(self, uuid: str):
        """将Bare层从缓存移动到活动"""
        if uuid in self.bare_cache_layers:
            bare_layer = self.bare_cache_layers[uuid]
            self.add_active_bare_layer(bare_layer)
            self.remove_bare_cache_layer(uuid)
            return bare_layer
        return None
    
    def move_lang_layer_to_active(self, uuid: str):
        """将Lang层从缓存移动到活动"""
        if uuid in self.lang_cache_layers:
            lang_layer = self.lang_cache_layers[uuid]
            self.add_active_lang_layer(lang_layer)
            self.remove_lang_cache_layer(uuid)
            return lang_layer
        return None
        
    def get_bare_cache_layer(self):
        """获取一个缓存Bare层（按创建时间最新的）"""
        # 使用get_bare_cache_layer_by_type
        logging.warning("get_bare_cache_layer is deprecated, use get_bare_cache_layer_by_type instead")
        return None
        
    def get_bare_cache_layer_by_type(self, lang_type: int):
        """获取指定语言类型的缓存Bare层（按创建时间最新的）"""
        if not self.has_bare_cache_layer_by_type(lang_type):
            return None
        
        # 默认取最新创建的
        newest_uuid = max(self.bare_cache_index[lang_type], 
                         key=lambda uuid: self.bare_cache_layers[uuid].creation_time)
        return self.bare_cache_layers[newest_uuid]

    def get_lang_cache_layer_by_type(self, lang_type: int):
        """获取指定类型的缓存Lang层（按创建时间最新的）"""
        if not self.has_lang_cache_layer_by_type(lang_type):
            return None
        
        # 默认取最新创建的
        newest_uuid = max(self.lang_cache_index[lang_type], 
                         key=lambda uuid: self.lang_cache_layers[uuid].creation_time)
        return self.lang_cache_layers[newest_uuid]

    def get_bare_cache_layer_by_uuid(self, uuid: str) -> Optional[BareLayer]:
        return self.bare_cache_layers.get(uuid)

    def get_lang_cache_layer_by_uuid(self, uuid: str) -> Optional[LangLayer]:
        return self.lang_cache_layers.get(uuid)
#endregion

#region ---------------------方法相关类----------------------
# 概率计算类
class ProbPair:
    def __init__(self, func_type: int, probability: float):
        self.func_type = func_type  # <funcType>
        self.probability = probability  # <probability>

class ProbPairVec:
    def __init__(self):
        self.prob_pair_v: List[ProbPair] = []  # 存储 ProbPair 对象的列表

    def sort_vec(self):
        self.prob_pair_v.sort(key=lambda pp: pp.probability)  # 按概率排序

    def push_back(self, pp: ProbPair):
        self.prob_pair_v.append(pp)  # 添加 ProbPair 对象

# Zipf样本类
class Zipf:
    def __init__(self, a, b, s):
        q = 1 - s
        self.qInv = 1 / q
        self.aPowQ = math.exp(math.log(a) * q)
        bPowQ = math.exp(math.log(b) * q)

        self.c = q / (bPowQ - self.aPowQ)
        self.qDivC = q / self.c

    def float64(self, u):
        ln = math.log(u * self.qDivC + self.aPowQ)
        t = math.exp(ln * self.qInv)
        return t

#endregion

#############################################################

#region ---------------------初始化相关----------------------
# 创建函数实例 10个函数 3种语言
def init_func_map():
    # 从latency.py中获取函数执行时间数据
    # 数据格式为 [Setup env.(Bare层), Init.lang.(Lang层), Load lib/code(User层), Execution(执行时间)]
    # 单位为毫秒，需要转换为秒
    latency_data = {
        # Node.js - 统一Bare层(Setup env.)为250，Lang层(Init.lang.)为350
        'AC-Js': [250, 350, 1800, 2100],      
        'IS-Js': [250, 350, 2300, 2700],     
        'UL-Js': [250, 350, 1900, 2300],      
        'TN-Js': [250, 350, 2300, 3100],         
        # Python - 统一Bare层(Setup env.)为300，Lang层(Init.lang.)为300
        'IR-Py': [300, 300, 1200, 6300],   
        'DV-Py': [300, 300, 200, 1400],  
        'FC-Py': [300, 300, 800, 900],       
        'MD-Py': [300, 300, 500, 400],       
        # Java - 统一Bare层(Setup env.)为350，Lang层(Init.lang.)为450
        'DG-Java': [350, 450, 200, 800],     
        'DS-Java': [350, 450, 200, 600],     
    }
    
    # 毫秒转秒的转换因子
    ms_to_sec = 0.001
    
    # --------------------------Node.js-----------------------------
    # 为Node.js设置统一的Bare层和Lang层值
    js_bare_delay = latency_data['AC-Js'][0] * ms_to_sec  # 250ms -> 0.25s
    js_lang_delay = latency_data['AC-Js'][1] * ms_to_sec  # 350ms -> 0.35s
    js_bare_size = 30
    js_lang_size = 120
    
    # AC-Js (1)
    user_delay = latency_data['AC-Js'][2] * ms_to_sec  # 1800ms -> 1.8s
    execution_time = latency_data['AC-Js'][3] * ms_to_sec  # 2100ms -> 2.1s
    cold_start_time = js_bare_delay + js_lang_delay + user_delay
    fi = FunctionInfo(cold_start_time, js_bare_delay, js_lang_delay, user_delay, execution_time, 1, 200, js_bare_size, js_lang_size, 50)
    funcInfoMap_G.add(1, fi)

    # IS-Js (2)
    user_delay = latency_data['IS-Js'][2] * ms_to_sec  # 2300ms -> 2.3s
    execution_time = latency_data['IS-Js'][3] * ms_to_sec  # 2700ms -> 2.7s
    cold_start_time = js_bare_delay + js_lang_delay + user_delay
    fi = FunctionInfo(cold_start_time, js_bare_delay, js_lang_delay, user_delay, execution_time, 1, 260, js_bare_size, js_lang_size, 65)
    funcInfoMap_G.add(2, fi)

    # UL-Js (3)
    user_delay = latency_data['UL-Js'][2] * ms_to_sec  # 1900ms -> 1.9s
    execution_time = latency_data['UL-Js'][3] * ms_to_sec  # 2300ms -> 2.3s
    cold_start_time = js_bare_delay + js_lang_delay + user_delay
    fi = FunctionInfo(cold_start_time, js_bare_delay, js_lang_delay, user_delay, execution_time, 1, 220, js_bare_size, js_lang_size, 60)
    funcInfoMap_G.add(3, fi)

    # TN-Js (4)
    user_delay = latency_data['TN-Js'][2] * ms_to_sec  # 2300ms -> 2.3s
    execution_time = latency_data['TN-Js'][3] * ms_to_sec  # 3100ms -> 3.1s
    cold_start_time = js_bare_delay + js_lang_delay + user_delay
    fi = FunctionInfo(cold_start_time, js_bare_delay, js_lang_delay, user_delay, execution_time, 1, 180, js_bare_size, js_lang_size, 40)
    funcInfoMap_G.add(4, fi)

    # --------------------------Python-----------------------------
    # 为Python设置统一的Bare层和Lang层值
    py_bare_delay = latency_data['IR-Py'][0] * ms_to_sec  # 300ms -> 0.3s
    py_lang_delay = latency_data['IR-Py'][1] * ms_to_sec  # 300ms -> 0.3s
    py_bare_size = 35
    py_lang_size = 50
    
    # IR-Py (5)
    user_delay = latency_data['IR-Py'][2] * ms_to_sec  # 1200ms -> 1.2s
    execution_time = latency_data['IR-Py'][3] * ms_to_sec  # 6300ms -> 6.3s
    cold_start_time = py_bare_delay + py_lang_delay + user_delay
    fi = FunctionInfo(cold_start_time, py_bare_delay, py_lang_delay, user_delay, execution_time, 2, 450, py_bare_size, py_lang_size, 360)
    funcInfoMap_G.add(5, fi)
    
    # DV-Py (6)
    user_delay = latency_data['DV-Py'][2] * ms_to_sec  # 200ms -> 0.2s
    execution_time = latency_data['DV-Py'][3] * ms_to_sec  # 1400ms -> 1.4s
    cold_start_time = py_bare_delay + py_lang_delay + user_delay
    fi = FunctionInfo(cold_start_time, py_bare_delay, py_lang_delay, user_delay, execution_time, 2, 160, py_bare_size, py_lang_size, 70)
    funcInfoMap_G.add(6, fi)

    # FC-Py (7)
    user_delay = latency_data['FC-Py'][2] * ms_to_sec  # 800ms -> 0.8s
    execution_time = latency_data['FC-Py'][3] * ms_to_sec  # 900ms -> 0.9s
    cold_start_time = py_bare_delay + py_lang_delay + user_delay
    fi = FunctionInfo(cold_start_time, py_bare_delay, py_lang_delay, user_delay, execution_time, 2, 190, py_bare_size, py_lang_size, 120)
    funcInfoMap_G.add(7, fi)

    # MD-Py (8)
    user_delay = latency_data['MD-Py'][2] * ms_to_sec  # 500ms -> 0.5s
    execution_time = latency_data['MD-Py'][3] * ms_to_sec  # 400ms -> 0.4s
    cold_start_time = py_bare_delay + py_lang_delay + user_delay
    fi = FunctionInfo(cold_start_time, py_bare_delay, py_lang_delay, user_delay, execution_time, 2, 180, py_bare_size, py_lang_size, 100)
    funcInfoMap_G.add(8, fi)

    # --------------------------Java-----------------------------
    # 为Java设置统一的Bare层和Lang层值
    java_bare_delay = latency_data['DG-Java'][0] * ms_to_sec  # 350ms -> 0.35s
    java_lang_delay = latency_data['DG-Java'][1] * ms_to_sec  # 450ms -> 0.45s
    java_bare_size = 30
    java_lang_size = 175
    
    # DG-Java (9)
    user_delay = latency_data['DG-Java'][2] * ms_to_sec  # 200ms -> 0.2s
    execution_time = latency_data['DG-Java'][3] * ms_to_sec  # 800ms -> 0.8s
    cold_start_time = java_bare_delay + java_lang_delay + user_delay
    fi = FunctionInfo(cold_start_time, java_bare_delay, java_lang_delay, user_delay, execution_time, 3, 300, java_bare_size, java_lang_size, 90)
    funcInfoMap_G.add(9, fi)

    # DS-Java (10)
    user_delay = latency_data['DS-Java'][2] * ms_to_sec  # 200ms -> 0.2s
    execution_time = latency_data['DS-Java'][3] * ms_to_sec  # 600ms -> 0.6s
    cold_start_time = java_bare_delay + java_lang_delay + user_delay
    fi = FunctionInfo(cold_start_time, java_bare_delay, java_lang_delay, user_delay, execution_time, 3, 310, java_bare_size, java_lang_size, 100)
    funcInfoMap_G.add(10, fi)

# 获取容器总大小
def get_container_size(funcType: int) -> int:
    return funcInfoMap_G.get_size(funcType)

# 获取容器总延迟
def get_container_delay(funcType: int) -> float:
    """
    获取某个函数类型的总冷启动延迟时间
    """
    if funcType in funcInfoMap_G.func_map:
        return funcInfoMap_G.func_map[funcType].coldStartTime
    return 0.0

# 初始化分层缓存
def init_layer_map():
    global layer_map_G, topo_G
    for node_id in topo_G.nodes:
        layer_map_G[node_id] = Layer_Cache(node_id)
#endregion

#region ----------------------拓扑相关-----------------------
def load_topo():
    try:
        f = open(config_G.topo_file, 'r')
        # print("open topo file succeessfully.....")
        logging.info("open topo file succeessfully.....")
    except FileNotFoundError:
        print(f"无法找到拓扑文件 {config_G.topo_file}")
        return  # 如果文件未找到，直接返回

    csv_reader = csv.reader(f)
    node_id = 1
    for index, row in enumerate(csv_reader):
        if index == 0:  # 跳过标题行
            continue
        if row[0] == "":  # 跳过空行
            continue

        # 解析 CSV 行并创建 PhyNode 实例
        lat = float(row[1])
        long = float(row[2])

        loc = Location()
        loc.init(lat, long)
        node_map_G[node_id] = loc
        # 创建 PhyNode 实例并添加到 topo_G.nodes
        phynode = PhyNode(
            id=node_id,
            lat=lat,
            long=long,
            mem=config_G.mem_cap,          
            cpuFreq=config_G.cpu_Freq      
        )
        logging.info(f"node {node_id} lat {lat:.6f} long {long:.6f}")
        topo_G.add_node(node_id, phynode)
        node_id += 1
    f.close()
    return

def update_topo(operator: str, phyNodeID: int, mem: float):
    if operator == "add":
        topo_G.update("add", phyNodeID, mem)  # 增加内存
    elif operator == "minus":
        topo_G.update("minus", phyNodeID, mem)  # 减少内存

def get_phy_node(ingress_id: int):
    """根据入口节点 ID 获取物理节点"""
    for node in topo_G.nodes.values():
        if node.id == ingress_id:
            return node, None
    logging.error(f"phyNode {ingress_id} does not exist.....")
    return None, f"Non-exist phyNode {ingress_id}"  

def sort_phynodes(request: Request) -> DistSlice:
    """
    根据请求到物理节点的距离对节点进行排序
    """
    ds = DistSlice() 
    for node in topo_G.nodes.values():
        dist = calculate_distance(request.ingress, node)  # 计算距离
        d = Distance(phyNodeID=node.id, distance=dist)  # 实例化 Distance 对象
        ds.slice.append(d)  # 将 Distance 对象添加到 DistSlice 的切片中

    # 按距离排序
    ds.slice.sort(key=lambda d: d.distance)  # 使用 Python 的 sort 方法进行排序

    return ds  # 返回排序后的 DistSlice 对象

def calculate_distance(phyNode1: PhyNode, phyNode2: PhyNode, unit: str = "K") -> float:
    """
    计算两个物理节点之间的距离
    """
    #print(f"phyNode1 type: {type(phyNode1)}")
    #print(f"phyNode2 type: {type(phyNode2)}")
    lat1 = phyNode1.lat
    lng1 = phyNode1.long
    lat2 = phyNode2.lat
    lng2 = phyNode2.long

    # 如果坐标相同，距离为0
    if lat1 == lat2 and lng1 == lng2:
        return 0.0

    radlat1 = math.radians(lat1)
    radlat2 = math.radians(lat2)

    theta = lng1 - lng2
    radtheta = math.radians(theta)

    dist = math.sin(radlat1) * math.sin(radlat2) + math.cos(radlat1) * math.cos(radlat2) * math.cos(radtheta)
    if dist > 1:
        dist = 1

    dist = math.acos(dist)
    dist = math.degrees(dist)
    dist = dist * 60 * 1.1515  # 转换为英里

    if unit == "K":
        dist = dist * 1.609344  # 转换为公里
    elif unit == "N":
        dist = dist * 0.8684  # 转换为海里

    return dist

def show_nodes_memory():
        logging.info("----node memomry----")
        for node_id in topo_G.nodes:        
            logging.info(f"node {node_id} : {topo_G.nodes[node_id].mem} MB")

#endregion

#region ----------------------请求相关----------------------
# 加载请求文件并创建请求
def process_requests(request_file: str):
    try:
        with open(request_file, mode='r') as file:
            logging.info("open request file succeessfully.....")

            current_time_slot = 0

            for line in file:
                line = line.strip()
                if not line:
                    continue

                # 检查是否是时间槽标题行
                if line.startswith("time_slot"):
                    current_time_slot = int(line.split()[1])
                    continue

                # 解析节点请求数据：节点ID,函数类型1,时间1,函数类型2,时间2,...
                parts = line.split(",")
                if len(parts) < 1:
                    continue

                try:
                    node_id = int(parts[0])

                    # 解析函数类型和时间对
                    i = 1
                    request_count = 0  # 计算当前节点的请求数量

                    while i < len(parts) - 1:
                        try:
                            func_type = int(parts[i])
                            arrival_time = int(parts[i + 1])  # 秒级时间
                            request_count += 1
                            i += 2
                        except (ValueError, IndexError):
                            break

                    # 应用redu_factor来减少请求数量
                    reduced_request_count = max(1, int(request_count / config_G.redu_factor))

                    # 如果减少后的请求数量为0，跳过这个节点
                    if reduced_request_count == 0:
                        continue

                    # 重新解析并创建减少后的请求
                    i = 1
                    created_requests = 0
                    requests_to_create = []

                    # 收集所有请求信息
                    while i < len(parts) - 1 and created_requests < request_count:
                        try:
                            func_type = int(parts[i])
                            arrival_time = int(parts[i + 1])
                            requests_to_create.append((func_type, arrival_time))
                            created_requests += 1
                            i += 2
                        except (ValueError, IndexError):
                            break

                    # 按redu_factor采样请求
                    if len(requests_to_create) > 0:
                        # 计算采样间隔
                        step = max(1, len(requests_to_create) // reduced_request_count)
                        sampled_requests = requests_to_create[::step][:reduced_request_count]

                        # 创建采样后的请求
                        for func_type, arrival_time in sampled_requests:
                            request, err = create_request_with_time(current_time_slot, func_type, node_id, arrival_time)
                            if not err:
                                requestsMap_G.add(request, current_time_slot)

                except ValueError:
                    continue

            file.close()

            # 统计总请求数量
            total_requests = 0
            for time_slot in range(1, current_time_slot + 1):
                requests, found = requestsMap_G.get(time_slot)
                if found:
                    total_requests += len(requests)
            return

    except FileNotFoundError:
        print(f"无法打开请求文件: {request_file}")
        return

def create_request_with_time(time_slot: int, func_type: int, ingress_id: int, arrival_time: int):
    """
    创建带有具体到达时间的请求

    Args:
        time_slot: 时间槽
        func_type: 函数类型
        ingress_id: 入口节点ID
        arrival_time: 在时间槽内的具体到达时间（秒）
    """
    global req_count_G
    # 获取物理节点位置
    loc = node_map_G.get(ingress_id)
    if loc:
        # 创建 PhyNode 实例
        ingress = PhyNode(
            id=ingress_id,
            lat=loc.latitude,
            long=loc.longitude,
            mem=config_G.mem_cap,
            cpuFreq=config_G.cpu_Freq
        )

        # 获取函数信息
        function_info = funcInfoMap_G.func_map.get(func_type)
        if function_info:
            # 创建函数实例
            function = Function(
                type=func_type,
                executionTime=function_info.executionTime,
                coldStartTime=function_info.coldStartTime,
                size=function_info.size,
                creation_time=time_slot,
                lang_type=function_info.lang_type,
                bare_size=function_info.bare_size,
                lang_size=function_info.lang_size,
                user_size=function_info.user_size,
                bare_delay=function_info.bare_delay,
                lang_delay=function_info.lang_delay,
                user_delay=function_info.user_delay
            )

            # 创建请求对象，将具体到达时间存储在请求中
            request = Request(
                id=req_count_G,
                function=function,
                ingress=ingress,
                arriveTime=time_slot,
                served=False,
                isColdStart=False,
                deployNode=None
            )
            # 将秒级时间信息存储在请求的额外属性中
            request.arrival_second = arrival_time  # 在时间槽内的具体秒数
            req_count_G += 1
            return request, None
        else:
            logging.error(f"Cannot find funcType {func_type}")
            return None, f"Cannot find funcType {func_type}"
    else:
        logging.error(f"Non-exist phyNode {ingress_id}")
        return None, f"Non-exist phyNode {ingress_id}"
#endregion

#region ----------------------系统成本----------------------
# 分层容器的实例化成本
def get_layer_instan_cost(phyNodeID: int, bare_size: float = 0.0, lang_size: float = 0.0, user_size: float = 0.0):
    """
    计算分层容器的实例化成本，根据实际创建的层来计算
    """
    cpu_freq = get_cpu(phyNodeID)
    
    if cpu_freq == 0:
        return 0
    
    # 计算实际需要创建的层的总大小
    total_size = bare_size + lang_size + user_size
    
    # 使用与get_instan_cost相同的系数
    instan_cost = total_size / cpu_freq * 0.1
    
    if instan_cost == 0:
        logging.info("layered instant cost is 0")
        
    return instan_cost

# 实例化成本
def get_instan_cost(phyNodeID: int, funcType: int) -> float:
    """
    获取某个物理节点中某个函数类型的实例化成本
    """
    cpu_freq = get_cpu(phyNodeID)
    size = get_container_size(funcType)

    if cpu_freq == 0:
        return 0

    instan_cost = size / cpu_freq * 0.1

    if instan_cost == 0:
        logging.info("instant cost is 0")

    return instan_cost

# 运行成本
def get_run_cost(phyNodeID: int, funcType: int) -> float:
    """
    获取某个物理节点中某个函数类型的运行成本
    """
    cpu_freq = get_cpu(phyNodeID)
    size = get_container_size(funcType)

    if cpu_freq == 0:
        logging.info("run cost cpuFreq is 0")
        return 0

    run_cost = size * cpu_freq * config_G.alpha * 0.01

    return run_cost

# 节点的CPU频率
def get_cpu(phyNodeID: int) -> float:
    """
    获取某个物理节点的CPU频率
    """
    phy_node = topo_G.get(phyNodeID)
    if phy_node.id == 0:
        logging.info("Cannot find the node 0")
        return 0
    else:
        return phy_node.cpuFreq

def check_and_update_memory(node_id: int, required_memory: float, operation: str) -> bool:
    """
    检查节点内存是否足够，并根据操作类型更新内存

    参数:
        node_id: 节点ID
        required_memory: 所需内存大小
        operation: 操作类型 ("check" 仅检查, "reserve" 检查并预留)

    返回值:
        True: 内存足够，False: 内存不足
    """
    current_memory = topo_G.nodes[node_id].mem

    if operation == "check":
        return required_memory <= current_memory
    elif operation == "reserve":
        if required_memory <= current_memory:
            update_topo("minus", node_id, required_memory)
            return True
        else:
            return False

    return False

#endregion

#region ----------------------秒级管理----------------------
def update_second_clock():
    """
    更新秒级时钟，用于跟踪容器执行进度
    """
    global second_clock_G
    second_clock_G += 1
    
def schedule_container_execution(request: Request, node_id: int):
    """
    安排容器执行，计算执行结束时间
    
    参数:
      - request: 请求对象
      - node_id: 节点ID
      
    返回值:
      - 无，但会更新node_execution_end_times_G字典
    """
    global node_execution_end_times_G
    
    # 初始化节点的执行结束时间字典
    if node_id not in node_execution_end_times_G:
        node_execution_end_times_G[node_id] = {}
    
    # 设置请求的开始执行时间
    request.startExecTime = second_clock_G
    
    # 计算执行结束时间
    end_time = second_clock_G + request.executionDuration
    request.endExecTime = end_time
    
    # 记录容器执行结束时间
    node_execution_end_times_G[node_id][request.id] = end_time
    
def check_finished_containers():
    """
    检查已完成执行的容器，处理等待队列中的请求
    
    返回值:
      - 已完成执行的容器数量
    """
    global node_execution_end_times_G
    
    finished_count = 0
    
    # 遍历所有节点
    for node_id in list(node_execution_end_times_G.keys()):
        # 遍历节点上的所有容器
        for request_id in list(node_execution_end_times_G[node_id].keys()):
            # 如果容器执行已完成
            if node_execution_end_times_G[node_id][request_id] <= second_clock_G:
                # 处理该节点上的等待队列
                process_finished_container(node_id)
                # 删除已完成的容器记录
                del node_execution_end_times_G[node_id][request_id]
                finished_count += 1
    
    return finished_count

def process_finished_container(node_id: int):
    """
    处理节点上已完成容器的等待队列
    
    参数:
      - node_id: 节点ID
      
    返回值:
      - 处理的请求数量
    """
    global served_req_count_G
    
    # 如果节点没有等待队列，直接返回
    if node_id not in waiting_queues_G:
        return 0
    
    # 获取节点的等待队列
    queue = waiting_queues_G[node_id]
    
    # 如果队列为空，直接返回
    if queue.is_empty():
        return 0
    
    processed_count = 0
    
    # 尝试处理等待队列中的请求
    max_retries_per_second = MAX_RETRIES_PER_SECOND  # 每秒最大重试次数，避免同一秒内无限重试
    while not queue.is_empty():
        # 获取下一个等待的请求
        request = queue.get_next_request()

        # 检查在当前秒的重试次数
        retry_count = queue.get_retry_count(request.id)
        if retry_count > max_retries_per_second:
            # 超过当前秒的重试次数，将请求重新放入队列，等待下一秒或下一时间槽处理
            queue.add_request(request)
            break  # 跳出循环，等待下一秒

        # 根据缓存策略尝试部署请求
        if config_G.cache_method == "LayerCache":
            # 尝试在当前节点部署请求
            if deploy_to_current(request, True):
                # 如果部署成功，更新请求状态
                request.waitTime = second_clock_G - request.arriveTime
                schedule_container_execution(request, node_id)
                processed_count += 1
                # 继续处理下一个请求
                continue
            else:
                # 如果部署失败，尝试创建新容器
                create_to_current_layercache(request)
                # 如果请求已被服务（create_to_current_layercache成功处理了请求）
                if request.served:
                    request.waitTime = second_clock_G - request.arriveTime
                    schedule_container_execution(request, node_id)
                    processed_count += 1
                    # 继续处理下一个请求
                    continue
                else:
                    # 如果仍然无法处理，检查重试次数
                    if retry_count < max_retries_per_second:
                        # 重新添加到队列末尾
                        queue.add_request(request)
                        break
                    else:
                        # 超过当前秒的重试次数，重新放入队列等待下一秒处理
                        queue.add_request(request)
                        break
        else:
            # 对于其他缓存策略，尝试在当前节点部署请求
            if deploy_to_current(request, False):
                # 如果部署成功，更新请求状态
                request.waitTime = second_clock_G - request.arriveTime
                schedule_container_execution(request, node_id)
                processed_count += 1
                # 继续处理下一个请求
                continue
            else:
                # 如果部署失败，根据缓存策略尝试创建新容器
                if config_G.cache_method == "FaaSCache":
                    create_to_current_faascache(request)
                elif config_G.cache_method == "CrossEdge":
                    create_to_current_crossedge(request)
                elif config_G.cache_method == "OpenWhisk":
                    create_to_current_openwhisk(request)

                # 如果请求已被服务
                if request.served:
                    request.waitTime = second_clock_G - request.arriveTime
                    schedule_container_execution(request, node_id)
                    processed_count += 1
                    # 继续处理下一个请求
                    continue
                else:
                    # 如果仍然无法处理，检查重试次数
                    if retry_count < max_retries_per_second:
                        # 重新添加到队列末尾
                        queue.add_request(request)
                        break
                    else:
                        # 超过当前秒的重试次数，重新放入队列等待下一秒处理
                        queue.add_request(request)
                        break
    
    return processed_count

def move_waiting_requests_to_next_slot(current_slot: int, next_slot: int):
    """
    将当前时间槽中未处理的请求移至下一时间槽
    
    参数:
      - current_slot: 当前时间槽
      - next_slot: 下一时间槽
      
    返回值:
      - 移动的请求数量
    """
    global waiting_queues_G, requestsMap_G
    
    moved_count = 0
    
    # 遍历所有节点的等待队列
    for node_id, queue in waiting_queues_G.items():
        # 如果队列不为空，将请求移至下一时间槽
        if not queue.is_empty():
            # 获取队列中的请求数量
            queue_size = queue.size()
            moved_count += queue_size
            
            # 将请求移至下一时间槽
            queue.move_to_next_slot(next_slot, requestsMap_G)
                # 删除日志记录
            # logging.info(f"节点 {node_id} 将 {queue_size} 个请求从时间槽 {current_slot} 移至时间槽 {next_slot}")
    
    return moved_count

def add_request_to_waiting_queue(request: Request, node_id: int):
    """
    将请求添加到指定节点的等待队列

    参数:
        request: 请求对象
        node_id: 节点ID
    """
    global waiting_queues_G, wait_count_G, is_extra_slot_G, is_processing_waiting_queue_G

    # 在额外时间槽中或处理等待队列时，不添加请求到等待队列，避免重复添加
    if is_extra_slot_G or is_processing_waiting_queue_G:
        return

    if node_id not in waiting_queues_G:
        waiting_queues_G[node_id] = WaitingQueue(node_id)

    # 设置请求的执行时间（秒）
    request.executionDuration = request.function.executionTime

    # 添加到等待队列
    waiting_queues_G[node_id].add_request(request)
    # 注意：wait_count_G 的计数现在由调用方控制，确保计数逻辑的一致性

#endregion

#region ----------------------预热保活----------------------
def prewarm_containers(is_layered: bool = None):
    """
    统一的容器预热函数，支持全容器和分层容器两种缓存策略
    根据函数调用频率预热容器

    参数:
        is_layered: 是否为分层缓存（None时自动检测）
    """
    global layer_map_G, cacheMap_G, topo_G, funcInfoMap_G, clock_G

    # 自动检测缓存类型（如果未指定）
    if is_layered is None:
        is_layered = config_G.cache_method == "LayerCache"

    # 获取函数调用频率
    function_frequencies = {}
    for func_type, freq in functionfreq_G.map.items():
        function_frequencies[func_type] = freq

    # 按频率排序
    sorted_functions = sorted(function_frequencies.items(), key=lambda x: x[1], reverse=True)

    # 对于全容器缓存，限制预热数量
    if not is_layered:
        sorted_functions = sorted_functions[:5]  # 预热前5个高频函数
    
    # 为每个节点预热容器
    for node_id, node in topo_G.nodes.items():
        # 获取节点的可用内存
        node_memory = node.getMem()

        # 确保节点内存足够
        if node_memory <= 0:
            if is_layered:
                logging.warning(f"node {node_id} memory empty ({node_memory} MB), skip prewarm")
            continue

        available_memory = node_memory

        # 根据缓存类型初始化相应的数据结构
        if is_layered:
            # 获取或创建节点的分层缓存
            if node_id not in layer_map_G:
                layer_map_G[node_id] = Layer_Cache(node_id)
            layered_cache = layer_map_G[node_id]
        else:
            # 获取或创建节点的全容器缓存
            if node_id not in cacheMap_G.caches:
                cacheMap_G.caches[node_id] = Cache(node_id)
            cache = cacheMap_G.caches[node_id]

        # 预热容器（按照函数调用频率排序）
        for func_type, _ in sorted_functions:
            # 检查可用内存
            available_memory = node.getMem()
            if available_memory <= 0:
                if is_layered:
                    logging.warning(f"node {node_id} memory empty ({available_memory} MB), skip prewarm")
                break

            # 检查该函数类型是否已存在
            if is_layered:
                # 分层缓存：检查User层是否已存在于cacheMap_G中
                f, i = cacheMap_G.get_idle_function(node_id, func_type)
                if i != -1:
                    continue
            else:
                # 全容器缓存：检查函数是否已存在于cache中
                if func_type in [f.type for f in cache.functionList]:
                    continue

            # 获取函数信息
            if func_type not in funcInfoMap_G.func_map:
                continue
                
            function_info = funcInfoMap_G.func_map[func_type]

            if is_layered:
                # 分层缓存：计算三层内存
                user_size = function_info.user_size
                lang_type = function_info.lang_type
                lang_size = function_info.lang_size
                bare_size = function_info.bare_size

                # 计算所需总内存
                required_memory = 0

                # 如果没有对应语言类型的Bare层，需要创建
                bare_exists = check_layer_exists("bare", lang_type, layered_cache)
                if not bare_exists:
                    required_memory += bare_size

                # 如果没有对应的Lang层，需要创建
                lang_exists = check_layer_exists("lang", lang_type, layered_cache)
                if not lang_exists:
                    required_memory += lang_size

                # 加上User层所需内存
                required_memory += user_size
            else:
                # 全容器缓存：只需要计算单层内存
                required_memory = function_info.size

            # 检查总内存是否足够
            if available_memory < required_memory:
                continue

            if is_layered:
                # 分层缓存：创建三层结构
                # 创建必要的Bare层（如果不存在）
                if not bare_exists:
                    bare_layer = BareLayer(bare_size, lang_type, clock_G)
                    # 将Bare层添加到活动层
                    layered_cache.add_active_bare_layer(bare_layer)
                    layered_cache.total_memory_usage += bare_size
                    available_memory -= bare_size
                    update_topo("minus", node_id, bare_size)
                else:
                    # 使用现有的Bare层，从缓存移动到活动状态，不需要更新内存
                    if lang_type in layered_cache.bare_cache_index and layered_cache.bare_cache_index[lang_type]:
                        bare_uuid = layered_cache.bare_cache_index[lang_type][0]  # 获取对应语言类型的第一个缓存Bare层
                        bare_layer = layered_cache.move_bare_layer_to_active(bare_uuid)
                        # 只更新使用时间，无需更新内存
                        if bare_layer is not None:
                            bare_layer.update_usage(clock_G)

                # 创建必要的Lang层（如果不存在）
                if not lang_exists:
                    lang_layer = LangLayer(
                        lang_size, lang_type,
                        bare_layer.uuid,  # 改为使用UUID引用而不是直接对象引用
                        clock_G)
                    # 将Lang层添加到活动层
                    layered_cache.add_active_lang_layer(lang_layer)
                    layered_cache.total_memory_usage += lang_size
                    available_memory -= lang_size
                    update_topo("minus", node_id, lang_size)
                else:
                    # 使用现有的Lang层，从缓存移动到活动状态，不需要更新内存
                    if lang_type in layered_cache.lang_cache_index:
                        lang_uuid = layered_cache.lang_cache_index[lang_type][0]  # 获取指定类型的第一个缓存Lang层
                        lang_layer = layered_cache.move_lang_layer_to_active(lang_uuid)
                        # 只更新使用时间，无需更新内存
                        if lang_layer is not None:
                            lang_layer.update_usage(clock_G)

                # 创建User层（使用activeFunctions_G和cacheMap_G管理）
                # 创建函数实例
                f = Function(
                    type=func_type,
                    executionTime=function_info.executionTime,
                    coldStartTime=function_info.coldStartTime,
                    size=function_info.size,
                    phyNode=node,
                    lang_type=lang_type,
                    bare_size=bare_size,
                    lang_size=lang_size,
                    user_size=user_size,
                    bare_delay=function_info.bare_delay,
                    lang_delay=function_info.lang_delay,
                    user_delay=function_info.user_delay
                )

                # 建立对Lang层和Bare层的UUID引用
                f.lang_layer_uuid = lang_layer.uuid
                f.bare_layer_uuid = lang_layer.bare_layer_uuid

                # 添加到活动函数列表
                activeFunctions_G.add(f, node_id)

                # 更新内存（User层）
                update_topo("minus", node_id, user_size)
            else:
                # 全容器缓存：创建单层函数
                # 创建函数实例
                function = Function(
                    type=func_type,
                    size=function_info.size,
                    phyNode=node
                )

                # 添加到缓存
                cacheMap_G.add(function)
                node_memory -= function_info.size
                update_topo("minus", node_id, function_info.size)

def update_cache():
    """
    在每个时间间隔结束时，更新优先级，将活动函数移动到缓存函数
    支持全容器和分层容器两种缓存策略
    """
    global clock_G, activeFunctions_G, cacheMap_G, layer_map_G

    # 遍历所有节点上的活动函数，只处理执行完成的容器
    completed_functions = []  # 存储执行完成的函数
    is_layered = config_G.cache_method == "LayerCache"

    for phyNodeID, node_functions in activeFunctions_G.map.items():
        # 遍历节点上各种类型的函数
        for funcType, functions in node_functions.functions.items():
            # 从后往前遍历，以便安全删除
            for i in range(len(functions.slice) - 1, -1, -1):
                f = functions.slice[i]
                # 只处理执行完成的容器（不在执行队列中的）
                if not is_container_executing(phyNodeID, f):
                    # 清除容器的执行状态
                    if hasattr(f, 'is_executing'):
                        f.is_executing = False
                    cacheMap_G.add(f)
                    completed_functions.append((phyNodeID, funcType, i))

    # 从活动函数列表中移除执行完成的函数
    for phyNodeID, funcType, index in completed_functions:
        if is_layered:
            # 分层缓存：直接访问activeFunctions_G.map
            if phyNodeID in activeFunctions_G.map:
                node_functions = activeFunctions_G.map[phyNodeID]
                if funcType in node_functions.functions:
                    functions = node_functions.functions[funcType]
                    if index < len(functions.slice):
                        functions.slice.pop(index)
        else:
            # 全容器缓存：通过activeFunctions_G.map.map访问
            if phyNodeID in activeFunctions_G.map.map:
                node_functions = activeFunctions_G.map.map[phyNodeID]
                if funcType in node_functions.functions:
                    functions = node_functions.functions[funcType]
                    if index < len(functions.slice):
                        functions.slice.pop(index)

    # 根据不同的缓存方法调用相应的排序函数
    if config_G.cache_method == "OpenWhisk":
        cacheMap_G.sort_life_time()
    else:
        cacheMap_G.sort()

    # 处理特定缓存策略的额外逻辑
    if is_layered:
        update_lifecycle_layercache()
    elif config_G.cache_method == "OpenWhisk":
        update_lifecycle_openwhisk()

def update_lifecycle_layercache():
    """
    更新分层容器的保活状态，处理层的生命周期管理
    """
    global clock_G, layer_map_G

    # 更新分层容器的保活状态
    for node_id, layer_cache in layer_map_G.items():        
        # 处理缓存中的User层容器（检查TTL并降级到Lang层）
        if node_id in cacheMap_G.caches:
            cache = cacheMap_G.caches[node_id]
            functions_to_remove = []

            for i, func in enumerate(cache.functionList):
                # 检查User层容器是否有TTL并且已过期
                if hasattr(func, 'lang_layer_uuid') and hasattr(func, 'user_delay'):
                    # 计算User层的TTL（使用统一的计算方法）
                    user_ttl = calculate_keep_alive_time(func, func.type)

                    # 检查是否超过TTL（使用函数的创建时间或最后使用时间）
                    last_used = getattr(func, 'last_used_time', getattr(func, 'creation_time', clock_G))
                    if clock_G - last_used > user_ttl:
                        # User层容器超时，降级到Lang层
                        # 释放User层占用的内存
                        user_size = getattr(func, 'user_size', func.size)
                        update_topo("add", node_id, user_size)

                        # 记录降级操作
                        if node_id == 96:
                            logging.info(f"DEBUG: 节点96 User层容器超时降级，释放内存 {user_size}MB，类型: {func.type}")

                        # 标记要移除的函数
                        functions_to_remove.append(i)

            # 从后往前删除，避免索引问题
            for i in reversed(functions_to_remove):
                cache.functionList.pop(i)
        
        # 将活动Bare层移动到缓存列表
        for bare_uuid, bare_layer in list(layer_cache.active_bare_layers.items()):
            layer_cache.add_bare_cache_layer(bare_layer)
            bare_layer.update_usage(clock_G)
            layer_cache.remove_active_bare_layer(bare_uuid)

        # 将活动Lang层移动到缓存列表
        for lang_uuid, lang_layer in list(layer_cache.active_lang_layers.items()):
            layer_cache.add_lang_cache_layer(lang_layer)
            lang_layer.update_usage(clock_G)
            layer_cache.remove_active_lang_layer(lang_uuid)

        # 处理缓存中的Lang层容器
        for lang_uuid, lang_layer in list(layer_cache.lang_cache_layers.items()):
            # 检查是否有User层引用该Lang层
            has_reference = False
            if node_id in cacheMap_G.caches:
                cache = cacheMap_G.caches[node_id]
                for func in cache.functionList:
                    if hasattr(func, 'lang_layer_uuid') and func.lang_layer_uuid == lang_layer.uuid:
                        has_reference = True
                        break

            if not has_reference:
                ttl = calculate_keep_alive_time(lang_layer)
                if clock_G - lang_layer.last_used_time > ttl:
                    if lang_uuid in layer_cache.lang_cache_layers:
                        lang_size = lang_layer.size
                        layer_cache.total_memory_usage -= lang_size
                        creation_node_id = lang_layer.node_id if lang_layer.node_id is not None else node_id
                        if creation_node_id == 96:
                            logging.info(f"DEBUG: 节点96释放Lang层内存 {lang_size}MB，UUID: {lang_uuid}")
                        update_topo("add", creation_node_id, lang_size)
                        layer_cache.remove_lang_cache_layer(lang_uuid)

        # 处理缓存中的Bare层容器
        for lang_type in list(layer_cache.bare_cache_index.keys()):
            for bare_uuid in list(layer_cache.bare_cache_index.get(lang_type, [])):
                has_reference = False
                for lang_uuid, lang_layer in layer_cache.lang_cache_layers.items():
                    if hasattr(lang_layer, 'bare_layer_uuid') and lang_layer.bare_layer_uuid == bare_uuid:
                        has_reference = True
                        break

                if not has_reference:
                    bare_layer = layer_cache.bare_cache_layers.get(bare_uuid)
                    if bare_layer:
                        ttl = calculate_keep_alive_time(bare_layer, None)
                        if clock_G - bare_layer.last_used_time > ttl:
                            if bare_uuid in layer_cache.bare_cache_layers:
                                bare_size = bare_layer.size
                                layer_cache.total_memory_usage -= bare_size
                                creation_node_id = bare_layer.node_id if bare_layer.node_id is not None else node_id
                                if creation_node_id == 96:
                                    logging.info(f"DEBUG: 节点96释放Bare层内存 {bare_size}MB，UUID: {bare_uuid}")
                                update_topo("add", creation_node_id, bare_size)
                                layer_cache.remove_bare_cache_layer(bare_uuid)

def update_lifecycle_openwhisk():
    """
    处理OpenWhisk策略的容器生命周期管理
    """
    # 遍历所有节点，处理缓存中的函数生命周期
    for phyNodeID in list(cacheMap_G.caches.keys()):
        if phyNodeID in cacheMap_G.caches:
            cache = cacheMap_G.caches[phyNodeID]
            i = 0
            while i < len(cache.functionList):
                # 减少生命周期
                if cache.functionList[i].minusLife():
                    # 如果生命周期结束，释放内存并删除函数
                    function = cache.functionList[i]
                    cache.functionList.pop(i)
                    # 更新节点内存
                    update_topo("add", phyNodeID, function.size)
                else:
                    i += 1

def is_container_executing(node_id: int, function, is_layered: bool = None) -> bool:
    """
    统一的容器执行状态检查函数，支持全容器和分层容器

    参数:
        node_id: 节点ID
        function: 函数对象
        is_layered: 是否为分层缓存（None时自动检测）

    返回值:
        True: 容器正在执行，False: 容器未在执行
    """
    # 自动检测缓存类型（如果未指定）
    if is_layered is None:
        is_layered = config_G.cache_method == "LayerCache"

    # 检查函数的执行状态标志
    if hasattr(function, 'is_executing') and function.is_executing:
        return True

    # 检查是否在执行队列中
    for execution_info in node_execution_end_times_G:
        if isinstance(execution_info, dict):
            if (execution_info.get('node_id') == node_id and
                execution_info.get('function') == function):
                return True
        elif not is_layered:
            # 对于全容器缓存，处理其他结构（如果需要）
            continue

    return False

def set_container_executing_state(request: Request, function: Function, node_id: int, is_cold_start: bool, link_delay: float):
    """
    设置容器的执行状态并更新相关数据结构

    参数:
        request: 请求对象
        function: 函数对象
        node_id: 节点ID
        is_cold_start: 是否为冷启动
        link_delay: 链路延迟
    """
    # 设置容器为执行状态
    function.is_executing = True

    # 添加到活动函数列表
    activeFunctions_G.add(function, node_id)

    # 更新拓扑内存
    update_topo("minus", node_id, function.size)

    # 获取部署节点
    deploy_node = topo_G.get(node_id)
    if deploy_node.id != 0:
        request.update(function, deploy_node, is_cold_start, link_delay)

# 目前固定TTL=10
def calculate_keep_alive_time(container_layer, func_type: int = None) -> int:
    """
    计算容器的保活时间（TTL）
    TTL = min(IAT, β)，其中 β = (α * t)/((1-α) * m)

    参数:
        container_layer: ContainerLayer对象（Bare/Lang层）或Function对象（User层）
        func_type: 函数类型（可选）
    """
    global config_G, funcInfoMap_G

    # 获取容器内存占用
    # 对于User层容器（Function对象），使用user_size；对于其他层，使用size
    if hasattr(container_layer, 'user_size'):
        # User层容器
        memory_usage = container_layer.user_size
        # 如果没有提供func_type，从Function对象获取
        if func_type is None:
            func_type = container_layer.type
    else:
        # Bare/Lang层容器
        memory_usage = container_layer.size
    
    # 获取启动延迟时间
    startup_delay = 0

    # 获取函数的层级冷启动时间
    if func_type is not None and func_type in funcInfoMap_G.func_map:
        # 根据层类型获取对应的延迟时间
        if hasattr(container_layer, 'user_size'):
            # User层容器（Function对象）
            startup_delay = funcInfoMap_G.func_map[func_type].user_delay
        elif isinstance(container_layer, LangLayer):
            # Lang层容器
            startup_delay = funcInfoMap_G.func_map[func_type].lang_delay
        elif isinstance(container_layer, BareLayer):
            # Bare层容器
            startup_delay = funcInfoMap_G.func_map[func_type].bare_delay
    else:
        # 如果没有提供func_type，尝试从容器层本身获取信息
        if hasattr(container_layer, 'user_size'):
            # User层容器，尝试从Function对象获取
            if hasattr(container_layer, 'user_delay'):
                startup_delay = container_layer.user_delay
        elif isinstance(container_layer, BareLayer) and hasattr(container_layer, 'lang_type'):
            lang_type = container_layer.lang_type
            # 查找使用该语言类型的任意函数
            for f_type, f_info in funcInfoMap_G.func_map.items():
                if f_info.lang_type == lang_type:
                    startup_delay = f_info.bare_delay
                    break
    
    alpha = config_G.alpha  
    beta = (alpha * startup_delay) / ((1 - alpha) * memory_usage)
    
    # 预测的请求间隔时间（IAT）
    # 这里可以基于历史数据使用泊松分布预测，简化实现可以使用函数调用频率的倒数
    iat = 10  # 默认IAT值
    if func_type is not None and func_type in functionfreq_G.map and functionfreq_G.map[func_type] > 0:
        iat = max(5, 100 / functionfreq_G.map[func_type])  # 防止频率过低导致IAT过大
        # 这里func_type为KeyError: None
        # print(f"freq:{functionfreq_G.map[func_type]}")
    
    # 返回 min(IAT, β)
    # IAT范围为5-10是否合理
    ttl = min(iat, beta * 10000)    # 乘以系数调整时间单位
    # print(f"iat: {iat}, beta:{beta*10000}")
    # return max(5, int(ttl))
    return 10

#endregion

#region ----------------------对比方法----------------------
# 基于优先级的全容器驱逐策略(FaaSCache)
def create_to_current_faascache(request: Request):
    global new_total_memory_G, new_container_counter_G, wait_count_G, waiting_queues_G, is_extra_slot_G, is_processing_waiting_queue_G
    global create_current_req_G
    
    # 进入创建容器逻辑，先增加计数（在处理等待队列时不重复计数）
    if not is_processing_waiting_queue_G:
        create_current_req_G += 1

    request.function.active_priority()  # 更新函数优先级
    f = Function()
    succ_flag = True
    # 如果节点内存不足，先尝试删除低优先级容器
    if request.function.size > topo_G.nodes[request.ingress.id].mem:
        while succ_flag:
            # 获取最低优先级的容器
            lowest_priority = cacheMap_G.get_lowest_priority(request.ingress.id)
            # 请求的优先级 > 优先级最低的容器        
            if request.function.priority > lowest_priority:
                f, succ_flag = cacheMap_G.delete_low_function(request.ingress.id)
                if succ_flag:
                    update_topo("add", request.ingress.id, f.size)  # 更新内存
                else:
                    # 无法删除低优先级容器，将请求添加到等待队列
                    # 在额外时间槽中或处理等待队列时不添加到等待队列，避免重复添加
                    if not is_extra_slot_G and not is_processing_waiting_queue_G:
                        add_request_to_waiting_queue(request, request.ingress.id)
                        # 调整计数：创建失败，从create_current_req_G转移到wait_count_G
                        create_current_req_G -= 1
                        if not is_processing_waiting_queue_G:
                            wait_count_G += 1
                            # logging.info(f"[wait_count调试] FaaSCache创建失败1: wait_count_G += 1, 当前值: {wait_count_G}, 请求类型: {request.function.type}")
                    return
                # 如果销毁后的内存空间充足
                if request.function.size <= topo_G.nodes[request.ingress.id].mem:
                    request.function.phyNode = request.ingress  # 设置请求的物理节点为当前节点
                    set_container_executing_state(request, request.function, request.ingress.id, True, 0)

                    # 记录新创建全容器的内存使用
                    new_total_memory_G += request.function.size
                    new_container_counter_G += 1  # 增加全容器计数
                    return
            else:
                # 请求优先级不够，将请求添加到等待队列
                # 在额外时间槽中或处理等待队列时不添加到等待队列，避免重复添加
                if not is_extra_slot_G and not is_processing_waiting_queue_G:
                    add_request_to_waiting_queue(request, request.ingress.id)
                    # 调整计数：创建失败，从create_current_req_G转移到wait_count_G
                    create_current_req_G -= 1
                    if not is_processing_waiting_queue_G:
                        wait_count_G += 1
                        # logging.info(f"[wait_count调试] FaaSCache创建失败2: wait_count_G += 1, 当前值: {wait_count_G}, 请求类型: {request.function.type}")
                return
    # 内存足够，创建新容器
    else:
        # 记录新创建全容器的内存使用
        new_total_memory_G += request.function.size
        new_container_counter_G += 1  # 增加全容器计数

        request.function.phyNode = request.ingress  # 设置请求的物理节点为当前节点
        set_container_executing_state(request, request.function, request.ingress.id, True, 0)

# 基于驱逐概率的驱逐策略(CrossEdge)
def create_to_current_crossedge(request: Request):
    global new_total_memory_G, new_container_counter_G, wait_count_G, waiting_queues_G, is_extra_slot_G, is_processing_waiting_queue_G
    global create_current_req_G

    # 进入创建容器逻辑，先增加计数（在处理等待队列时不重复计数）
    if not is_processing_waiting_queue_G:
        create_current_req_G += 1

    succ_flag = True
    count = 0
    # 检查请求的函数大小超过当前节点的内存容量，尝试驱逐容器
    if request.function.size > topo_G.nodes[request.ingress.id].mem:  
        while count < MAX_EVICTION_ATTEMPTS:
            count += 1
            # 获取随机驱逐的函数类型
            func_type = get_evicted_container(request.ingress.id, request.function.type)
            
            # 要驱逐的容器类型与请求的相同，则添加到等待队列
            if func_type == request.function.type:
                # 将请求添加到等待队列
                # 在额外时间槽中或处理等待队列时不添加到等待队列，避免重复添加
                if not is_extra_slot_G and not is_processing_waiting_queue_G:
                    add_request_to_waiting_queue(request, request.ingress.id)
                    # 调整计数：创建失败，从create_current_req_G转移到wait_count_G
                    create_current_req_G -= 1
                    if not is_processing_waiting_queue_G:
                        wait_count_G += 1
                return

            succ_flag = cacheMap_G.delete_Prob(request.ingress.id, func_type)
            function_size = get_container_size(func_type)
            
            # 如果删除成功，更新节点的内存信息
            if succ_flag:  
                update_topo("add", request.ingress.id, function_size)  # 更新内存
            
            # 节点内存足够以创建新的请求容器，标记为冷启动，更新信息
            if request.function.size <= topo_G.get(request.ingress.id).mem:
                # 记录新创建全容器的内存使用
                new_total_memory_G += request.function.size
                new_container_counter_G += 1  # 增加全容器计数

                request.function.phyNode = request.ingress

                # 设置容器为执行状态
                request.function.is_executing = True
                activeFunctions_G.add(request.function, request.ingress.id)

                # CrossEdge特有的频率和最近使用时间更新
                topo_G.addFreqAll(request.function.type)
                topo_G.setRecencyAll(request.function.type, float(request.arriveTime))

                update_topo("minus", request.ingress.id, request.function.size)
                request.update(request.function, request.ingress, True, 0)
                return
            
            # 如果删除不成功且没有足够空间，则继续尝试
            if not succ_flag: 
                continue
        
        # 尝试1000次后仍无法腾出足够空间，将请求添加到等待队列
        # 在额外时间槽中或处理等待队列时不添加到等待队列，避免重复添加
        if not is_extra_slot_G and not is_processing_waiting_queue_G:
            add_request_to_waiting_queue(request, request.ingress.id)
            # 调整计数：创建失败，从create_current_req_G转移到wait_count_G
            create_current_req_G -= 1
            if not is_processing_waiting_queue_G:
                wait_count_G += 1
        return
    else:
        # 记录新创建全容器的内存使用
        new_total_memory_G += request.function.size
        new_container_counter_G += 1  # 增加全容器计数

        request.function.phyNode = request.ingress

        # 设置容器为执行状态
        request.function.is_executing = True
        activeFunctions_G.add(request.function, request.ingress.id)

        # CrossEdge特有的频率和最近使用时间更新
        topo_G.addFreqAll(request.function.type)
        topo_G.setRecencyAll(request.function.type, float(request.arriveTime))

        update_topo("minus", request.ingress.id, request.function.size)
        request.update(request.function, request.ingress, True, 0)
        return

# 计算容器的驱逐概率(CrossEdge)
def get_prob(nodeID: int, funcType: int) -> float:
    phy_node = topo_G.get(nodeID)
    instan_cost = get_instan_cost(nodeID, funcType)
    freq = phy_node.getFreq(funcType)
    recency = phy_node.getRecency(funcType)

    size = get_container_size(funcType)

    if freq == 0:
        logging.error(f"cannot find freq for funcType {funcType} at node {nodeID}")
        return 0
    if recency == 0:
        logging.error(f"cannot find recent for funcType {funcType} at node {nodeID}")
        return 0
    if size == 0:
        logging.error(f"cannot find size for funcType {funcType} at node {nodeID}")
        return 0

    # 将单位从MB转换，这将减少size在概率计算中的影响
    # return (float)size / (freq*instanCost + recent)
    return size / (freq + recency) / 1000

# 获取要被驱逐的容器类型(CrossEdge)
def get_evicted_container(nodeID: int, reqFuncType: int) -> int:
    # 计算请求函数类型的驱逐概率
    threshold = get_prob(nodeID, reqFuncType)

    # 创建一个字典来存储概率
    prob_map = {}
    total_prob = 0

    # 计算每个函数类型的概率
    for funcType in funcInfoMap_G.func_map.keys():
        prob = get_prob(nodeID, funcType)
        prob_map[funcType] = prob
        total_prob += prob

    # 将阈值转换为概率
    threshold = threshold / total_prob

    # 归一化概率
    prob_pair_vec = ProbPairVec()
    for funcType, prob in prob_map.items():
        normalized_prob = prob / total_prob
        prob_pair = ProbPair(funcType, normalized_prob)
        prob_pair_vec.push_back(prob_pair)

    # 对概率进行排序
    prob_pair_vec.sort_vec()

    # 生成一个0到100之间的随机整数
    val = np.random.randint(0, 100) 

    # 累积概率并确定要驱逐的函数类型
    accum_prob = 0
    for prob_pair in prob_pair_vec.prob_pair_v:
        accum_prob += prob_pair.probability
        if float(val) < (accum_prob * 100):          
            # logging.info(f"evict type {prob_pair.func_type}")
            return prob_pair.func_type

    return 0  # 如果没有需要驱逐的容器，返回 0

# 基于固定缓存周期策略(OpenWhisk)
def create_to_current_openwhisk(request: Request):
    """
    使用固定缓存（OpenWhisk）策略在当前节点创建新容器
    容器将保持固定时间（lifeTime）的活动状态
    1. 如果内存不足，尝试删除生命周期最低的容器，直到有足够空间
    2. 如果仍然无法腾出足够空间，将请求添加到等待队列
    """
    global new_total_memory_G, new_container_counter_G, wait_count_G, waiting_queues_G, is_extra_slot_G, is_processing_waiting_queue_G
    global create_current_req_G

    # 进入创建容器逻辑，先增加计数（在处理等待队列时不重复计数）
    if not is_processing_waiting_queue_G:
        create_current_req_G += 1

    succ_flag = True
    count = 0
    if request.function.size > topo_G.get(request.ingress.id).mem:
        while count < MAX_EVICTION_ATTEMPTS:
            count += 1            
            # 获取生命周期最低的函数
            function, succ_flag = cacheMap_G.delete_low_lifetime_function(request.ingress.id)
            
            if succ_flag:
                update_topo("add", request.ingress.id, function.size)
            
            if function.type == request.function.type:
                # 如果要驱逐的容器类型与请求相同，添加到等待队列
                # 在额外时间槽中或处理等待队列时不添加到等待队列，避免重复添加
                if not is_extra_slot_G and not is_processing_waiting_queue_G:
                    add_request_to_waiting_queue(request, request.ingress.id)
                    # 调整计数：创建失败，从create_current_req_G转移到wait_count_G
                    create_current_req_G -= 1
                    if not is_processing_waiting_queue_G:
                        wait_count_G += 1
                return
            
            if request.function.size <= topo_G.get(request.ingress.id).mem:
                # 有足够空间，创建新容器
                # 记录新创建全容器的内存使用
                new_total_memory_G += request.function.size
                new_container_counter_G += 1  # 增加全容器计数

                request.function.phyNode = request.ingress
                request.function.creation_time = clock_G  # 记录创建时间
                request.function.activeLifeTime(CONTAINER_LIFETIME)  # 设置容器生命周期
                set_container_executing_state(request, request.function, request.ingress.id, True, 0)
                return
            
            if not succ_flag:
                # 无法删除容器，添加到等待队列
                # 在额外时间槽中或处理等待队列时不添加到等待队列，避免重复添加
                if not is_extra_slot_G and not is_processing_waiting_queue_G:
                    add_request_to_waiting_queue(request, request.ingress.id)
                    # 调整计数：创建失败，从create_current_req_G转移到wait_count_G
                    create_current_req_G -= 1
                    if not is_processing_waiting_queue_G:
                        wait_count_G += 1
                return

        # 尝试1000次后仍无法腾出足够空间，将请求添加到等待队列
        # 在额外时间槽中或处理等待队列时不添加到等待队列，避免重复添加
        if not is_extra_slot_G and not is_processing_waiting_queue_G:
            add_request_to_waiting_queue(request, request.ingress.id)
            # 调整计数：创建失败，从create_current_req_G转移到wait_count_G
            create_current_req_G -= 1
            if not is_processing_waiting_queue_G:
                wait_count_G += 1
        return
    else:
        # 内存足够，直接创建容器
        # 记录新创建全容器的内存使用
        new_total_memory_G += request.function.size
        new_container_counter_G += 1  # 增加全容器计数

        request.function.phyNode = request.ingress
        request.function.creation_time = clock_G  # 记录创建时间
        request.function.activeLifeTime(CONTAINER_LIFETIME)  # 设置容器生命周期
        set_container_executing_state(request, request.function, request.ingress.id, True, 0)

# 检查容器是否过期(OpenWhisk)
def is_container_expired(function: Function) -> bool:
    """
    根据容器的lifeTime判断是否过期
    lifeTime小于等于0表示容器已过期
    """
    return function.lifeTime <= 0

#endregion

# region --------------------LayerCache---------------------
# 基于分层容器的User层驱逐策略(LayerCache)
def create_to_current_layercache(request: Request):
    """
    在当前节点创建新容器，使用RainbowCake的分层容器缓存机制
    # 1. 计算所需内存
    # 2. 检查各层是否存在
    # 3. 如果内存不足，调用evict_user_layer
    # 4. 创建和部署容器
    """
    # 声明所有需要使用的全局变量
    global new_bare_memory_G, new_lang_memory_G, new_user_memory_G, new_total_memory_G
    global new_bare_counter_G, new_lang_counter_G, new_user_counter_G, is_extra_slot_G
    global create_current_req_G, wait_count_G, is_processing_waiting_queue_G
    
    node_id = request.ingress.id

    # 进入创建容器逻辑，先增加计数（在处理等待队列时不重复计数）
    if not is_processing_waiting_queue_G:
        create_current_req_G += 1

    # 获取或创建节点的分层缓存
    if node_id not in layer_map_G:
        layer_map_G[node_id] = Layer_Cache(node_id)
        logging.info(f"为节点 {node_id} 创建新的分层缓存")
    layered_cache = layer_map_G[node_id]
    
    # 步骤1：计算所需内存（包括新的User层和可能的Bare/Lang层）
    function = request.function
    required_memory = calculate_required_memory(request, layered_cache)
    current_memory = topo_G.nodes[node_id].mem

    # 添加调试信息
    if node_id == 96:
        logging.info(f"DEBUG: 节点96内存检查 - 需要内存: {required_memory}MB, 可用内存: {current_memory}MB, 函数类型: {function.type}")

    # 步骤2：检查基础层是否已存在，避免重复创建
    bare_exists = check_layer_exists("bare", function.lang_type, layered_cache)
    lang_exists = check_layer_exists("lang", function.lang_type, layered_cache)

    # 步骤3：内存不足时，尝试使用基于优先级的驱逐策略
    if required_memory > current_memory:
        if node_id == 96:
            logging.info(f"DEBUG: 节点96内存不足，尝试驱逐容器")
        if evict_user_layer(request):
        # if evict_full_container(request): # 消融实验
            # logging.info(f"node {node_id} evict success")
            if node_id == 96:
                new_current_memory = topo_G.nodes[node_id].mem
                logging.info(f"DEBUG: 节点96驱逐后可用内存: {new_current_memory}MB")
        else:
            if node_id == 96:
                logging.info(f"DEBUG: 节点96驱逐失败，仍然内存不足")

    # 驱逐后重新检查内存是否足够
    current_memory_after_eviction = topo_G.nodes[node_id].mem
    if required_memory > current_memory_after_eviction:
        if node_id == 96:
            logging.info(f"DEBUG: 节点96驱逐后仍然内存不足 - 需要: {required_memory}MB, 可用: {current_memory_after_eviction}MB")
        # 内存仍然不足，将请求放入等待队列（类似全容器方法）
        # 在额外时间槽中不添加到等待队列，避免重复添加
        if not is_extra_slot_G:
            add_request_to_waiting_queue(request, node_id)
            # 调整计数：创建失败，从create_current_req_G转移到wait_count_G
            if not is_extra_slot_G and not is_processing_waiting_queue_G:
                create_current_req_G -= 1
                wait_count_G += 1
        return False
    
    # 驱逐后再次检查layer是否存在，避免驱逐函数移除了cache层而标志未更新
    bare_exists = check_layer_exists("bare", function.lang_type, layered_cache)
    lang_exists = check_layer_exists("lang", function.lang_type, layered_cache)
    
    cold = 0.0
    # 用于计算实际的实例化成本
    bare_size = 0.0
    lang_size = 0.0
    user_size = function.user_size  # User层总是需要创建的
    
    # 获取或创建Bare层
    if not bare_exists:            
        # 创建新的Bare层，需要更新内存
        bare_layer = BareLayer(function.bare_size, function.lang_type, clock_G, node_id)
        layered_cache.add_active_bare_layer(bare_layer)
        layered_cache.total_memory_usage += bare_layer.size
        cold = function.bare_delay  # 使用Bare层的冷启动延迟
        # 添加调试信息
        if node_id == 96:
            logging.info(f"DEBUG: 节点96创建Bare层，减少内存 {bare_layer.size}MB，UUID: {bare_layer.uuid}")
        update_topo("minus", node_id, bare_layer.size)
        bare_size = function.bare_size  # 记录实际创建的Bare层大小
        new_bare_memory_G += bare_size
        new_bare_counter_G += 1  # 增加Bare层计数
    else:
        # 使用现有的Bare层，从缓存移动到活动状态
        if function.lang_type in layered_cache.bare_cache_index and layered_cache.bare_cache_index[function.lang_type]:
            bare_uuid = layered_cache.bare_cache_index[function.lang_type][0]  # 获取第一个缓存的Bare层
            bare_layer = layered_cache.move_bare_layer_to_active(bare_uuid)
            bare_layer.update_usage(clock_G)
    
    # 获取或创建Lang层
    if not lang_exists:            
        # 创建新的Lang层，需要更新内存
        lang_layer = LangLayer(
            function.lang_size,
            function.lang_type,
            bare_layer.uuid,  # 改为使用UUID引用
            clock_G,
            node_id)
        layered_cache.add_active_lang_layer(lang_layer)
        layered_cache.total_memory_usage += lang_layer.size
        cold += function.lang_delay  # 累加Lang层的冷启动延迟
        # 添加调试信息
        if node_id == 96:
            logging.info(f"DEBUG: 节点96创建Lang层，减少内存 {lang_layer.size}MB，UUID: {lang_layer.uuid}")
        update_topo("minus", node_id, lang_layer.size)
        lang_size = function.lang_size  # 记录实际创建的Lang层大小
        new_lang_memory_G += lang_size
        new_lang_counter_G += 1  # 增加Lang层计数
    else:
        # 使用现有的Lang层
        if function.lang_type in layered_cache.lang_cache_index and layered_cache.lang_cache_index[function.lang_type]:
            lang_uuid = layered_cache.lang_cache_index[function.lang_type][0]  # 获取指定类型的第一个缓存Lang层
            lang_layer = layered_cache.move_lang_layer_to_active(lang_uuid)
            lang_layer.update_usage(clock_G)
        else:
            # 创建新的Lang层，需要更新内存
            lang_layer = LangLayer(
                function.lang_size,
                function.lang_type,
                bare_layer.uuid,  # 改为使用UUID引用而不是直接对象引用
                clock_G,
                node_id)
            layered_cache.add_active_lang_layer(lang_layer)
            layered_cache.total_memory_usage += lang_layer.size
            cold += function.lang_delay  # 累加Lang层的冷启动延迟
            # 这里需要减少内存，因为创建了新的Lang层
            update_topo("minus", node_id, lang_layer.size)
            lang_size = function.lang_size  # 记录实际创建的Lang层大小

            # 记录新创建Lang层的内存使用
            new_lang_memory_G += lang_size
            new_lang_counter_G += 1  # 增加Lang层计数
    
    # 总是需要创建User层，累加User层的冷启动延迟
    cold += function.user_delay

    # 创建User层时减少内存
    update_topo("minus", node_id, function.user_size)

    new_user_memory_G += function.user_size
    new_user_counter_G += 1  # 增加User层计数

    # 记录新创建的内存使用
    new_total_memory_G = new_bare_memory_G + new_lang_memory_G + new_user_memory_G
            
    # 计算分层容器的实例化成本
    instan_cost = get_layer_instan_cost(node_id, bare_size, lang_size, user_size)
    
    # 更新请求对象，将实例化成本存储在请求对象中
    request.instan_cost = instan_cost
    
    # 创建函数实例（User层由cacheMap_G和activeFunctions_G管理）
    f = Function(
        type=function.type,
        executionTime=function.executionTime,
        coldStartTime=cold,
        size=function.size,
        phyNode=request.ingress,
        lang_type=function.lang_type,
        bare_size=function.bare_size,
        lang_size=function.lang_size,
        user_size=function.user_size,
        bare_delay=function.bare_delay,
        lang_delay=function.lang_delay,
        user_delay=function.user_delay
    )
    
    # 建立对Lang层和Bare层的UUID引用
    f.lang_layer_uuid = lang_layer.uuid
    f.bare_layer_uuid = lang_layer.bare_layer_uuid
    
    # 添加User层到活动函数列表
    request.function.phyNode = request.ingress
    activeFunctions_G.add(f, node_id)

    # 添加调试信息
    if node_id == 96:
        logging.info(f"DEBUG: 节点96创建User层容器，减少内存 {user_size}MB，类型: {function.type}")

    request.update(function, request.ingress, True, 0)

    # 添加等待队列调试信息
    if is_processing_waiting_queue_G:
        logging.debug(f"[等待队列调试] 等待队列中的请求成功创建容器: {request.function.type}")

# 基于优先级的User层驱逐策略(LayerCache)
def evict_user_layer(request: Request) -> bool:
    """
    当内存不足时，尝试驱逐低优先级容器来为新请求腾出空间
    如果无法驱逐，将请求添加到等待队列
    
    参数:
      - request: 请求对象
      
    返回值:
      - 如果成功腾出足够空间则返回True，否则返回False
    """
    global wait_count_G, waiting_queues_G, is_extra_slot_G, is_processing_waiting_queue_G
    
    node_id = request.ingress.id
    # 更新函数优先级，用于与现有容器比较
    request.function.active_priority()
    
    # 获取节点的分层缓存
    if node_id not in layer_map_G:
        layer_map_G[node_id] = Layer_Cache(node_id)
    layered_cache = layer_map_G[node_id]
    
    # 计算需要的总内存
    required_memory = calculate_required_memory(request, layered_cache)
    current_memory = topo_G.nodes[node_id].mem
    
    success = False
    
    # 如果节点内存不足，开始驱逐过程
    while required_memory > current_memory:
        # 获取最低优先级的容器
        lowest_priority = cacheMap_G.get_lowest_priority(node_id)
        
        # 如果请求优先级高于最低优先级容器，则驱逐该容器
        if lowest_priority < request.function.priority:
            f, succ_flag = cacheMap_G.delete_low_function(node_id)
            if succ_flag:
                # 如果驱逐成功，更新拓扑结构中的内存
                # 在分层容器中，驱逐只释放User层的内存，因为Bare层和Lang层可能被其他容器共享
                if config_G.cache_method == "LayerCache":
                    # 分层容器：只释放User层内存
                    update_topo("add", node_id, f.user_size)
                else:
                    # 全容器方法：释放完整容器内存
                    update_topo("add", node_id, f.size)
                current_memory = topo_G.nodes[node_id].mem
                # 如果已腾出足够空间，标记为成功并退出循环
                if required_memory <= current_memory:
                    success = True
                    break
            else:
                # 如果驱逐失败，将请求添加到等待队列
                # 在额外时间槽中不添加到等待队列，避免重复添加
                if not is_extra_slot_G:
                    add_request_to_waiting_queue(request, node_id)
                    # 部署失败进入等待队列，增加wait_count_G
                    if not is_processing_waiting_queue_G:
                        wait_count_G += 1
                        # logging.info(f"[wait_count调试] evict_user_layer部署失败1: wait_count_G += 1, 当前值: {wait_count_G}, 请求类型: {request.function.type}, 节点: {node_id}")
                return False
        else:
            # 如果没有优先级更低的容器，将请求添加到等待队列
            # 在额外时间槽中不添加到等待队列，避免重复添加
            if not is_extra_slot_G:
                add_request_to_waiting_queue(request, node_id)
                # 部署失败进入等待队列，增加wait_count_G
                if not is_processing_waiting_queue_G:
                    wait_count_G += 1
                    # logging.info(f"[wait_count调试] evict_user_layer部署失败2: wait_count_G += 1, 当前值: {wait_count_G}, 请求类型: {request.function.type}, 节点: {node_id}")
            return False
    
    return success

# 基于优先级的全容器驱逐策略（消融实验）
def evict_full_container(request: Request) -> bool:
    """
    当内存不足时，尝试驱逐完整容器（包括Lang和Bare层）来为新请求腾出空间
    如果无法驱逐，将请求添加到等待队列
    
    参数:
      - request: 请求对象
      
    返回值:
      - 如果成功腾出足够空间则返回True，否则返回False
    """
    global wait_count_G, waiting_queues_G
    
    node_id = request.ingress.id
    # 更新函数优先级，用于与现有容器比较
    request.function.active_priority()
    
    # 获取节点的分层缓存
    if node_id not in layer_map_G:
        layer_map_G[node_id] = Layer_Cache(node_id)
    layered_cache = layer_map_G[node_id]
    
    # 计算需要的总内存
    required_memory = calculate_required_memory(request, layered_cache)
    current_memory = topo_G.nodes[node_id].mem
    
    success = False
    evicted_functions = []  # 记录已驱逐的函数，用于检查layer依赖
    
    # 如果节点内存不足，开始驱逐过程
    while required_memory > current_memory:
        # 获取最低优先级的容器
        lowest_priority = cacheMap_G.get_lowest_priority(node_id)
        
        # 如果请求优先级高于最低优先级容器，则驱逐该容器
        if lowest_priority < request.function.priority:
            f, succ_flag = cacheMap_G.delete_low_function(node_id)
            if succ_flag:
                # 如果驱逐成功，记录该函数并更新拓扑结构中的内存
                evicted_functions.append(f)
                # 驱逐机制是融合的方法，释放完整容器的内存以快速腾出空间
                update_topo("add", node_id, f.size)
                current_memory = topo_G.nodes[node_id].mem
                
                # 如果已腾出足够空间，标记为成功并退出循环
                if required_memory <= current_memory:
                    success = True
                    break
            else:
                # 如果驱逐失败，将请求添加到等待队列
                # 在额外时间槽中不添加到等待队列，避免重复添加
                if not is_extra_slot_G:
                    add_request_to_waiting_queue(request, node_id)
                    # 部署失败进入等待队列，增加wait_count_G
                    if not is_processing_waiting_queue_G:
                        wait_count_G += 1
                        # logging.info(f"[wait_count调试] evict_user_layer部署失败3: wait_count_G += 1, 当前值: {wait_count_G}, 请求类型: {request.function.type}, 节点: {node_id}")
                return False
        else:
            # 如果没有优先级更低的容器，将请求添加到等待队列
            # 在额外时间槽中不添加到等待队列，避免重复添加
            if not is_extra_slot_G:
                add_request_to_waiting_queue(request, node_id)
                # 部署失败进入等待队列，增加wait_count_G
                if not is_processing_waiting_queue_G:
                    wait_count_G += 1
                    # logging.info(f"[wait_count调试] evict_user_layer部署失败4: wait_count_G += 1, 当前值: {wait_count_G}, 请求类型: {request.function.type}, 节点: {node_id}")
            return False
    
    # 注意：在分层容器中，Bare层和Lang层可能被多个User层共享
    # 简单的清理逻辑可能导致过早删除仍在使用的层，造成内存计算错误
    # 暂时移除清理共享层的逻辑，只在User层级别管理内存
    # TODO: 实现正确的引用计数机制来管理共享层的生命周期
    
    return success

# 计算请求所需内存(LayerCache)
def calculate_required_memory(request: Request, layered_cache: Layer_Cache) -> float:
    """
    计算在分层缓存中部署请求所需的内存
    考虑已经存在的层（Bare和Lang）
    
    参数:
      - request: 请求对象
      - layered_cache: 节点的分层缓存
      
    返回值:
      - 所需内存总量
    """
    function = request.function
    required_memory = 0.0
    
    # 检查是否需要创建Bare层
    bare_exists = check_layer_exists("bare", function.lang_type, layered_cache)
    if not bare_exists:
        required_memory += function.bare_size
    
    # 检查是否需要创建Lang层
    lang_exists = check_layer_exists("lang", function.lang_type, layered_cache)
    if not lang_exists:
        required_memory += function.lang_size
    
    # 需要创建User层
    required_memory += function.user_size
    
    return required_memory

# 检查层是否存在(LayerCache)
def check_layer_exists(layer_type: str, layer_id: int, layered_cache: Layer_Cache) -> bool:
    """
    检查指定类型和ID的层是否存在(活动或缓存)
    
    参数:
      - layer_type: 层类型("bare"或"lang")
      - layer_id: 层ID(bare层为语言类型，lang层为语言类型)
      - layered_cache: 节点的分层缓存
      
    返回值:
      - 如果层缓存存在返回True，否则返回False
    """
    if layer_type == "bare":
        # 检查Bare层是否存在于缓存列表中（活动层正在使用，不能复用）
        return (layer_id in layered_cache.bare_cache_index and
                len(layered_cache.bare_cache_index[layer_id]) > 0)
    elif layer_type == "lang":
        # 检查Lang层是否存在于缓存列表中（活动层正在使用，不能复用）
        return (layer_id in layered_cache.lang_cache_index and
                len(layered_cache.lang_cache_index[layer_id]) > 0)
    return False

#endregion

#region ----------------------请求分发----------------------
def deploy_requests():
    """
    部署所有时间槽的请求，按arrival_second顺序处理，实现秒级循环和容器执行完成检查
    支持全容器和分层容器两种缓存策略
    """
    global total_req_count_G, clock_G, second_clock_G

    # 根据缓存方法确定使用的函数
    is_layered = config_G.cache_method == "LayerCache"

    # 遍历所有时间槽
    for i in range(1, config_G.slot_num + 1):
        requests, found = requestsMap_G.get(i)  # 从请求映射中获取当前时间槽的请求
        if not found:
            print(f"--------cannot find time slot {i}--------")
            continue

        # 重置秒级时钟和额外时间槽标识
        global is_extra_slot_G
        second_clock_G = 0
        is_extra_slot_G = False

        logging.info(f"--------处理时间槽 {i} 的请求，共 {len(requests)} 个--------")
        show_nodes_memory()

        # 按arrival_second对请求进行排序
        sorted_requests = sorted(requests, key=lambda req: getattr(req, 'arrival_second', 0))

        # 创建按秒分组的请求字典
        requests_by_second = {}
        for request in sorted_requests:
            arrival_sec = getattr(request, 'arrival_second', 0)
            if arrival_sec not in requests_by_second:
                requests_by_second[arrival_sec] = []
            requests_by_second[arrival_sec].append(request)

        # 处理本时间槽内的秒级循环
        while second_clock_G < SECONDS_PER_SLOT:  # 使用常量替代魔法数字
            # 处理当前秒到达的请求
            if second_clock_G in requests_by_second:
                current_second_requests = requests_by_second[second_clock_G]
                logging.info(f"  秒 {second_clock_G}: 处理 {len(current_second_requests)} 个新请求")

                for request in current_second_requests:
                    # 根据缓存策略部署请求
                    deploy_request(request)
                    total_req_count_G += 1

                    # 如果请求被成功部署，安排容器执行
                    if request.served:
                        # 设置请求的执行时间（秒）
                        request.executionDuration = request.function.executionTime
                        # 安排容器执行
                        schedule_container_execution(request, request.deployNode.id)

            # 所有缓存策略都需要处理等待队列
            process_waiting_queues()

            # 检查是否有容器执行完成
            finished_count = check_finished_containers()

            # 如果有容器执行完成，立即更新缓存状态
            if finished_count > 0:
                update_cache()

            # 每10秒输出一次诊断信息
            if second_clock_G % 10 == 0:
                executing_containers = sum(len(containers) for containers in node_execution_end_times_G.values())
                waiting_requests = sum(q.size() for q in waiting_queues_G.values())
                if executing_containers > 0 or waiting_requests > 0:
                    logging.info(f"时间槽 {i}，秒 {second_clock_G}: 执行中容器 {executing_containers}，等待请求 {waiting_requests}")

            # 更新秒级时钟
            update_second_clock()

        # 将未处理的请求移至下一个时间槽
        if i < config_G.slot_num:
            if is_layered:
                current_waiting = sum(queue.size() for queue in waiting_queues_G.values())
                current_executing = sum(len(containers) for containers in node_execution_end_times_G.values())
                if current_waiting > 0:
                    logging.info(f"时间槽 {i} 结束，将 {current_waiting} 个等待请求移至时间槽 {i+1}")
                if current_executing > 0:
                    logging.info(f"时间槽 {i} 结束时，还有 {current_executing} 个容器正在执行，将继续在后台执行")

            move_waiting_requests_to_next_slot(i, i+1)

        # 更新时钟和缓存
        clock_G += 1
        update_cache()

    show_nodes_memory()

def deploy_request(request: Request):
    """
    部署单个请求，支持全容器和分层容器两种缓存策略
    """
    global deploy_current_req_G, deploy_neighbor_req_G, create_current_req_G, wait_count_G, is_extra_slot_G, is_processing_waiting_queue_G

    # 根据缓存方法确定使用的函数
    is_layered = config_G.cache_method == "LayerCache"

    # 根据请求到物理节点的距离，对节点进行排序
    ds = sort_phynodes(request)

    # 首先尝试在当前节点部署
    current_deploy_success = deploy_to_current(request, is_layered)

    if current_deploy_success:
        deploy_current_req_G += 1
        if is_processing_waiting_queue_G:
            logging.debug(f"[等待队列调试] 等待队列中的请求成功部署到当前节点: {request.function.type}")
        return

    # 如果当前节点无法部署，尝试邻近节点
    neighbor_deploy_success = deploy_to_neighbour(ds, request, is_layered)

    if neighbor_deploy_success:
        deploy_neighbor_req_G += 1
        if is_processing_waiting_queue_G:
            logging.debug(f"[等待队列调试] 等待队列中的请求成功部署到邻居节点: {request.function.type}")
        return

    # 如果无法直接部署，尝试在当前节点创建容器
    # 注意：create_current_req_G 的计数将在各个创建函数中根据实际结果进行
    if is_layered:
        create_to_current_layercache(request)
    elif config_G.cache_method == "OpenWhisk":
        create_to_current_openwhisk(request)
    elif config_G.cache_method == "CrossEdge":
        create_to_current_crossedge(request)
    else:   # FaaSCache
        create_to_current_faascache(request)

def process_waiting_queues():
    """
    处理等待队列中的请求，根据缓存策略使用不同的处理逻辑
    - LayerCache: 使用完整的部署逻辑（当前节点→邻近节点→创建新容器）
    - 其他策略: 只尝试当前节点部署，处理速度更快
    """
    global is_processing_waiting_queue_G, wait_count_G, waiting_queue_served_G

    # 设置等待队列处理标识，防止失败请求重复添加
    is_processing_waiting_queue_G = True

    try:
        # 根据缓存方法确定处理策略
        is_layered = config_G.cache_method == "LayerCache"

        # 限制总处理请求数量，避免爆炸性增长
        total_max_requests = MAX_REQUESTS_PER_SECOND  # 每秒最多处理的总请求数
        total_processed = 0

        # 为每个节点分配处理配额
        for node_id, queue in waiting_queues_G.items():
            if total_processed >= total_max_requests:
                break  # 达到总处理上限，停止处理

            processed_count = 0

            # 计算当前节点可处理的请求数量
            node_max_requests = min(queue.size(), total_max_requests - total_processed)

            # 修复逻辑：先尝试处理请求，成功后才从队列中移除
            processed_in_this_node = 0
            failed_attempts = 0  # 连续失败次数
            max_failed_attempts = 3  # 最大连续失败次数

            while not queue.is_empty() and processed_in_this_node < node_max_requests and failed_attempts < max_failed_attempts:
                # 查看队列中的下一个请求，但不移除
                request = queue.peek_next_request()
                if request is None:
                    break

# logging.info(f"[等待队列调试] 尝试处理等待队列请求: {request.function.type}, 节点: {node_id}, 队列剩余: {queue.size()}")

                # 使用统一的部署逻辑：当前节点部署 → 邻居节点部署 → 创建新容器
                deploy_request(request)

                # 如果部署成功，从队列中移除请求并更新状态
                if request.served:
                    # 成功处理，从队列中移除
                    removed_request = queue.get_next_request()
                    # logging.info(f"[等待队列调试] 请求成功处理并从队列移除: {request.function.type}, 节点: {node_id}")
                    waiting_queue_served_G += 1  # 增加等待队列成功处理计数
                    request.waitTime = second_clock_G - request.arriveTime
                    schedule_container_execution(request, request.deployNode.id)
                    processed_count += 1
                    total_processed += 1
                    processed_in_this_node += 1
                    failed_attempts = 0  # 重置失败计数
                else:
                    # 处理失败，请求保留在队列中
                    # logging.info(f"[等待队列调试] 请求处理失败，保留在队列中: {request.function.type}, 节点: {node_id}")
                    failed_attempts += 1
                    # 如果连续失败次数过多，停止处理这个节点的队列
                    if failed_attempts >= max_failed_attempts:
                        # logging.info(f"[等待队列调试] 节点{node_id}连续失败{failed_attempts}次，停止处理")
                        break
    finally:
        # 重置等待队列处理标识
        is_processing_waiting_queue_G = False

def try_deploy_container(request: Request, node_id: int, link_delay: float = 0.0, is_layered: bool = None) -> bool:
    """
    统一的容器部署函数，支持全容器和分层容器两种缓存策略，查找可用的User容器或全容器

    参数:
        request: 请求对象
        node_id: 目标节点ID
        link_delay: 链路延迟（邻近节点部署时使用）
        is_layered: 是否为分层缓存（None时自动检测）

    返回值:
        True: 成功部署，False: 未找到匹配容器
    """
    # 自动检测缓存类型（如果未指定）
    if is_layered is None:
        is_layered = config_G.cache_method == "LayerCache"

    # 分层缓存需要初始化layer_map_G
    if is_layered:
        global layer_map_G
        # 获取或创建节点的分层缓存
        if node_id not in layer_map_G:
            layer_map_G[node_id] = Layer_Cache(node_id)
            if node_id != request.ingress.id:  # 只在非当前节点时记录日志
                logging.info(f"为节点 {node_id} 创建新的分层缓存")

    # 寻找指定节点上可用的容器
    f, i = cacheMap_G.get_idle_function(node_id, request.function.type)

    if i != -1:
        # 找到可用容器，部署请求
        if node_id == request.ingress.id:
            # 当前节点部署
            place_to_current(request, f, i)
        else:
            # 邻近节点部署
            place_to_neighbour(request, f, i, node_id, link_delay)

        # 分层缓存的特殊说明
        if is_layered:
            # 注意：User层容器已经包含了对应的Bare和Lang层引用
            # 这些层应该已经在活动状态，或者会在容器执行时自动激活
            # 不需要手动移动层到活动状态，因为同一个函数只能有一个容器实例
            pass

        return True

    return False

def deploy_to_current(request: Request, is_layered: bool) -> bool:
    """
    统一的当前节点部署函数，支持全容器和分层容器

    参数:
        request: 请求对象
        is_layered: 是否为分层缓存

    返回值:
        True: 成功部署，False: 未找到匹配容器
    """
    return try_deploy_container(request, request.ingress.id, 0.0, is_layered)

def place_to_current(request: Request, function: Function, i: int):
    """
    将请求分配给当前节点的缓存容器
    """
    topo_G.addFreqAll(request.function.type)
    topo_G.setRecencyAll(request.function.type, float(request.arriveTime))

    # 设置容器为执行状态
    function.is_executing = True

    activeFunctions_G.add(function, request.ingress.id)
    cacheMap_G.delete(request.ingress.id, i)
    request.update(function, request.ingress, False, 0)

def deploy_to_neighbour(distances: DistSlice, request: Request, is_layered: bool) -> bool:
    """
    统一的邻近节点部署函数，支持全容器和分层容器

    参数:
        distances: 距离切片
        request: 请求对象
        is_layered: 是否为分层缓存

    返回值:
        True: 成功部署，False: 未找到匹配容器
    """
    global a, b
    for slice in distances.slice[1:]:
        node_id = slice.get_id() if is_layered else slice.phyNodeID
        link_delay = slice.distance * config_G.latency_para
        cold_start_time = get_container_delay(request.function.type)

        if link_delay > cold_start_time:
            a += 1
        # 传输延迟 < 冷启动时间
        if link_delay < cold_start_time:
            b += 1
            # 使用统一的部署函数
            if try_deploy_container(request, node_id, link_delay, is_layered):
                return True

    return False

def place_to_neighbour(request: Request, function: Function, i: int, phyNodeID: int, link_delay: float):
    """
    将请求分配给邻近的缓存函数
    """
    topo_G.addFreqAll(function.type)
    topo_G.setRecencyAll(function.type, float(request.arriveTime))

    # 设置容器为执行状态
    function.is_executing = True

    activeFunctions_G.add(function, phyNodeID)
    cacheMap_G.delete(phyNodeID, i)
    
    deploy_node = topo_G.get(phyNodeID)  # 获取物理节点
    if deploy_node.id == 0:
        return
    request.update(function, deploy_node, False, link_delay)  # 更新请求状态

#endregion

#region ----------------------打印结果----------------------
def print_result():
    print("-------- Schedule End --------")
    logging.info("-------- Schedule End --------")
    
    global total_req_count_G, served_req_count_G, cold_req_count_G
    global total_response_time_G, total_cost_G, total_wait_time_G
    global new_total_memory_G, new_bare_memory_G, new_lang_memory_G, new_user_memory_G
    global new_bare_counter_G, new_lang_counter_G, new_user_counter_G, new_container_counter_G
    global total_req_count_G, served_req_count_G, cold_req_count_G, wait_count_G, waiting_queue_served_G
    global deploy_current_req_G, deploy_neighbor_req_G, create_current_req_G

    print("部署在当前节点的请求:", deploy_current_req_G)
    print("部署在邻居节点的请求:", deploy_neighbor_req_G)
    print("当前节点创建新的请求:", create_current_req_G)
    print("存在过等待时间的请求:", wait_count_G)
    print("等待队列成功处理请求:", waiting_queue_served_G)
    print("------------------------------")
    logging.info(f"deploy_current_req_G: {deploy_current_req_G}")
    logging.info(f"deploy_neighbor_req_G: {deploy_neighbor_req_G}")
    logging.info(f"create_current_req_G: {create_current_req_G}")
    logging.info(f"wait_count_G: {wait_count_G}")
    logging.info("------------------------------")

    total_instan_cost = 0   # 实例化成本
    total_run_cost = 0      # 运行成本

    for i in range(1, requestsMap_G.size() + 1):
        if i in requestsMap_G.map:
            requests = requestsMap_G.map[i].requests
            for request in requests:
                # 跳过非被服务的请求
                if not request.served:
                    continue
                served_req_count_G += 1  # 统计所有被服务的请求

                # 计算运行成本
                run_cost = get_run_cost(request.deployNode.id, request.function.type)
                total_run_cost += run_cost

                # 计算请求响应时间
                response_time = request.linkDelay
                
                # 如果是冷启动，计算实例化成本和额外的冷启动响应时间
                instan_cost = 0
                if request.isColdStart:
                    cold_req_count_G += 1
                    # 对于LayerCache方法，使用请求对象中存储的实例化成本
                    if config_G.cache_method == "LayerCache" and hasattr(request, 'instan_cost'):
                        instan_cost = request.instan_cost
                    else:
                        # 对于其他方法，使用传统的get_instan_cost函数
                        instan_cost = get_instan_cost(request.deployNode.id, request.function.type)
                    total_instan_cost += instan_cost
                    # 冷启动时间 = 处理时间 + 链接延迟 + 冷启动时间
                    response_time += request.function.coldStartTime
                
                # 添加等待时间到响应时间
                if hasattr(request, 'waitTime') and request.waitTime > 0:
                    response_time += request.waitTime
                    total_wait_time_G += request.waitTime
                
                # 累加总响应时间
                total_response_time_G += response_time
                
                # 计算单个请求的总成本
                total_cost_G += run_cost + instan_cost

    # 计算平均成本
    avg_cost = total_cost_G / served_req_count_G 
    avg_instan_cost = total_instan_cost / served_req_count_G
    avg_run_cost = total_run_cost / served_req_count_G
    
    # 计算平均响应时间
    avg_response_time = total_response_time_G / served_req_count_G
    
    # 计算平均等待时间
    if total_wait_time_G > 0:
        avg_wait_time = total_wait_time_G / wait_count_G
    else:
        avg_wait_time = 0
    
    # 统计冷启动数
    unserved_req_count = total_req_count_G - served_req_count_G
    cold_start_frequency = create_current_req_G / total_req_count_G
    
    print(f"实际请求总数: {total_req_count_G}")
    print(f"服务请求总数: {served_req_count_G}")
    print(f"未服务请求总数: {unserved_req_count}")
    print(f"冷启动请求: {cold_req_count_G}")
    print(f"冷启动频率: {cold_start_frequency:.2f}")
    print("------------------------------")
    print(f"平均实例成本: {avg_instan_cost:.2f}")
    print(f"平均运行成本: {avg_run_cost:.2f}")
    print(f"总成本: {total_cost_G:.2f}")
    print(f"平均总成本: {avg_cost:.2f}")
    print("------------------------------")
    print(f"总响应时间: {total_response_time_G:.2f}s")
    print(f"平均响应时间: {avg_response_time:.2f}s")
    print(f"总等待时间: {total_wait_time_G:.2f}s")
    print(f"平均等待时间: {avg_wait_time:.2f}s")
    print("------------------------------")

    logging.info(f"total_req_count_G: {total_req_count_G}")
    logging.info(f"served_req_count_G: {served_req_count_G}")
    logging.info(f"unserved_req_count: {unserved_req_count}")
    logging.info(f"waiting_queue_total: {wait_count_G}")
    logging.info(f"waiting_queue_served: {waiting_queue_served_G}")
    logging.info(f"cold_req_count_G: {cold_req_count_G}")
    logging.info(f"cold_start_frequency: {cold_start_frequency:.2f}")
    logging.info("------------------------------")
    logging.info(f"avg_instan_cost: {avg_instan_cost:.2f}")
    logging.info(f"avg_run_cost: {avg_run_cost:.2f}")
    logging.info(f"total_cost_G: {total_cost_G:.2f}")
    logging.info(f"avg_cost: {avg_cost:.2f}")
    logging.info("------------------------------")
    logging.info(f"total_response_time_G: {total_response_time_G:.2f}s")
    logging.info(f"avg_response_time: {avg_response_time:.2f}s")
    logging.info(f"total_wait_time_G: {total_wait_time_G:.2f}s")
    logging.info(f"avg_wait_time: {avg_wait_time:.2f}s")
    logging.info("------------------------------")

    # 记录内存使用情况
    if config_G.cache_method == "LayerCache":
        avg_total_memory = new_total_memory_G / (new_user_counter_G + new_lang_counter_G + new_bare_counter_G)
        print(f"新创建总内存: {new_total_memory_G:.0f}MB")
        print(f"平均加载内存: {avg_total_memory:.2f}MB")
        logging.info(f"new_total_memory_G: {new_total_memory_G:.0f}MB")
        logging.info(f"avg_total_memory: {avg_total_memory:.2f}MB")
    else:
        avg_total_memory = new_total_memory_G / new_container_counter_G
        print(f"新创建全容器: {new_total_memory_G:.0f}MB")
        print(f"平均加载内存: {avg_total_memory:.2f}MB")
        logging.info(f"new_total_memory_G: {new_total_memory_G:.0f}MB")
        logging.info(f"avg_total_memory: {avg_total_memory:.2f}MB")

    print("------------------------------")
 

    # 输出指标和请求情况
    if config_G == topo_configs['EUA']:
        result_to_csv(f'./result/EUA/{config_G.cache_method}-{config_G.beta:.2f}-{config_G.alpha:.3f}.csv')
    else:
        result_to_csv(f'./result/Telecom/{config_G.cache_method}-{config_G.beta:.2f}-{config_G.alpha:.3f}.csv')
    
def result_to_csv(output_file: str):
    """
    将结果写入CSV文件，包括性能指标和内存使用情况
    
    参数:
      - output_file: 输出文件路径
    """
    global new_total_memory_G, new_bare_memory_G, new_lang_memory_G, new_user_memory_G
    global new_bare_counter_G, new_lang_counter_G, new_user_counter_G, new_container_counter_G
    global wait_count_G, total_wait_time_G
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        logging.info(f"创建输出目录: {output_dir}")
    
    # 打开CSV文件进行写入
    with open(output_file, mode='w', newline='') as file:
        writer = csv.writer(file)

        avg_response_time = total_response_time_G / served_req_count_G       
        avg_cost = total_cost_G / served_req_count_G
        cold_start_frequency = create_current_req_G / total_req_count_G 
        if total_wait_time_G > 0:
            avg_wait_time = total_wait_time_G / wait_count_G
        else:
            avg_wait_time = 0

        # 计算平均内存使用量
        if config_G.cache_method == "LayerCache":
            avg_total_memory = new_total_memory_G / (new_user_counter_G + new_lang_counter_G + new_bare_counter_G)
        else:
            avg_total_memory = new_total_memory_G / new_container_counter_G

        # 性能指标
        writer.writerow([
            "total_req_count",
            "served_req_count",
            "cold_req_count",
            "wait_req_count",
            "cold_start_frequency",
            "avg_response_time",
            "avg_wait_time",
            "weighted_response_time",
            "avg_cost",
            "avg_total_memory",
            "new_memory_usage",
            "new_bare_memory",
            "new_lang_memory",
            "new_user_memory"
        ])
        writer.writerow([
            total_req_count_G,
            served_req_count_G,
            create_current_req_G,
            wait_count_G,
            f"{cold_start_frequency:.3f}",
            f"{avg_response_time:.3f}",
            f"{avg_wait_time:.3f}",
            f"{avg_cost:.3f}",
            f"{avg_total_memory:.2f}",
            f"{new_total_memory_G:.0f}",
            f"{new_bare_memory_G:.0f}",
            f"{new_lang_memory_G:.0f}",
            f"{new_user_memory_G:.0f}"
        ])
        
        # 写入请求详情CSV头
        writer.writerow([
            'Time', 
            'ID', 
            'Function', 
            'Ingress', 
            'DeployNode', 
            'IsColdStart', 
            'RunCost', 
            'InstanCost', 
            'TotalCost',
            'ResponseTime',
            'WaitTime'
        ])

        # 遍历请求映射中的每个时间槽
        for i in range(1, requestsMap_G.size() + 1):
            if i in requestsMap_G.map:
                requests = requestsMap_G.map[i].requests
                for request in requests:
                    if request.served:
                        run_cost = get_run_cost(request.deployNode.id, request.function.type)
                        instan_cost = 0
                        response_time = request.linkDelay
                        wait_time = 0
                        
                        if request.isColdStart:
                            if config_G.cache_method == "LayerCache" and hasattr(request, 'instan_cost'):
                                instan_cost = request.instan_cost
                            else:
                                instan_cost = get_instan_cost(request.deployNode.id, request.function.type)
                            response_time += request.function.coldStartTime
                        
                        # 添加等待时间
                        if hasattr(request, 'waitTime') and request.waitTime > 0:
                            wait_time = request.waitTime
                            response_time += wait_time
                        
                        total_cost = run_cost + instan_cost
                        
                        writer.writerow([
                            request.arriveTime,
                            request.id,
                            request.function.type,
                            request.ingress.id,
                            request.deployNode.id,
                            request.isColdStart,
                            f"{run_cost:.2f}",
                            f"{instan_cost:.2f}",
                            f"{total_cost:.2f}",
                            f"{response_time:.2f}",
                            f"{wait_time:.2f}"
                        ])

#endregion

topo_G = Topology()
node_map_G: Dict[int, Location] = {}        # nodeID, Location
layer_map_G: Dict[int, Layer_Cache]= {}     # nodeID, Layer_Cache

def schedule_requests():
    """
    根据配置的缓存方法选择合适的调度策略，
    并在所有时间槽处理完后，如果仍有未处理的请求，添加额外时间槽直到所有请求处理完成
    """
    global new_total_memory_G, new_bare_memory_G, new_lang_memory_G, new_user_memory_G
    global new_bare_counter_G, new_lang_counter_G, new_user_counter_G, new_container_counter_G
    global total_req_count_G, served_req_count_G, waiting_queues_G, clock_G

    load_topo()
    initGlobal()
    init_func_map()
    if config_G.cache_method == "LayerCache":
        init_layer_map()  # 初始化分层缓存

    request_file = f'./data/request/requests-{config_G.beta:.2f}.csv'
    process_requests(request_file)

    print("--------Schedule Start--------")
    logging.info("--------Schedule Start--------")
    
    # 记录原始时间槽数量
    original_slot_num = config_G.slot_num
    
    # 使用统一的请求调度函数，自动根据缓存方法选择策略
    deploy_requests()  # 支持全容器和分层容器两种缓存策略

    # 如果还有未处理的请求，添加额外时间槽
    extra_slot = 0
    max_extra_slots = 5  # 限制最大额外时间槽数量，避免无限循环
    initial_waiting_requests = sum(queue.size() for queue in waiting_queues_G.values())

    while extra_slot < max_extra_slots:
        # 每次循环开始时重新计算等待请求数量
        total_waiting_requests = sum(queue.size() for queue in waiting_queues_G.values())

        if total_waiting_requests == 0:
            break  # 没有等待请求，退出循环

        # 如果等待请求数量超过初始数量的2倍，停止处理避免无限增长
        if extra_slot > 1 and total_waiting_requests > initial_waiting_requests * 2:
            logging.warning(f"等待请求数量异常增长（{total_waiting_requests} > {initial_waiting_requests * 2}），停止额外时间槽处理")
            break

        extra_slot += 1
        next_slot = original_slot_num + extra_slot

        logging.info(f"--------添加额外时间槽 {next_slot}，当前等待队列中有 {total_waiting_requests} 个等待请求--------")

        # 检查是否是第一个额外时间槽
        if extra_slot == 1:
            logging.info(f"说明：这些是在时间槽 {original_slot_num} 处理过程中新产生的等待请求，需要额外处理")
        else:
            logging.info(f"警告：额外时间槽 {extra_slot} 中仍有等待请求，可能存在处理效率问题")

        # 简化逻辑：不移动等待队列中的请求到时间槽
        # 等待队列保持全局性，直接在额外时间槽中处理
        logging.info(f"额外时间槽 {next_slot} 将直接处理等待队列中的 {total_waiting_requests} 个请求")
        logging.info(f"不移动请求到时间槽，保持等待队列的全局性")

        # 处理额外时间槽 - 简化版本
        # 不处理时间槽中的请求，直接处理等待队列
        current_waiting = sum(q.size() for q in waiting_queues_G.values())

        logging.info(f"--------处理额外时间槽 {next_slot}，等待队列中有 {current_waiting} 个请求--------")
        logging.info(f"说明：直接处理等待队列，不移动请求到时间槽中")
        logging.info(f"当前秒级时钟: {second_clock_G}")

        # 简化的额外时间槽处理：只处理等待队列，不处理时间槽中的请求
        # 秒级循环，专注于处理等待队列
        max_seconds = 3600  # 设置最大秒数，避免死循环
        second_count = 0

        while second_count < max_seconds:
            # 更新秒级时钟
            update_second_clock()
            second_count += 1

            # 检查已完成执行的容器
            finished_count = check_finished_containers()

            # 如果有容器执行完成，更新缓存状态
            if finished_count > 0:
                update_cache()

            # 处理等待队列中的请求
            process_waiting_queues()

            # 每10秒输出一次状态信息
            if second_count % 10 == 0:
                executing_containers = sum(len(containers) for containers in node_execution_end_times_G.values())
                waiting_requests = sum(q.size() for q in waiting_queues_G.values())
                logging.info(f"额外时间槽 {next_slot}，秒 {second_count}: 执行中容器 {executing_containers}，等待请求 {waiting_requests}")

            # 检查是否可以结束秒级循环
            executing_containers = sum(len(containers) for containers in node_execution_end_times_G.values())
            waiting_requests = sum(q.size() for q in waiting_queues_G.values())

            # 如果没有正在执行的容器且所有等待队列为空，结束秒级循环
            if executing_containers == 0 and waiting_requests == 0:
                logging.info(f"额外时间槽 {next_slot} 处理完成：无执行中容器，无等待请求，退出秒级循环")
                break

        # 简化的额外时间槽处理：直接处理等待队列
        logging.info(f"额外时间槽 {next_slot} 开始处理等待队列")

        # 设置额外时间槽标识
        global is_extra_slot_G
        is_extra_slot_G = True

        # 秒级循环，专注于处理等待队列
        max_seconds = 3600
        second_count = 0

        while second_count < max_seconds:
            # 更新秒级时钟
            update_second_clock()
            second_count += 1

            # 检查已完成执行的容器
            finished_count = check_finished_containers()

            # 如果有容器执行完成，更新缓存状态
            if finished_count > 0:
                update_cache()

            # 处理等待队列中的请求
            process_waiting_queues()

            # 每10秒输出一次状态信息
            if second_count % 100 == 0:
                executing_containers = sum(len(containers) for containers in node_execution_end_times_G.values())
                waiting_requests = sum(q.size() for q in waiting_queues_G.values())
                logging.info(f"额外时间槽 {next_slot}，秒 {second_count}: 执行中容器 {executing_containers}，等待请求 {waiting_requests}")

            # 检查是否可以结束秒级循环
            executing_containers = sum(len(containers) for containers in node_execution_end_times_G.values())
            waiting_requests = sum(q.size() for q in waiting_queues_G.values())

            # 如果没有正在执行的容器且所有等待队列为空，结束秒级循环
            if executing_containers == 0 and waiting_requests == 0:
                logging.info(f"额外时间槽 {next_slot} 处理完成：无执行中容器，无等待请求，退出秒级循环")
                break

        # 更新时钟
        clock_G += 1

    # 如果达到最大额外时间槽数量，记录警告
    if extra_slot >= max_extra_slots:
        total_remaining = sum(queue.size() for queue in waiting_queues_G.values())
        logging.warning(f"达到最大额外时间槽数量 {max_extra_slots}，仍有 {total_remaining} 个等待请求未处理")

def main():
    # 解析命令行参数
    args = parse_args()
    
    # 保留原始配置对象，使用命令行参数覆盖特定属性
    global config_G
    
    # 确保结果目录存在
    os.makedirs('./result/EUA', exist_ok=True)
    os.makedirs('./result/Telecom', exist_ok=True)
    os.makedirs('./result/log', exist_ok=True)
    
    # 根据拓扑文件选择配置
    if args.topo_file and args.topo_file in topo_configs:
        config_G = topo_configs[args.topo_file]
        print(f"使用 {args.topo_file} 拓扑的配置参数")
    else:
        # 如果没有指定拓扑文件或指定的拓扑文件不在配置中，使用默认配置
        print(f"EUA Dataset: {config_G.topo_file}")
    
    # 命令行参数覆盖配置参数
    config_G.beta = args.beta
    config_G.cache_method = args.cache_method
    config_G.redu_factor = args.redu_factor
    config_G.slot_num = args.slot_num
    config_G.alpha = args.alpha
    config_G.mem_cap = args.mem_cap
    
    # 如果指定了拓扑文件但不在预设配置中，更新配置的拓扑文件路径
    if args.topo_file and args.topo_file not in topo_configs:
        config_G.topo_file = args.topo_file
        print(f"警告: 使用未预设的拓扑文件 {args.topo_file}，使用默认配置参数")
    
    # 设置日志文件名
    log_file = f'./result/log/{config_G.cache_method}-{config_G.beta:.2f}-{config_G.redu_factor}-{config_G.alpha:.3f}.log'
    
    # 转换日志级别字符串为对应的常量
    log_level_map = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    log_level = log_level_map.get(args.log_level, logging.INFO)
    
    # 是否输出到控制台
    console_output = args.console_log
    
    # 设置日志配置
    setup_logging(log_file, level=log_level, console_output=console_output)
    
    # 记录配置信息
    logging.info(f"topo_file: {config_G.topo_file}")
    logging.info(f"node_num: {config_G.node_num}, mem_cap: {config_G.mem_cap}, cpu_Freq: {config_G.cpu_Freq}")
    logging.info(f"latency_para: {config_G.latency_para}, redu_factor: {config_G.redu_factor}, slot_num: {config_G.slot_num}")
    logging.info(f"cache_method: {config_G.cache_method}, beta: {config_G.beta}, alpha: {config_G.alpha}")
    
    # 运行调度器
    schedule_requests()

    # 输出结果
    print_result()

if __name__ == "__main__":
   
    # 默认 beta = 1.00 alpha = 0.015
    # python simulation-7.30.py --beta 0.50 --cache-method LayerCache
    # python simulation-7.30.py --beta 0.75 --cache-method LayerCache
    # python simulation-7.30.py --beta 1.00 --cache-method LayerCache
    # python simulation-7.30.py --beta 1.25 --cache-method LayerCache
    # python simulation-7.30.py --beta 1.50 --cache-method LayerCache

    # python simulation-7.30.py --cache-method LayerCache --alpha 0.001
    # python simulation-7.30.py --cache-method LayerCache --alpha 0.003
    # python simulation-7.30.py --cache-method LayerCache --alpha 0.005
    # python simulation-7.30.py --cache-method LayerCache --alpha 0.010
    # python simulation-7.30.py --cache-method LayerCache --alpha 0.015
    
    # python simulation-7.30.py --beta 0.50 --cache-method FaaSCache
    # python simulation-7.30.py --beta 0.50 --cache-method OpenWhisk
    # python simulation-7.30.py --beta 0.50 --cache-method CrossEdge

    main()
    print("请求转到邻居节点的延迟：")
    print(f"传输延迟更高：{a}")
    # b越大转发到邻居节点的请求越多
    print(f"创建延迟更高：{b}")
    logging.info(f"传输延迟更高：{a}")
    logging.info(f"创建延迟更高：{b}")
