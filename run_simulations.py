import subprocess
import time
import os
import sys
import argparse

def run_command(command):
    """运行命令并实时显示输出"""
    print("-" * 80)
    print(f"执行命令: {command}")
    print("-" * 80)
    
    # 创建子进程并执行命令
    process = subprocess.Popen(
        command,
        shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True
    )
    
    # 实时获取并打印输出
    while True:
        output = process.stdout.readline()
        if output == '' and process.poll() is not None:
            break
        if output:
            print(output.strip())
    
    # 等待进程完成并获取返回码
    return_code = process.poll()
    
    print("-" * 80)
    print(f"命令执行完成，返回码: {return_code}")
    print("-" * 80)
    print("\n")
    
    return return_code

def ensure_result_dir():
    """确保结果目录存在"""
    dirs = ['./result', './result/log']
    for dir_path in dirs:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='运行缓存方法模拟对比实验',
        formatter_class=argparse.RawTextHelpFormatter
    )
    
    parser.add_argument(
        '--mode', 
        type=str, 
        choices=['method', 'beta', 'multi-beta', 'alpha', 'multi-alpha'], 
        default='method',
        help='运行模式:\n'
             '  method: 固定beta值，运行不同缓存方法\n'
             '  beta: 固定缓存方法，运行不同beta值\n'
             '  multi-beta: 对每个beta值分别运行不同缓存方法\n'
             '  alpha: 固定缓存方法和beta值，运行不同alpha值\n'
             '  multi-alpha: 对每个alpha值分别运行不同缓存方法'
    )
    
    parser.add_argument(
        '--methods',
        nargs='+',
            default=['LayerCache', 'CrossEdge', 'FaaSCache', 'OpenWhisk'],
        help='要运行的缓存方法列表，默认为所有方法'
    )
    
    parser.add_argument(
        '--betas',
        nargs='+',
        type=float,
        default=[0.50, 0.75, 1.00, 1.25, 1.50],
    )
    
    parser.add_argument(
        '--alphas',
        nargs='+',
        type=float,
        default=[0.005, 0.015, 0.025, 0.035, 0.045],
        help='alpha值列表，用于alpha和multi-alpha模式'
    )
    
    parser.add_argument(
        '--method',
        type=str,
        default='LayerCache',
        help='beta模式或alpha模式下要使用的缓存方法'
    )
    
    parser.add_argument(
        '--beta',
        type=float,
        default=0.50,
        help='method模式或alpha模式下要使用的beta值'
    )
    
    parser.add_argument(
        '--alpha',
        type=float,
        default=0.015,
        help='method模式或beta模式下要使用的alpha值'
    )
    
    parser.add_argument(
        '--redu-factor',
        type=int,
        default=None,
        help='已废弃: 冗余因子参数 (现在不再使用)'
    )
    
    # 添加是否显示详细输出的选项
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        default=False,
        help='是否显示详细输出'
    )
    
    return parser.parse_args()

def generate_commands(args):
    """根据运行模式生成命令列表"""
    commands = []
    
    if args.mode == 'method':
        # 方法模式：固定beta，运行不同缓存方法
        for method in args.methods:
            command = f"python simulation-7.30.py --beta {args.beta:.2f} --cache-method {method} --alpha {args.alpha:.3f}"
            commands.append(command)
    elif args.mode == 'beta':
        # beta模式：固定缓存方法，运行不同beta值
        for beta in args.betas:
            command = f"python simulation-7.30.py --beta {beta:.2f} --cache-method {args.method} --alpha {args.alpha:.3f}"
            commands.append(command)
    elif args.mode == 'alpha':
        # alpha模式：固定缓存方法和beta值，运行不同alpha值
        for alpha in args.alphas:
            command = f"python simulation-7.30.py --beta {args.beta:.2f} --cache-method {args.method} --alpha {alpha:.3f}"
            commands.append(command)
    elif args.mode == 'multi-beta':
        # multi-beta模式：对每个beta值分别运行不同缓存方法
        for beta in args.betas:
            for method in args.methods:
                command = f"python simulation-7.30.py --beta {beta:.2f} --cache-method {method} --alpha {args.alpha:.3f}"
                commands.append(command)
    else:  # multi-alpha模式
        # multi-alpha模式：对每个alpha值分别运行不同缓存方法
        for alpha in args.alphas:
            for method in args.methods:
                command = f"python simulation-7.30.py --beta {args.beta:.2f} --cache-method {method} --alpha {alpha:.3f}"
                commands.append(command)
    
    return commands

def print_result_files(args):
    """打印结果文件路径"""
    print("\n结果文件:")
    
    if args.mode == 'method':
        # 方法模式：不同方法，相同beta
        for method in args.methods:
            print(f"  {method} 结果: ./result/{method}-{args.beta:.2f}-10-{args.alpha:.3f}.csv")
    elif args.mode == 'beta':
        # beta模式：相同方法，不同beta
        for beta in args.betas:
            print(f"  {args.method} (beta={beta}) 结果: ./result/{args.method}-{beta:.2f}-10-{args.alpha:.3f}.csv")
    elif args.mode == 'alpha':
        # alpha模式：相同方法，不同alpha
        for alpha in args.alphas:
            print(f"  {args.method} (beta={args.beta}, alpha={alpha}) 结果: ./result/{args.method}-{args.beta:.2f}-10-{alpha:.3f}.csv")
    elif args.mode == 'multi-beta':
        # multi-beta模式：不同方法，不同beta
        for beta in args.betas:
            for method in args.methods:
                print(f"  {method} (beta={beta}) 结果: ./result/{method}-{beta:.2f}-10-{args.alpha:.3f}.csv")
    else:  # multi-alpha模式
        # multi-alpha模式：不同方法，不同alpha
        for alpha in args.alphas:
            for method in args.methods:
                print(f"  {method} (beta={args.beta}, alpha={alpha}) 结果: ./result/{method}-{args.beta:.2f}-10-{alpha:.3f}.csv")

def main():
    """主函数：依次执行指定模拟"""
    # 解析命令行参数
    args = parse_args()
    
    # 检查是否使用了已废弃的redu_factor参数
    if args.redu_factor is not None:
        print("\n警告: --redu-factor 参数已被废弃，将被忽略。")
    
    # 确保结果目录存在
    ensure_result_dir()
    
    # 生成要运行的命令
    commands = generate_commands(args)
    
    # 记录开始时间
    start_time = time.time()
    
    # 显示运行信息
    if args.verbose:
        print(f"\n运行模式: {args.mode}")
        if args.mode == 'method':
            print(f"固定beta值: {args.beta}")
            print(f"固定alpha值: {args.alpha}")
            print(f"要运行的方法: {', '.join(args.methods)}")
        elif args.mode == 'beta':
            print(f"固定缓存方法: {args.method}")
            print(f"固定alpha值: {args.alpha}")
            print(f"要运行的beta值: {', '.join(map(str, args.betas))}")
        elif args.mode == 'alpha':
            print(f"固定缓存方法: {args.method}")
            print(f"固定beta值: {args.beta}")
            print(f"要运行的alpha值: {', '.join(map(str, args.alphas))}")
        elif args.mode == 'multi-beta':
            print(f"固定alpha值: {args.alpha}")
            print(f"要运行的beta值: {', '.join(map(str, args.betas))}")
            print(f"要运行的方法: {', '.join(args.methods)}")
        else:  # multi-alpha模式
            print(f"固定beta值: {args.beta}")
            print(f"要运行的alpha值: {', '.join(map(str, args.alphas))}")
            print(f"要运行的方法: {', '.join(args.methods)}")
    
    # 依次执行命令
    for cmd in commands:
        print(f"\n开始执行: {cmd}")
        return_code = run_command(cmd)
        if return_code != 0:
            print(f"命令执行失败: {cmd}")
            sys.exit(return_code)
        print(f"成功完成: {cmd}")
    
    # 计算总执行时间
    elapsed_time = time.time() - start_time
    minutes = int(elapsed_time // 60)
    seconds = elapsed_time % 60

    print("\n" + "=" * 80)
    print(f"所有模拟已完成！总用时: {minutes} 分 {seconds:.2f} 秒 (共 {elapsed_time:.2f} 秒)")
    print("=" * 80)
    
    # 输出结果文件路径提示
    print_result_files(args)
    
    return 0

if __name__ == "__main__":
    
    # 1. 固定beta=0.50，运行所有方法: 
    #    python run_simulations.py --mode method --beta 0.50
    #    python run_simulations.py --mode method --beta 1.00
    #    python run_simulations.py --mode method --beta 1.50
    # 2. 固定method=LayerCache，运行不同beta值: 
    #    python run_simulations.py --mode beta --method LayerCache --betas 0.50 0.75 1.00 1.25 1.50
    #    python run_simulations.py --mode beta --method FaaSCache --betas 0.50 0.75 1.00 1.25 1.50
    #    python run_simulations.py --mode beta --method CrossEdge --betas 0.50 0.75 1.00 1.25 1.50
    #    python run_simulations.py --mode beta --method OpenWhisk --betas 0.50 0.75 1.00 1.25 1.50
    # 3. 对每个beta值分别运行不同方法:
    #    python run_simulations.py --mode multi-beta --betas 0.50 0.75 1.00 1.25 1.50 --methods LayerCache FaaSCache CrossEdge OpenWhisk
    # 4. 固定method=LayerCache和beta=0.50，运行不同alpha值:
    
    sys.exit(main()) 