2025-07-30 01:43:16,073 - INFO - topo_file: ./data/topo/site-optus-melbCBD.csv
2025-07-30 01:43:16,075 - INFO - node_num: 125, mem_cap: 15000, cpu_Freq: 3
2025-07-30 01:43:16,075 - INFO - latency_para: 1, redu_factor: 30, slot_num: 15
2025-07-30 01:43:16,075 - INFO - cache_method: LayerCache, beta: 0.5, alpha: 0.015
2025-07-30 01:43:16,076 - INFO - open topo file succeessfully.....
2025-07-30 01:43:16,076 - INFO - node 1 lat -37.815170 long 144.974760
2025-07-30 01:43:16,076 - INFO - node 2 lat -37.815240 long 144.952560
2025-07-30 01:43:16,076 - INFO - node 3 lat -37.812390 long 144.971200
2025-07-30 01:43:16,076 - INFO - node 4 lat -37.816790 long 144.969180
2025-07-30 01:43:16,076 - INFO - node 5 lat -37.818080 long 144.956920
2025-07-30 01:43:16,076 - INFO - node 6 lat -37.811269 long 144.957909
2025-07-30 01:43:16,076 - INFO - node 7 lat -37.809081 long 144.968930
2025-07-30 01:43:16,076 - INFO - node 8 lat -37.815461 long 144.962656
2025-07-30 01:43:16,077 - INFO - node 9 lat -37.812850 long 144.955026
2025-07-30 01:43:16,077 - INFO - node 10 lat -37.816356 long 144.962313
2025-07-30 01:43:16,077 - INFO - node 11 lat -37.818071 long 144.957211
2025-07-30 01:43:16,077 - INFO - node 12 lat -37.818202 long 144.954367
2025-07-30 01:43:16,077 - INFO - node 13 lat -37.814225 long 144.971971
2025-07-30 01:43:16,077 - INFO - node 14 lat -37.815975 long 144.955905
2025-07-30 01:43:16,077 - INFO - node 15 lat -37.815493 long 144.956714
2025-07-30 01:43:16,077 - INFO - node 16 lat -37.818520 long 144.957141
2025-07-30 01:43:16,077 - INFO - node 17 lat -37.817303 long 144.962344
2025-07-30 01:43:16,077 - INFO - node 18 lat -37.818162 long 144.959934
2025-07-30 01:43:16,077 - INFO - node 19 lat -37.813381 long 144.959612
2025-07-30 01:43:16,077 - INFO - node 20 lat -37.812084 long 144.967997
2025-07-30 01:43:16,077 - INFO - node 21 lat -37.818494 long 144.957705
2025-07-30 01:43:16,077 - INFO - node 22 lat -37.812122 long 144.970710
2025-07-30 01:43:16,077 - INFO - node 23 lat -37.811625 long 144.964919
2025-07-30 01:43:16,077 - INFO - node 24 lat -37.814263 long 144.972105
2025-07-30 01:43:16,077 - INFO - node 25 lat -37.815549 long 144.966686
2025-07-30 01:43:16,077 - INFO - node 26 lat -37.818166 long 144.964831
2025-07-30 01:43:16,077 - INFO - node 27 lat -37.817384 long 144.959217
2025-07-30 01:43:16,077 - INFO - node 28 lat -37.814896 long 144.971668
2025-07-30 01:43:16,077 - INFO - node 29 lat -37.811126 long 144.962116
2025-07-30 01:43:16,077 - INFO - node 30 lat -37.818656 long 144.956626
2025-07-30 01:43:16,077 - INFO - node 31 lat -37.817261 long 144.962515
2025-07-30 01:43:16,077 - INFO - node 32 lat -37.812845 long 144.968248
2025-07-30 01:43:16,077 - INFO - node 33 lat -37.811978 long 144.962388
2025-07-30 01:43:16,077 - INFO - node 34 lat -37.812723 long 144.960164
2025-07-30 01:43:16,077 - INFO - node 35 lat -37.813463 long 144.973217
2025-07-30 01:43:16,077 - INFO - node 36 lat -37.810996 long 144.967095
2025-07-30 01:43:16,077 - INFO - node 37 lat -37.814989 long 144.960908
2025-07-30 01:43:16,078 - INFO - node 38 lat -37.820910 long 144.955155
2025-07-30 01:43:16,078 - INFO - node 39 lat -37.820123 long 144.957552
2025-07-30 01:43:16,078 - INFO - node 40 lat -37.812899 long 144.959886
2025-07-30 01:43:16,078 - INFO - node 41 lat -37.811678 long 144.962783
2025-07-30 01:43:16,078 - INFO - node 42 lat -37.819576 long 144.959850
2025-07-30 01:43:16,078 - INFO - node 43 lat -37.817069 long 144.961828
2025-07-30 01:43:16,078 - INFO - node 44 lat -37.818218 long 144.960705
2025-07-30 01:43:16,078 - INFO - node 45 lat -37.809885 long 144.963286
2025-07-30 01:43:16,078 - INFO - node 46 lat -37.815196 long 144.962970
2025-07-30 01:43:16,078 - INFO - node 47 lat -37.812799 long 144.954686
2025-07-30 01:43:16,078 - INFO - node 48 lat -37.811928 long 144.956414
2025-07-30 01:43:16,078 - INFO - node 49 lat -37.816011 long 144.972058
2025-07-30 01:43:16,079 - INFO - node 50 lat -37.814431 long 144.954653
2025-07-30 01:43:16,079 - INFO - node 51 lat -37.810382 long 144.971223
2025-07-30 01:43:16,079 - INFO - node 52 lat -37.816467 long 144.958334
2025-07-30 01:43:16,079 - INFO - node 53 lat -37.815777 long 144.970508
2025-07-30 01:43:16,079 - INFO - node 54 lat -37.812578 long 144.965314
2025-07-30 01:43:16,079 - INFO - node 55 lat -37.816070 long 144.957254
2025-07-30 01:43:16,079 - INFO - node 56 lat -37.814398 long 144.966320
2025-07-30 01:43:16,080 - INFO - node 57 lat -37.813963 long 144.957301
2025-07-30 01:43:16,080 - INFO - node 58 lat -37.816158 long 144.960706
2025-07-30 01:43:16,080 - INFO - node 59 lat -37.817779 long 144.955969
2025-07-30 01:43:16,080 - INFO - node 60 lat -37.818018 long 144.964596
2025-07-30 01:43:16,080 - INFO - node 61 lat -37.812857 long 144.968430
2025-07-30 01:43:16,080 - INFO - node 62 lat -37.811627 long 144.965056
2025-07-30 01:43:16,080 - INFO - node 63 lat -37.816122 long 144.964421
2025-07-30 01:43:16,080 - INFO - node 64 lat -37.813447 long 144.966028
2025-07-30 01:43:16,080 - INFO - node 65 lat -37.815488 long 144.966824
2025-07-30 01:43:16,080 - INFO - node 66 lat -37.814765 long 144.969286
2025-07-30 01:43:16,080 - INFO - node 67 lat -37.812088 long 144.970836
2025-07-30 01:43:16,080 - INFO - node 68 lat -37.816206 long 144.966634
2025-07-30 01:43:16,080 - INFO - node 69 lat -37.819471 long 144.960069
2025-07-30 01:43:16,080 - INFO - node 70 lat -37.810219 long 144.961800
2025-07-30 01:43:16,080 - INFO - node 71 lat -37.809538 long 144.964091
2025-07-30 01:43:16,080 - INFO - node 72 lat -37.812653 long 144.956622
2025-07-30 01:43:16,081 - INFO - node 73 lat -37.811251 long 144.964157
2025-07-30 01:43:16,081 - INFO - node 74 lat -37.813540 long 144.971421
2025-07-30 01:43:16,081 - INFO - node 75 lat -37.809969 long 144.970780
2025-07-30 01:43:16,081 - INFO - node 76 lat -37.809264 long 144.971708
2025-07-30 01:43:16,081 - INFO - node 77 lat -37.812711 long 144.959971
2025-07-30 01:43:16,081 - INFO - node 78 lat -37.816530 long 144.961967
2025-07-30 01:43:16,081 - INFO - node 79 lat -37.815387 long 144.968815
2025-07-30 01:43:16,081 - INFO - node 80 lat -37.817510 long 144.959237
2025-07-30 01:43:16,081 - INFO - node 81 lat -37.814257 long 144.963370
2025-07-30 01:43:16,081 - INFO - node 82 lat -37.809041 long 144.971828
2025-07-30 01:43:16,081 - INFO - node 83 lat -37.820744 long 144.955485
2025-07-30 01:43:16,081 - INFO - node 84 lat -37.817483 long 144.959445
2025-07-30 01:43:16,081 - INFO - node 85 lat -37.812934 long 144.952075
2025-07-30 01:43:16,081 - INFO - node 86 lat -37.812343 long 144.954135
2025-07-30 01:43:16,081 - INFO - node 87 lat -37.811533 long 144.972614
2025-07-30 01:43:16,081 - INFO - node 88 lat -37.813573 long 144.973404
2025-07-30 01:43:16,081 - INFO - node 89 lat -37.815793 long 144.958295
2025-07-30 01:43:16,081 - INFO - node 90 lat -37.818474 long 144.956805
2025-07-30 01:43:16,081 - INFO - node 91 lat -37.814395 long 144.963537
2025-07-30 01:43:16,081 - INFO - node 92 lat -37.815891 long 144.958861
2025-07-30 01:43:16,081 - INFO - node 93 lat -37.815195 long 144.974409
2025-07-30 01:43:16,081 - INFO - node 94 lat -37.815371 long 144.973076
2025-07-30 01:43:16,081 - INFO - node 95 lat -37.818151 long 144.960810
2025-07-30 01:43:16,081 - INFO - node 96 lat -37.816299 long 144.959020
2025-07-30 01:43:16,081 - INFO - node 97 lat -37.814905 long 144.954913
2025-07-30 01:43:16,081 - INFO - node 98 lat -37.816209 long 144.961635
2025-07-30 01:43:16,081 - INFO - node 99 lat -37.812379 long 144.956402
2025-07-30 01:43:16,081 - INFO - node 100 lat -37.816822 long 144.963209
2025-07-30 01:43:16,081 - INFO - node 101 lat -37.815309 long 144.969476
2025-07-30 01:43:16,081 - INFO - node 102 lat -37.809356 long 144.970729
2025-07-30 01:43:16,081 - INFO - node 103 lat -37.811313 long 144.972958
2025-07-30 01:43:16,081 - INFO - node 104 lat -37.810641 long 144.970534
2025-07-30 01:43:16,081 - INFO - node 105 lat -37.810911 long 144.962690
2025-07-30 01:43:16,081 - INFO - node 106 lat -37.810065 long 144.963281
2025-07-30 01:43:16,081 - INFO - node 107 lat -37.811398 long 144.972672
2025-07-30 01:43:16,083 - INFO - node 108 lat -37.816891 long 144.961957
2025-07-30 01:43:16,083 - INFO - node 109 lat -37.813545 long 144.971705
2025-07-30 01:43:16,083 - INFO - node 110 lat -37.814265 long 144.971685
2025-07-30 01:43:16,083 - INFO - node 111 lat -37.815992 long 144.961028
2025-07-30 01:43:16,083 - INFO - node 112 lat -37.814484 long 144.963500
2025-07-30 01:43:16,083 - INFO - node 113 lat -37.813167 long 144.965468
2025-07-30 01:43:16,083 - INFO - node 114 lat -37.819220 long 144.958485
2025-07-30 01:43:16,083 - INFO - node 115 lat -37.815858 long 144.954818
2025-07-30 01:43:16,083 - INFO - node 116 lat -37.816217 long 144.970928
2025-07-30 01:43:16,083 - INFO - node 117 lat -37.810269 long 144.969863
2025-07-30 01:43:16,083 - INFO - node 118 lat -37.819101 long 144.954229
2025-07-30 01:43:16,083 - INFO - node 119 lat -37.811022 long 144.959234
2025-07-30 01:43:16,083 - INFO - node 120 lat -37.812150 long 144.961955
2025-07-30 01:43:16,083 - INFO - node 121 lat -37.816496 long 144.965090
2025-07-30 01:43:16,084 - INFO - node 122 lat -37.815296 long 144.958369
2025-07-30 01:43:16,084 - INFO - node 123 lat -37.816310 long 144.964351
2025-07-30 01:43:16,084 - INFO - node 124 lat -37.817386 long 144.961230
2025-07-30 01:43:16,084 - INFO - node 125 lat -37.813175 long 144.952919
2025-07-30 01:43:16,084 - INFO - open request file succeessfully.....
2025-07-30 01:43:18,659 - INFO - --------Schedule Start--------
2025-07-30 01:43:18,659 - INFO - --------处理时间槽 1 的请求，共 915 个--------
2025-07-30 01:43:18,660 - INFO - ----node memomry----
2025-07-30 01:43:18,660 - INFO - node 1 : 15000 MB
2025-07-30 01:43:18,660 - INFO - node 2 : 15000 MB
2025-07-30 01:43:18,660 - INFO - node 3 : 15000 MB
2025-07-30 01:43:18,660 - INFO - node 4 : 15000 MB
2025-07-30 01:43:18,660 - INFO - node 5 : 15000 MB
2025-07-30 01:43:18,660 - INFO - node 6 : 15000 MB
2025-07-30 01:43:18,660 - INFO - node 7 : 15000 MB
2025-07-30 01:43:18,660 - INFO - node 8 : 15000 MB
2025-07-30 01:43:18,660 - INFO - node 9 : 15000 MB
2025-07-30 01:43:18,660 - INFO - node 10 : 15000 MB
2025-07-30 01:43:18,660 - INFO - node 11 : 15000 MB
2025-07-30 01:43:18,660 - INFO - node 12 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 13 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 14 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 15 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 16 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 17 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 18 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 19 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 20 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 21 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 22 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 23 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 24 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 25 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 26 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 27 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 28 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 29 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 30 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 31 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 32 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 33 : 15000 MB
2025-07-30 01:43:18,661 - INFO - node 34 : 15000 MB
2025-07-30 01:43:18,662 - INFO - node 35 : 15000 MB
2025-07-30 01:43:18,662 - INFO - node 36 : 15000 MB
2025-07-30 01:43:18,662 - INFO - node 37 : 15000 MB
2025-07-30 01:43:18,662 - INFO - node 38 : 15000 MB
2025-07-30 01:43:18,662 - INFO - node 39 : 15000 MB
2025-07-30 01:43:18,662 - INFO - node 40 : 15000 MB
2025-07-30 01:43:18,662 - INFO - node 41 : 15000 MB
2025-07-30 01:43:18,662 - INFO - node 42 : 15000 MB
2025-07-30 01:43:18,662 - INFO - node 43 : 15000 MB
2025-07-30 01:43:18,662 - INFO - node 44 : 15000 MB
2025-07-30 01:43:18,663 - INFO - node 45 : 15000 MB
2025-07-30 01:43:18,663 - INFO - node 46 : 15000 MB
2025-07-30 01:43:18,663 - INFO - node 47 : 15000 MB
2025-07-30 01:43:18,663 - INFO - node 48 : 15000 MB
2025-07-30 01:43:18,663 - INFO - node 49 : 15000 MB
2025-07-30 01:43:18,663 - INFO - node 50 : 15000 MB
2025-07-30 01:43:18,663 - INFO - node 51 : 15000 MB
2025-07-30 01:43:18,663 - INFO - node 52 : 15000 MB
2025-07-30 01:43:18,663 - INFO - node 53 : 15000 MB
2025-07-30 01:43:18,663 - INFO - node 54 : 15000 MB
2025-07-30 01:43:18,663 - INFO - node 55 : 15000 MB
2025-07-30 01:43:18,663 - INFO - node 56 : 15000 MB
2025-07-30 01:43:18,663 - INFO - node 57 : 15000 MB
2025-07-30 01:43:18,663 - INFO - node 58 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 59 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 60 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 61 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 62 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 63 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 64 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 65 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 66 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 67 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 68 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 69 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 70 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 71 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 72 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 73 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 74 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 75 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 76 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 77 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 78 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 79 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 80 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 81 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 82 : 15000 MB
2025-07-30 01:43:18,664 - INFO - node 83 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 84 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 85 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 86 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 87 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 88 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 89 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 90 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 91 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 92 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 93 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 94 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 95 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 96 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 97 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 98 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 99 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 100 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 101 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 102 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 103 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 104 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 105 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 106 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 107 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 108 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 109 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 110 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 111 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 112 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 113 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 114 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 115 : 15000 MB
2025-07-30 01:43:18,665 - INFO - node 116 : 15000 MB
2025-07-30 01:43:18,666 - INFO - node 117 : 15000 MB
2025-07-30 01:43:18,666 - INFO - node 118 : 15000 MB
2025-07-30 01:43:18,666 - INFO - node 119 : 15000 MB
2025-07-30 01:43:18,666 - INFO - node 120 : 15000 MB
2025-07-30 01:43:18,666 - INFO - node 121 : 15000 MB
2025-07-30 01:43:18,666 - INFO - node 122 : 15000 MB
2025-07-30 01:43:18,666 - INFO - node 123 : 15000 MB
2025-07-30 01:43:18,666 - INFO - node 124 : 15000 MB
2025-07-30 01:43:18,666 - INFO - node 125 : 15000 MB
2025-07-30 01:43:18,667 - INFO -   秒 0: 处理 110 个新请求
2025-07-30 01:43:18,696 - INFO - DEBUG: 节点96内存检查 - 需要内存: 190.0MB, 可用内存: 15000MB, 函数类型: 4
2025-07-30 01:43:18,696 - INFO - DEBUG: 节点96创建Bare层，减少内存 30MB，UUID: 4b50c6f1-435c-4d6d-8c57-64905862108b
2025-07-30 01:43:18,696 - INFO - DEBUG: 节点96创建Lang层，减少内存 120MB，UUID: c3d4e8d5-91c5-4026-abc5-b1e441fb1896
2025-07-30 01:43:18,696 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:18,704 - INFO - 时间槽 1，秒 0: 执行中容器 110，等待请求 0
2025-07-30 01:43:18,704 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:43:18,707 - INFO -   秒 2: 处理 3 个新请求
2025-07-30 01:43:18,708 - INFO -   秒 3: 处理 22 个新请求
2025-07-30 01:43:18,717 - INFO -   秒 4: 处理 10 个新请求
2025-07-30 01:43:18,720 - INFO -   秒 5: 处理 19 个新请求
2025-07-30 01:43:18,753 - INFO -   秒 6: 处理 15 个新请求
2025-07-30 01:43:18,759 - INFO -   秒 7: 处理 11 个新请求
2025-07-30 01:43:18,769 - INFO -   秒 8: 处理 10 个新请求
2025-07-30 01:43:18,772 - INFO -   秒 9: 处理 20 个新请求
2025-07-30 01:43:18,784 - INFO -   秒 10: 处理 15 个新请求
2025-07-30 01:43:18,789 - INFO - 时间槽 1，秒 10: 执行中容器 45，等待请求 0
2025-07-30 01:43:18,789 - INFO -   秒 11: 处理 19 个新请求
2025-07-30 01:43:18,793 - INFO -   秒 12: 处理 13 个新请求
2025-07-30 01:43:18,799 - INFO -   秒 13: 处理 11 个新请求
2025-07-30 01:43:18,803 - INFO -   秒 14: 处理 18 个新请求
2025-07-30 01:43:18,810 - INFO -   秒 15: 处理 19 个新请求
2025-07-30 01:43:18,821 - INFO -   秒 16: 处理 13 个新请求
2025-07-30 01:43:18,825 - INFO -   秒 17: 处理 13 个新请求
2025-07-30 01:43:18,828 - INFO -   秒 18: 处理 9 个新请求
2025-07-30 01:43:18,831 - INFO -   秒 19: 处理 26 个新请求
2025-07-30 01:43:18,839 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 14810MB, 函数类型: 1
2025-07-30 01:43:18,839 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:18,841 - INFO -   秒 20: 处理 20 个新请求
2025-07-30 01:43:18,849 - INFO - 时间槽 1，秒 20: 执行中容器 51，等待请求 0
2025-07-30 01:43:18,849 - INFO -   秒 21: 处理 6 个新请求
2025-07-30 01:43:18,852 - INFO -   秒 22: 处理 9 个新请求
2025-07-30 01:43:18,856 - INFO -   秒 23: 处理 22 个新请求
2025-07-30 01:43:18,861 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 14760MB, 函数类型: 4
2025-07-30 01:43:18,862 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:18,864 - INFO -   秒 24: 处理 17 个新请求
2025-07-30 01:43:18,866 - INFO -   秒 25: 处理 13 个新请求
2025-07-30 01:43:18,871 - INFO -   秒 26: 处理 8 个新请求
2025-07-30 01:43:18,876 - INFO -   秒 27: 处理 15 个新请求
2025-07-30 01:43:18,878 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 14720MB, 函数类型: 4
2025-07-30 01:43:18,878 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:18,880 - INFO -   秒 28: 处理 11 个新请求
2025-07-30 01:43:18,883 - INFO -   秒 29: 处理 26 个新请求
2025-07-30 01:43:18,890 - INFO -   秒 30: 处理 18 个新请求
2025-07-30 01:43:18,895 - INFO - 时间槽 1，秒 30: 执行中容器 54，等待请求 0
2025-07-30 01:43:18,896 - INFO -   秒 31: 处理 19 个新请求
2025-07-30 01:43:18,901 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 14680MB, 函数类型: 1
2025-07-30 01:43:18,902 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:18,904 - INFO -   秒 32: 处理 3 个新请求
2025-07-30 01:43:18,906 - INFO -   秒 33: 处理 14 个新请求
2025-07-30 01:43:18,909 - INFO -   秒 34: 处理 23 个新请求
2025-07-30 01:43:18,917 - INFO -   秒 35: 处理 15 个新请求
2025-07-30 01:43:18,922 - INFO -   秒 36: 处理 14 个新请求
2025-07-30 01:43:18,927 - INFO -   秒 37: 处理 8 个新请求
2025-07-30 01:43:18,929 - INFO -   秒 38: 处理 13 个新请求
2025-07-30 01:43:18,933 - INFO -   秒 39: 处理 25 个新请求
2025-07-30 01:43:18,940 - INFO -   秒 40: 处理 13 个新请求
2025-07-30 01:43:18,944 - INFO - 时间槽 1，秒 40: 执行中容器 49，等待请求 0
2025-07-30 01:43:18,944 - INFO -   秒 41: 处理 14 个新请求
2025-07-30 01:43:18,949 - INFO -   秒 42: 处理 10 个新请求
2025-07-30 01:43:18,951 - INFO -   秒 43: 处理 15 个新请求
2025-07-30 01:43:18,953 - INFO - DEBUG: 节点96内存检查 - 需要内存: 295.0MB, 可用内存: 14630MB, 函数类型: 9
2025-07-30 01:43:18,953 - INFO - DEBUG: 节点96创建Bare层，减少内存 30MB，UUID: 83ae8bdd-1397-4074-a844-906e0629b1b3
2025-07-30 01:43:18,953 - INFO - DEBUG: 节点96创建Lang层，减少内存 175MB，UUID: 0d28402d-f41c-4ef7-9dbd-e04f9b2efe71
2025-07-30 01:43:18,953 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:18,955 - INFO -   秒 44: 处理 18 个新请求
2025-07-30 01:43:18,961 - INFO -   秒 45: 处理 13 个新请求
2025-07-30 01:43:18,967 - INFO -   秒 46: 处理 12 个新请求
2025-07-30 01:43:18,971 - INFO -   秒 47: 处理 19 个新请求
2025-07-30 01:43:18,978 - INFO - DEBUG: 节点96内存检查 - 需要内存: 155.0MB, 可用内存: 14335MB, 函数类型: 6
2025-07-30 01:43:18,978 - INFO - DEBUG: 节点96创建Bare层，减少内存 35MB，UUID: eb500ae7-1ea8-4edc-b5bc-d9612008c1d2
2025-07-30 01:43:18,978 - INFO - DEBUG: 节点96创建Lang层，减少内存 50MB，UUID: dad43d12-b636-4e49-875b-2749bd50c510
2025-07-30 01:43:18,978 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:43:18,981 - INFO -   秒 48: 处理 15 个新请求
2025-07-30 01:43:18,987 - INFO -   秒 49: 处理 18 个新请求
2025-07-30 01:43:18,992 - INFO -   秒 50: 处理 13 个新请求
2025-07-30 01:43:18,995 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 14180MB, 函数类型: 1
2025-07-30 01:43:18,995 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:18,997 - INFO - 时间槽 1，秒 50: 执行中容器 47，等待请求 0
2025-07-30 01:43:18,997 - INFO -   秒 51: 处理 13 个新请求
2025-07-30 01:43:19,003 - INFO -   秒 52: 处理 11 个新请求
2025-07-30 01:43:19,007 - INFO -   秒 53: 处理 16 个新请求
2025-07-30 01:43:19,012 - INFO -   秒 54: 处理 11 个新请求
2025-07-30 01:43:19,015 - INFO -   秒 55: 处理 15 个新请求
2025-07-30 01:43:19,021 - INFO -   秒 56: 处理 10 个新请求
2025-07-30 01:43:19,025 - INFO - 时间槽 1 结束时，还有 9 个容器正在执行，将继续在后台执行
2025-07-30 01:43:19,026 - INFO - --------处理时间槽 2 的请求，共 915 个--------
2025-07-30 01:43:19,026 - INFO - ----node memomry----
2025-07-30 01:43:19,026 - INFO - node 1 : 15000 MB
2025-07-30 01:43:19,026 - INFO - node 2 : 14785 MB
2025-07-30 01:43:19,027 - INFO - node 3 : 13780 MB
2025-07-30 01:43:19,027 - INFO - node 4 : 14810 MB
2025-07-30 01:43:19,027 - INFO - node 5 : 13950 MB
2025-07-30 01:43:19,027 - INFO - node 6 : 14800 MB
2025-07-30 01:43:19,027 - INFO - node 7 : 14845 MB
2025-07-30 01:43:19,027 - INFO - node 8 : 14465 MB
2025-07-30 01:43:19,027 - INFO - node 9 : 14400 MB
2025-07-30 01:43:19,027 - INFO - node 10 : 14725 MB
2025-07-30 01:43:19,027 - INFO - node 11 : 14810 MB
2025-07-30 01:43:19,027 - INFO - node 12 : 14645 MB
2025-07-30 01:43:19,027 - INFO - node 13 : 13910 MB
2025-07-30 01:43:19,027 - INFO - node 14 : 14705 MB
2025-07-30 01:43:19,027 - INFO - node 15 : 14705 MB
2025-07-30 01:43:19,027 - INFO - node 16 : 14500 MB
2025-07-30 01:43:19,027 - INFO - node 17 : 14175 MB
2025-07-30 01:43:19,027 - INFO - node 18 : 15000 MB
2025-07-30 01:43:19,027 - INFO - node 19 : 14800 MB
2025-07-30 01:43:19,028 - INFO - node 20 : 14800 MB
2025-07-30 01:43:19,028 - INFO - node 21 : 14295 MB
2025-07-30 01:43:19,028 - INFO - node 22 : 14410 MB
2025-07-30 01:43:19,028 - INFO - node 23 : 14000 MB
2025-07-30 01:43:19,028 - INFO - node 24 : 14425 MB
2025-07-30 01:43:19,028 - INFO - node 25 : 14385 MB
2025-07-30 01:43:19,028 - INFO - node 26 : 14260 MB
2025-07-30 01:43:19,028 - INFO - node 27 : 14845 MB
2025-07-30 01:43:19,028 - INFO - node 28 : 14710 MB
2025-07-30 01:43:19,028 - INFO - node 29 : 14505 MB
2025-07-30 01:43:19,028 - INFO - node 30 : 14580 MB
2025-07-30 01:43:19,028 - INFO - node 31 : 13790 MB
2025-07-30 01:43:19,028 - INFO - node 32 : 14290 MB
2025-07-30 01:43:19,028 - INFO - node 33 : 14810 MB
2025-07-30 01:43:19,028 - INFO - node 34 : 14800 MB
2025-07-30 01:43:19,028 - INFO - node 35 : 14465 MB
2025-07-30 01:43:19,028 - INFO - node 36 : 14795 MB
2025-07-30 01:43:19,028 - INFO - node 37 : 14700 MB
2025-07-30 01:43:19,028 - INFO - node 38 : 14145 MB
2025-07-30 01:43:19,028 - INFO - node 39 : 13800 MB
2025-07-30 01:43:19,028 - INFO - node 40 : 14220 MB
2025-07-30 01:43:19,028 - INFO - node 41 : 14845 MB
2025-07-30 01:43:19,028 - INFO - node 42 : 14225 MB
2025-07-30 01:43:19,028 - INFO - node 43 : 14310 MB
2025-07-30 01:43:19,028 - INFO - node 44 : 14455 MB
2025-07-30 01:43:19,028 - INFO - node 45 : 14530 MB
2025-07-30 01:43:19,028 - INFO - node 46 : 15000 MB
2025-07-30 01:43:19,028 - INFO - node 47 : 13845 MB
2025-07-30 01:43:19,028 - INFO - node 48 : 14360 MB
2025-07-30 01:43:19,028 - INFO - node 49 : 14810 MB
2025-07-30 01:43:19,028 - INFO - node 50 : 14500 MB
2025-07-30 01:43:19,028 - INFO - node 51 : 14130 MB
2025-07-30 01:43:19,028 - INFO - node 52 : 14605 MB
2025-07-30 01:43:19,028 - INFO - node 53 : 14845 MB
2025-07-30 01:43:19,028 - INFO - node 54 : 14785 MB
2025-07-30 01:43:19,028 - INFO - node 55 : 14550 MB
2025-07-30 01:43:19,028 - INFO - node 56 : 14800 MB
2025-07-30 01:43:19,028 - INFO - node 57 : 14585 MB
2025-07-30 01:43:19,029 - INFO - node 58 : 14155 MB
2025-07-30 01:43:19,029 - INFO - node 59 : 14415 MB
2025-07-30 01:43:19,029 - INFO - node 60 : 15000 MB
2025-07-30 01:43:19,029 - INFO - node 61 : 13550 MB
2025-07-30 01:43:19,029 - INFO - node 62 : 14240 MB
2025-07-30 01:43:19,029 - INFO - node 63 : 14330 MB
2025-07-30 01:43:19,029 - INFO - node 64 : 14740 MB
2025-07-30 01:43:19,029 - INFO - node 65 : 13625 MB
2025-07-30 01:43:19,029 - INFO - node 66 : 14465 MB
2025-07-30 01:43:19,029 - INFO - node 67 : 14705 MB
2025-07-30 01:43:19,029 - INFO - node 68 : 15000 MB
2025-07-30 01:43:19,029 - INFO - node 69 : 13925 MB
2025-07-30 01:43:19,029 - INFO - node 70 : 15000 MB
2025-07-30 01:43:19,029 - INFO - node 71 : 14450 MB
2025-07-30 01:43:19,029 - INFO - node 72 : 14745 MB
2025-07-30 01:43:19,029 - INFO - node 73 : 14705 MB
2025-07-30 01:43:19,030 - INFO - node 74 : 13960 MB
2025-07-30 01:43:19,030 - INFO - node 75 : 14495 MB
2025-07-30 01:43:19,030 - INFO - node 76 : 14595 MB
2025-07-30 01:43:19,030 - INFO - node 77 : 14160 MB
2025-07-30 01:43:19,030 - INFO - node 78 : 15000 MB
2025-07-30 01:43:19,030 - INFO - node 79 : 13540 MB
2025-07-30 01:43:19,030 - INFO - node 80 : 14205 MB
2025-07-30 01:43:19,030 - INFO - node 81 : 13480 MB
2025-07-30 01:43:19,030 - INFO - node 82 : 14555 MB
2025-07-30 01:43:19,030 - INFO - node 83 : 15000 MB
2025-07-30 01:43:19,030 - INFO - node 84 : 14400 MB
2025-07-30 01:43:19,030 - INFO - node 85 : 13990 MB
2025-07-30 01:43:19,030 - INFO - node 86 : 14140 MB
2025-07-30 01:43:19,030 - INFO - node 87 : 14315 MB
2025-07-30 01:43:19,030 - INFO - node 88 : 14325 MB
2025-07-30 01:43:19,030 - INFO - node 89 : 13650 MB
2025-07-30 01:43:19,030 - INFO - node 90 : 13755 MB
2025-07-30 01:43:19,030 - INFO - node 91 : 14555 MB
2025-07-30 01:43:19,030 - INFO - node 92 : 14810 MB
2025-07-30 01:43:19,030 - INFO - node 93 : 14705 MB
2025-07-30 01:43:19,030 - INFO - node 94 : 14210 MB
2025-07-30 01:43:19,030 - INFO - node 95 : 14175 MB
2025-07-30 01:43:19,031 - INFO - node 96 : 14130 MB
2025-07-30 01:43:19,031 - INFO - node 97 : 14505 MB
2025-07-30 01:43:19,031 - INFO - node 98 : 13350 MB
2025-07-30 01:43:19,031 - INFO - node 99 : 14800 MB
2025-07-30 01:43:19,031 - INFO - node 100 : 14690 MB
2025-07-30 01:43:19,031 - INFO - node 101 : 14575 MB
2025-07-30 01:43:19,031 - INFO - node 102 : 14725 MB
2025-07-30 01:43:19,031 - INFO - node 103 : 14505 MB
2025-07-30 01:43:19,031 - INFO - node 104 : 14705 MB
2025-07-30 01:43:19,031 - INFO - node 105 : 14705 MB
2025-07-30 01:43:19,031 - INFO - node 106 : 14515 MB
2025-07-30 01:43:19,031 - INFO - node 107 : 14230 MB
2025-07-30 01:43:19,031 - INFO - node 108 : 15000 MB
2025-07-30 01:43:19,031 - INFO - node 109 : 14565 MB
2025-07-30 01:43:19,031 - INFO - node 110 : 14265 MB
2025-07-30 01:43:19,031 - INFO - node 111 : 13965 MB
2025-07-30 01:43:19,031 - INFO - node 112 : 14245 MB
2025-07-30 01:43:19,031 - INFO - node 113 : 13700 MB
2025-07-30 01:43:19,031 - INFO - node 114 : 14255 MB
2025-07-30 01:43:19,031 - INFO - node 115 : 14555 MB
2025-07-30 01:43:19,031 - INFO - node 116 : 13790 MB
2025-07-30 01:43:19,031 - INFO - node 117 : 14845 MB
2025-07-30 01:43:19,031 - INFO - node 118 : 14810 MB
2025-07-30 01:43:19,031 - INFO - node 119 : 14465 MB
2025-07-30 01:43:19,031 - INFO - node 120 : 14705 MB
2025-07-30 01:43:19,031 - INFO - node 121 : 14785 MB
2025-07-30 01:43:19,031 - INFO - node 122 : 14705 MB
2025-07-30 01:43:19,031 - INFO - node 123 : 15000 MB
2025-07-30 01:43:19,031 - INFO - node 124 : 14545 MB
2025-07-30 01:43:19,031 - INFO - node 125 : 15000 MB
2025-07-30 01:43:19,032 - INFO -   秒 0: 处理 110 个新请求
2025-07-30 01:43:19,056 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 14130MB, 函数类型: 4
2025-07-30 01:43:19,056 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:19,063 - INFO - 时间槽 2，秒 0: 执行中容器 119，等待请求 0
2025-07-30 01:43:19,063 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:43:19,067 - INFO -   秒 2: 处理 3 个新请求
2025-07-30 01:43:19,068 - INFO -   秒 3: 处理 20 个新请求
2025-07-30 01:43:19,073 - INFO -   秒 4: 处理 15 个新请求
2025-07-30 01:43:19,077 - INFO -   秒 5: 处理 14 个新请求
2025-07-30 01:43:19,083 - INFO -   秒 6: 处理 17 个新请求
2025-07-30 01:43:19,086 - INFO -   秒 7: 处理 10 个新请求
2025-07-30 01:43:19,090 - INFO -   秒 8: 处理 11 个新请求
2025-07-30 01:43:19,095 - INFO -   秒 9: 处理 22 个新请求
2025-07-30 01:43:19,104 - INFO -   秒 10: 处理 13 个新请求
2025-07-30 01:43:19,109 - INFO - 时间槽 2，秒 10: 执行中容器 53，等待请求 0
2025-07-30 01:43:19,109 - INFO -   秒 11: 处理 21 个新请求
2025-07-30 01:43:19,115 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 14090MB, 函数类型: 7
2025-07-30 01:43:19,115 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:43:19,117 - INFO -   秒 12: 处理 10 个新请求
2025-07-30 01:43:19,122 - INFO -   秒 13: 处理 11 个新请求
2025-07-30 01:43:19,127 - INFO -   秒 14: 处理 18 个新请求
2025-07-30 01:43:19,134 - INFO -   秒 15: 处理 18 个新请求
2025-07-30 01:43:19,142 - INFO -   秒 16: 处理 14 个新请求
2025-07-30 01:43:19,148 - INFO -   秒 17: 处理 10 个新请求
2025-07-30 01:43:19,154 - INFO -   秒 18: 处理 13 个新请求
2025-07-30 01:43:19,162 - INFO -   秒 19: 处理 26 个新请求
2025-07-30 01:43:19,166 - INFO - DEBUG: 节点96内存检查 - 需要内存: 360.0MB, 可用内存: 13970MB, 函数类型: 5
2025-07-30 01:43:19,167 - INFO - DEBUG: 节点96创建User层容器，减少内存 360MB，类型: 5
2025-07-30 01:43:19,168 - INFO -   秒 20: 处理 17 个新请求
2025-07-30 01:43:19,177 - INFO - 时间槽 2，秒 20: 执行中容器 66，等待请求 0
2025-07-30 01:43:19,177 - INFO -   秒 21: 处理 8 个新请求
2025-07-30 01:43:19,182 - INFO -   秒 22: 处理 14 个新请求
2025-07-30 01:43:19,189 - INFO -   秒 23: 处理 18 个新请求
2025-07-30 01:43:19,193 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 13610MB, 函数类型: 9
2025-07-30 01:43:19,193 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:19,195 - INFO -   秒 24: 处理 15 个新请求
2025-07-30 01:43:19,200 - INFO -   秒 25: 处理 13 个新请求
2025-07-30 01:43:19,204 - INFO -   秒 26: 处理 10 个新请求
2025-07-30 01:43:19,208 - INFO -   秒 27: 处理 13 个新请求
2025-07-30 01:43:19,211 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 13520MB, 函数类型: 1
2025-07-30 01:43:19,211 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:19,213 - INFO -   秒 28: 处理 14 个新请求
2025-07-30 01:43:19,219 - INFO -   秒 29: 处理 27 个新请求
2025-07-30 01:43:19,226 - INFO -   秒 30: 处理 18 个新请求
2025-07-30 01:43:19,232 - INFO - 时间槽 2，秒 30: 执行中容器 61，等待请求 0
2025-07-30 01:43:19,232 - INFO -   秒 31: 处理 15 个新请求
2025-07-30 01:43:19,235 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 13470MB, 函数类型: 9
2025-07-30 01:43:19,235 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:19,236 - INFO -   秒 32: 处理 9 个新请求
2025-07-30 01:43:19,240 - INFO -   秒 33: 处理 9 个新请求
2025-07-30 01:43:19,244 - INFO -   秒 34: 处理 18 个新请求
2025-07-30 01:43:19,252 - INFO -   秒 35: 处理 21 个新请求
2025-07-30 01:43:19,256 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 13380MB, 函数类型: 2
2025-07-30 01:43:19,256 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:19,258 - INFO -   秒 36: 处理 12 个新请求
2025-07-30 01:43:19,264 - INFO -   秒 37: 处理 11 个新请求
2025-07-30 01:43:19,269 - INFO -   秒 38: 处理 9 个新请求
2025-07-30 01:43:19,274 - INFO -   秒 39: 处理 28 个新请求
2025-07-30 01:43:19,282 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 13315MB, 函数类型: 1
2025-07-30 01:43:19,282 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:19,285 - INFO -   秒 40: 处理 13 个新请求
2025-07-30 01:43:19,288 - INFO - 时间槽 2，秒 40: 执行中容器 52，等待请求 0
2025-07-30 01:43:19,288 - INFO -   秒 41: 处理 10 个新请求
2025-07-30 01:43:19,293 - INFO -   秒 42: 处理 15 个新请求
2025-07-30 01:43:19,300 - INFO -   秒 43: 处理 12 个新请求
2025-07-30 01:43:19,302 - INFO -   秒 44: 处理 19 个新请求
2025-07-30 01:43:19,310 - INFO -   秒 45: 处理 14 个新请求
2025-07-30 01:43:19,315 - INFO -   秒 46: 处理 12 个新请求
2025-07-30 01:43:19,318 - INFO -   秒 47: 处理 21 个新请求
2025-07-30 01:43:19,323 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 13265MB, 函数类型: 4
2025-07-30 01:43:19,323 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:19,326 - INFO -   秒 48: 处理 14 个新请求
2025-07-30 01:43:19,334 - INFO -   秒 49: 处理 17 个新请求
2025-07-30 01:43:19,339 - INFO -   秒 50: 处理 13 个新请求
2025-07-30 01:43:19,344 - INFO - 时间槽 2，秒 50: 执行中容器 53，等待请求 0
2025-07-30 01:43:19,344 - INFO -   秒 51: 处理 13 个新请求
2025-07-30 01:43:19,347 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 13225MB, 函数类型: 4
2025-07-30 01:43:19,348 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:19,351 - INFO -   秒 52: 处理 14 个新请求
2025-07-30 01:43:19,359 - INFO -   秒 53: 处理 12 个新请求
2025-07-30 01:43:19,366 - INFO -   秒 54: 处理 11 个新请求
2025-07-30 01:43:19,371 - INFO -   秒 55: 处理 16 个新请求
2025-07-30 01:43:19,383 - INFO -   秒 56: 处理 8 个新请求
2025-07-30 01:43:19,390 - INFO -   秒 57: 处理 2 个新请求
2025-07-30 01:43:19,394 - INFO - 时间槽 2 结束时，还有 13 个容器正在执行，将继续在后台执行
2025-07-30 01:43:19,398 - INFO - --------处理时间槽 3 的请求，共 902 个--------
2025-07-30 01:43:19,398 - INFO - ----node memomry----
2025-07-30 01:43:19,398 - INFO - node 1 : 14705 MB
2025-07-30 01:43:19,399 - INFO - node 2 : 14720 MB
2025-07-30 01:43:19,399 - INFO - node 3 : 13535 MB
2025-07-30 01:43:19,399 - INFO - node 4 : 14595 MB
2025-07-30 01:43:19,399 - INFO - node 5 : 13665 MB
2025-07-30 01:43:19,399 - INFO - node 6 : 14735 MB
2025-07-30 01:43:19,399 - INFO - node 7 : 14645 MB
2025-07-30 01:43:19,399 - INFO - node 8 : 14400 MB
2025-07-30 01:43:19,399 - INFO - node 9 : 14145 MB
2025-07-30 01:43:19,399 - INFO - node 10 : 14315 MB
2025-07-30 01:43:19,399 - INFO - node 11 : 14760 MB
2025-07-30 01:43:19,399 - INFO - node 12 : 14565 MB
2025-07-30 01:43:19,399 - INFO - node 13 : 13790 MB
2025-07-30 01:43:19,399 - INFO - node 14 : 14705 MB
2025-07-30 01:43:19,399 - INFO - node 15 : 14490 MB
2025-07-30 01:43:19,399 - INFO - node 16 : 13785 MB
2025-07-30 01:43:19,399 - INFO - node 17 : 14060 MB
2025-07-30 01:43:19,399 - INFO - node 18 : 14810 MB
2025-07-30 01:43:19,399 - INFO - node 19 : 14440 MB
2025-07-30 01:43:19,399 - INFO - node 20 : 14645 MB
2025-07-30 01:43:19,399 - INFO - node 21 : 14190 MB
2025-07-30 01:43:19,399 - INFO - node 22 : 14255 MB
2025-07-30 01:43:19,399 - INFO - node 23 : 13885 MB
2025-07-30 01:43:19,399 - INFO - node 24 : 13510 MB
2025-07-30 01:43:19,399 - INFO - node 25 : 13970 MB
2025-07-30 01:43:19,399 - INFO - node 26 : 13850 MB
2025-07-30 01:43:19,400 - INFO - node 27 : 14645 MB
2025-07-30 01:43:19,400 - INFO - node 28 : 14515 MB
2025-07-30 01:43:19,400 - INFO - node 29 : 14455 MB
2025-07-30 01:43:19,400 - INFO - node 30 : 13965 MB
2025-07-30 01:43:19,400 - INFO - node 31 : 12725 MB
2025-07-30 01:43:19,400 - INFO - node 32 : 14085 MB
2025-07-30 01:43:19,400 - INFO - node 33 : 14810 MB
2025-07-30 01:43:19,400 - INFO - node 34 : 14735 MB
2025-07-30 01:43:19,400 - INFO - node 35 : 14020 MB
2025-07-30 01:43:19,400 - INFO - node 36 : 14605 MB
2025-07-30 01:43:19,400 - INFO - node 37 : 14505 MB
2025-07-30 01:43:19,400 - INFO - node 38 : 13725 MB
2025-07-30 01:43:19,400 - INFO - node 39 : 12830 MB
2025-07-30 01:43:19,400 - INFO - node 40 : 13740 MB
2025-07-30 01:43:19,400 - INFO - node 41 : 14630 MB
2025-07-30 01:43:19,400 - INFO - node 42 : 13415 MB
2025-07-30 01:43:19,400 - INFO - node 43 : 13735 MB
2025-07-30 01:43:19,400 - INFO - node 44 : 13810 MB
2025-07-30 01:43:19,400 - INFO - node 45 : 14280 MB
2025-07-30 01:43:19,400 - INFO - node 46 : 15000 MB
2025-07-30 01:43:19,400 - INFO - node 47 : 13540 MB
2025-07-30 01:43:19,400 - INFO - node 48 : 13645 MB
2025-07-30 01:43:19,401 - INFO - node 49 : 14365 MB
2025-07-30 01:43:19,401 - INFO - node 50 : 14220 MB
2025-07-30 01:43:19,401 - INFO - node 51 : 13470 MB
2025-07-30 01:43:19,401 - INFO - node 52 : 14075 MB
2025-07-30 01:43:19,401 - INFO - node 53 : 14635 MB
2025-07-30 01:43:19,401 - INFO - node 54 : 14735 MB
2025-07-30 01:43:19,401 - INFO - node 55 : 14220 MB
2025-07-30 01:43:19,401 - INFO - node 56 : 14760 MB
2025-07-30 01:43:19,401 - INFO - node 57 : 14185 MB
2025-07-30 01:43:19,401 - INFO - node 58 : 13470 MB
2025-07-30 01:43:19,401 - INFO - node 59 : 13815 MB
2025-07-30 01:43:19,401 - INFO - node 60 : 15000 MB
2025-07-30 01:43:19,401 - INFO - node 61 : 12975 MB
2025-07-30 01:43:19,401 - INFO - node 62 : 14100 MB
2025-07-30 01:43:19,401 - INFO - node 63 : 13735 MB
2025-07-30 01:43:19,401 - INFO - node 64 : 14700 MB
2025-07-30 01:43:19,401 - INFO - node 65 : 12320 MB
2025-07-30 01:43:19,401 - INFO - node 66 : 14250 MB
2025-07-30 01:43:19,401 - INFO - node 67 : 14550 MB
2025-07-30 01:43:19,401 - INFO - node 68 : 15000 MB
2025-07-30 01:43:19,401 - INFO - node 69 : 13340 MB
2025-07-30 01:43:19,401 - INFO - node 70 : 15000 MB
2025-07-30 01:43:19,402 - INFO - node 71 : 13895 MB
2025-07-30 01:43:19,402 - INFO - node 72 : 14255 MB
2025-07-30 01:43:19,402 - INFO - node 73 : 14705 MB
2025-07-30 01:43:19,402 - INFO - node 74 : 13325 MB
2025-07-30 01:43:19,402 - INFO - node 75 : 14050 MB
2025-07-30 01:43:19,402 - INFO - node 76 : 13765 MB
2025-07-30 01:43:19,402 - INFO - node 77 : 13620 MB
2025-07-30 01:43:19,402 - INFO - node 78 : 15000 MB
2025-07-30 01:43:19,402 - INFO - node 79 : 12815 MB
2025-07-30 01:43:19,402 - INFO - node 80 : 13715 MB
2025-07-30 01:43:19,402 - INFO - node 81 : 12515 MB
2025-07-30 01:43:19,402 - INFO - node 82 : 14355 MB
2025-07-30 01:43:19,402 - INFO - node 83 : 14810 MB
2025-07-30 01:43:19,402 - INFO - node 84 : 13940 MB
2025-07-30 01:43:19,402 - INFO - node 85 : 12715 MB
2025-07-30 01:43:19,402 - INFO - node 86 : 13565 MB
2025-07-30 01:43:19,402 - INFO - node 87 : 12990 MB
2025-07-30 01:43:19,402 - INFO - node 88 : 13795 MB
2025-07-30 01:43:19,402 - INFO - node 89 : 12705 MB
2025-07-30 01:43:19,402 - INFO - node 90 : 12755 MB
2025-07-30 01:43:19,402 - INFO - node 91 : 14340 MB
2025-07-30 01:43:19,402 - INFO - node 92 : 14615 MB
2025-07-30 01:43:19,402 - INFO - node 93 : 14335 MB
2025-07-30 01:43:19,402 - INFO - node 94 : 13885 MB
2025-07-30 01:43:19,402 - INFO - node 95 : 13760 MB
2025-07-30 01:43:19,402 - INFO - node 96 : 13185 MB
2025-07-30 01:43:19,402 - INFO - node 97 : 13810 MB
2025-07-30 01:43:19,403 - INFO - node 98 : 12960 MB
2025-07-30 01:43:19,403 - INFO - node 99 : 14505 MB
2025-07-30 01:43:19,403 - INFO - node 100 : 14570 MB
2025-07-30 01:43:19,403 - INFO - node 101 : 14000 MB
2025-07-30 01:43:19,403 - INFO - node 102 : 14490 MB
2025-07-30 01:43:19,403 - INFO - node 103 : 14395 MB
2025-07-30 01:43:19,403 - INFO - node 104 : 14505 MB
2025-07-30 01:43:19,403 - INFO - node 105 : 14615 MB
2025-07-30 01:43:19,403 - INFO - node 106 : 13900 MB
2025-07-30 01:43:19,403 - INFO - node 107 : 14025 MB
2025-07-30 01:43:19,403 - INFO - node 108 : 15000 MB
2025-07-30 01:43:19,403 - INFO - node 109 : 14040 MB
2025-07-30 01:43:19,403 - INFO - node 110 : 13830 MB
2025-07-30 01:43:19,403 - INFO - node 111 : 13100 MB
2025-07-30 01:43:19,403 - INFO - node 112 : 13865 MB
2025-07-30 01:43:19,405 - INFO - node 113 : 13450 MB
2025-07-30 01:43:19,406 - INFO - node 114 : 13210 MB
2025-07-30 01:43:19,406 - INFO - node 115 : 14000 MB
2025-07-30 01:43:19,406 - INFO - node 116 : 13310 MB
2025-07-30 01:43:19,406 - INFO - node 117 : 14550 MB
2025-07-30 01:43:19,406 - INFO - node 118 : 14655 MB
2025-07-30 01:43:19,406 - INFO - node 119 : 13880 MB
2025-07-30 01:43:19,406 - INFO - node 120 : 14500 MB
2025-07-30 01:43:19,406 - INFO - node 121 : 14490 MB
2025-07-30 01:43:19,406 - INFO - node 122 : 14550 MB
2025-07-30 01:43:19,406 - INFO - node 123 : 15000 MB
2025-07-30 01:43:19,406 - INFO - node 124 : 13800 MB
2025-07-30 01:43:19,406 - INFO - node 125 : 15000 MB
2025-07-30 01:43:19,407 - INFO -   秒 0: 处理 108 个新请求
2025-07-30 01:43:19,438 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 13185MB, 函数类型: 4
2025-07-30 01:43:19,439 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:19,450 - INFO - 时间槽 3，秒 0: 执行中容器 121，等待请求 0
2025-07-30 01:43:19,450 - INFO -   秒 1: 处理 6 个新请求
2025-07-30 01:43:19,457 - INFO -   秒 2: 处理 5 个新请求
2025-07-30 01:43:19,460 - INFO -   秒 3: 处理 17 个新请求
2025-07-30 01:43:19,466 - INFO -   秒 4: 处理 15 个新请求
2025-07-30 01:43:19,470 - INFO -   秒 5: 处理 16 个新请求
2025-07-30 01:43:19,478 - INFO -   秒 6: 处理 12 个新请求
2025-07-30 01:43:19,485 - INFO -   秒 7: 处理 13 个新请求
2025-07-30 01:43:19,490 - INFO -   秒 8: 处理 10 个新请求
2025-07-30 01:43:19,495 - INFO -   秒 9: 处理 24 个新请求
2025-07-30 01:43:19,506 - INFO -   秒 10: 处理 10 个新请求
2025-07-30 01:43:19,510 - INFO - 时间槽 3，秒 10: 执行中容器 58，等待请求 0
2025-07-30 01:43:19,510 - INFO -   秒 11: 处理 17 个新请求
2025-07-30 01:43:19,518 - INFO -   秒 12: 处理 15 个新请求
2025-07-30 01:43:19,525 - INFO -   秒 13: 处理 10 个新请求
2025-07-30 01:43:19,530 - INFO -   秒 14: 处理 19 个新请求
2025-07-30 01:43:19,538 - INFO -   秒 15: 处理 17 个新请求
2025-07-30 01:43:19,543 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 13145MB, 函数类型: 7
2025-07-30 01:43:19,543 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:43:19,548 - INFO -   秒 16: 处理 12 个新请求
2025-07-30 01:43:19,556 - INFO -   秒 17: 处理 13 个新请求
2025-07-30 01:43:19,561 - INFO -   秒 18: 处理 10 个新请求
2025-07-30 01:43:19,567 - INFO -   秒 19: 处理 30 个新请求
2025-07-30 01:43:19,576 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 13025MB, 函数类型: 9
2025-07-30 01:43:19,576 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:19,581 - INFO -   秒 20: 处理 13 个新请求
2025-07-30 01:43:19,585 - INFO - 时间槽 3，秒 20: 执行中容器 60，等待请求 0
2025-07-30 01:43:19,585 - INFO -   秒 21: 处理 10 个新请求
2025-07-30 01:43:19,590 - INFO -   秒 22: 处理 11 个新请求
2025-07-30 01:43:19,597 - INFO -   秒 23: 处理 16 个新请求
2025-07-30 01:43:19,602 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 12935MB, 函数类型: 1
2025-07-30 01:43:19,602 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:19,604 - INFO -   秒 24: 处理 16 个新请求
2025-07-30 01:43:19,612 - INFO -   秒 25: 处理 18 个新请求
2025-07-30 01:43:19,620 - INFO -   秒 26: 处理 6 个新请求
2025-07-30 01:43:19,623 - INFO -   秒 27: 处理 10 个新请求
2025-07-30 01:43:19,628 - INFO -   秒 28: 处理 18 个新请求
2025-07-30 01:43:19,636 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 12885MB, 函数类型: 2
2025-07-30 01:43:19,636 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:19,639 - INFO -   秒 29: 处理 30 个新请求
2025-07-30 01:43:19,651 - INFO -   秒 30: 处理 11 个新请求
2025-07-30 01:43:19,656 - INFO - 时间槽 3，秒 30: 执行中容器 66，等待请求 0
2025-07-30 01:43:19,657 - INFO -   秒 31: 处理 17 个新请求
2025-07-30 01:43:19,661 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 12820MB, 函数类型: 1
2025-07-30 01:43:19,661 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:19,666 - INFO -   秒 32: 处理 11 个新请求
2025-07-30 01:43:19,671 - INFO -   秒 33: 处理 7 个新请求
2025-07-30 01:43:19,676 - INFO -   秒 34: 处理 15 个新请求
2025-07-30 01:43:19,684 - INFO -   秒 35: 处理 19 个新请求
2025-07-30 01:43:19,688 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 12770MB, 函数类型: 1
2025-07-30 01:43:19,688 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:19,690 - INFO -   秒 36: 处理 14 个新请求
2025-07-30 01:43:19,697 - INFO -   秒 37: 处理 9 个新请求
2025-07-30 01:43:19,702 - INFO -   秒 38: 处理 17 个新请求
2025-07-30 01:43:19,711 - INFO -   秒 39: 处理 25 个新请求
2025-07-30 01:43:19,719 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 12720MB, 函数类型: 2
2025-07-30 01:43:19,719 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:19,724 - INFO -   秒 40: 处理 11 个新请求
2025-07-30 01:43:19,729 - INFO - 时间槽 3，秒 40: 执行中容器 61，等待请求 0
2025-07-30 01:43:19,730 - INFO -   秒 41: 处理 11 个新请求
2025-07-30 01:43:19,736 - INFO -   秒 42: 处理 15 个新请求
2025-07-30 01:43:19,743 - INFO -   秒 43: 处理 11 个新请求
2025-07-30 01:43:19,750 - INFO -   秒 44: 处理 20 个新请求
2025-07-30 01:43:19,759 - INFO -   秒 45: 处理 13 个新请求
2025-07-30 01:43:19,765 - INFO -   秒 46: 处理 14 个新请求
2025-07-30 01:43:19,769 - INFO -   秒 47: 处理 13 个新请求
2025-07-30 01:43:19,772 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 12655MB, 函数类型: 9
2025-07-30 01:43:19,772 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:19,775 - INFO -   秒 48: 处理 18 个新请求
2025-07-30 01:43:19,782 - INFO -   秒 49: 处理 19 个新请求
2025-07-30 01:43:19,791 - INFO -   秒 50: 处理 10 个新请求
2025-07-30 01:43:19,796 - INFO - 时间槽 3，秒 50: 执行中容器 56，等待请求 0
2025-07-30 01:43:19,796 - INFO -   秒 51: 处理 14 个新请求
2025-07-30 01:43:19,800 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 12565MB, 函数类型: 4
2025-07-30 01:43:19,800 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:19,801 - INFO -   秒 52: 处理 15 个新请求
2025-07-30 01:43:19,806 - INFO -   秒 53: 处理 15 个新请求
2025-07-30 01:43:19,813 - INFO -   秒 54: 处理 10 个新请求
2025-07-30 01:43:19,817 - INFO -   秒 55: 处理 12 个新请求
2025-07-30 01:43:19,819 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 12525MB, 函数类型: 1
2025-07-30 01:43:19,819 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:19,821 - INFO -   秒 56: 处理 8 个新请求
2025-07-30 01:43:19,825 - INFO -   秒 57: 处理 1 个新请求
2025-07-30 01:43:19,828 - INFO - 时间槽 3 结束时，还有 19 个容器正在执行，将继续在后台执行
2025-07-30 01:43:19,830 - INFO - --------处理时间槽 4 的请求，共 865 个--------
2025-07-30 01:43:19,830 - INFO - ----node memomry----
2025-07-30 01:43:19,830 - INFO - node 1 : 14495 MB
2025-07-30 01:43:19,830 - INFO - node 2 : 14720 MB
2025-07-30 01:43:19,830 - INFO - node 3 : 13415 MB
2025-07-30 01:43:19,830 - INFO - node 4 : 14595 MB
2025-07-30 01:43:19,831 - INFO - node 5 : 13255 MB
2025-07-30 01:43:19,831 - INFO - node 6 : 14735 MB
2025-07-30 01:43:19,831 - INFO - node 7 : 14645 MB
2025-07-30 01:43:19,831 - INFO - node 8 : 14205 MB
2025-07-30 01:43:19,831 - INFO - node 9 : 14075 MB
2025-07-30 01:43:19,831 - INFO - node 10 : 13490 MB
2025-07-30 01:43:19,831 - INFO - node 11 : 14555 MB
2025-07-30 01:43:19,831 - INFO - node 12 : 14105 MB
2025-07-30 01:43:19,831 - INFO - node 13 : 13360 MB
2025-07-30 01:43:19,831 - INFO - node 14 : 14515 MB
2025-07-30 01:43:19,831 - INFO - node 15 : 14450 MB
2025-07-30 01:43:19,831 - INFO - node 16 : 13705 MB
2025-07-30 01:43:19,831 - INFO - node 17 : 13865 MB
2025-07-30 01:43:19,831 - INFO - node 18 : 14810 MB
2025-07-30 01:43:19,831 - INFO - node 19 : 14350 MB
2025-07-30 01:43:19,831 - INFO - node 20 : 14645 MB
2025-07-30 01:43:19,831 - INFO - node 21 : 13845 MB
2025-07-30 01:43:19,831 - INFO - node 22 : 14095 MB
2025-07-30 01:43:19,831 - INFO - node 23 : 13835 MB
2025-07-30 01:43:19,832 - INFO - node 24 : 13065 MB
2025-07-30 01:43:19,832 - INFO - node 25 : 13700 MB
2025-07-30 01:43:19,832 - INFO - node 26 : 13670 MB
2025-07-30 01:43:19,832 - INFO - node 27 : 14605 MB
2025-07-30 01:43:19,832 - INFO - node 28 : 14275 MB
2025-07-30 01:43:19,832 - INFO - node 29 : 14260 MB
2025-07-30 01:43:19,832 - INFO - node 30 : 13695 MB
2025-07-30 01:43:19,832 - INFO - node 31 : 12495 MB
2025-07-30 01:43:19,832 - INFO - node 32 : 13560 MB
2025-07-30 01:43:19,832 - INFO - node 33 : 14750 MB
2025-07-30 01:43:19,832 - INFO - node 34 : 14440 MB
2025-07-30 01:43:19,833 - INFO - node 35 : 13545 MB
2025-07-30 01:43:19,833 - INFO - node 36 : 14565 MB
2025-07-30 01:43:19,833 - INFO - node 37 : 14120 MB
2025-07-30 01:43:19,833 - INFO - node 38 : 13470 MB
2025-07-30 01:43:19,833 - INFO - node 39 : 11750 MB
2025-07-30 01:43:19,833 - INFO - node 40 : 13325 MB
2025-07-30 01:43:19,833 - INFO - node 41 : 14580 MB
2025-07-30 01:43:19,833 - INFO - node 42 : 12855 MB
2025-07-30 01:43:19,833 - INFO - node 43 : 13365 MB
2025-07-30 01:43:19,833 - INFO - node 44 : 13115 MB
2025-07-30 01:43:19,833 - INFO - node 45 : 13645 MB
2025-07-30 01:43:19,833 - INFO - node 46 : 15000 MB
2025-07-30 01:43:19,833 - INFO - node 47 : 12930 MB
2025-07-30 01:43:19,833 - INFO - node 48 : 12745 MB
2025-07-30 01:43:19,833 - INFO - node 49 : 14300 MB
2025-07-30 01:43:19,833 - INFO - node 50 : 13995 MB
2025-07-30 01:43:19,833 - INFO - node 51 : 13050 MB
2025-07-30 01:43:19,833 - INFO - node 52 : 13535 MB
2025-07-30 01:43:19,833 - INFO - node 53 : 14635 MB
2025-07-30 01:43:19,833 - INFO - node 54 : 14580 MB
2025-07-30 01:43:19,833 - INFO - node 55 : 14090 MB
2025-07-30 01:43:19,833 - INFO - node 56 : 14710 MB
2025-07-30 01:43:19,833 - INFO - node 57 : 14070 MB
2025-07-30 01:43:19,833 - INFO - node 58 : 12730 MB
2025-07-30 01:43:19,833 - INFO - node 59 : 13305 MB
2025-07-30 01:43:19,833 - INFO - node 60 : 15000 MB
2025-07-30 01:43:19,833 - INFO - node 61 : 12605 MB
2025-07-30 01:43:19,833 - INFO - node 62 : 13660 MB
2025-07-30 01:43:19,833 - INFO - node 63 : 13510 MB
2025-07-30 01:43:19,833 - INFO - node 64 : 14205 MB
2025-07-30 01:43:19,833 - INFO - node 65 : 11470 MB
2025-07-30 01:43:19,833 - INFO - node 66 : 13670 MB
2025-07-30 01:43:19,833 - INFO - node 67 : 14360 MB
2025-07-30 01:43:19,833 - INFO - node 68 : 15000 MB
2025-07-30 01:43:19,833 - INFO - node 69 : 12530 MB
2025-07-30 01:43:19,833 - INFO - node 70 : 15000 MB
2025-07-30 01:43:19,833 - INFO - node 71 : 13705 MB
2025-07-30 01:43:19,833 - INFO - node 72 : 14085 MB
2025-07-30 01:43:19,833 - INFO - node 73 : 14515 MB
2025-07-30 01:43:19,834 - INFO - node 74 : 12425 MB
2025-07-30 01:43:19,834 - INFO - node 75 : 13900 MB
2025-07-30 01:43:19,834 - INFO - node 76 : 13625 MB
2025-07-30 01:43:19,834 - INFO - node 77 : 12655 MB
2025-07-30 01:43:19,834 - INFO - node 78 : 15000 MB
2025-07-30 01:43:19,834 - INFO - node 79 : 11680 MB
2025-07-30 01:43:19,834 - INFO - node 80 : 13015 MB
2025-07-30 01:43:19,834 - INFO - node 81 : 11680 MB
2025-07-30 01:43:19,834 - INFO - node 82 : 13995 MB
2025-07-30 01:43:19,834 - INFO - node 83 : 14770 MB
2025-07-30 01:43:19,834 - INFO - node 84 : 13650 MB
2025-07-30 01:43:19,834 - INFO - node 85 : 12280 MB
2025-07-30 01:43:19,834 - INFO - node 86 : 13295 MB
2025-07-30 01:43:19,834 - INFO - node 87 : 12780 MB
2025-07-30 01:43:19,834 - INFO - node 88 : 13385 MB
2025-07-30 01:43:19,834 - INFO - node 89 : 11325 MB
2025-07-30 01:43:19,834 - INFO - node 90 : 11755 MB
2025-07-30 01:43:19,834 - INFO - node 91 : 14290 MB
2025-07-30 01:43:19,834 - INFO - node 92 : 14490 MB
2025-07-30 01:43:19,834 - INFO - node 93 : 14215 MB
2025-07-30 01:43:19,834 - INFO - node 94 : 13640 MB
2025-07-30 01:43:19,834 - INFO - node 95 : 13500 MB
2025-07-30 01:43:19,834 - INFO - node 96 : 12475 MB
2025-07-30 01:43:19,834 - INFO - node 97 : 13680 MB
2025-07-30 01:43:19,834 - INFO - node 98 : 12030 MB
2025-07-30 01:43:19,834 - INFO - node 99 : 14465 MB
2025-07-30 01:43:19,834 - INFO - node 100 : 14195 MB
2025-07-30 01:43:19,834 - INFO - node 101 : 13780 MB
2025-07-30 01:43:19,834 - INFO - node 102 : 14005 MB
2025-07-30 01:43:19,834 - INFO - node 103 : 14305 MB
2025-07-30 01:43:19,834 - INFO - node 104 : 14465 MB
2025-07-30 01:43:19,834 - INFO - node 105 : 14425 MB
2025-07-30 01:43:19,834 - INFO - node 106 : 13530 MB
2025-07-30 01:43:19,834 - INFO - node 107 : 13255 MB
2025-07-30 01:43:19,834 - INFO - node 108 : 15000 MB
2025-07-30 01:43:19,834 - INFO - node 109 : 13870 MB
2025-07-30 01:43:19,834 - INFO - node 110 : 13555 MB
2025-07-30 01:43:19,834 - INFO - node 111 : 11710 MB
2025-07-30 01:43:19,834 - INFO - node 112 : 13465 MB
2025-07-30 01:43:19,834 - INFO - node 113 : 13410 MB
2025-07-30 01:43:19,835 - INFO - node 114 : 12695 MB
2025-07-30 01:43:19,835 - INFO - node 115 : 13510 MB
2025-07-30 01:43:19,835 - INFO - node 116 : 12375 MB
2025-07-30 01:43:19,835 - INFO - node 117 : 14480 MB
2025-07-30 01:43:19,835 - INFO - node 118 : 14360 MB
2025-07-30 01:43:19,835 - INFO - node 119 : 13415 MB
2025-07-30 01:43:19,835 - INFO - node 120 : 14410 MB
2025-07-30 01:43:19,835 - INFO - node 121 : 14400 MB
2025-07-30 01:43:19,835 - INFO - node 122 : 14335 MB
2025-07-30 01:43:19,835 - INFO - node 123 : 14810 MB
2025-07-30 01:43:19,835 - INFO - node 124 : 13480 MB
2025-07-30 01:43:19,835 - INFO - node 125 : 15000 MB
2025-07-30 01:43:19,835 - INFO -   秒 0: 处理 108 个新请求
2025-07-30 01:43:19,859 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 12475MB, 函数类型: 6
2025-07-30 01:43:19,859 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:43:19,867 - INFO - 时间槽 4，秒 0: 执行中容器 127，等待请求 0
2025-07-30 01:43:19,867 - INFO -   秒 1: 处理 6 个新请求
2025-07-30 01:43:19,870 - INFO -   秒 2: 处理 3 个新请求
2025-07-30 01:43:19,873 - INFO -   秒 3: 处理 17 个新请求
2025-07-30 01:43:19,877 - INFO -   秒 4: 处理 14 个新请求
2025-07-30 01:43:19,881 - INFO -   秒 5: 处理 15 个新请求
2025-07-30 01:43:19,886 - INFO -   秒 6: 处理 15 个新请求
2025-07-30 01:43:19,890 - INFO -   秒 7: 处理 13 个新请求
2025-07-30 01:43:19,893 - INFO -   秒 8: 处理 10 个新请求
2025-07-30 01:43:19,897 - INFO -   秒 9: 处理 14 个新请求
2025-07-30 01:43:19,903 - INFO -   秒 10: 处理 15 个新请求
2025-07-30 01:43:19,909 - INFO - 时间槽 4，秒 10: 执行中容器 52，等待请求 0
2025-07-30 01:43:19,909 - INFO -   秒 11: 处理 17 个新请求
2025-07-30 01:43:19,915 - INFO -   秒 12: 处理 15 个新请求
2025-07-30 01:43:19,917 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 12405MB, 函数类型: 1
2025-07-30 01:43:19,918 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:19,919 - INFO -   秒 13: 处理 11 个新请求
2025-07-30 01:43:19,923 - INFO -   秒 14: 处理 17 个新请求
2025-07-30 01:43:19,928 - INFO -   秒 15: 处理 12 个新请求
2025-07-30 01:43:19,934 - INFO -   秒 16: 处理 15 个新请求
2025-07-30 01:43:19,936 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 12355MB, 函数类型: 9
2025-07-30 01:43:19,936 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:19,939 - INFO -   秒 17: 处理 13 个新请求
2025-07-30 01:43:19,944 - INFO -   秒 18: 处理 11 个新请求
2025-07-30 01:43:19,949 - INFO -   秒 19: 处理 21 个新请求
2025-07-30 01:43:19,958 - INFO -   秒 20: 处理 17 个新请求
2025-07-30 01:43:19,965 - INFO - 时间槽 4，秒 20: 执行中容器 63，等待请求 0
2025-07-30 01:43:19,965 - INFO -   秒 21: 处理 14 个新请求
2025-07-30 01:43:19,970 - INFO -   秒 22: 处理 11 个新请求
2025-07-30 01:43:19,974 - INFO -   秒 23: 处理 16 个新请求
2025-07-30 01:43:19,982 - INFO -   秒 24: 处理 14 个新请求
2025-07-30 01:43:19,985 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 12265MB, 函数类型: 9
2025-07-30 01:43:19,985 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:19,987 - INFO -   秒 25: 处理 11 个新请求
2025-07-30 01:43:19,992 - INFO -   秒 26: 处理 14 个新请求
2025-07-30 01:43:20,000 - INFO -   秒 27: 处理 10 个新请求
2025-07-30 01:43:20,003 - INFO -   秒 28: 处理 11 个新请求
2025-07-30 01:43:20,005 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 12175MB, 函数类型: 2
2025-07-30 01:43:20,005 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:20,008 - INFO -   秒 29: 处理 29 个新请求
2025-07-30 01:43:20,018 - INFO -   秒 30: 处理 12 个新请求
2025-07-30 01:43:20,021 - INFO - 时间槽 4，秒 30: 执行中容器 69，等待请求 0
2025-07-30 01:43:20,021 - INFO -   秒 31: 处理 5 个新请求
2025-07-30 01:43:20,025 - INFO -   秒 32: 处理 18 个新请求
2025-07-30 01:43:20,032 - INFO -   秒 33: 处理 14 个新请求
2025-07-30 01:43:20,036 - INFO -   秒 34: 处理 15 个新请求
2025-07-30 01:43:20,045 - INFO -   秒 35: 处理 14 个新请求
2025-07-30 01:43:20,049 - INFO -   秒 36: 处理 10 个新请求
2025-07-30 01:43:20,050 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 12110MB, 函数类型: 4
2025-07-30 01:43:20,050 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:20,052 - INFO -   秒 37: 处理 11 个新请求
2025-07-30 01:43:20,057 - INFO -   秒 38: 处理 20 个新请求
2025-07-30 01:43:20,063 - INFO -   秒 39: 处理 20 个新请求
2025-07-30 01:43:20,071 - INFO -   秒 40: 处理 10 个新请求
2025-07-30 01:43:20,077 - INFO - 时间槽 4，秒 40: 执行中容器 63，等待请求 0
2025-07-30 01:43:20,077 - INFO -   秒 41: 处理 16 个新请求
2025-07-30 01:43:20,082 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 12070MB, 函数类型: 2
2025-07-30 01:43:20,082 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:20,086 - INFO -   秒 42: 处理 7 个新请求
2025-07-30 01:43:20,091 - INFO -   秒 43: 处理 14 个新请求
2025-07-30 01:43:20,097 - INFO -   秒 44: 处理 17 个新请求
2025-07-30 01:43:20,107 - INFO -   秒 45: 处理 12 个新请求
2025-07-30 01:43:20,111 - INFO -   秒 46: 处理 13 个新请求
2025-07-30 01:43:20,119 - INFO -   秒 47: 处理 14 个新请求
2025-07-30 01:43:20,125 - INFO -   秒 48: 处理 18 个新请求
2025-07-30 01:43:20,133 - INFO -   秒 49: 处理 17 个新请求
2025-07-30 01:43:20,138 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 12005MB, 函数类型: 6
2025-07-30 01:43:20,139 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:43:20,142 - INFO -   秒 50: 处理 10 个新请求
2025-07-30 01:43:20,148 - INFO - 时间槽 4，秒 50: 执行中容器 55，等待请求 0
2025-07-30 01:43:20,148 - INFO -   秒 51: 处理 14 个新请求
2025-07-30 01:43:20,155 - INFO -   秒 52: 处理 9 个新请求
2025-07-30 01:43:20,161 - INFO -   秒 53: 处理 12 个新请求
2025-07-30 01:43:20,163 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 11935MB, 函数类型: 6
2025-07-30 01:43:20,163 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:43:20,167 - INFO -   秒 54: 处理 18 个新请求
2025-07-30 01:43:20,173 - INFO -   秒 55: 处理 12 个新请求
2025-07-30 01:43:20,177 - INFO -   秒 56: 处理 4 个新请求
2025-07-30 01:43:20,183 - INFO - 时间槽 4 结束时，还有 20 个容器正在执行，将继续在后台执行
2025-07-30 01:43:20,184 - INFO - --------处理时间槽 5 的请求，共 908 个--------
2025-07-30 01:43:20,184 - INFO - ----node memomry----
2025-07-30 01:43:20,184 - INFO - node 1 : 14445 MB
2025-07-30 01:43:20,184 - INFO - node 2 : 14720 MB
2025-07-30 01:43:20,184 - INFO - node 3 : 13125 MB
2025-07-30 01:43:20,184 - INFO - node 4 : 14555 MB
2025-07-30 01:43:20,184 - INFO - node 5 : 13080 MB
2025-07-30 01:43:20,184 - INFO - node 6 : 14735 MB
2025-07-30 01:43:20,184 - INFO - node 7 : 14645 MB
2025-07-30 01:43:20,184 - INFO - node 8 : 14205 MB
2025-07-30 01:43:20,184 - INFO - node 9 : 13885 MB
2025-07-30 01:43:20,184 - INFO - node 10 : 12755 MB
2025-07-30 01:43:20,184 - INFO - node 11 : 14435 MB
2025-07-30 01:43:20,184 - INFO - node 12 : 13720 MB
2025-07-30 01:43:20,184 - INFO - node 13 : 13000 MB
2025-07-30 01:43:20,184 - INFO - node 14 : 14475 MB
2025-07-30 01:43:20,184 - INFO - node 15 : 14410 MB
2025-07-30 01:43:20,184 - INFO - node 16 : 13640 MB
2025-07-30 01:43:20,185 - INFO - node 17 : 13815 MB
2025-07-30 01:43:20,185 - INFO - node 18 : 14770 MB
2025-07-30 01:43:20,185 - INFO - node 19 : 14350 MB
2025-07-30 01:43:20,185 - INFO - node 20 : 14580 MB
2025-07-30 01:43:20,185 - INFO - node 21 : 13445 MB
2025-07-30 01:43:20,185 - INFO - node 22 : 13685 MB
2025-07-30 01:43:20,185 - INFO - node 23 : 13675 MB
2025-07-30 01:43:20,185 - INFO - node 24 : 12115 MB
2025-07-30 01:43:20,185 - INFO - node 25 : 13385 MB
2025-07-30 01:43:20,185 - INFO - node 26 : 13540 MB
2025-07-30 01:43:20,185 - INFO - node 27 : 14565 MB
2025-07-30 01:43:20,185 - INFO - node 28 : 13930 MB
2025-07-30 01:43:20,185 - INFO - node 29 : 14190 MB
2025-07-30 01:43:20,185 - INFO - node 30 : 13470 MB
2025-07-30 01:43:20,185 - INFO - node 31 : 12385 MB
2025-07-30 01:43:20,185 - INFO - node 32 : 13165 MB
2025-07-30 01:43:20,185 - INFO - node 33 : 14710 MB
2025-07-30 01:43:20,185 - INFO - node 34 : 14375 MB
2025-07-30 01:43:20,185 - INFO - node 35 : 13185 MB
2025-07-30 01:43:20,185 - INFO - node 36 : 14525 MB
2025-07-30 01:43:20,185 - INFO - node 37 : 13885 MB
2025-07-30 01:43:20,185 - INFO - node 38 : 12990 MB
2025-07-30 01:43:20,186 - INFO - node 39 : 10810 MB
2025-07-30 01:43:20,186 - INFO - node 40 : 13185 MB
2025-07-30 01:43:20,186 - INFO - node 41 : 14540 MB
2025-07-30 01:43:20,186 - INFO - node 42 : 12555 MB
2025-07-30 01:43:20,186 - INFO - node 43 : 13030 MB
2025-07-30 01:43:20,186 - INFO - node 44 : 13010 MB
2025-07-30 01:43:20,186 - INFO - node 45 : 13395 MB
2025-07-30 01:43:20,186 - INFO - node 46 : 14790 MB
2025-07-30 01:43:20,186 - INFO - node 47 : 12740 MB
2025-07-30 01:43:20,186 - INFO - node 48 : 11540 MB
2025-07-30 01:43:20,186 - INFO - node 49 : 14250 MB
2025-07-30 01:43:20,186 - INFO - node 50 : 13835 MB
2025-07-30 01:43:20,186 - INFO - node 51 : 12620 MB
2025-07-30 01:43:20,186 - INFO - node 52 : 13395 MB
2025-07-30 01:43:20,186 - INFO - node 53 : 14595 MB
2025-07-30 01:43:20,186 - INFO - node 54 : 14530 MB
2025-07-30 01:43:20,186 - INFO - node 55 : 14025 MB
2025-07-30 01:43:20,186 - INFO - node 56 : 14670 MB
2025-07-30 01:43:20,186 - INFO - node 57 : 13950 MB
2025-07-30 01:43:20,186 - INFO - node 58 : 11675 MB
2025-07-30 01:43:20,186 - INFO - node 59 : 12670 MB
2025-07-30 01:43:20,186 - INFO - node 60 : 15000 MB
2025-07-30 01:43:20,186 - INFO - node 61 : 12080 MB
2025-07-30 01:43:20,186 - INFO - node 62 : 13545 MB
2025-07-30 01:43:20,186 - INFO - node 63 : 13470 MB
2025-07-30 01:43:20,186 - INFO - node 64 : 13805 MB
2025-07-30 01:43:20,186 - INFO - node 65 : 10955 MB
2025-07-30 01:43:20,186 - INFO - node 66 : 13125 MB
2025-07-30 01:43:20,186 - INFO - node 67 : 14310 MB
2025-07-30 01:43:20,186 - INFO - node 68 : 15000 MB
2025-07-30 01:43:20,186 - INFO - node 69 : 11630 MB
2025-07-30 01:43:20,186 - INFO - node 70 : 15000 MB
2025-07-30 01:43:20,186 - INFO - node 71 : 13280 MB
2025-07-30 01:43:20,186 - INFO - node 72 : 14045 MB
2025-07-30 01:43:20,186 - INFO - node 73 : 14450 MB
2025-07-30 01:43:20,186 - INFO - node 74 : 11865 MB
2025-07-30 01:43:20,186 - INFO - node 75 : 13510 MB
2025-07-30 01:43:20,186 - INFO - node 76 : 13495 MB
2025-07-30 01:43:20,186 - INFO - node 77 : 11865 MB
2025-07-30 01:43:20,186 - INFO - node 78 : 15000 MB
2025-07-30 01:43:20,186 - INFO - node 79 : 11110 MB
2025-07-30 01:43:20,186 - INFO - node 80 : 12625 MB
2025-07-30 01:43:20,186 - INFO - node 81 : 11185 MB
2025-07-30 01:43:20,186 - INFO - node 82 : 13945 MB
2025-07-30 01:43:20,186 - INFO - node 83 : 14615 MB
2025-07-30 01:43:20,186 - INFO - node 84 : 13295 MB
2025-07-30 01:43:20,186 - INFO - node 85 : 11415 MB
2025-07-30 01:43:20,186 - INFO - node 86 : 13185 MB
2025-07-30 01:43:20,186 - INFO - node 87 : 12485 MB
2025-07-30 01:43:20,186 - INFO - node 88 : 13035 MB
2025-07-30 01:43:20,186 - INFO - node 89 : 10090 MB
2025-07-30 01:43:20,186 - INFO - node 90 : 11135 MB
2025-07-30 01:43:20,186 - INFO - node 91 : 14240 MB
2025-07-30 01:43:20,186 - INFO - node 92 : 14195 MB
2025-07-30 01:43:20,186 - INFO - node 93 : 13735 MB
2025-07-30 01:43:20,186 - INFO - node 94 : 13315 MB
2025-07-30 01:43:20,187 - INFO - node 95 : 12860 MB
2025-07-30 01:43:20,187 - INFO - node 96 : 11865 MB
2025-07-30 01:43:20,187 - INFO - node 97 : 13455 MB
2025-07-30 01:43:20,187 - INFO - node 98 : 11710 MB
2025-07-30 01:43:20,187 - INFO - node 99 : 14400 MB
2025-07-30 01:43:20,187 - INFO - node 100 : 13990 MB
2025-07-30 01:43:20,187 - INFO - node 101 : 13550 MB
2025-07-30 01:43:20,187 - INFO - node 102 : 13885 MB
2025-07-30 01:43:20,187 - INFO - node 103 : 14215 MB
2025-07-30 01:43:20,188 - INFO - node 104 : 14425 MB
2025-07-30 01:43:20,188 - INFO - node 105 : 14270 MB
2025-07-30 01:43:20,188 - INFO - node 106 : 13335 MB
2025-07-30 01:43:20,188 - INFO - node 107 : 12430 MB
2025-07-30 01:43:20,188 - INFO - node 108 : 15000 MB
2025-07-30 01:43:20,188 - INFO - node 109 : 13010 MB
2025-07-30 01:43:20,189 - INFO - node 110 : 13265 MB
2025-07-30 01:43:20,189 - INFO - node 111 : 11165 MB
2025-07-30 01:43:20,189 - INFO - node 112 : 13295 MB
2025-07-30 01:43:20,189 - INFO - node 113 : 13045 MB
2025-07-30 01:43:20,189 - INFO - node 114 : 12050 MB
2025-07-30 01:43:20,189 - INFO - node 115 : 13040 MB
2025-07-30 01:43:20,189 - INFO - node 116 : 11890 MB
2025-07-30 01:43:20,189 - INFO - node 117 : 14290 MB
2025-07-30 01:43:20,189 - INFO - node 118 : 14290 MB
2025-07-30 01:43:20,189 - INFO - node 119 : 12525 MB
2025-07-30 01:43:20,189 - INFO - node 120 : 14220 MB
2025-07-30 01:43:20,189 - INFO - node 121 : 14350 MB
2025-07-30 01:43:20,189 - INFO - node 122 : 14245 MB
2025-07-30 01:43:20,189 - INFO - node 123 : 14770 MB
2025-07-30 01:43:20,189 - INFO - node 124 : 13260 MB
2025-07-30 01:43:20,189 - INFO - node 125 : 15000 MB
2025-07-30 01:43:20,189 - INFO -   秒 0: 处理 110 个新请求
2025-07-30 01:43:20,214 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 11865MB, 函数类型: 6
2025-07-30 01:43:20,214 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:43:20,219 - INFO - 时间槽 5，秒 0: 执行中容器 130，等待请求 0
2025-07-30 01:43:20,219 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:43:20,225 - INFO -   秒 2: 处理 5 个新请求
2025-07-30 01:43:20,228 - INFO -   秒 3: 处理 16 个新请求
2025-07-30 01:43:20,232 - INFO -   秒 4: 处理 17 个新请求
2025-07-30 01:43:20,237 - INFO -   秒 5: 处理 18 个新请求
2025-07-30 01:43:20,244 - INFO -   秒 6: 处理 11 个新请求
2025-07-30 01:43:20,249 - INFO -   秒 7: 处理 10 个新请求
2025-07-30 01:43:20,252 - INFO -   秒 8: 处理 12 个新请求
2025-07-30 01:43:20,259 - INFO -   秒 9: 处理 21 个新请求
2025-07-30 01:43:20,267 - INFO -   秒 10: 处理 16 个新请求
2025-07-30 01:43:20,271 - INFO - 时间槽 5，秒 10: 执行中容器 69，等待请求 0
2025-07-30 01:43:20,271 - INFO -   秒 11: 处理 18 个新请求
2025-07-30 01:43:20,277 - INFO -   秒 12: 处理 10 个新请求
2025-07-30 01:43:20,282 - INFO -   秒 13: 处理 12 个新请求
2025-07-30 01:43:20,287 - INFO -   秒 14: 处理 21 个新请求
2025-07-30 01:43:20,294 - INFO -   秒 15: 处理 12 个新请求
2025-07-30 01:43:20,301 - INFO -   秒 16: 处理 17 个新请求
2025-07-30 01:43:20,303 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 11795MB, 函数类型: 2
2025-07-30 01:43:20,303 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:20,306 - INFO -   秒 17: 处理 13 个新请求
2025-07-30 01:43:20,312 - INFO -   秒 18: 处理 6 个新请求
2025-07-30 01:43:20,316 - INFO -   秒 19: 处理 33 个新请求
2025-07-30 01:43:20,326 - INFO -   秒 20: 处理 9 个新请求
2025-07-30 01:43:20,329 - INFO - 时间槽 5，秒 20: 执行中容器 70，等待请求 0
2025-07-30 01:43:20,329 - INFO -   秒 21: 处理 13 个新请求
2025-07-30 01:43:20,335 - INFO -   秒 22: 处理 9 个新请求
2025-07-30 01:43:20,339 - INFO -   秒 23: 处理 22 个新请求
2025-07-30 01:43:20,346 - INFO -   秒 24: 处理 19 个新请求
2025-07-30 01:43:20,349 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 11730MB, 函数类型: 1
2025-07-30 01:43:20,349 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:20,353 - INFO -   秒 25: 处理 9 个新请求
2025-07-30 01:43:20,358 - INFO -   秒 26: 处理 9 个新请求
2025-07-30 01:43:20,363 - INFO -   秒 27: 处理 14 个新请求
2025-07-30 01:43:20,368 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 11680MB, 函数类型: 1
2025-07-30 01:43:20,368 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:20,370 - INFO -   秒 28: 处理 14 个新请求
2025-07-30 01:43:20,377 - INFO -   秒 29: 处理 26 个新请求
2025-07-30 01:43:20,386 - INFO -   秒 30: 处理 16 个新请求
2025-07-30 01:43:20,393 - INFO - 时间槽 5，秒 30: 执行中容器 75，等待请求 0
2025-07-30 01:43:20,393 - INFO -   秒 31: 处理 15 个新请求
2025-07-30 01:43:20,401 - INFO -   秒 32: 处理 12 个新请求
2025-07-30 01:43:20,404 - INFO -   秒 33: 处理 8 个新请求
2025-07-30 01:43:20,409 - INFO -   秒 34: 处理 19 个新请求
2025-07-30 01:43:20,417 - INFO -   秒 35: 处理 16 个新请求
2025-07-30 01:43:20,419 - INFO - DEBUG: 节点96内存检查 - 需要内存: 360.0MB, 可用内存: 11630MB, 函数类型: 5
2025-07-30 01:43:20,419 - INFO - DEBUG: 节点96创建User层容器，减少内存 360MB，类型: 5
2025-07-30 01:43:20,422 - INFO -   秒 36: 处理 14 个新请求
2025-07-30 01:43:20,427 - INFO -   秒 37: 处理 11 个新请求
2025-07-30 01:43:20,433 - INFO -   秒 38: 处理 13 个新请求
2025-07-30 01:43:20,436 - INFO -   秒 39: 处理 26 个新请求
2025-07-30 01:43:20,443 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 11270MB, 函数类型: 4
2025-07-30 01:43:20,443 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:20,446 - INFO -   秒 40: 处理 12 个新请求
2025-07-30 01:43:20,451 - INFO - 时间槽 5，秒 40: 执行中容器 65，等待请求 0
2025-07-30 01:43:20,451 - INFO -   秒 41: 处理 12 个新请求
2025-07-30 01:43:20,457 - INFO -   秒 42: 处理 13 个新请求
2025-07-30 01:43:20,463 - INFO -   秒 43: 处理 13 个新请求
2025-07-30 01:43:20,468 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 11230MB, 函数类型: 9
2025-07-30 01:43:20,468 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:20,471 - INFO -   秒 44: 处理 20 个新请求
2025-07-30 01:43:20,482 - INFO -   秒 45: 处理 9 个新请求
2025-07-30 01:43:20,486 - INFO -   秒 46: 处理 13 个新请求
2025-07-30 01:43:20,493 - INFO -   秒 47: 处理 20 个新请求
2025-07-30 01:43:20,499 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 11140MB, 函数类型: 9
2025-07-30 01:43:20,499 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:20,503 - INFO -   秒 48: 处理 13 个新请求
2025-07-30 01:43:20,509 - INFO -   秒 49: 处理 21 个新请求
2025-07-30 01:43:20,518 - INFO -   秒 50: 处理 11 个新请求
2025-07-30 01:43:20,523 - INFO - 时间槽 5，秒 50: 执行中容器 72，等待请求 0
2025-07-30 01:43:20,523 - INFO -   秒 51: 处理 9 个新请求
2025-07-30 01:43:20,529 - INFO -   秒 52: 处理 20 个新请求
2025-07-30 01:43:20,537 - INFO -   秒 53: 处理 14 个新请求
2025-07-30 01:43:20,543 - INFO -   秒 54: 处理 7 个新请求
2025-07-30 01:43:20,548 - INFO -   秒 55: 处理 12 个新请求
2025-07-30 01:43:20,550 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 11050MB, 函数类型: 9
2025-07-30 01:43:20,550 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:20,552 - INFO -   秒 56: 处理 13 个新请求
2025-07-30 01:43:20,565 - INFO - 时间槽 5 结束时，还有 24 个容器正在执行，将继续在后台执行
2025-07-30 01:43:20,567 - INFO - --------处理时间槽 6 的请求，共 904 个--------
2025-07-30 01:43:20,567 - INFO - ----node memomry----
2025-07-30 01:43:20,567 - INFO - node 1 : 14445 MB
2025-07-30 01:43:20,567 - INFO - node 2 : 14720 MB
2025-07-30 01:43:20,567 - INFO - node 3 : 12855 MB
2025-07-30 01:43:20,567 - INFO - node 4 : 14555 MB
2025-07-30 01:43:20,567 - INFO - node 5 : 12780 MB
2025-07-30 01:43:20,567 - INFO - node 6 : 14735 MB
2025-07-30 01:43:20,567 - INFO - node 7 : 14645 MB
2025-07-30 01:43:20,567 - INFO - node 8 : 14090 MB
2025-07-30 01:43:20,567 - INFO - node 9 : 13665 MB
2025-07-30 01:43:20,567 - INFO - node 10 : 12685 MB
2025-07-30 01:43:20,567 - INFO - node 11 : 14385 MB
2025-07-30 01:43:20,567 - INFO - node 12 : 13600 MB
2025-07-30 01:43:20,567 - INFO - node 13 : 12855 MB
2025-07-30 01:43:20,567 - INFO - node 14 : 14320 MB
2025-07-30 01:43:20,567 - INFO - node 15 : 14360 MB
2025-07-30 01:43:20,567 - INFO - node 16 : 13510 MB
2025-07-30 01:43:20,567 - INFO - node 17 : 13595 MB
2025-07-30 01:43:20,567 - INFO - node 18 : 14770 MB
2025-07-30 01:43:20,567 - INFO - node 19 : 14105 MB
2025-07-30 01:43:20,567 - INFO - node 20 : 14540 MB
2025-07-30 01:43:20,567 - INFO - node 21 : 13315 MB
2025-07-30 01:43:20,567 - INFO - node 22 : 13400 MB
2025-07-30 01:43:20,567 - INFO - node 23 : 13505 MB
2025-07-30 01:43:20,567 - INFO - node 24 : 11775 MB
2025-07-30 01:43:20,567 - INFO - node 25 : 13215 MB
2025-07-30 01:43:20,567 - INFO - node 26 : 13400 MB
2025-07-30 01:43:20,567 - INFO - node 27 : 14525 MB
2025-07-30 01:43:20,567 - INFO - node 28 : 13160 MB
2025-07-30 01:43:20,567 - INFO - node 29 : 14050 MB
2025-07-30 01:43:20,567 - INFO - node 30 : 12735 MB
2025-07-30 01:43:20,567 - INFO - node 31 : 11880 MB
2025-07-30 01:43:20,567 - INFO - node 32 : 13015 MB
2025-07-30 01:43:20,567 - INFO - node 33 : 14415 MB
2025-07-30 01:43:20,567 - INFO - node 34 : 14285 MB
2025-07-30 01:43:20,567 - INFO - node 35 : 12865 MB
2025-07-30 01:43:20,567 - INFO - node 36 : 14425 MB
2025-07-30 01:43:20,567 - INFO - node 37 : 13485 MB
2025-07-30 01:43:20,567 - INFO - node 38 : 12540 MB
2025-07-30 01:43:20,567 - INFO - node 39 : 10325 MB
2025-07-30 01:43:20,567 - INFO - node 40 : 13115 MB
2025-07-30 01:43:20,567 - INFO - node 41 : 14490 MB
2025-07-30 01:43:20,567 - INFO - node 42 : 11635 MB
2025-07-30 01:43:20,567 - INFO - node 43 : 12980 MB
2025-07-30 01:43:20,567 - INFO - node 44 : 12505 MB
2025-07-30 01:43:20,567 - INFO - node 45 : 13190 MB
2025-07-30 01:43:20,567 - INFO - node 46 : 14790 MB
2025-07-30 01:43:20,567 - INFO - node 47 : 12130 MB
2025-07-30 01:43:20,567 - INFO - node 48 : 11120 MB
2025-07-30 01:43:20,567 - INFO - node 49 : 13955 MB
2025-07-30 01:43:20,567 - INFO - node 50 : 13385 MB
2025-07-30 01:43:20,567 - INFO - node 51 : 11690 MB
2025-07-30 01:43:20,567 - INFO - node 52 : 13085 MB
2025-07-30 01:43:20,567 - INFO - node 53 : 14475 MB
2025-07-30 01:43:20,568 - INFO - node 54 : 14235 MB
2025-07-30 01:43:20,568 - INFO - node 55 : 13485 MB
2025-07-30 01:43:20,568 - INFO - node 56 : 14670 MB
2025-07-30 01:43:20,568 - INFO - node 57 : 13435 MB
2025-07-30 01:43:20,568 - INFO - node 58 : 10770 MB
2025-07-30 01:43:20,568 - INFO - node 59 : 12085 MB
2025-07-30 01:43:20,568 - INFO - node 60 : 15000 MB
2025-07-30 01:43:20,568 - INFO - node 61 : 11600 MB
2025-07-30 01:43:20,568 - INFO - node 62 : 13350 MB
2025-07-30 01:43:20,568 - INFO - node 63 : 12540 MB
2025-07-30 01:43:20,568 - INFO - node 64 : 13440 MB
2025-07-30 01:43:20,568 - INFO - node 65 : 10300 MB
2025-07-30 01:43:20,568 - INFO - node 66 : 12905 MB
2025-07-30 01:43:20,568 - INFO - node 67 : 14260 MB
2025-07-30 01:43:20,568 - INFO - node 68 : 15000 MB
2025-07-30 01:43:20,568 - INFO - node 69 : 11230 MB
2025-07-30 01:43:20,568 - INFO - node 70 : 15000 MB
2025-07-30 01:43:20,568 - INFO - node 71 : 13190 MB
2025-07-30 01:43:20,568 - INFO - node 72 : 13505 MB
2025-07-30 01:43:20,568 - INFO - node 73 : 14245 MB
2025-07-30 01:43:20,568 - INFO - node 74 : 11100 MB
2025-07-30 01:43:20,568 - INFO - node 75 : 13235 MB
2025-07-30 01:43:20,568 - INFO - node 76 : 13345 MB
2025-07-30 01:43:20,568 - INFO - node 77 : 11530 MB
2025-07-30 01:43:20,568 - INFO - node 78 : 15000 MB
2025-07-30 01:43:20,568 - INFO - node 79 : 10755 MB
2025-07-30 01:43:20,568 - INFO - node 80 : 12285 MB
2025-07-30 01:43:20,568 - INFO - node 81 : 10445 MB
2025-07-30 01:43:20,568 - INFO - node 82 : 13650 MB
2025-07-30 01:43:20,568 - INFO - node 83 : 14615 MB
2025-07-30 01:43:20,568 - INFO - node 84 : 13125 MB
2025-07-30 01:43:20,568 - INFO - node 85 : 10650 MB
2025-07-30 01:43:20,568 - INFO - node 86 : 12925 MB
2025-07-30 01:43:20,568 - INFO - node 87 : 11825 MB
2025-07-30 01:43:20,568 - INFO - node 88 : 12385 MB
2025-07-30 01:43:20,568 - INFO - node 89 : 8960 MB
2025-07-30 01:43:20,568 - INFO - node 90 : 10800 MB
2025-07-30 01:43:20,568 - INFO - node 91 : 14200 MB
2025-07-30 01:43:20,568 - INFO - node 92 : 14080 MB
2025-07-30 01:43:20,568 - INFO - node 93 : 13625 MB
2025-07-30 01:43:20,568 - INFO - node 94 : 12770 MB
2025-07-30 01:43:20,568 - INFO - node 95 : 12740 MB
2025-07-30 01:43:20,568 - INFO - node 96 : 10960 MB
2025-07-30 01:43:20,568 - INFO - node 97 : 13345 MB
2025-07-30 01:43:20,568 - INFO - node 98 : 10895 MB
2025-07-30 01:43:20,568 - INFO - node 99 : 14360 MB
2025-07-30 01:43:20,568 - INFO - node 100 : 13480 MB
2025-07-30 01:43:20,568 - INFO - node 101 : 12985 MB
2025-07-30 01:43:20,568 - INFO - node 102 : 13545 MB
2025-07-30 01:43:20,568 - INFO - node 103 : 14155 MB
2025-07-30 01:43:20,568 - INFO - node 104 : 14365 MB
2025-07-30 01:43:20,569 - INFO - node 105 : 14230 MB
2025-07-30 01:43:20,569 - INFO - node 106 : 13100 MB
2025-07-30 01:43:20,569 - INFO - node 107 : 11890 MB
2025-07-30 01:43:20,569 - INFO - node 108 : 15000 MB
2025-07-30 01:43:20,569 - INFO - node 109 : 12430 MB
2025-07-30 01:43:20,569 - INFO - node 110 : 13035 MB
2025-07-30 01:43:20,569 - INFO - node 111 : 10605 MB
2025-07-30 01:43:20,569 - INFO - node 112 : 12720 MB
2025-07-30 01:43:20,569 - INFO - node 113 : 12745 MB
2025-07-30 01:43:20,569 - INFO - node 114 : 11105 MB
2025-07-30 01:43:20,569 - INFO - node 115 : 12500 MB
2025-07-30 01:43:20,569 - INFO - node 116 : 10990 MB
2025-07-30 01:43:20,569 - INFO - node 117 : 14240 MB
2025-07-30 01:43:20,569 - INFO - node 118 : 14170 MB
2025-07-30 01:43:20,569 - INFO - node 119 : 12300 MB
2025-07-30 01:43:20,569 - INFO - node 120 : 14170 MB
2025-07-30 01:43:20,569 - INFO - node 121 : 14310 MB
2025-07-30 01:43:20,569 - INFO - node 122 : 13885 MB
2025-07-30 01:43:20,569 - INFO - node 123 : 14770 MB
2025-07-30 01:43:20,569 - INFO - node 124 : 13020 MB
2025-07-30 01:43:20,569 - INFO - node 125 : 15000 MB
2025-07-30 01:43:20,570 - INFO -   秒 0: 处理 108 个新请求
2025-07-30 01:43:20,591 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 10960MB, 函数类型: 1
2025-07-30 01:43:20,593 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:20,599 - INFO - 时间槽 6，秒 0: 执行中容器 132，等待请求 0
2025-07-30 01:43:20,599 - INFO -   秒 1: 处理 6 个新请求
2025-07-30 01:43:20,605 - INFO -   秒 2: 处理 3 个新请求
2025-07-30 01:43:20,608 - INFO -   秒 3: 处理 19 个新请求
2025-07-30 01:43:20,614 - INFO -   秒 4: 处理 15 个新请求
2025-07-30 01:43:20,620 - INFO -   秒 5: 处理 16 个新请求
2025-07-30 01:43:20,626 - INFO -   秒 6: 处理 13 个新请求
2025-07-30 01:43:20,633 - INFO -   秒 7: 处理 13 个新请求
2025-07-30 01:43:20,636 - INFO -   秒 8: 处理 9 个新请求
2025-07-30 01:43:20,640 - INFO -   秒 9: 处理 20 个新请求
2025-07-30 01:43:20,647 - INFO -   秒 10: 处理 17 个新请求
2025-07-30 01:43:20,656 - INFO - 时间槽 6，秒 10: 执行中容器 67，等待请求 0
2025-07-30 01:43:20,656 - INFO -   秒 11: 处理 17 个新请求
2025-07-30 01:43:20,661 - INFO -   秒 12: 处理 10 个新请求
2025-07-30 01:43:20,663 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 10910MB, 函数类型: 9
2025-07-30 01:43:20,663 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:20,667 - INFO -   秒 13: 处理 13 个新请求
2025-07-30 01:43:20,673 - INFO -   秒 14: 处理 20 个新请求
2025-07-30 01:43:20,682 - INFO -   秒 15: 处理 14 个新请求
2025-07-30 01:43:20,689 - INFO -   秒 16: 处理 14 个新请求
2025-07-30 01:43:20,693 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 10820MB, 函数类型: 2
2025-07-30 01:43:20,693 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:20,696 - INFO -   秒 17: 处理 15 个新请求
2025-07-30 01:43:20,703 - INFO -   秒 18: 处理 7 个新请求
2025-07-30 01:43:20,708 - INFO -   秒 19: 处理 27 个新请求
2025-07-30 01:43:20,719 - INFO -   秒 20: 处理 18 个新请求
2025-07-30 01:43:20,724 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 10755MB, 函数类型: 7
2025-07-30 01:43:20,725 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:43:20,727 - INFO - 时间槽 6，秒 20: 执行中容器 82，等待请求 0
2025-07-30 01:43:20,727 - INFO -   秒 21: 处理 10 个新请求
2025-07-30 01:43:20,734 - INFO -   秒 22: 处理 12 个新请求
2025-07-30 01:43:20,740 - INFO -   秒 23: 处理 16 个新请求
2025-07-30 01:43:20,748 - INFO -   秒 24: 处理 16 个新请求
2025-07-30 01:43:20,750 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 10635MB, 函数类型: 7
2025-07-30 01:43:20,750 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:43:20,754 - INFO -   秒 25: 处理 15 个新请求
2025-07-30 01:43:20,761 - INFO -   秒 26: 处理 9 个新请求
2025-07-30 01:43:20,767 - INFO -   秒 27: 处理 12 个新请求
2025-07-30 01:43:20,772 - INFO -   秒 28: 处理 13 个新请求
2025-07-30 01:43:20,776 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 10515MB, 函数类型: 1
2025-07-30 01:43:20,776 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:20,779 - INFO -   秒 29: 处理 31 个新请求
2025-07-30 01:43:20,790 - INFO -   秒 30: 处理 12 个新请求
2025-07-30 01:43:20,795 - INFO - 时间槽 6，秒 30: 执行中容器 72，等待请求 0
2025-07-30 01:43:20,795 - INFO -   秒 31: 处理 16 个新请求
2025-07-30 01:43:20,799 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 10465MB, 函数类型: 6
2025-07-30 01:43:20,799 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:43:20,802 - INFO -   秒 32: 处理 10 个新请求
2025-07-30 01:43:20,808 - INFO -   秒 33: 处理 8 个新请求
2025-07-30 01:43:20,812 - INFO -   秒 34: 处理 23 个新请求
2025-07-30 01:43:20,820 - INFO -   秒 35: 处理 15 个新请求
2025-07-30 01:43:20,827 - INFO -   秒 36: 处理 8 个新请求
2025-07-30 01:43:20,832 - INFO -   秒 37: 处理 14 个新请求
2025-07-30 01:43:20,835 - INFO -   秒 38: 处理 17 个新请求
2025-07-30 01:43:20,844 - INFO -   秒 39: 处理 22 个新请求
2025-07-30 01:43:20,850 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 10395MB, 函数类型: 6
2025-07-30 01:43:20,850 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:43:20,852 - INFO -   秒 40: 处理 13 个新请求
2025-07-30 01:43:20,858 - INFO - 时间槽 6，秒 40: 执行中容器 70，等待请求 0
2025-07-30 01:43:20,858 - INFO -   秒 41: 处理 10 个新请求
2025-07-30 01:43:20,861 - INFO -   秒 42: 处理 16 个新请求
2025-07-30 01:43:20,869 - INFO -   秒 43: 处理 15 个新请求
2025-07-30 01:43:20,875 - INFO -   秒 44: 处理 16 个新请求
2025-07-30 01:43:20,881 - INFO -   秒 45: 处理 13 个新请求
2025-07-30 01:43:20,886 - INFO -   秒 46: 处理 13 个新请求
2025-07-30 01:43:20,892 - INFO -   秒 47: 处理 18 个新请求
2025-07-30 01:43:20,896 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 10325MB, 函数类型: 2
2025-07-30 01:43:20,896 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:20,900 - INFO -   秒 48: 处理 14 个新请求
2025-07-30 01:43:20,906 - INFO -   秒 49: 处理 17 个新请求
2025-07-30 01:43:20,913 - INFO -   秒 50: 处理 12 个新请求
2025-07-30 01:43:20,920 - INFO - 时间槽 6，秒 50: 执行中容器 63，等待请求 0
2025-07-30 01:43:20,920 - INFO -   秒 51: 处理 12 个新请求
2025-07-30 01:43:20,927 - INFO -   秒 52: 处理 18 个新请求
2025-07-30 01:43:20,934 - INFO -   秒 53: 处理 13 个新请求
2025-07-30 01:43:20,939 - INFO -   秒 54: 处理 9 个新请求
2025-07-30 01:43:20,945 - INFO -   秒 55: 处理 14 个新请求
2025-07-30 01:43:20,949 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 10260MB, 函数类型: 1
2025-07-30 01:43:20,949 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:20,952 - INFO -   秒 56: 处理 7 个新请求
2025-07-30 01:43:20,957 - INFO -   秒 57: 处理 1 个新请求
2025-07-30 01:43:20,961 - INFO - 时间槽 6 结束时，还有 30 个容器正在执行，将继续在后台执行
2025-07-30 01:43:20,965 - INFO - --------处理时间槽 7 的请求，共 926 个--------
2025-07-30 01:43:20,965 - INFO - ----node memomry----
2025-07-30 01:43:20,965 - INFO - node 1 : 14445 MB
2025-07-30 01:43:20,965 - INFO - node 2 : 14720 MB
2025-07-30 01:43:20,965 - INFO - node 3 : 12495 MB
2025-07-30 01:43:20,965 - INFO - node 4 : 14505 MB
2025-07-30 01:43:20,965 - INFO - node 5 : 12690 MB
2025-07-30 01:43:20,965 - INFO - node 6 : 14695 MB
2025-07-30 01:43:20,965 - INFO - node 7 : 14645 MB
2025-07-30 01:43:20,965 - INFO - node 8 : 14090 MB
2025-07-30 01:43:20,965 - INFO - node 9 : 13575 MB
2025-07-30 01:43:20,965 - INFO - node 10 : 11915 MB
2025-07-30 01:43:20,965 - INFO - node 11 : 14315 MB
2025-07-30 01:43:20,965 - INFO - node 12 : 13330 MB
2025-07-30 01:43:20,965 - INFO - node 13 : 12660 MB
2025-07-30 01:43:20,965 - INFO - node 14 : 14255 MB
2025-07-30 01:43:20,965 - INFO - node 15 : 14155 MB
2025-07-30 01:43:20,965 - INFO - node 16 : 13510 MB
2025-07-30 01:43:20,965 - INFO - node 17 : 13595 MB
2025-07-30 01:43:20,965 - INFO - node 18 : 14770 MB
2025-07-30 01:43:20,965 - INFO - node 19 : 13935 MB
2025-07-30 01:43:20,965 - INFO - node 20 : 14490 MB
2025-07-30 01:43:20,965 - INFO - node 21 : 13200 MB
2025-07-30 01:43:20,966 - INFO - node 22 : 13245 MB
2025-07-30 01:43:20,966 - INFO - node 23 : 13250 MB
2025-07-30 01:43:20,966 - INFO - node 24 : 11335 MB
2025-07-30 01:43:20,966 - INFO - node 25 : 12670 MB
2025-07-30 01:43:20,966 - INFO - node 26 : 13195 MB
2025-07-30 01:43:20,966 - INFO - node 27 : 14485 MB
2025-07-30 01:43:20,966 - INFO - node 28 : 12670 MB
2025-07-30 01:43:20,966 - INFO - node 29 : 14010 MB
2025-07-30 01:43:20,966 - INFO - node 30 : 12475 MB
2025-07-30 01:43:20,966 - INFO - node 31 : 11485 MB
2025-07-30 01:43:20,966 - INFO - node 32 : 12800 MB
2025-07-30 01:43:20,966 - INFO - node 33 : 14210 MB
2025-07-30 01:43:20,966 - INFO - node 34 : 14235 MB
2025-07-30 01:43:20,966 - INFO - node 35 : 12590 MB
2025-07-30 01:43:20,966 - INFO - node 36 : 14375 MB
2025-07-30 01:43:20,966 - INFO - node 37 : 13340 MB
2025-07-30 01:43:20,966 - INFO - node 38 : 11810 MB
2025-07-30 01:43:20,966 - INFO - node 39 : 9950 MB
2025-07-30 01:43:20,966 - INFO - node 40 : 12930 MB
2025-07-30 01:43:20,966 - INFO - node 41 : 14195 MB
2025-07-30 01:43:20,966 - INFO - node 42 : 10925 MB
2025-07-30 01:43:20,966 - INFO - node 43 : 12640 MB
2025-07-30 01:43:20,966 - INFO - node 44 : 12125 MB
2025-07-30 01:43:20,966 - INFO - node 45 : 12905 MB
2025-07-30 01:43:20,966 - INFO - node 46 : 14790 MB
2025-07-30 01:43:20,966 - INFO - node 47 : 11935 MB
2025-07-30 01:43:20,966 - INFO - node 48 : 10460 MB
2025-07-30 01:43:20,966 - INFO - node 49 : 13595 MB
2025-07-30 01:43:20,966 - INFO - node 50 : 13240 MB
2025-07-30 01:43:20,966 - INFO - node 51 : 11295 MB
2025-07-30 01:43:20,966 - INFO - node 52 : 12895 MB
2025-07-30 01:43:20,966 - INFO - node 53 : 14180 MB
2025-07-30 01:43:20,966 - INFO - node 54 : 14165 MB
2025-07-30 01:43:20,966 - INFO - node 55 : 13365 MB
2025-07-30 01:43:20,966 - INFO - node 56 : 14630 MB
2025-07-30 01:43:20,966 - INFO - node 57 : 13115 MB
2025-07-30 01:43:20,966 - INFO - node 58 : 10205 MB
2025-07-30 01:43:20,966 - INFO - node 59 : 11335 MB
2025-07-30 01:43:20,966 - INFO - node 60 : 15000 MB
2025-07-30 01:43:20,966 - INFO - node 61 : 10740 MB
2025-07-30 01:43:20,966 - INFO - node 62 : 13195 MB
2025-07-30 01:43:20,967 - INFO - node 63 : 12305 MB
2025-07-30 01:43:20,967 - INFO - node 64 : 13350 MB
2025-07-30 01:43:20,967 - INFO - node 65 : 9180 MB
2025-07-30 01:43:20,967 - INFO - node 66 : 12365 MB
2025-07-30 01:43:20,967 - INFO - node 67 : 14220 MB
2025-07-30 01:43:20,967 - INFO - node 68 : 15000 MB
2025-07-30 01:43:20,967 - INFO - node 69 : 10040 MB
2025-07-30 01:43:20,967 - INFO - node 70 : 15000 MB
2025-07-30 01:43:20,967 - INFO - node 71 : 13100 MB
2025-07-30 01:43:20,967 - INFO - node 72 : 13400 MB
2025-07-30 01:43:20,967 - INFO - node 73 : 14195 MB
2025-07-30 01:43:20,967 - INFO - node 74 : 10425 MB
2025-07-30 01:43:20,967 - INFO - node 75 : 12695 MB
2025-07-30 01:43:20,967 - INFO - node 76 : 13165 MB
2025-07-30 01:43:20,967 - INFO - node 77 : 10175 MB
2025-07-30 01:43:20,967 - INFO - node 78 : 15000 MB
2025-07-30 01:43:20,967 - INFO - node 79 : 10440 MB
2025-07-30 01:43:20,967 - INFO - node 80 : 11875 MB
2025-07-30 01:43:20,967 - INFO - node 81 : 10195 MB
2025-07-30 01:43:20,967 - INFO - node 82 : 13580 MB
2025-07-30 01:43:20,967 - INFO - node 83 : 14615 MB
2025-07-30 01:43:20,967 - INFO - node 84 : 12780 MB
2025-07-30 01:43:20,967 - INFO - node 85 : 10160 MB
2025-07-30 01:43:20,967 - INFO - node 86 : 12695 MB
2025-07-30 01:43:20,967 - INFO - node 87 : 11125 MB
2025-07-30 01:43:20,967 - INFO - node 88 : 11865 MB
2025-07-30 01:43:20,967 - INFO - node 89 : 7965 MB
2025-07-30 01:43:20,967 - INFO - node 90 : 10350 MB
2025-07-30 01:43:20,967 - INFO - node 91 : 14140 MB
2025-07-30 01:43:20,967 - INFO - node 92 : 14015 MB
2025-07-30 01:43:20,967 - INFO - node 93 : 13545 MB
2025-07-30 01:43:20,967 - INFO - node 94 : 12440 MB
2025-07-30 01:43:20,967 - INFO - node 95 : 12460 MB
2025-07-30 01:43:20,967 - INFO - node 96 : 10210 MB
2025-07-30 01:43:20,967 - INFO - node 97 : 13305 MB
2025-07-30 01:43:20,967 - INFO - node 98 : 10025 MB
2025-07-30 01:43:20,967 - INFO - node 99 : 14300 MB
2025-07-30 01:43:20,967 - INFO - node 100 : 13300 MB
2025-07-30 01:43:20,968 - INFO - node 101 : 12530 MB
2025-07-30 01:43:20,968 - INFO - node 102 : 13175 MB
2025-07-30 01:43:20,968 - INFO - node 103 : 13975 MB
2025-07-30 01:43:20,968 - INFO - node 104 : 14275 MB
2025-07-30 01:43:20,968 - INFO - node 105 : 14180 MB
2025-07-30 01:43:20,968 - INFO - node 106 : 12605 MB
2025-07-30 01:43:20,968 - INFO - node 107 : 10800 MB
2025-07-30 01:43:20,968 - INFO - node 108 : 15000 MB
2025-07-30 01:43:20,968 - INFO - node 109 : 11795 MB
2025-07-30 01:43:20,968 - INFO - node 110 : 12485 MB
2025-07-30 01:43:20,968 - INFO - node 111 : 10160 MB
2025-07-30 01:43:20,968 - INFO - node 112 : 11985 MB
2025-07-30 01:43:20,968 - INFO - node 113 : 12485 MB
2025-07-30 01:43:20,968 - INFO - node 114 : 10255 MB
2025-07-30 01:43:20,968 - INFO - node 115 : 12195 MB
2025-07-30 01:43:20,968 - INFO - node 116 : 9790 MB
2025-07-30 01:43:20,968 - INFO - node 117 : 14190 MB
2025-07-30 01:43:20,968 - INFO - node 118 : 14130 MB
2025-07-30 01:43:20,968 - INFO - node 119 : 12100 MB
2025-07-30 01:43:20,968 - INFO - node 120 : 14080 MB
2025-07-30 01:43:20,968 - INFO - node 121 : 14105 MB
2025-07-30 01:43:20,968 - INFO - node 122 : 13835 MB
2025-07-30 01:43:20,968 - INFO - node 123 : 14325 MB
2025-07-30 01:43:20,968 - INFO - node 124 : 12890 MB
2025-07-30 01:43:20,968 - INFO - node 125 : 15000 MB
2025-07-30 01:43:20,968 - INFO -   秒 0: 处理 110 个新请求
2025-07-30 01:43:20,990 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 10210MB, 函数类型: 9
2025-07-30 01:43:20,991 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:20,998 - INFO - 时间槽 7，秒 0: 执行中容器 140，等待请求 0
2025-07-30 01:43:20,999 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:43:21,003 - INFO -   秒 2: 处理 6 个新请求
2025-07-30 01:43:21,007 - INFO -   秒 3: 处理 18 个新请求
2025-07-30 01:43:21,014 - INFO -   秒 4: 处理 15 个新请求
2025-07-30 01:43:21,020 - INFO -   秒 5: 处理 13 个新请求
2025-07-30 01:43:21,028 - INFO -   秒 6: 处理 18 个新请求
2025-07-30 01:43:21,035 - INFO -   秒 7: 处理 12 个新请求
2025-07-30 01:43:21,038 - INFO -   秒 8: 处理 10 个新请求
2025-07-30 01:43:21,043 - INFO -   秒 9: 处理 19 个新请求
2025-07-30 01:43:21,049 - INFO -   秒 10: 处理 17 个新请求
2025-07-30 01:43:21,056 - INFO - 时间槽 7，秒 10: 执行中容器 75，等待请求 0
2025-07-30 01:43:21,056 - INFO -   秒 11: 处理 15 个新请求
2025-07-30 01:43:21,064 - INFO -   秒 12: 处理 16 个新请求
2025-07-30 01:43:21,070 - INFO -   秒 13: 处理 14 个新请求
2025-07-30 01:43:21,077 - INFO -   秒 14: 处理 16 个新请求
2025-07-30 01:43:21,084 - INFO -   秒 15: 处理 18 个新请求
2025-07-30 01:43:21,086 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 10120MB, 函数类型: 4
2025-07-30 01:43:21,087 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:21,090 - INFO -   秒 16: 处理 14 个新请求
2025-07-30 01:43:21,095 - INFO -   秒 17: 处理 10 个新请求
2025-07-30 01:43:21,101 - INFO -   秒 18: 处理 11 个新请求
2025-07-30 01:43:21,106 - INFO -   秒 19: 处理 33 个新请求
2025-07-30 01:43:21,114 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 10080MB, 函数类型: 1
2025-07-30 01:43:21,114 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:21,119 - INFO -   秒 20: 处理 12 个新请求
2025-07-30 01:43:21,123 - INFO - 时间槽 7，秒 20: 执行中容器 81，等待请求 0
2025-07-30 01:43:21,123 - INFO -   秒 21: 处理 11 个新请求
2025-07-30 01:43:21,129 - INFO -   秒 22: 处理 11 个新请求
2025-07-30 01:43:21,136 - INFO -   秒 23: 处理 19 个新请求
2025-07-30 01:43:21,140 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 10030MB, 函数类型: 9
2025-07-30 01:43:21,141 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:21,145 - INFO -   秒 24: 处理 19 个新请求
2025-07-30 01:43:21,151 - INFO -   秒 25: 处理 8 个新请求
2025-07-30 01:43:21,156 - INFO -   秒 26: 处理 12 个新请求
2025-07-30 01:43:21,160 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 9940MB, 函数类型: 4
2025-07-30 01:43:21,161 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:21,166 - INFO -   秒 27: 处理 20 个新请求
2025-07-30 01:43:21,175 - INFO -   秒 28: 处理 4 个新请求
2025-07-30 01:43:21,179 - INFO -   秒 29: 处理 30 个新请求
2025-07-30 01:43:21,191 - INFO -   秒 30: 处理 16 个新请求
2025-07-30 01:43:21,201 - INFO - 时间槽 7，秒 30: 执行中容器 81，等待请求 0
2025-07-30 01:43:21,201 - INFO -   秒 31: 处理 14 个新请求
2025-07-30 01:43:21,209 - INFO -   秒 32: 处理 14 个新请求
2025-07-30 01:43:21,217 - INFO -   秒 33: 处理 9 个新请求
2025-07-30 01:43:21,223 - INFO -   秒 34: 处理 18 个新请求
2025-07-30 01:43:21,227 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 9900MB, 函数类型: 9
2025-07-30 01:43:21,227 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:21,233 - INFO -   秒 35: 处理 17 个新请求
2025-07-30 01:43:21,239 - INFO -   秒 36: 处理 12 个新请求
2025-07-30 01:43:21,249 - INFO -   秒 37: 处理 12 个新请求
2025-07-30 01:43:21,257 - INFO -   秒 38: 处理 12 个新请求
2025-07-30 01:43:21,260 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 9810MB, 函数类型: 1
2025-07-30 01:43:21,261 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:21,265 - INFO -   秒 39: 处理 29 个新请求
2025-07-30 01:43:21,275 - INFO -   秒 40: 处理 12 个新请求
2025-07-30 01:43:21,280 - INFO - 时间槽 7，秒 40: 执行中容器 80，等待请求 0
2025-07-30 01:43:21,281 - INFO -   秒 41: 处理 15 个新请求
2025-07-30 01:43:21,287 - INFO -   秒 42: 处理 13 个新请求
2025-07-30 01:43:21,294 - INFO -   秒 43: 处理 13 个新请求
2025-07-30 01:43:21,299 - INFO -   秒 44: 处理 16 个新请求
2025-07-30 01:43:21,306 - INFO -   秒 45: 处理 11 个新请求
2025-07-30 01:43:21,309 - INFO -   秒 46: 处理 21 个新请求
2025-07-30 01:43:21,315 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 9760MB, 函数类型: 1
2025-07-30 01:43:21,315 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:21,316 - INFO -   秒 47: 处理 12 个新请求
2025-07-30 01:43:21,321 - INFO -   秒 48: 处理 14 个新请求
2025-07-30 01:43:21,327 - INFO -   秒 49: 处理 20 个新请求
2025-07-30 01:43:21,334 - INFO -   秒 50: 处理 16 个新请求
2025-07-30 01:43:21,339 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 9710MB, 函数类型: 2
2025-07-30 01:43:21,339 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:21,343 - INFO - 时间槽 7，秒 50: 执行中容器 81，等待请求 0
2025-07-30 01:43:21,343 - INFO -   秒 51: 处理 9 个新请求
2025-07-30 01:43:21,349 - INFO -   秒 52: 处理 13 个新请求
2025-07-30 01:43:21,355 - INFO -   秒 53: 处理 15 个新请求
2025-07-30 01:43:21,361 - INFO -   秒 54: 处理 18 个新请求
2025-07-30 01:43:21,367 - INFO -   秒 55: 处理 12 个新请求
2025-07-30 01:43:21,372 - INFO -   秒 56: 处理 7 个新请求
2025-07-30 01:43:21,378 - INFO -   秒 57: 处理 1 个新请求
2025-07-30 01:43:21,387 - INFO - 时间槽 7 结束时，还有 37 个容器正在执行，将继续在后台执行
2025-07-30 01:43:21,390 - INFO - --------处理时间槽 8 的请求，共 834 个--------
2025-07-30 01:43:21,390 - INFO - ----node memomry----
2025-07-30 01:43:21,390 - INFO - node 1 : 14445 MB
2025-07-30 01:43:21,390 - INFO - node 2 : 14720 MB
2025-07-30 01:43:21,390 - INFO - node 3 : 12030 MB
2025-07-30 01:43:21,390 - INFO - node 4 : 14385 MB
2025-07-30 01:43:21,390 - INFO - node 5 : 12560 MB
2025-07-30 01:43:21,390 - INFO - node 6 : 14655 MB
2025-07-30 01:43:21,390 - INFO - node 7 : 14575 MB
2025-07-30 01:43:21,390 - INFO - node 8 : 14040 MB
2025-07-30 01:43:21,390 - INFO - node 9 : 12995 MB
2025-07-30 01:43:21,390 - INFO - node 10 : 11490 MB
2025-07-30 01:43:21,390 - INFO - node 11 : 14250 MB
2025-07-30 01:43:21,390 - INFO - node 12 : 12980 MB
2025-07-30 01:43:21,390 - INFO - node 13 : 12195 MB
2025-07-30 01:43:21,391 - INFO - node 14 : 14205 MB
2025-07-30 01:43:21,391 - INFO - node 15 : 14065 MB
2025-07-30 01:43:21,391 - INFO - node 16 : 13260 MB
2025-07-30 01:43:21,391 - INFO - node 17 : 13320 MB
2025-07-30 01:43:21,391 - INFO - node 18 : 14705 MB
2025-07-30 01:43:21,391 - INFO - node 19 : 13775 MB
2025-07-30 01:43:21,391 - INFO - node 20 : 14450 MB
2025-07-30 01:43:21,391 - INFO - node 21 : 13110 MB
2025-07-30 01:43:21,391 - INFO - node 22 : 12995 MB
2025-07-30 01:43:21,391 - INFO - node 23 : 12940 MB
2025-07-30 01:43:21,391 - INFO - node 24 : 10955 MB
2025-07-30 01:43:21,391 - INFO - node 25 : 12480 MB
2025-07-30 01:43:21,391 - INFO - node 26 : 13005 MB
2025-07-30 01:43:21,391 - INFO - node 27 : 14425 MB
2025-07-30 01:43:21,391 - INFO - node 28 : 12140 MB
2025-07-30 01:43:21,391 - INFO - node 29 : 13800 MB
2025-07-30 01:43:21,391 - INFO - node 30 : 12255 MB
2025-07-30 01:43:21,391 - INFO - node 31 : 11085 MB
2025-07-30 01:43:21,391 - INFO - node 32 : 12490 MB
2025-07-30 01:43:21,391 - INFO - node 33 : 14150 MB
2025-07-30 01:43:21,391 - INFO - node 34 : 14195 MB
2025-07-30 01:43:21,391 - INFO - node 35 : 12220 MB
2025-07-30 01:43:21,391 - INFO - node 36 : 14325 MB
2025-07-30 01:43:21,391 - INFO - node 37 : 13085 MB
2025-07-30 01:43:21,391 - INFO - node 38 : 11560 MB
2025-07-30 01:43:21,391 - INFO - node 39 : 9345 MB
2025-07-30 01:43:21,391 - INFO - node 40 : 12740 MB
2025-07-30 01:43:21,391 - INFO - node 41 : 14130 MB
2025-07-30 01:43:21,391 - INFO - node 42 : 10165 MB
2025-07-30 01:43:21,391 - INFO - node 43 : 12325 MB
2025-07-30 01:43:21,391 - INFO - node 44 : 11985 MB
2025-07-30 01:43:21,391 - INFO - node 45 : 12680 MB
2025-07-30 01:43:21,391 - INFO - node 46 : 14585 MB
2025-07-30 01:43:21,391 - INFO - node 47 : 11725 MB
2025-07-30 01:43:21,391 - INFO - node 48 : 10015 MB
2025-07-30 01:43:21,391 - INFO - node 49 : 13475 MB
2025-07-30 01:43:21,391 - INFO - node 50 : 13080 MB
2025-07-30 01:43:21,393 - INFO - node 51 : 10665 MB
2025-07-30 01:43:21,393 - INFO - node 52 : 12750 MB
2025-07-30 01:43:21,393 - INFO - node 53 : 14130 MB
2025-07-30 01:43:21,393 - INFO - node 54 : 14115 MB
2025-07-30 01:43:21,393 - INFO - node 55 : 12915 MB
2025-07-30 01:43:21,393 - INFO - node 56 : 14590 MB
2025-07-30 01:43:21,393 - INFO - node 57 : 12715 MB
2025-07-30 01:43:21,393 - INFO - node 58 : 9440 MB
2025-07-30 01:43:21,393 - INFO - node 59 : 10775 MB
2025-07-30 01:43:21,393 - INFO - node 60 : 15000 MB
2025-07-30 01:43:21,393 - INFO - node 61 : 10230 MB
2025-07-30 01:43:21,393 - INFO - node 62 : 13005 MB
2025-07-30 01:43:21,393 - INFO - node 63 : 12065 MB
2025-07-30 01:43:21,393 - INFO - node 64 : 13190 MB
2025-07-30 01:43:21,393 - INFO - node 65 : 8800 MB
2025-07-30 01:43:21,393 - INFO - node 66 : 12215 MB
2025-07-30 01:43:21,393 - INFO - node 67 : 14130 MB
2025-07-30 01:43:21,393 - INFO - node 68 : 15000 MB
2025-07-30 01:43:21,393 - INFO - node 69 : 8735 MB
2025-07-30 01:43:21,393 - INFO - node 70 : 15000 MB
2025-07-30 01:43:21,394 - INFO - node 71 : 12910 MB
2025-07-30 01:43:21,394 - INFO - node 72 : 13270 MB
2025-07-30 01:43:21,394 - INFO - node 73 : 14105 MB
2025-07-30 01:43:21,394 - INFO - node 74 : 9910 MB
2025-07-30 01:43:21,394 - INFO - node 75 : 12385 MB
2025-07-30 01:43:21,394 - INFO - node 76 : 13045 MB
2025-07-30 01:43:21,394 - INFO - node 77 : 9675 MB
2025-07-30 01:43:21,394 - INFO - node 78 : 15000 MB
2025-07-30 01:43:21,394 - INFO - node 79 : 9900 MB
2025-07-30 01:43:21,394 - INFO - node 80 : 11530 MB
2025-07-30 01:43:21,394 - INFO - node 81 : 9865 MB
2025-07-30 01:43:21,394 - INFO - node 82 : 13530 MB
2025-07-30 01:43:21,394 - INFO - node 83 : 14550 MB
2025-07-30 01:43:21,395 - INFO - node 84 : 12310 MB
2025-07-30 01:43:21,395 - INFO - node 85 : 9735 MB
2025-07-30 01:43:21,395 - INFO - node 86 : 11730 MB
2025-07-30 01:43:21,395 - INFO - node 87 : 10240 MB
2025-07-30 01:43:21,395 - INFO - node 88 : 11160 MB
2025-07-30 01:43:21,395 - INFO - node 89 : 7400 MB
2025-07-30 01:43:21,395 - INFO - node 90 : 9260 MB
2025-07-30 01:43:21,395 - INFO - node 91 : 14100 MB
2025-07-30 01:43:21,395 - INFO - node 92 : 13655 MB
2025-07-30 01:43:21,395 - INFO - node 93 : 13455 MB
2025-07-30 01:43:21,395 - INFO - node 94 : 11730 MB
2025-07-30 01:43:21,395 - INFO - node 95 : 12070 MB
2025-07-30 01:43:21,395 - INFO - node 96 : 9645 MB
2025-07-30 01:43:21,395 - INFO - node 97 : 13095 MB
2025-07-30 01:43:21,395 - INFO - node 98 : 9535 MB
2025-07-30 01:43:21,395 - INFO - node 99 : 14260 MB
2025-07-30 01:43:21,395 - INFO - node 100 : 13075 MB
2025-07-30 01:43:21,395 - INFO - node 101 : 11770 MB
2025-07-30 01:43:21,395 - INFO - node 102 : 12895 MB
2025-07-30 01:43:21,395 - INFO - node 103 : 13835 MB
2025-07-30 01:43:21,395 - INFO - node 104 : 14235 MB
2025-07-30 01:43:21,395 - INFO - node 105 : 14090 MB
2025-07-30 01:43:21,395 - INFO - node 106 : 12430 MB
2025-07-30 01:43:21,395 - INFO - node 107 : 10455 MB
2025-07-30 01:43:21,395 - INFO - node 108 : 15000 MB
2025-07-30 01:43:21,395 - INFO - node 109 : 11590 MB
2025-07-30 01:43:21,395 - INFO - node 110 : 12290 MB
2025-07-30 01:43:21,395 - INFO - node 111 : 9130 MB
2025-07-30 01:43:21,395 - INFO - node 112 : 10975 MB
2025-07-30 01:43:21,395 - INFO - node 113 : 12270 MB
2025-07-30 01:43:21,395 - INFO - node 114 : 8705 MB
2025-07-30 01:43:21,395 - INFO - node 115 : 11820 MB
2025-07-30 01:43:21,395 - INFO - node 116 : 9200 MB
2025-07-30 01:43:21,395 - INFO - node 117 : 13830 MB
2025-07-30 01:43:21,395 - INFO - node 118 : 13770 MB
2025-07-30 01:43:21,395 - INFO - node 119 : 11660 MB
2025-07-30 01:43:21,395 - INFO - node 120 : 13960 MB
2025-07-30 01:43:21,395 - INFO - node 121 : 14055 MB
2025-07-30 01:43:21,396 - INFO - node 122 : 13785 MB
2025-07-30 01:43:21,396 - INFO - node 123 : 14325 MB
2025-07-30 01:43:21,396 - INFO - node 124 : 12680 MB
2025-07-30 01:43:21,396 - INFO - node 125 : 14785 MB
2025-07-30 01:43:21,396 - INFO -   秒 0: 处理 106 个新请求
2025-07-30 01:43:21,425 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 9645MB, 函数类型: 9
2025-07-30 01:43:21,425 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:21,433 - INFO - 时间槽 8，秒 0: 执行中容器 143，等待请求 0
2025-07-30 01:43:21,433 - INFO -   秒 1: 处理 7 个新请求
2025-07-30 01:43:21,442 - INFO -   秒 2: 处理 1 个新请求
2025-07-30 01:43:21,445 - INFO -   秒 3: 处理 16 个新请求
2025-07-30 01:43:21,452 - INFO -   秒 4: 处理 13 个新请求
2025-07-30 01:43:21,457 - INFO -   秒 5: 处理 13 个新请求
2025-07-30 01:43:21,463 - INFO -   秒 6: 处理 17 个新请求
2025-07-30 01:43:21,471 - INFO -   秒 7: 处理 15 个新请求
2025-07-30 01:43:21,480 - INFO -   秒 8: 处理 9 个新请求
2025-07-30 01:43:21,483 - INFO -   秒 9: 处理 7 个新请求
2025-07-30 01:43:21,487 - INFO -   秒 10: 处理 14 个新请求
2025-07-30 01:43:21,494 - INFO - 时间槽 8，秒 10: 执行中容器 66，等待请求 0
2025-07-30 01:43:21,494 - INFO -   秒 11: 处理 21 个新请求
2025-07-30 01:43:21,501 - INFO - DEBUG: 节点96内存检查 - 需要内存: 60.0MB, 可用内存: 9555MB, 函数类型: 3
2025-07-30 01:43:21,501 - INFO - DEBUG: 节点96创建User层容器，减少内存 60MB，类型: 3
2025-07-30 01:43:21,506 - INFO -   秒 12: 处理 16 个新请求
2025-07-30 01:43:21,513 - INFO -   秒 13: 处理 10 个新请求
2025-07-30 01:43:21,518 - INFO -   秒 14: 处理 15 个新请求
2025-07-30 01:43:21,523 - INFO -   秒 15: 处理 18 个新请求
2025-07-30 01:43:21,532 - INFO -   秒 16: 处理 11 个新请求
2025-07-30 01:43:21,536 - INFO -   秒 17: 处理 5 个新请求
2025-07-30 01:43:21,540 - INFO -   秒 18: 处理 21 个新请求
2025-07-30 01:43:21,549 - INFO -   秒 19: 处理 12 个新请求
2025-07-30 01:43:21,555 - INFO -   秒 20: 处理 14 个新请求
2025-07-30 01:43:21,556 - INFO - DEBUG: 节点96内存检查 - 需要内存: 60.0MB, 可用内存: 9495MB, 函数类型: 3
2025-07-30 01:43:21,556 - INFO - DEBUG: 节点96创建User层容器，减少内存 60MB，类型: 3
2025-07-30 01:43:21,560 - INFO - 时间槽 8，秒 20: 执行中容器 79，等待请求 0
2025-07-30 01:43:21,562 - INFO -   秒 21: 处理 18 个新请求
2025-07-30 01:43:21,567 - INFO -   秒 22: 处理 11 个新请求
2025-07-30 01:43:21,573 - INFO -   秒 23: 处理 14 个新请求
2025-07-30 01:43:21,581 - INFO -   秒 24: 处理 14 个新请求
2025-07-30 01:43:21,583 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 9435MB, 函数类型: 9
2025-07-30 01:43:21,585 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:21,586 - INFO -   秒 25: 处理 10 个新请求
2025-07-30 01:43:21,592 - INFO -   秒 26: 处理 15 个新请求
2025-07-30 01:43:21,601 - INFO -   秒 27: 处理 14 个新请求
2025-07-30 01:43:21,607 - INFO -   秒 28: 处理 12 个新请求
2025-07-30 01:43:21,612 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 9345MB, 函数类型: 9
2025-07-30 01:43:21,612 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:21,616 - INFO -   秒 29: 处理 12 个新请求
2025-07-30 01:43:21,620 - INFO -   秒 30: 处理 18 个新请求
2025-07-30 01:43:21,629 - INFO - 时间槽 8，秒 30: 执行中容器 79，等待请求 0
2025-07-30 01:43:21,629 - INFO -   秒 31: 处理 12 个新请求
2025-07-30 01:43:21,634 - INFO -   秒 32: 处理 13 个新请求
2025-07-30 01:43:21,640 - INFO -   秒 33: 处理 15 个新请求
2025-07-30 01:43:21,647 - INFO -   秒 34: 处理 11 个新请求
2025-07-30 01:43:21,652 - INFO -   秒 35: 处理 10 个新请求
2025-07-30 01:43:21,659 - INFO -   秒 36: 处理 17 个新请求
2025-07-30 01:43:21,665 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 9255MB, 函数类型: 1
2025-07-30 01:43:21,665 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:21,670 - INFO -   秒 37: 处理 20 个新请求
2025-07-30 01:43:21,678 - INFO -   秒 38: 处理 11 个新请求
2025-07-30 01:43:21,686 - INFO -   秒 39: 处理 14 个新请求
2025-07-30 01:43:21,696 - INFO -   秒 40: 处理 14 个新请求
2025-07-30 01:43:21,706 - INFO - 时间槽 8，秒 40: 执行中容器 76，等待请求 0
2025-07-30 01:43:21,706 - INFO -   秒 41: 处理 9 个新请求
2025-07-30 01:43:21,711 - INFO -   秒 42: 处理 15 个新请求
2025-07-30 01:43:21,718 - INFO -   秒 43: 处理 11 个新请求
2025-07-30 01:43:21,724 - INFO -   秒 44: 处理 14 个新请求
2025-07-30 01:43:21,730 - INFO -   秒 45: 处理 11 个新请求
2025-07-30 01:43:21,732 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 9205MB, 函数类型: 1
2025-07-30 01:43:21,732 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:21,734 - INFO -   秒 46: 处理 18 个新请求
2025-07-30 01:43:21,742 - INFO -   秒 47: 处理 13 个新请求
2025-07-30 01:43:21,750 - INFO -   秒 48: 处理 17 个新请求
2025-07-30 01:43:21,761 - INFO -   秒 49: 处理 9 个新请求
2025-07-30 01:43:21,767 - INFO -   秒 50: 处理 14 个新请求
2025-07-30 01:43:21,770 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 9155MB, 函数类型: 9
2025-07-30 01:43:21,770 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:21,776 - INFO - 时间槽 8，秒 50: 执行中容器 73，等待请求 0
2025-07-30 01:43:21,776 - INFO -   秒 51: 处理 9 个新请求
2025-07-30 01:43:21,783 - INFO -   秒 52: 处理 16 个新请求
2025-07-30 01:43:21,790 - INFO -   秒 53: 处理 12 个新请求
2025-07-30 01:43:21,798 - INFO -   秒 54: 处理 12 个新请求
2025-07-30 01:43:21,806 - INFO -   秒 55: 处理 13 个新请求
2025-07-30 01:43:21,810 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 9065MB, 函数类型: 9
2025-07-30 01:43:21,810 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:21,816 - INFO -   秒 56: 处理 5 个新请求
2025-07-30 01:43:21,827 - INFO - 时间槽 8 结束时，还有 42 个容器正在执行，将继续在后台执行
2025-07-30 01:43:21,829 - INFO - --------处理时间槽 9 的请求，共 886 个--------
2025-07-30 01:43:21,829 - INFO - ----node memomry----
2025-07-30 01:43:21,829 - INFO - node 1 : 14445 MB
2025-07-30 01:43:21,829 - INFO - node 2 : 14680 MB
2025-07-30 01:43:21,829 - INFO - node 3 : 11790 MB
2025-07-30 01:43:21,829 - INFO - node 4 : 14265 MB
2025-07-30 01:43:21,829 - INFO - node 5 : 12420 MB
2025-07-30 01:43:21,829 - INFO - node 6 : 14655 MB
2025-07-30 01:43:21,829 - INFO - node 7 : 14535 MB
2025-07-30 01:43:21,829 - INFO - node 8 : 13640 MB
2025-07-30 01:43:21,829 - INFO - node 9 : 12845 MB
2025-07-30 01:43:21,829 - INFO - node 10 : 11080 MB
2025-07-30 01:43:21,830 - INFO - node 11 : 13850 MB
2025-07-30 01:43:21,830 - INFO - node 12 : 12865 MB
2025-07-30 01:43:21,830 - INFO - node 13 : 11635 MB
2025-07-30 01:43:21,830 - INFO - node 14 : 14135 MB
2025-07-30 01:43:21,830 - INFO - node 15 : 14025 MB
2025-07-30 01:43:21,830 - INFO - node 16 : 13210 MB
2025-07-30 01:43:21,830 - INFO - node 17 : 13060 MB
2025-07-30 01:43:21,830 - INFO - node 18 : 14705 MB
2025-07-30 01:43:21,830 - INFO - node 19 : 13665 MB
2025-07-30 01:43:21,830 - INFO - node 20 : 14400 MB
2025-07-30 01:43:21,830 - INFO - node 21 : 13070 MB
2025-07-30 01:43:21,830 - INFO - node 22 : 12840 MB
2025-07-30 01:43:21,830 - INFO - node 23 : 12740 MB
2025-07-30 01:43:21,830 - INFO - node 24 : 10610 MB
2025-07-30 01:43:21,830 - INFO - node 25 : 12215 MB
2025-07-30 01:43:21,830 - INFO - node 26 : 12645 MB
2025-07-30 01:43:21,830 - INFO - node 27 : 14355 MB
2025-07-30 01:43:21,830 - INFO - node 28 : 11665 MB
2025-07-30 01:43:21,830 - INFO - node 29 : 13685 MB
2025-07-30 01:43:21,830 - INFO - node 30 : 12095 MB
2025-07-30 01:43:21,830 - INFO - node 31 : 10720 MB
2025-07-30 01:43:21,830 - INFO - node 32 : 12260 MB
2025-07-30 01:43:21,830 - INFO - node 33 : 14060 MB
2025-07-30 01:43:21,830 - INFO - node 34 : 14145 MB
2025-07-30 01:43:21,830 - INFO - node 35 : 11870 MB
2025-07-30 01:43:21,830 - INFO - node 36 : 14285 MB
2025-07-30 01:43:21,831 - INFO - node 37 : 12695 MB
2025-07-30 01:43:21,831 - INFO - node 38 : 10950 MB
2025-07-30 01:43:21,831 - INFO - node 39 : 8990 MB
2025-07-30 01:43:21,831 - INFO - node 40 : 12650 MB
2025-07-30 01:43:21,831 - INFO - node 41 : 14010 MB
2025-07-30 01:43:21,831 - INFO - node 42 : 9610 MB
2025-07-30 01:43:21,831 - INFO - node 43 : 11585 MB
2025-07-30 01:43:21,831 - INFO - node 44 : 11695 MB
2025-07-30 01:43:21,831 - INFO - node 45 : 12520 MB
2025-07-30 01:43:21,831 - INFO - node 46 : 14585 MB
2025-07-30 01:43:21,831 - INFO - node 47 : 11415 MB
2025-07-30 01:43:21,831 - INFO - node 48 : 9375 MB
2025-07-30 01:43:21,831 - INFO - node 49 : 13475 MB
2025-07-30 01:43:21,831 - INFO - node 50 : 12915 MB
2025-07-30 01:43:21,831 - INFO - node 51 : 10005 MB
2025-07-30 01:43:21,831 - INFO - node 52 : 12515 MB
2025-07-30 01:43:21,831 - INFO - node 53 : 14040 MB
2025-07-30 01:43:21,832 - INFO - node 54 : 14045 MB
2025-07-30 01:43:21,832 - INFO - node 55 : 12875 MB
2025-07-30 01:43:21,832 - INFO - node 56 : 14295 MB
2025-07-30 01:43:21,832 - INFO - node 57 : 12575 MB
2025-07-30 01:43:21,832 - INFO - node 58 : 9025 MB
2025-07-30 01:43:21,832 - INFO - node 59 : 9585 MB
2025-07-30 01:43:21,832 - INFO - node 60 : 15000 MB
2025-07-30 01:43:21,832 - INFO - node 61 : 9670 MB
2025-07-30 01:43:21,832 - INFO - node 62 : 12845 MB
2025-07-30 01:43:21,832 - INFO - node 63 : 11975 MB
2025-07-30 01:43:21,832 - INFO - node 64 : 12780 MB
2025-07-30 01:43:21,832 - INFO - node 65 : 8075 MB
2025-07-30 01:43:21,832 - INFO - node 66 : 12000 MB
2025-07-30 01:43:21,832 - INFO - node 67 : 14010 MB
2025-07-30 01:43:21,832 - INFO - node 68 : 15000 MB
2025-07-30 01:43:21,832 - INFO - node 69 : 8020 MB
2025-07-30 01:43:21,832 - INFO - node 70 : 15000 MB
2025-07-30 01:43:21,832 - INFO - node 71 : 12665 MB
2025-07-30 01:43:21,832 - INFO - node 72 : 13120 MB
2025-07-30 01:43:21,832 - INFO - node 73 : 14040 MB
2025-07-30 01:43:21,832 - INFO - node 74 : 9420 MB
2025-07-30 01:43:21,833 - INFO - node 75 : 11720 MB
2025-07-30 01:43:21,833 - INFO - node 76 : 12235 MB
2025-07-30 01:43:21,833 - INFO - node 77 : 9100 MB
2025-07-30 01:43:21,833 - INFO - node 78 : 15000 MB
2025-07-30 01:43:21,833 - INFO - node 79 : 9555 MB
2025-07-30 01:43:21,833 - INFO - node 80 : 11270 MB
2025-07-30 01:43:21,833 - INFO - node 81 : 8615 MB
2025-07-30 01:43:21,833 - INFO - node 82 : 13480 MB
2025-07-30 01:43:21,833 - INFO - node 83 : 14255 MB
2025-07-30 01:43:21,833 - INFO - node 84 : 11930 MB
2025-07-30 01:43:21,833 - INFO - node 85 : 8940 MB
2025-07-30 01:43:21,833 - INFO - node 86 : 11380 MB
2025-07-30 01:43:21,833 - INFO - node 87 : 9885 MB
2025-07-30 01:43:21,833 - INFO - node 88 : 10460 MB
2025-07-30 01:43:21,833 - INFO - node 89 : 6835 MB
2025-07-30 01:43:21,833 - INFO - node 90 : 8270 MB
2025-07-30 01:43:21,833 - INFO - node 91 : 13805 MB
2025-07-30 01:43:21,833 - INFO - node 92 : 13230 MB
2025-07-30 01:43:21,833 - INFO - node 93 : 13390 MB
2025-07-30 01:43:21,833 - INFO - node 94 : 11420 MB
2025-07-30 01:43:21,833 - INFO - node 95 : 11745 MB
2025-07-30 01:43:21,833 - INFO - node 96 : 8975 MB
2025-07-30 01:43:21,834 - INFO - node 97 : 12990 MB
2025-07-30 01:43:21,834 - INFO - node 98 : 8135 MB
2025-07-30 01:43:21,834 - INFO - node 99 : 14195 MB
2025-07-30 01:43:21,834 - INFO - node 100 : 13005 MB
2025-07-30 01:43:21,834 - INFO - node 101 : 11535 MB
2025-07-30 01:43:21,834 - INFO - node 102 : 12765 MB
2025-07-30 01:43:21,834 - INFO - node 103 : 13745 MB
2025-07-30 01:43:21,834 - INFO - node 104 : 14185 MB
2025-07-30 01:43:21,834 - INFO - node 105 : 14050 MB
2025-07-30 01:43:21,834 - INFO - node 106 : 12090 MB
2025-07-30 01:43:21,834 - INFO - node 107 : 9670 MB
2025-07-30 01:43:21,835 - INFO - node 108 : 15000 MB
2025-07-30 01:43:21,835 - INFO - node 109 : 11255 MB
2025-07-30 01:43:21,835 - INFO - node 110 : 11980 MB
2025-07-30 01:43:21,835 - INFO - node 111 : 8130 MB
2025-07-30 01:43:21,835 - INFO - node 112 : 10565 MB
2025-07-30 01:43:21,835 - INFO - node 113 : 12010 MB
2025-07-30 01:43:21,835 - INFO - node 114 : 8375 MB
2025-07-30 01:43:21,835 - INFO - node 115 : 11495 MB
2025-07-30 01:43:21,835 - INFO - node 116 : 8530 MB
2025-07-30 01:43:21,835 - INFO - node 117 : 13710 MB
2025-07-30 01:43:21,835 - INFO - node 118 : 13650 MB
2025-07-30 01:43:21,835 - INFO - node 119 : 11360 MB
2025-07-30 01:43:21,835 - INFO - node 120 : 13895 MB
2025-07-30 01:43:21,835 - INFO - node 121 : 14055 MB
2025-07-30 01:43:21,835 - INFO - node 122 : 13745 MB
2025-07-30 01:43:21,835 - INFO - node 123 : 14325 MB
2025-07-30 01:43:21,835 - INFO - node 124 : 12490 MB
2025-07-30 01:43:21,835 - INFO - node 125 : 14785 MB
2025-07-30 01:43:21,835 - INFO -   秒 0: 处理 109 个新请求
2025-07-30 01:43:21,859 - INFO - DEBUG: 节点96内存检查 - 需要内存: 60.0MB, 可用内存: 8975MB, 函数类型: 3
2025-07-30 01:43:21,860 - INFO - DEBUG: 节点96创建User层容器，减少内存 60MB，类型: 3
2025-07-30 01:43:21,869 - INFO - 时间槽 9，秒 0: 执行中容器 151，等待请求 0
2025-07-30 01:43:21,869 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:43:21,876 - INFO -   秒 2: 处理 4 个新请求
2025-07-30 01:43:21,881 - INFO -   秒 3: 处理 19 个新请求
2025-07-30 01:43:21,889 - INFO -   秒 4: 处理 13 个新请求
2025-07-30 01:43:21,894 - INFO -   秒 5: 处理 15 个新请求
2025-07-30 01:43:21,899 - INFO -   秒 6: 处理 13 个新请求
2025-07-30 01:43:21,902 - INFO -   秒 7: 处理 14 个新请求
2025-07-30 01:43:21,909 - INFO -   秒 8: 处理 12 个新请求
2025-07-30 01:43:21,914 - INFO -   秒 9: 处理 15 个新请求
2025-07-30 01:43:21,924 - INFO -   秒 10: 处理 15 个新请求
2025-07-30 01:43:21,932 - INFO - 时间槽 9，秒 10: 执行中容器 81，等待请求 0
2025-07-30 01:43:21,932 - INFO -   秒 11: 处理 16 个新请求
2025-07-30 01:43:21,938 - INFO -   秒 12: 处理 12 个新请求
2025-07-30 01:43:21,940 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 8915MB, 函数类型: 9
2025-07-30 01:43:21,940 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:21,943 - INFO -   秒 13: 处理 11 个新请求
2025-07-30 01:43:21,949 - INFO -   秒 14: 处理 21 个新请求
2025-07-30 01:43:21,956 - INFO -   秒 15: 处理 17 个新请求
2025-07-30 01:43:21,966 - INFO -   秒 16: 处理 9 个新请求
2025-07-30 01:43:21,970 - INFO -   秒 17: 处理 13 个新请求
2025-07-30 01:43:21,977 - INFO -   秒 18: 处理 12 个新请求
2025-07-30 01:43:21,983 - INFO -   秒 19: 处理 22 个新请求
2025-07-30 01:43:21,993 - INFO -   秒 20: 处理 21 个新请求
2025-07-30 01:43:21,999 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 8825MB, 函数类型: 4
2025-07-30 01:43:21,999 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:22,002 - INFO - 时间槽 9，秒 20: 执行中容器 94，等待请求 0
2025-07-30 01:43:22,002 - INFO -   秒 21: 处理 13 个新请求
2025-07-30 01:43:22,009 - INFO -   秒 22: 处理 7 个新请求
2025-07-30 01:43:22,013 - INFO -   秒 23: 处理 14 个新请求
2025-07-30 01:43:22,015 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 8785MB, 函数类型: 9
2025-07-30 01:43:22,016 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:22,018 - INFO -   秒 24: 处理 23 个新请求
2025-07-30 01:43:22,025 - INFO -   秒 25: 处理 11 个新请求
2025-07-30 01:43:22,031 - INFO -   秒 26: 处理 11 个新请求
2025-07-30 01:43:22,038 - INFO -   秒 27: 处理 11 个新请求
2025-07-30 01:43:22,046 - INFO -   秒 28: 处理 12 个新请求
2025-07-30 01:43:22,051 - INFO -   秒 29: 处理 24 个新请求
2025-07-30 01:43:22,063 - INFO -   秒 30: 处理 17 个新请求
2025-07-30 01:43:22,070 - INFO - 时间槽 9，秒 30: 执行中容器 92，等待请求 0
2025-07-30 01:43:22,070 - INFO -   秒 31: 处理 15 个新请求
2025-07-30 01:43:22,074 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 8695MB, 函数类型: 2
2025-07-30 01:43:22,074 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:22,078 - INFO -   秒 32: 处理 8 个新请求
2025-07-30 01:43:22,083 - INFO -   秒 33: 处理 16 个新请求
2025-07-30 01:43:22,090 - INFO -   秒 34: 处理 15 个新请求
2025-07-30 01:43:22,097 - INFO -   秒 35: 处理 11 个新请求
2025-07-30 01:43:22,102 - INFO -   秒 36: 处理 14 个新请求
2025-07-30 01:43:22,108 - INFO -   秒 37: 处理 13 个新请求
2025-07-30 01:43:22,115 - INFO -   秒 38: 处理 16 个新请求
2025-07-30 01:43:22,120 - INFO -   秒 39: 处理 23 个新请求
2025-07-30 01:43:22,126 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 8630MB, 函数类型: 4
2025-07-30 01:43:22,126 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:22,131 - INFO -   秒 40: 处理 13 个新请求
2025-07-30 01:43:22,137 - INFO - 时间槽 9，秒 40: 执行中容器 92，等待请求 0
2025-07-30 01:43:22,138 - INFO -   秒 41: 处理 10 个新请求
2025-07-30 01:43:22,145 - INFO -   秒 42: 处理 14 个新请求
2025-07-30 01:43:22,150 - INFO -   秒 43: 处理 12 个新请求
2025-07-30 01:43:22,156 - INFO -   秒 44: 处理 20 个新请求
2025-07-30 01:43:22,164 - INFO -   秒 45: 处理 12 个新请求
2025-07-30 01:43:22,169 - INFO -   秒 46: 处理 11 个新请求
2025-07-30 01:43:22,175 - INFO -   秒 47: 处理 13 个新请求
2025-07-30 01:43:22,178 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 8590MB, 函数类型: 9
2025-07-30 01:43:22,178 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:22,182 - INFO -   秒 48: 处理 24 个新请求
2025-07-30 01:43:22,191 - INFO -   秒 49: 处理 14 个新请求
2025-07-30 01:43:22,197 - INFO -   秒 50: 处理 8 个新请求
2025-07-30 01:43:22,201 - INFO - 时间槽 9，秒 50: 执行中容器 76，等待请求 0
2025-07-30 01:43:22,201 - INFO -   秒 51: 处理 15 个新请求
2025-07-30 01:43:22,208 - INFO -   秒 52: 处理 11 个新请求
2025-07-30 01:43:22,212 - INFO -   秒 53: 处理 16 个新请求
2025-07-30 01:43:22,219 - INFO -   秒 54: 处理 9 个新请求
2025-07-30 01:43:22,223 - INFO -   秒 55: 处理 17 个新请求
2025-07-30 01:43:22,225 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 8500MB, 函数类型: 4
2025-07-30 01:43:22,225 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:22,230 - INFO -   秒 56: 处理 6 个新请求
2025-07-30 01:43:22,239 - INFO - 时间槽 9 结束时，还有 45 个容器正在执行，将继续在后台执行
2025-07-30 01:43:22,241 - INFO - --------处理时间槽 10 的请求，共 911 个--------
2025-07-30 01:43:22,241 - INFO - ----node memomry----
2025-07-30 01:43:22,241 - INFO - node 1 : 14445 MB
2025-07-30 01:43:22,241 - INFO - node 2 : 14680 MB
2025-07-30 01:43:22,241 - INFO - node 3 : 11340 MB
2025-07-30 01:43:22,241 - INFO - node 4 : 14145 MB
2025-07-30 01:43:22,241 - INFO - node 5 : 12380 MB
2025-07-30 01:43:22,241 - INFO - node 6 : 14360 MB
2025-07-30 01:43:22,241 - INFO - node 7 : 14535 MB
2025-07-30 01:43:22,241 - INFO - node 8 : 13640 MB
2025-07-30 01:43:22,241 - INFO - node 9 : 12695 MB
2025-07-30 01:43:22,241 - INFO - node 10 : 10855 MB
2025-07-30 01:43:22,241 - INFO - node 11 : 13810 MB
2025-07-30 01:43:22,241 - INFO - node 12 : 12865 MB
2025-07-30 01:43:22,241 - INFO - node 13 : 11405 MB
2025-07-30 01:43:22,241 - INFO - node 14 : 14135 MB
2025-07-30 01:43:22,241 - INFO - node 15 : 14025 MB
2025-07-30 01:43:22,241 - INFO - node 16 : 13050 MB
2025-07-30 01:43:22,241 - INFO - node 17 : 12930 MB
2025-07-30 01:43:22,241 - INFO - node 18 : 14705 MB
2025-07-30 01:43:22,241 - INFO - node 19 : 13185 MB
2025-07-30 01:43:22,241 - INFO - node 20 : 14400 MB
2025-07-30 01:43:22,241 - INFO - node 21 : 12960 MB
2025-07-30 01:43:22,241 - INFO - node 22 : 12490 MB
2025-07-30 01:43:22,243 - INFO - node 23 : 12610 MB
2025-07-30 01:43:22,243 - INFO - node 24 : 10235 MB
2025-07-30 01:43:22,243 - INFO - node 25 : 11840 MB
2025-07-30 01:43:22,243 - INFO - node 26 : 12455 MB
2025-07-30 01:43:22,243 - INFO - node 27 : 14315 MB
2025-07-30 01:43:22,243 - INFO - node 28 : 11035 MB
2025-07-30 01:43:22,243 - INFO - node 29 : 13645 MB
2025-07-30 01:43:22,243 - INFO - node 30 : 11805 MB
2025-07-30 01:43:22,243 - INFO - node 31 : 10540 MB
2025-07-30 01:43:22,243 - INFO - node 32 : 12020 MB
2025-07-30 01:43:22,243 - INFO - node 33 : 13995 MB
2025-07-30 01:43:22,243 - INFO - node 34 : 14105 MB
2025-07-30 01:43:22,243 - INFO - node 35 : 11555 MB
2025-07-30 01:43:22,243 - INFO - node 36 : 13990 MB
2025-07-30 01:43:22,243 - INFO - node 37 : 12205 MB
2025-07-30 01:43:22,243 - INFO - node 38 : 10360 MB
2025-07-30 01:43:22,243 - INFO - node 39 : 8440 MB
2025-07-30 01:43:22,243 - INFO - node 40 : 12600 MB
2025-07-30 01:43:22,243 - INFO - node 41 : 13960 MB
2025-07-30 01:43:22,243 - INFO - node 42 : 8365 MB
2025-07-30 01:43:22,243 - INFO - node 43 : 10985 MB
2025-07-30 01:43:22,243 - INFO - node 44 : 10950 MB
2025-07-30 01:43:22,243 - INFO - node 45 : 12290 MB
2025-07-30 01:43:22,243 - INFO - node 46 : 14585 MB
2025-07-30 01:43:22,243 - INFO - node 47 : 10830 MB
2025-07-30 01:43:22,243 - INFO - node 48 : 9275 MB
2025-07-30 01:43:22,243 - INFO - node 49 : 13410 MB
2025-07-30 01:43:22,243 - INFO - node 50 : 12755 MB
2025-07-30 01:43:22,243 - INFO - node 51 : 9640 MB
2025-07-30 01:43:22,244 - INFO - node 52 : 12235 MB
2025-07-30 01:43:22,244 - INFO - node 53 : 13980 MB
2025-07-30 01:43:22,244 - INFO - node 54 : 13685 MB
2025-07-30 01:43:22,244 - INFO - node 55 : 12785 MB
2025-07-30 01:43:22,244 - INFO - node 56 : 14255 MB
2025-07-30 01:43:22,244 - INFO - node 57 : 12415 MB
2025-07-30 01:43:22,244 - INFO - node 58 : 8410 MB
2025-07-30 01:43:22,244 - INFO - node 59 : 9170 MB
2025-07-30 01:43:22,244 - INFO - node 60 : 15000 MB
2025-07-30 01:43:22,244 - INFO - node 61 : 9020 MB
2025-07-30 01:43:22,244 - INFO - node 62 : 12640 MB
2025-07-30 01:43:22,244 - INFO - node 63 : 11475 MB
2025-07-30 01:43:22,244 - INFO - node 64 : 12715 MB
2025-07-30 01:43:22,244 - INFO - node 65 : 7245 MB
2025-07-30 01:43:22,244 - INFO - node 66 : 11660 MB
2025-07-30 01:43:22,244 - INFO - node 67 : 13970 MB
2025-07-30 01:43:22,244 - INFO - node 68 : 15000 MB
2025-07-30 01:43:22,244 - INFO - node 69 : 7360 MB
2025-07-30 01:43:22,244 - INFO - node 70 : 14845 MB
2025-07-30 01:43:22,244 - INFO - node 71 : 12445 MB
2025-07-30 01:43:22,244 - INFO - node 72 : 13010 MB
2025-07-30 01:43:22,244 - INFO - node 73 : 13975 MB
2025-07-30 01:43:22,244 - INFO - node 74 : 9030 MB
2025-07-30 01:43:22,244 - INFO - node 75 : 11230 MB
2025-07-30 01:43:22,244 - INFO - node 76 : 11885 MB
2025-07-30 01:43:22,244 - INFO - node 77 : 7775 MB
2025-07-30 01:43:22,244 - INFO - node 78 : 15000 MB
2025-07-30 01:43:22,244 - INFO - node 79 : 9165 MB
2025-07-30 01:43:22,244 - INFO - node 80 : 10645 MB
2025-07-30 01:43:22,244 - INFO - node 81 : 8390 MB
2025-07-30 01:43:22,244 - INFO - node 82 : 13430 MB
2025-07-30 01:43:22,244 - INFO - node 83 : 14215 MB
2025-07-30 01:43:22,244 - INFO - node 84 : 11605 MB
2025-07-30 01:43:22,244 - INFO - node 85 : 8050 MB
2025-07-30 01:43:22,244 - INFO - node 86 : 11080 MB
2025-07-30 01:43:22,244 - INFO - node 87 : 9670 MB
2025-07-30 01:43:22,244 - INFO - node 88 : 9935 MB
2025-07-30 01:43:22,244 - INFO - node 89 : 5270 MB
2025-07-30 01:43:22,244 - INFO - node 90 : 7865 MB
2025-07-30 01:43:22,244 - INFO - node 91 : 13715 MB
2025-07-30 01:43:22,244 - INFO - node 92 : 13140 MB
2025-07-30 01:43:22,244 - INFO - node 93 : 13260 MB
2025-07-30 01:43:22,244 - INFO - node 94 : 11180 MB
2025-07-30 01:43:22,244 - INFO - node 95 : 11125 MB
2025-07-30 01:43:22,244 - INFO - node 96 : 8460 MB
2025-07-30 01:43:22,244 - INFO - node 97 : 12825 MB
2025-07-30 01:43:22,244 - INFO - node 98 : 7555 MB
2025-07-30 01:43:22,244 - INFO - node 99 : 14145 MB
2025-07-30 01:43:22,245 - INFO - node 100 : 12825 MB
2025-07-30 01:43:22,245 - INFO - node 101 : 11390 MB
2025-07-30 01:43:22,245 - INFO - node 102 : 12195 MB
2025-07-30 01:43:22,245 - INFO - node 103 : 13490 MB
2025-07-30 01:43:22,245 - INFO - node 104 : 14125 MB
2025-07-30 01:43:22,245 - INFO - node 105 : 13960 MB
2025-07-30 01:43:22,245 - INFO - node 106 : 11900 MB
2025-07-30 01:43:22,245 - INFO - node 107 : 9220 MB
2025-07-30 01:43:22,245 - INFO - node 108 : 15000 MB
2025-07-30 01:43:22,245 - INFO - node 109 : 10875 MB
2025-07-30 01:43:22,245 - INFO - node 110 : 11790 MB
2025-07-30 01:43:22,245 - INFO - node 111 : 7340 MB
2025-07-30 01:43:22,245 - INFO - node 112 : 10090 MB
2025-07-30 01:43:22,245 - INFO - node 113 : 11855 MB
2025-07-30 01:43:22,245 - INFO - node 114 : 6915 MB
2025-07-30 01:43:22,245 - INFO - node 115 : 11155 MB
2025-07-30 01:43:22,245 - INFO - node 116 : 7825 MB
2025-07-30 01:43:22,245 - INFO - node 117 : 13620 MB
2025-07-30 01:43:22,245 - INFO - node 118 : 13580 MB
2025-07-30 01:43:22,245 - INFO - node 119 : 10935 MB
2025-07-30 01:43:22,245 - INFO - node 120 : 13830 MB
2025-07-30 01:43:22,245 - INFO - node 121 : 13935 MB
2025-07-30 01:43:22,245 - INFO - node 122 : 13695 MB
2025-07-30 01:43:22,245 - INFO - node 123 : 14030 MB
2025-07-30 01:43:22,245 - INFO - node 124 : 12070 MB
2025-07-30 01:43:22,245 - INFO - node 125 : 14785 MB
2025-07-30 01:43:22,246 - INFO -   秒 0: 处理 110 个新请求
2025-07-30 01:43:22,266 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 8460MB, 函数类型: 6
2025-07-30 01:43:22,266 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:43:22,273 - INFO - 时间槽 10，秒 0: 执行中容器 155，等待请求 0
2025-07-30 01:43:22,273 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:43:22,278 - INFO -   秒 2: 处理 4 个新请求
2025-07-30 01:43:22,283 - INFO -   秒 3: 处理 21 个新请求
2025-07-30 01:43:22,289 - INFO -   秒 4: 处理 12 个新请求
2025-07-30 01:43:22,295 - INFO -   秒 5: 处理 14 个新请求
2025-07-30 01:43:22,302 - INFO -   秒 6: 处理 14 个新请求
2025-07-30 01:43:22,308 - INFO -   秒 7: 处理 14 个新请求
2025-07-30 01:43:22,314 - INFO -   秒 8: 处理 10 个新请求
2025-07-30 01:43:22,319 - INFO -   秒 9: 处理 19 个新请求
2025-07-30 01:43:22,328 - INFO -   秒 10: 处理 18 个新请求
2025-07-30 01:43:22,334 - INFO - 时间槽 10，秒 10: 执行中容器 87，等待请求 0
2025-07-30 01:43:22,334 - INFO -   秒 11: 处理 21 个新请求
2025-07-30 01:43:22,337 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 8390MB, 函数类型: 9
2025-07-30 01:43:22,337 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:22,341 - INFO -   秒 12: 处理 10 个新请求
2025-07-30 01:43:22,345 - INFO -   秒 13: 处理 7 个新请求
2025-07-30 01:43:22,350 - INFO -   秒 14: 处理 16 个新请求
2025-07-30 01:43:22,356 - INFO -   秒 15: 处理 25 个新请求
2025-07-30 01:43:22,365 - INFO -   秒 16: 处理 10 个新请求
2025-07-30 01:43:22,370 - INFO -   秒 17: 处理 15 个新请求
2025-07-30 01:43:22,377 - INFO -   秒 18: 处理 9 个新请求
2025-07-30 01:43:22,382 - INFO -   秒 19: 处理 30 个新请求
2025-07-30 01:43:22,388 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 8300MB, 函数类型: 4
2025-07-30 01:43:22,388 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:22,393 - INFO -   秒 20: 处理 12 个新请求
2025-07-30 01:43:22,400 - INFO - 时间槽 10，秒 20: 执行中容器 92，等待请求 0
2025-07-30 01:43:22,400 - INFO -   秒 21: 处理 13 个新请求
2025-07-30 01:43:22,404 - INFO -   秒 22: 处理 9 个新请求
2025-07-30 01:43:22,411 - INFO -   秒 23: 处理 18 个新请求
2025-07-30 01:43:22,415 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 8260MB, 函数类型: 1
2025-07-30 01:43:22,415 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:22,417 - INFO -   秒 24: 处理 21 个新请求
2025-07-30 01:43:22,423 - INFO -   秒 25: 处理 11 个新请求
2025-07-30 01:43:22,430 - INFO -   秒 26: 处理 8 个新请求
2025-07-30 01:43:22,435 - INFO -   秒 27: 处理 13 个新请求
2025-07-30 01:43:22,441 - INFO -   秒 28: 处理 9 个新请求
2025-07-30 01:43:22,447 - INFO -   秒 29: 处理 32 个新请求
2025-07-30 01:43:22,457 - INFO -   秒 30: 处理 18 个新请求
2025-07-30 01:43:22,465 - INFO - 时间槽 10，秒 30: 执行中容器 95，等待请求 0
2025-07-30 01:43:22,465 - INFO -   秒 31: 处理 15 个新请求
2025-07-30 01:43:22,468 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 8210MB, 函数类型: 1
2025-07-30 01:43:22,468 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:22,471 - INFO -   秒 32: 处理 8 个新请求
2025-07-30 01:43:22,476 - INFO -   秒 33: 处理 10 个新请求
2025-07-30 01:43:22,485 - INFO -   秒 34: 处理 20 个新请求
2025-07-30 01:43:22,493 - INFO -   秒 35: 处理 14 个新请求
2025-07-30 01:43:22,500 - INFO -   秒 36: 处理 17 个新请求
2025-07-30 01:43:22,505 - INFO -   秒 37: 处理 8 个新请求
2025-07-30 01:43:22,511 - INFO -   秒 38: 处理 10 个新请求
2025-07-30 01:43:22,516 - INFO -   秒 39: 处理 31 个新请求
2025-07-30 01:43:22,521 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 8160MB, 函数类型: 7
2025-07-30 01:43:22,521 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:43:22,526 - INFO -   秒 40: 处理 11 个新请求
2025-07-30 01:43:22,532 - INFO - 时间槽 10，秒 40: 执行中容器 94，等待请求 0
2025-07-30 01:43:22,532 - INFO -   秒 41: 处理 12 个新请求
2025-07-30 01:43:22,537 - INFO -   秒 42: 处理 10 个新请求
2025-07-30 01:43:22,542 - INFO -   秒 43: 处理 13 个新请求
2025-07-30 01:43:22,549 - INFO -   秒 44: 处理 19 个新请求
2025-07-30 01:43:22,556 - INFO -   秒 45: 处理 14 个新请求
2025-07-30 01:43:22,561 - INFO -   秒 46: 处理 15 个新请求
2025-07-30 01:43:22,570 - INFO -   秒 47: 处理 15 个新请求
2025-07-30 01:43:22,577 - INFO -   秒 48: 处理 16 个新请求
2025-07-30 01:43:22,585 - INFO -   秒 49: 处理 20 个新请求
2025-07-30 01:43:22,593 - INFO -   秒 50: 处理 11 个新请求
2025-07-30 01:43:22,599 - INFO - 时间槽 10，秒 50: 执行中容器 88，等待请求 0
2025-07-30 01:43:22,599 - INFO -   秒 51: 处理 13 个新请求
2025-07-30 01:43:22,602 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 8040MB, 函数类型: 2
2025-07-30 01:43:22,602 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:22,605 - INFO -   秒 52: 处理 17 个新请求
2025-07-30 01:43:22,614 - INFO -   秒 53: 处理 12 个新请求
2025-07-30 01:43:22,619 - INFO -   秒 54: 处理 7 个新请求
2025-07-30 01:43:22,625 - INFO -   秒 55: 处理 16 个新请求
2025-07-30 01:43:22,628 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7975MB, 函数类型: 9
2025-07-30 01:43:22,628 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:22,634 - INFO -   秒 56: 处理 10 个新请求
2025-07-30 01:43:22,645 - INFO - 时间槽 10 结束时，还有 50 个容器正在执行，将继续在后台执行
2025-07-30 01:43:22,647 - INFO - --------处理时间槽 11 的请求，共 889 个--------
2025-07-30 01:43:22,647 - INFO - ----node memomry----
2025-07-30 01:43:22,647 - INFO - node 1 : 14445 MB
2025-07-30 01:43:22,647 - INFO - node 2 : 14525 MB
2025-07-30 01:43:22,647 - INFO - node 3 : 11065 MB
2025-07-30 01:43:22,647 - INFO - node 4 : 14020 MB
2025-07-30 01:43:22,647 - INFO - node 5 : 12310 MB
2025-07-30 01:43:22,647 - INFO - node 6 : 14360 MB
2025-07-30 01:43:22,647 - INFO - node 7 : 14495 MB
2025-07-30 01:43:22,648 - INFO - node 8 : 13485 MB
2025-07-30 01:43:22,648 - INFO - node 9 : 12540 MB
2025-07-30 01:43:22,648 - INFO - node 10 : 10725 MB
2025-07-30 01:43:22,648 - INFO - node 11 : 13810 MB
2025-07-30 01:43:22,648 - INFO - node 12 : 12865 MB
2025-07-30 01:43:22,648 - INFO - node 13 : 10790 MB
2025-07-30 01:43:22,648 - INFO - node 14 : 14135 MB
2025-07-30 01:43:22,648 - INFO - node 15 : 13955 MB
2025-07-30 01:43:22,648 - INFO - node 16 : 12960 MB
2025-07-30 01:43:22,648 - INFO - node 17 : 12805 MB
2025-07-30 01:43:22,648 - INFO - node 18 : 14705 MB
2025-07-30 01:43:22,648 - INFO - node 19 : 13135 MB
2025-07-30 01:43:22,648 - INFO - node 20 : 14350 MB
2025-07-30 01:43:22,648 - INFO - node 21 : 12920 MB
2025-07-30 01:43:22,648 - INFO - node 22 : 11935 MB
2025-07-30 01:43:22,648 - INFO - node 23 : 12490 MB
2025-07-30 01:43:22,648 - INFO - node 24 : 9735 MB
2025-07-30 01:43:22,648 - INFO - node 25 : 11760 MB
2025-07-30 01:43:22,648 - INFO - node 26 : 12325 MB
2025-07-30 01:43:22,649 - INFO - node 27 : 14020 MB
2025-07-30 01:43:22,649 - INFO - node 28 : 10925 MB
2025-07-30 01:43:22,649 - INFO - node 29 : 13580 MB
2025-07-30 01:43:22,649 - INFO - node 30 : 11535 MB
2025-07-30 01:43:22,649 - INFO - node 31 : 10325 MB
2025-07-30 01:43:22,649 - INFO - node 32 : 11690 MB
2025-07-30 01:43:22,649 - INFO - node 33 : 13945 MB
2025-07-30 01:43:22,649 - INFO - node 34 : 14015 MB
2025-07-30 01:43:22,649 - INFO - node 35 : 11270 MB
2025-07-30 01:43:22,649 - INFO - node 36 : 13850 MB
2025-07-30 01:43:22,649 - INFO - node 37 : 12065 MB
2025-07-30 01:43:22,649 - INFO - node 38 : 9520 MB
2025-07-30 01:43:22,649 - INFO - node 39 : 7560 MB
2025-07-30 01:43:22,649 - INFO - node 40 : 12460 MB
2025-07-30 01:43:22,649 - INFO - node 41 : 13910 MB
2025-07-30 01:43:22,649 - INFO - node 42 : 7965 MB
2025-07-30 01:43:22,649 - INFO - node 43 : 10815 MB
2025-07-30 01:43:22,649 - INFO - node 44 : 10290 MB
2025-07-30 01:43:22,649 - INFO - node 45 : 12000 MB
2025-07-30 01:43:22,649 - INFO - node 46 : 14585 MB
2025-07-30 01:43:22,649 - INFO - node 47 : 10190 MB
2025-07-30 01:43:22,649 - INFO - node 48 : 8755 MB
2025-07-30 01:43:22,649 - INFO - node 49 : 13360 MB
2025-07-30 01:43:22,649 - INFO - node 50 : 12595 MB
2025-07-30 01:43:22,649 - INFO - node 51 : 9080 MB
2025-07-30 01:43:22,649 - INFO - node 52 : 11710 MB
2025-07-30 01:43:22,649 - INFO - node 53 : 13940 MB
2025-07-30 01:43:22,649 - INFO - node 54 : 13645 MB
2025-07-30 01:43:22,649 - INFO - node 55 : 12335 MB
2025-07-30 01:43:22,649 - INFO - node 56 : 14205 MB
2025-07-30 01:43:22,649 - INFO - node 57 : 12145 MB
2025-07-30 01:43:22,649 - INFO - node 58 : 7715 MB
2025-07-30 01:43:22,649 - INFO - node 59 : 8405 MB
2025-07-30 01:43:22,649 - INFO - node 60 : 15000 MB
2025-07-30 01:43:22,649 - INFO - node 61 : 8330 MB
2025-07-30 01:43:22,649 - INFO - node 62 : 12570 MB
2025-07-30 01:43:22,649 - INFO - node 63 : 11395 MB
2025-07-30 01:43:22,649 - INFO - node 64 : 12675 MB
2025-07-30 01:43:22,649 - INFO - node 65 : 6290 MB
2025-07-30 01:43:22,649 - INFO - node 66 : 11505 MB
2025-07-30 01:43:22,649 - INFO - node 67 : 13905 MB
2025-07-30 01:43:22,649 - INFO - node 68 : 15000 MB
2025-07-30 01:43:22,649 - INFO - node 69 : 6550 MB
2025-07-30 01:43:22,649 - INFO - node 70 : 14845 MB
2025-07-30 01:43:22,649 - INFO - node 71 : 12290 MB
2025-07-30 01:43:22,649 - INFO - node 72 : 12870 MB
2025-07-30 01:43:22,649 - INFO - node 73 : 13615 MB
2025-07-30 01:43:22,650 - INFO - node 74 : 8445 MB
2025-07-30 01:43:22,650 - INFO - node 75 : 11070 MB
2025-07-30 01:43:22,650 - INFO - node 76 : 11645 MB
2025-07-30 01:43:22,650 - INFO - node 77 : 7085 MB
2025-07-30 01:43:22,650 - INFO - node 78 : 15000 MB
2025-07-30 01:43:22,650 - INFO - node 79 : 8460 MB
2025-07-30 01:43:22,650 - INFO - node 80 : 10050 MB
2025-07-30 01:43:22,650 - INFO - node 81 : 8025 MB
2025-07-30 01:43:22,650 - INFO - node 82 : 13340 MB
2025-07-30 01:43:22,650 - INFO - node 83 : 14215 MB
2025-07-30 01:43:22,650 - INFO - node 84 : 11235 MB
2025-07-30 01:43:22,650 - INFO - node 85 : 7350 MB
2025-07-30 01:43:22,650 - INFO - node 86 : 10110 MB
2025-07-30 01:43:22,650 - INFO - node 87 : 8825 MB
2025-07-30 01:43:22,650 - INFO - node 88 : 9705 MB
2025-07-30 01:43:22,650 - INFO - node 89 : 4580 MB
2025-07-30 01:43:22,650 - INFO - node 90 : 7355 MB
2025-07-30 01:43:22,650 - INFO - node 91 : 13625 MB
2025-07-30 01:43:22,650 - INFO - node 92 : 13035 MB
2025-07-30 01:43:22,650 - INFO - node 93 : 13120 MB
2025-07-30 01:43:22,650 - INFO - node 94 : 10760 MB
2025-07-30 01:43:22,650 - INFO - node 95 : 10700 MB
2025-07-30 01:43:22,650 - INFO - node 96 : 7885 MB
2025-07-30 01:43:22,650 - INFO - node 97 : 12655 MB
2025-07-30 01:43:22,650 - INFO - node 98 : 7080 MB
2025-07-30 01:43:22,650 - INFO - node 99 : 13940 MB
2025-07-30 01:43:22,650 - INFO - node 100 : 12725 MB
2025-07-30 01:43:22,650 - INFO - node 101 : 11080 MB
2025-07-30 01:43:22,650 - INFO - node 102 : 12035 MB
2025-07-30 01:43:22,650 - INFO - node 103 : 13350 MB
2025-07-30 01:43:22,650 - INFO - node 104 : 14075 MB
2025-07-30 01:43:22,650 - INFO - node 105 : 13920 MB
2025-07-30 01:43:22,650 - INFO - node 106 : 11810 MB
2025-07-30 01:43:22,650 - INFO - node 107 : 8590 MB
2025-07-30 01:43:22,650 - INFO - node 108 : 15000 MB
2025-07-30 01:43:22,650 - INFO - node 109 : 10655 MB
2025-07-30 01:43:22,650 - INFO - node 110 : 11465 MB
2025-07-30 01:43:22,650 - INFO - node 111 : 6730 MB
2025-07-30 01:43:22,650 - INFO - node 112 : 9410 MB
2025-07-30 01:43:22,650 - INFO - node 113 : 11165 MB
2025-07-30 01:43:22,650 - INFO - node 114 : 5965 MB
2025-07-30 01:43:22,650 - INFO - node 115 : 10690 MB
2025-07-30 01:43:22,650 - INFO - node 116 : 7195 MB
2025-07-30 01:43:22,650 - INFO - node 117 : 13530 MB
2025-07-30 01:43:22,650 - INFO - node 118 : 13540 MB
2025-07-30 01:43:22,650 - INFO - node 119 : 10600 MB
2025-07-30 01:43:22,650 - INFO - node 120 : 13770 MB
2025-07-30 01:43:22,650 - INFO - node 121 : 13870 MB
2025-07-30 01:43:22,650 - INFO - node 122 : 13655 MB
2025-07-30 01:43:22,650 - INFO - node 123 : 13910 MB
2025-07-30 01:43:22,650 - INFO - node 124 : 11840 MB
2025-07-30 01:43:22,650 - INFO - node 125 : 14630 MB
2025-07-30 01:43:22,651 - INFO -   秒 0: 处理 110 个新请求
2025-07-30 01:43:22,675 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:22,675 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:22,683 - INFO - 时间槽 11，秒 0: 执行中容器 160，等待请求 0
2025-07-30 01:43:22,683 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:43:22,688 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:22,688 - INFO -   秒 2: 处理 4 个新请求
2025-07-30 01:43:22,693 - INFO -   秒 3: 处理 19 个新请求
2025-07-30 01:43:22,698 - INFO - DEBUG: 节点96内存检查 - 需要内存: 360.0MB, 可用内存: 7885MB, 函数类型: 5
2025-07-30 01:43:22,698 - INFO - DEBUG: 节点96创建User层容器，减少内存 360MB，类型: 5
2025-07-30 01:43:22,704 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 360MB，类型: 5
2025-07-30 01:43:22,704 - INFO -   秒 4: 处理 13 个新请求
2025-07-30 01:43:22,711 - INFO -   秒 5: 处理 16 个新请求
2025-07-30 01:43:22,720 - INFO -   秒 6: 处理 12 个新请求
2025-07-30 01:43:22,727 - INFO -   秒 7: 处理 15 个新请求
2025-07-30 01:43:22,731 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:22,731 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:22,734 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:22,735 - INFO -   秒 8: 处理 8 个新请求
2025-07-30 01:43:22,739 - INFO -   秒 9: 处理 18 个新请求
2025-07-30 01:43:22,749 - INFO -   秒 10: 处理 16 个新请求
2025-07-30 01:43:22,756 - INFO - 时间槽 11，秒 10: 执行中容器 94，等待请求 0
2025-07-30 01:43:22,756 - INFO -   秒 11: 处理 17 个新请求
2025-07-30 01:43:22,759 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:22,759 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:22,763 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:22,763 - INFO -   秒 12: 处理 14 个新请求
2025-07-30 01:43:22,770 - INFO -   秒 13: 处理 9 个新请求
2025-07-30 01:43:22,776 - INFO -   秒 14: 处理 18 个新请求
2025-07-30 01:43:22,785 - INFO -   秒 15: 处理 17 个新请求
2025-07-30 01:43:22,788 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7885MB, 函数类型: 9
2025-07-30 01:43:22,788 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:22,792 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:43:22,793 - INFO -   秒 16: 处理 10 个新请求
2025-07-30 01:43:22,800 - INFO -   秒 17: 处理 14 个新请求
2025-07-30 01:43:22,807 - INFO -   秒 18: 处理 14 个新请求
2025-07-30 01:43:22,813 - INFO -   秒 19: 处理 24 个新请求
2025-07-30 01:43:22,819 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:22,819 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:22,823 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:22,825 - INFO -   秒 20: 处理 15 个新请求
2025-07-30 01:43:22,831 - INFO - 时间槽 11，秒 20: 执行中容器 96，等待请求 0
2025-07-30 01:43:22,832 - INFO -   秒 21: 处理 14 个新请求
2025-07-30 01:43:22,837 - INFO -   秒 22: 处理 10 个新请求
2025-07-30 01:43:22,844 - INFO -   秒 23: 处理 17 个新请求
2025-07-30 01:43:22,847 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:22,847 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:22,851 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:22,852 - INFO -   秒 24: 处理 18 个新请求
2025-07-30 01:43:22,859 - INFO -   秒 25: 处理 9 个新请求
2025-07-30 01:43:22,864 - INFO -   秒 26: 处理 11 个新请求
2025-07-30 01:43:22,870 - INFO -   秒 27: 处理 12 个新请求
2025-07-30 01:43:22,874 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:22,874 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:22,878 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:22,878 - INFO -   秒 28: 处理 12 个新请求
2025-07-30 01:43:22,884 - INFO -   秒 29: 处理 21 个新请求
2025-07-30 01:43:22,893 - INFO -   秒 30: 处理 22 个新请求
2025-07-30 01:43:22,902 - INFO - 时间槽 11，秒 30: 执行中容器 103，等待请求 0
2025-07-30 01:43:22,902 - INFO -   秒 31: 处理 12 个新请求
2025-07-30 01:43:22,904 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7885MB, 函数类型: 9
2025-07-30 01:43:22,904 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:22,908 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:43:22,908 - INFO -   秒 32: 处理 13 个新请求
2025-07-30 01:43:22,917 - INFO -   秒 33: 处理 10 个新请求
2025-07-30 01:43:22,922 - INFO -   秒 34: 处理 17 个新请求
2025-07-30 01:43:22,926 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:22,926 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:22,930 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:22,931 - INFO -   秒 35: 处理 16 个新请求
2025-07-30 01:43:22,939 - INFO -   秒 36: 处理 9 个新请求
2025-07-30 01:43:22,945 - INFO -   秒 37: 处理 14 个新请求
2025-07-30 01:43:22,953 - INFO -   秒 38: 处理 17 个新请求
2025-07-30 01:43:22,957 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:22,957 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:22,961 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:22,961 - INFO -   秒 39: 处理 22 个新请求
2025-07-30 01:43:22,970 - INFO -   秒 40: 处理 12 个新请求
2025-07-30 01:43:22,979 - INFO - 时间槽 11，秒 40: 执行中容器 94，等待请求 0
2025-07-30 01:43:22,979 - INFO -   秒 41: 处理 11 个新请求
2025-07-30 01:43:22,986 - INFO -   秒 42: 处理 14 个新请求
2025-07-30 01:43:22,988 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:22,988 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:22,993 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:22,993 - INFO -   秒 43: 处理 14 个新请求
2025-07-30 01:43:23,002 - INFO -   秒 44: 处理 17 个新请求
2025-07-30 01:43:23,011 - INFO -   秒 45: 处理 15 个新请求
2025-07-30 01:43:23,021 - INFO -   秒 46: 处理 10 个新请求
2025-07-30 01:43:23,024 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7885MB, 函数类型: 9
2025-07-30 01:43:23,025 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:23,029 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:43:23,029 - INFO -   秒 47: 处理 15 个新请求
2025-07-30 01:43:23,037 - INFO -   秒 48: 处理 14 个新请求
2025-07-30 01:43:23,046 - INFO -   秒 49: 处理 18 个新请求
2025-07-30 01:43:23,058 - INFO -   秒 50: 处理 17 个新请求
2025-07-30 01:43:23,063 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:23,063 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:23,068 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:23,068 - INFO - 时间槽 11，秒 50: 执行中容器 100，等待请求 0
2025-07-30 01:43:23,068 - INFO -   秒 51: 处理 9 个新请求
2025-07-30 01:43:23,077 - INFO -   秒 52: 处理 13 个新请求
2025-07-30 01:43:23,082 - INFO -   秒 53: 处理 13 个新请求
2025-07-30 01:43:23,089 - INFO -   秒 54: 处理 17 个新请求
2025-07-30 01:43:23,091 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 7885MB, 函数类型: 2
2025-07-30 01:43:23,092 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:23,097 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:43:23,097 - INFO -   秒 55: 处理 9 个新请求
2025-07-30 01:43:23,105 - INFO -   秒 56: 处理 8 个新请求
2025-07-30 01:43:23,125 - INFO - 时间槽 11 结束时，还有 60 个容器正在执行，将继续在后台执行
2025-07-30 01:43:23,128 - INFO - --------处理时间槽 12 的请求，共 895 个--------
2025-07-30 01:43:23,129 - INFO - ----node memomry----
2025-07-30 01:43:23,129 - INFO - node 1 : 14445 MB
2025-07-30 01:43:23,129 - INFO - node 2 : 14525 MB
2025-07-30 01:43:23,129 - INFO - node 3 : 11065 MB
2025-07-30 01:43:23,129 - INFO - node 4 : 14020 MB
2025-07-30 01:43:23,129 - INFO - node 5 : 12310 MB
2025-07-30 01:43:23,129 - INFO - node 6 : 14360 MB
2025-07-30 01:43:23,129 - INFO - node 7 : 14495 MB
2025-07-30 01:43:23,129 - INFO - node 8 : 13690 MB
2025-07-30 01:43:23,129 - INFO - node 9 : 12540 MB
2025-07-30 01:43:23,129 - INFO - node 10 : 10725 MB
2025-07-30 01:43:23,129 - INFO - node 11 : 13605 MB
2025-07-30 01:43:23,129 - INFO - node 12 : 12865 MB
2025-07-30 01:43:23,129 - INFO - node 13 : 10790 MB
2025-07-30 01:43:23,130 - INFO - node 14 : 14340 MB
2025-07-30 01:43:23,130 - INFO - node 15 : 13955 MB
2025-07-30 01:43:23,130 - INFO - node 16 : 12960 MB
2025-07-30 01:43:23,130 - INFO - node 17 : 12805 MB
2025-07-30 01:43:23,130 - INFO - node 18 : 14620 MB
2025-07-30 01:43:23,130 - INFO - node 19 : 13135 MB
2025-07-30 01:43:23,130 - INFO - node 20 : 14350 MB
2025-07-30 01:43:23,130 - INFO - node 21 : 12920 MB
2025-07-30 01:43:23,130 - INFO - node 22 : 11730 MB
2025-07-30 01:43:23,130 - INFO - node 23 : 12490 MB
2025-07-30 01:43:23,130 - INFO - node 24 : 9735 MB
2025-07-30 01:43:23,130 - INFO - node 25 : 11760 MB
2025-07-30 01:43:23,130 - INFO - node 26 : 12325 MB
2025-07-30 01:43:23,130 - INFO - node 27 : 14020 MB
2025-07-30 01:43:23,130 - INFO - node 28 : 10925 MB
2025-07-30 01:43:23,130 - INFO - node 29 : 13785 MB
2025-07-30 01:43:23,130 - INFO - node 30 : 11535 MB
2025-07-30 01:43:23,130 - INFO - node 31 : 10325 MB
2025-07-30 01:43:23,130 - INFO - node 32 : 11690 MB
2025-07-30 01:43:23,130 - INFO - node 33 : 13945 MB
2025-07-30 01:43:23,130 - INFO - node 34 : 14015 MB
2025-07-30 01:43:23,130 - INFO - node 35 : 11270 MB
2025-07-30 01:43:23,131 - INFO - node 36 : 13935 MB
2025-07-30 01:43:23,131 - INFO - node 37 : 12065 MB
2025-07-30 01:43:23,131 - INFO - node 38 : 9520 MB
2025-07-30 01:43:23,131 - INFO - node 39 : 7560 MB
2025-07-30 01:43:23,131 - INFO - node 40 : 12460 MB
2025-07-30 01:43:23,131 - INFO - node 41 : 13910 MB
2025-07-30 01:43:23,131 - INFO - node 42 : 7965 MB
2025-07-30 01:43:23,131 - INFO - node 43 : 10815 MB
2025-07-30 01:43:23,131 - INFO - node 44 : 10290 MB
2025-07-30 01:43:23,131 - INFO - node 45 : 12000 MB
2025-07-30 01:43:23,131 - INFO - node 46 : 14380 MB
2025-07-30 01:43:23,131 - INFO - node 47 : 10190 MB
2025-07-30 01:43:23,131 - INFO - node 48 : 8755 MB
2025-07-30 01:43:23,131 - INFO - node 49 : 13360 MB
2025-07-30 01:43:23,131 - INFO - node 50 : 12595 MB
2025-07-30 01:43:23,131 - INFO - node 51 : 9080 MB
2025-07-30 01:43:23,131 - INFO - node 52 : 11710 MB
2025-07-30 01:43:23,131 - INFO - node 53 : 13940 MB
2025-07-30 01:43:23,131 - INFO - node 54 : 13645 MB
2025-07-30 01:43:23,131 - INFO - node 55 : 12335 MB
2025-07-30 01:43:23,131 - INFO - node 56 : 14205 MB
2025-07-30 01:43:23,131 - INFO - node 57 : 12145 MB
2025-07-30 01:43:23,131 - INFO - node 58 : 7715 MB
2025-07-30 01:43:23,131 - INFO - node 59 : 8405 MB
2025-07-30 01:43:23,131 - INFO - node 60 : 14850 MB
2025-07-30 01:43:23,131 - INFO - node 61 : 8330 MB
2025-07-30 01:43:23,131 - INFO - node 62 : 12570 MB
2025-07-30 01:43:23,131 - INFO - node 63 : 11395 MB
2025-07-30 01:43:23,131 - INFO - node 64 : 12675 MB
2025-07-30 01:43:23,133 - INFO - node 65 : 6290 MB
2025-07-30 01:43:23,133 - INFO - node 66 : 11505 MB
2025-07-30 01:43:23,133 - INFO - node 67 : 13905 MB
2025-07-30 01:43:23,133 - INFO - node 68 : 14850 MB
2025-07-30 01:43:23,133 - INFO - node 69 : 6550 MB
2025-07-30 01:43:23,133 - INFO - node 70 : 14695 MB
2025-07-30 01:43:23,133 - INFO - node 71 : 12290 MB
2025-07-30 01:43:23,133 - INFO - node 72 : 12870 MB
2025-07-30 01:43:23,133 - INFO - node 73 : 13615 MB
2025-07-30 01:43:23,133 - INFO - node 74 : 8445 MB
2025-07-30 01:43:23,133 - INFO - node 75 : 11070 MB
2025-07-30 01:43:23,133 - INFO - node 76 : 11645 MB
2025-07-30 01:43:23,133 - INFO - node 77 : 7085 MB
2025-07-30 01:43:23,133 - INFO - node 78 : 14795 MB
2025-07-30 01:43:23,133 - INFO - node 79 : 8460 MB
2025-07-30 01:43:23,133 - INFO - node 80 : 10050 MB
2025-07-30 01:43:23,133 - INFO - node 81 : 8025 MB
2025-07-30 01:43:23,133 - INFO - node 82 : 13340 MB
2025-07-30 01:43:23,133 - INFO - node 83 : 14215 MB
2025-07-30 01:43:23,133 - INFO - node 84 : 11235 MB
2025-07-30 01:43:23,133 - INFO - node 85 : 7350 MB
2025-07-30 01:43:23,134 - INFO - node 86 : 10110 MB
2025-07-30 01:43:23,134 - INFO - node 87 : 8825 MB
2025-07-30 01:43:23,134 - INFO - node 88 : 9705 MB
2025-07-30 01:43:23,134 - INFO - node 89 : 4580 MB
2025-07-30 01:43:23,134 - INFO - node 90 : 7355 MB
2025-07-30 01:43:23,134 - INFO - node 91 : 13710 MB
2025-07-30 01:43:23,134 - INFO - node 92 : 13035 MB
2025-07-30 01:43:23,134 - INFO - node 93 : 13120 MB
2025-07-30 01:43:23,134 - INFO - node 94 : 10760 MB
2025-07-30 01:43:23,134 - INFO - node 95 : 10700 MB
2025-07-30 01:43:23,134 - INFO - node 96 : 7885 MB
2025-07-30 01:43:23,134 - INFO - node 97 : 12655 MB
2025-07-30 01:43:23,134 - INFO - node 98 : 7080 MB
2025-07-30 01:43:23,134 - INFO - node 99 : 13940 MB
2025-07-30 01:43:23,134 - INFO - node 100 : 12725 MB
2025-07-30 01:43:23,134 - INFO - node 101 : 11080 MB
2025-07-30 01:43:23,134 - INFO - node 102 : 12035 MB
2025-07-30 01:43:23,134 - INFO - node 103 : 13350 MB
2025-07-30 01:43:23,134 - INFO - node 104 : 14075 MB
2025-07-30 01:43:23,134 - INFO - node 105 : 13920 MB
2025-07-30 01:43:23,134 - INFO - node 106 : 11810 MB
2025-07-30 01:43:23,134 - INFO - node 107 : 8590 MB
2025-07-30 01:43:23,134 - INFO - node 108 : 14850 MB
2025-07-30 01:43:23,135 - INFO - node 109 : 10655 MB
2025-07-30 01:43:23,135 - INFO - node 110 : 11465 MB
2025-07-30 01:43:23,135 - INFO - node 111 : 6730 MB
2025-07-30 01:43:23,135 - INFO - node 112 : 9410 MB
2025-07-30 01:43:23,135 - INFO - node 113 : 11165 MB
2025-07-30 01:43:23,135 - INFO - node 114 : 5965 MB
2025-07-30 01:43:23,135 - INFO - node 115 : 10690 MB
2025-07-30 01:43:23,135 - INFO - node 116 : 7195 MB
2025-07-30 01:43:23,135 - INFO - node 117 : 13530 MB
2025-07-30 01:43:23,135 - INFO - node 118 : 13540 MB
2025-07-30 01:43:23,135 - INFO - node 119 : 10600 MB
2025-07-30 01:43:23,135 - INFO - node 120 : 13770 MB
2025-07-30 01:43:23,135 - INFO - node 121 : 13870 MB
2025-07-30 01:43:23,135 - INFO - node 122 : 13655 MB
2025-07-30 01:43:23,135 - INFO - node 123 : 13910 MB
2025-07-30 01:43:23,135 - INFO - node 124 : 11840 MB
2025-07-30 01:43:23,135 - INFO - node 125 : 14425 MB
2025-07-30 01:43:23,135 - INFO -   秒 0: 处理 108 个新请求
2025-07-30 01:43:23,160 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:23,160 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:23,168 - INFO - 时间槽 12，秒 0: 执行中容器 168，等待请求 0
2025-07-30 01:43:23,168 - INFO -   秒 1: 处理 3 个新请求
2025-07-30 01:43:23,173 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:23,174 - INFO -   秒 2: 处理 5 个新请求
2025-07-30 01:43:23,177 - INFO -   秒 3: 处理 21 个新请求
2025-07-30 01:43:23,183 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 7885MB, 函数类型: 4
2025-07-30 01:43:23,185 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:23,189 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:43:23,189 - INFO -   秒 4: 处理 11 个新请求
2025-07-30 01:43:23,199 - INFO -   秒 5: 处理 17 个新请求
2025-07-30 01:43:23,212 - INFO -   秒 6: 处理 13 个新请求
2025-07-30 01:43:23,221 - INFO -   秒 7: 处理 15 个新请求
2025-07-30 01:43:23,225 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:23,225 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:23,232 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:23,232 - INFO -   秒 8: 处理 8 个新请求
2025-07-30 01:43:23,238 - INFO -   秒 9: 处理 14 个新请求
2025-07-30 01:43:23,250 - INFO -   秒 10: 处理 21 个新请求
2025-07-30 01:43:23,262 - INFO - 时间槽 12，秒 10: 执行中容器 103，等待请求 0
2025-07-30 01:43:23,262 - INFO -   秒 11: 处理 19 个新请求
2025-07-30 01:43:23,269 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:23,269 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:23,275 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:23,276 - INFO -   秒 12: 处理 8 个新请求
2025-07-30 01:43:23,283 - INFO -   秒 13: 处理 14 个新请求
2025-07-30 01:43:23,294 - INFO -   秒 14: 处理 18 个新请求
2025-07-30 01:43:23,306 - INFO -   秒 15: 处理 16 个新请求
2025-07-30 01:43:23,309 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 7885MB, 函数类型: 4
2025-07-30 01:43:23,309 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:23,315 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:43:23,316 - INFO -   秒 16: 处理 10 个新请求
2025-07-30 01:43:23,323 - INFO -   秒 17: 处理 16 个新请求
2025-07-30 01:43:23,333 - INFO -   秒 18: 处理 10 个新请求
2025-07-30 01:43:23,339 - INFO -   秒 19: 处理 24 个新请求
2025-07-30 01:43:23,345 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 7885MB, 函数类型: 4
2025-07-30 01:43:23,345 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:23,350 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:43:23,351 - INFO -   秒 20: 处理 14 个新请求
2025-07-30 01:43:23,357 - INFO - 时间槽 12，秒 20: 执行中容器 105，等待请求 0
2025-07-30 01:43:23,357 - INFO -   秒 21: 处理 13 个新请求
2025-07-30 01:43:23,367 - INFO -   秒 22: 处理 12 个新请求
2025-07-30 01:43:23,375 - INFO -   秒 23: 处理 17 个新请求
2025-07-30 01:43:23,380 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 7885MB, 函数类型: 7
2025-07-30 01:43:23,381 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:43:23,386 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 120MB，类型: 7
2025-07-30 01:43:23,386 - INFO -   秒 24: 处理 19 个新请求
2025-07-30 01:43:23,393 - INFO -   秒 25: 处理 10 个新请求
2025-07-30 01:43:23,399 - INFO -   秒 26: 处理 9 个新请求
2025-07-30 01:43:23,403 - INFO -   秒 27: 处理 12 个新请求
2025-07-30 01:43:23,411 - INFO -   秒 28: 处理 14 个新请求
2025-07-30 01:43:23,414 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:23,414 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:23,419 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:23,420 - INFO -   秒 29: 处理 25 个新请求
2025-07-30 01:43:23,431 - INFO -   秒 30: 处理 20 个新请求
2025-07-30 01:43:23,439 - INFO - 时间槽 12，秒 30: 执行中容器 121，等待请求 0
2025-07-30 01:43:23,439 - INFO -   秒 31: 处理 13 个新请求
2025-07-30 01:43:23,449 - INFO -   秒 32: 处理 9 个新请求
2025-07-30 01:43:23,451 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7885MB, 函数类型: 9
2025-07-30 01:43:23,451 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:23,453 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:43:23,454 - INFO -   秒 33: 处理 12 个新请求
2025-07-30 01:43:23,461 - INFO -   秒 34: 处理 17 个新请求
2025-07-30 01:43:23,470 - INFO -   秒 35: 处理 17 个新请求
2025-07-30 01:43:23,483 - INFO -   秒 36: 处理 12 个新请求
2025-07-30 01:43:23,486 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 7885MB, 函数类型: 6
2025-07-30 01:43:23,486 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:43:23,491 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 70MB，类型: 6
2025-07-30 01:43:23,491 - INFO -   秒 37: 处理 10 个新请求
2025-07-30 01:43:23,499 - INFO -   秒 38: 处理 18 个新请求
2025-07-30 01:43:23,508 - INFO -   秒 39: 处理 22 个新请求
2025-07-30 01:43:23,520 - INFO -   秒 40: 处理 11 个新请求
2025-07-30 01:43:23,523 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7885MB, 函数类型: 9
2025-07-30 01:43:23,523 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:23,527 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:43:23,528 - INFO - 时间槽 12，秒 40: 执行中容器 105，等待请求 0
2025-07-30 01:43:23,528 - INFO -   秒 41: 处理 15 个新请求
2025-07-30 01:43:23,536 - INFO -   秒 42: 处理 7 个新请求
2025-07-30 01:43:23,541 - INFO -   秒 43: 处理 17 个新请求
2025-07-30 01:43:23,551 - INFO -   秒 44: 处理 18 个新请求
2025-07-30 01:43:23,554 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 7885MB, 函数类型: 4
2025-07-30 01:43:23,554 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:23,557 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:43:23,557 - INFO -   秒 45: 处理 11 个新请求
2025-07-30 01:43:23,563 - INFO -   秒 46: 处理 11 个新请求
2025-07-30 01:43:23,570 - INFO -   秒 47: 处理 17 个新请求
2025-07-30 01:43:23,578 - INFO -   秒 48: 处理 18 个新请求
2025-07-30 01:43:23,582 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 7885MB, 函数类型: 2
2025-07-30 01:43:23,582 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:23,586 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:43:23,586 - INFO -   秒 49: 处理 17 个新请求
2025-07-30 01:43:23,595 - INFO -   秒 50: 处理 12 个新请求
2025-07-30 01:43:23,602 - INFO - 时间槽 12，秒 50: 执行中容器 104，等待请求 0
2025-07-30 01:43:23,602 - INFO -   秒 51: 处理 13 个新请求
2025-07-30 01:43:23,609 - INFO -   秒 52: 处理 12 个新请求
2025-07-30 01:43:23,611 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 7885MB, 函数类型: 4
2025-07-30 01:43:23,612 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:23,617 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:43:23,617 - INFO -   秒 53: 处理 16 个新请求
2025-07-30 01:43:23,625 - INFO -   秒 54: 处理 11 个新请求
2025-07-30 01:43:23,633 - INFO -   秒 55: 处理 12 个新请求
2025-07-30 01:43:23,640 - INFO -   秒 56: 处理 11 个新请求
2025-07-30 01:43:23,643 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:23,644 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:23,646 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:23,654 - INFO - 时间槽 12 结束时，还有 61 个容器正在执行，将继续在后台执行
2025-07-30 01:43:23,658 - INFO - --------处理时间槽 13 的请求，共 926 个--------
2025-07-30 01:43:23,658 - INFO - ----node memomry----
2025-07-30 01:43:23,658 - INFO - node 1 : 14565 MB
2025-07-30 01:43:23,658 - INFO - node 2 : 14525 MB
2025-07-30 01:43:23,658 - INFO - node 3 : 11065 MB
2025-07-30 01:43:23,658 - INFO - node 4 : 13815 MB
2025-07-30 01:43:23,658 - INFO - node 5 : 12310 MB
2025-07-30 01:43:23,658 - INFO - node 6 : 14360 MB
2025-07-30 01:43:23,658 - INFO - node 7 : 14495 MB
2025-07-30 01:43:23,658 - INFO - node 8 : 13485 MB
2025-07-30 01:43:23,658 - INFO - node 9 : 12540 MB
2025-07-30 01:43:23,658 - INFO - node 10 : 10725 MB
2025-07-30 01:43:23,658 - INFO - node 11 : 13605 MB
2025-07-30 01:43:23,658 - INFO - node 12 : 12865 MB
2025-07-30 01:43:23,658 - INFO - node 13 : 10790 MB
2025-07-30 01:43:23,658 - INFO - node 14 : 14340 MB
2025-07-30 01:43:23,658 - INFO - node 15 : 13955 MB
2025-07-30 01:43:23,658 - INFO - node 16 : 12960 MB
2025-07-30 01:43:23,658 - INFO - node 17 : 12805 MB
2025-07-30 01:43:23,658 - INFO - node 18 : 14620 MB
2025-07-30 01:43:23,658 - INFO - node 19 : 13135 MB
2025-07-30 01:43:23,658 - INFO - node 20 : 14350 MB
2025-07-30 01:43:23,658 - INFO - node 21 : 12920 MB
2025-07-30 01:43:23,658 - INFO - node 22 : 11730 MB
2025-07-30 01:43:23,658 - INFO - node 23 : 12490 MB
2025-07-30 01:43:23,658 - INFO - node 24 : 9735 MB
2025-07-30 01:43:23,658 - INFO - node 25 : 11760 MB
2025-07-30 01:43:23,658 - INFO - node 26 : 12325 MB
2025-07-30 01:43:23,658 - INFO - node 27 : 14020 MB
2025-07-30 01:43:23,658 - INFO - node 28 : 10925 MB
2025-07-30 01:43:23,658 - INFO - node 29 : 13580 MB
2025-07-30 01:43:23,658 - INFO - node 30 : 11535 MB
2025-07-30 01:43:23,658 - INFO - node 31 : 10325 MB
2025-07-30 01:43:23,658 - INFO - node 32 : 11690 MB
2025-07-30 01:43:23,658 - INFO - node 33 : 13945 MB
2025-07-30 01:43:23,658 - INFO - node 34 : 14015 MB
2025-07-30 01:43:23,658 - INFO - node 35 : 11270 MB
2025-07-30 01:43:23,658 - INFO - node 36 : 13935 MB
2025-07-30 01:43:23,658 - INFO - node 37 : 12065 MB
2025-07-30 01:43:23,658 - INFO - node 38 : 9520 MB
2025-07-30 01:43:23,658 - INFO - node 39 : 7560 MB
2025-07-30 01:43:23,658 - INFO - node 40 : 12460 MB
2025-07-30 01:43:23,658 - INFO - node 41 : 13910 MB
2025-07-30 01:43:23,658 - INFO - node 42 : 7965 MB
2025-07-30 01:43:23,658 - INFO - node 43 : 10815 MB
2025-07-30 01:43:23,658 - INFO - node 44 : 10290 MB
2025-07-30 01:43:23,658 - INFO - node 45 : 12000 MB
2025-07-30 01:43:23,659 - INFO - node 46 : 14380 MB
2025-07-30 01:43:23,659 - INFO - node 47 : 10190 MB
2025-07-30 01:43:23,659 - INFO - node 48 : 8755 MB
2025-07-30 01:43:23,659 - INFO - node 49 : 13360 MB
2025-07-30 01:43:23,659 - INFO - node 50 : 12595 MB
2025-07-30 01:43:23,659 - INFO - node 51 : 9080 MB
2025-07-30 01:43:23,659 - INFO - node 52 : 11710 MB
2025-07-30 01:43:23,659 - INFO - node 53 : 13940 MB
2025-07-30 01:43:23,659 - INFO - node 54 : 13645 MB
2025-07-30 01:43:23,659 - INFO - node 55 : 12335 MB
2025-07-30 01:43:23,659 - INFO - node 56 : 14205 MB
2025-07-30 01:43:23,659 - INFO - node 57 : 12145 MB
2025-07-30 01:43:23,659 - INFO - node 58 : 7715 MB
2025-07-30 01:43:23,659 - INFO - node 59 : 8405 MB
2025-07-30 01:43:23,659 - INFO - node 60 : 14850 MB
2025-07-30 01:43:23,659 - INFO - node 61 : 8330 MB
2025-07-30 01:43:23,659 - INFO - node 62 : 12570 MB
2025-07-30 01:43:23,659 - INFO - node 63 : 11395 MB
2025-07-30 01:43:23,659 - INFO - node 64 : 12675 MB
2025-07-30 01:43:23,659 - INFO - node 65 : 6290 MB
2025-07-30 01:43:23,659 - INFO - node 66 : 11505 MB
2025-07-30 01:43:23,659 - INFO - node 67 : 13905 MB
2025-07-30 01:43:23,659 - INFO - node 68 : 14850 MB
2025-07-30 01:43:23,659 - INFO - node 69 : 6550 MB
2025-07-30 01:43:23,659 - INFO - node 70 : 14695 MB
2025-07-30 01:43:23,659 - INFO - node 71 : 12290 MB
2025-07-30 01:43:23,659 - INFO - node 72 : 12870 MB
2025-07-30 01:43:23,659 - INFO - node 73 : 13615 MB
2025-07-30 01:43:23,659 - INFO - node 74 : 8445 MB
2025-07-30 01:43:23,659 - INFO - node 75 : 11070 MB
2025-07-30 01:43:23,659 - INFO - node 76 : 11645 MB
2025-07-30 01:43:23,659 - INFO - node 77 : 7085 MB
2025-07-30 01:43:23,659 - INFO - node 78 : 14645 MB
2025-07-30 01:43:23,659 - INFO - node 79 : 8460 MB
2025-07-30 01:43:23,659 - INFO - node 80 : 10050 MB
2025-07-30 01:43:23,659 - INFO - node 81 : 8025 MB
2025-07-30 01:43:23,659 - INFO - node 82 : 13340 MB
2025-07-30 01:43:23,659 - INFO - node 83 : 14215 MB
2025-07-30 01:43:23,659 - INFO - node 84 : 11235 MB
2025-07-30 01:43:23,659 - INFO - node 85 : 7350 MB
2025-07-30 01:43:23,659 - INFO - node 86 : 10110 MB
2025-07-30 01:43:23,661 - INFO - node 87 : 8825 MB
2025-07-30 01:43:23,661 - INFO - node 88 : 9705 MB
2025-07-30 01:43:23,661 - INFO - node 89 : 4580 MB
2025-07-30 01:43:23,661 - INFO - node 90 : 7355 MB
2025-07-30 01:43:23,661 - INFO - node 91 : 13710 MB
2025-07-30 01:43:23,661 - INFO - node 92 : 13035 MB
2025-07-30 01:43:23,661 - INFO - node 93 : 13120 MB
2025-07-30 01:43:23,661 - INFO - node 94 : 10760 MB
2025-07-30 01:43:23,661 - INFO - node 95 : 10700 MB
2025-07-30 01:43:23,661 - INFO - node 96 : 7885 MB
2025-07-30 01:43:23,661 - INFO - node 97 : 12655 MB
2025-07-30 01:43:23,661 - INFO - node 98 : 7080 MB
2025-07-30 01:43:23,661 - INFO - node 99 : 14145 MB
2025-07-30 01:43:23,661 - INFO - node 100 : 12725 MB
2025-07-30 01:43:23,661 - INFO - node 101 : 11080 MB
2025-07-30 01:43:23,661 - INFO - node 102 : 12035 MB
2025-07-30 01:43:23,661 - INFO - node 103 : 13350 MB
2025-07-30 01:43:23,661 - INFO - node 104 : 14075 MB
2025-07-30 01:43:23,661 - INFO - node 105 : 13920 MB
2025-07-30 01:43:23,661 - INFO - node 106 : 11810 MB
2025-07-30 01:43:23,661 - INFO - node 107 : 8590 MB
2025-07-30 01:43:23,661 - INFO - node 108 : 14765 MB
2025-07-30 01:43:23,661 - INFO - node 109 : 10655 MB
2025-07-30 01:43:23,661 - INFO - node 110 : 11465 MB
2025-07-30 01:43:23,661 - INFO - node 111 : 6730 MB
2025-07-30 01:43:23,661 - INFO - node 112 : 9410 MB
2025-07-30 01:43:23,661 - INFO - node 113 : 11165 MB
2025-07-30 01:43:23,661 - INFO - node 114 : 5965 MB
2025-07-30 01:43:23,661 - INFO - node 115 : 10690 MB
2025-07-30 01:43:23,661 - INFO - node 116 : 7195 MB
2025-07-30 01:43:23,661 - INFO - node 117 : 13530 MB
2025-07-30 01:43:23,661 - INFO - node 118 : 13540 MB
2025-07-30 01:43:23,661 - INFO - node 119 : 10600 MB
2025-07-30 01:43:23,661 - INFO - node 120 : 13770 MB
2025-07-30 01:43:23,661 - INFO - node 121 : 13870 MB
2025-07-30 01:43:23,661 - INFO - node 122 : 13655 MB
2025-07-30 01:43:23,661 - INFO - node 123 : 13910 MB
2025-07-30 01:43:23,661 - INFO - node 124 : 11840 MB
2025-07-30 01:43:23,661 - INFO - node 125 : 14425 MB
2025-07-30 01:43:23,662 - INFO -   秒 0: 处理 109 个新请求
2025-07-30 01:43:23,685 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7885MB, 函数类型: 9
2025-07-30 01:43:23,685 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:23,693 - INFO - 时间槽 13，秒 0: 执行中容器 170，等待请求 0
2025-07-30 01:43:23,694 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:43:23,699 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:43:23,699 - INFO -   秒 2: 处理 6 个新请求
2025-07-30 01:43:23,704 - INFO -   秒 3: 处理 21 个新请求
2025-07-30 01:43:23,709 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:23,709 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:23,714 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:23,714 - INFO -   秒 4: 处理 13 个新请求
2025-07-30 01:43:23,721 - INFO -   秒 5: 处理 16 个新请求
2025-07-30 01:43:23,730 - INFO -   秒 6: 处理 16 个新请求
2025-07-30 01:43:23,738 - INFO -   秒 7: 处理 10 个新请求
2025-07-30 01:43:23,740 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:23,740 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:23,744 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:23,745 - INFO -   秒 8: 处理 10 个新请求
2025-07-30 01:43:23,750 - INFO -   秒 9: 处理 23 个新请求
2025-07-30 01:43:23,759 - INFO -   秒 10: 处理 12 个新请求
2025-07-30 01:43:23,768 - INFO - 时间槽 13，秒 10: 执行中容器 104，等待请求 0
2025-07-30 01:43:23,768 - INFO -   秒 11: 处理 19 个新请求
2025-07-30 01:43:23,770 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:23,771 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:23,775 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:23,776 - INFO -   秒 12: 处理 15 个新请求
2025-07-30 01:43:23,783 - INFO -   秒 13: 处理 14 个新请求
2025-07-30 01:43:23,790 - INFO -   秒 14: 处理 15 个新请求
2025-07-30 01:43:23,796 - INFO -   秒 15: 处理 18 个新请求
2025-07-30 01:43:23,800 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7885MB, 函数类型: 9
2025-07-30 01:43:23,800 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:23,803 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:43:23,804 - INFO -   秒 16: 处理 13 个新请求
2025-07-30 01:43:23,812 - INFO -   秒 17: 处理 12 个新请求
2025-07-30 01:43:23,819 - INFO -   秒 18: 处理 11 个新请求
2025-07-30 01:43:23,824 - INFO -   秒 19: 处理 34 个新请求
2025-07-30 01:43:23,831 - INFO - DEBUG: 节点96内存检查 - 需要内存: 360.0MB, 可用内存: 7885MB, 函数类型: 5
2025-07-30 01:43:23,831 - INFO - DEBUG: 节点96创建User层容器，减少内存 360MB，类型: 5
2025-07-30 01:43:23,835 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 360MB，类型: 5
2025-07-30 01:43:23,836 - INFO -   秒 20: 处理 11 个新请求
2025-07-30 01:43:23,842 - INFO - 时间槽 13，秒 20: 执行中容器 113，等待请求 0
2025-07-30 01:43:23,842 - INFO -   秒 21: 处理 10 个新请求
2025-07-30 01:43:23,849 - INFO -   秒 22: 处理 11 个新请求
2025-07-30 01:43:23,854 - INFO -   秒 23: 处理 18 个新请求
2025-07-30 01:43:23,859 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 7885MB, 函数类型: 4
2025-07-30 01:43:23,859 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:23,864 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:43:23,865 - INFO -   秒 24: 处理 19 个新请求
2025-07-30 01:43:23,872 - INFO -   秒 25: 处理 9 个新请求
2025-07-30 01:43:23,878 - INFO -   秒 26: 处理 12 个新请求
2025-07-30 01:43:23,885 - INFO -   秒 27: 处理 18 个新请求
2025-07-30 01:43:23,887 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7885MB, 函数类型: 9
2025-07-30 01:43:23,887 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:23,891 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:43:23,892 - INFO -   秒 28: 处理 7 个新请求
2025-07-30 01:43:23,897 - INFO -   秒 29: 处理 30 个新请求
2025-07-30 01:43:23,907 - INFO -   秒 30: 处理 14 个新请求
2025-07-30 01:43:23,915 - INFO - 时间槽 13，秒 30: 执行中容器 101，等待请求 0
2025-07-30 01:43:23,915 - INFO -   秒 31: 处理 14 个新请求
2025-07-30 01:43:23,918 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7885MB, 函数类型: 9
2025-07-30 01:43:23,918 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:23,921 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:43:23,922 - INFO -   秒 32: 处理 13 个新请求
2025-07-30 01:43:23,929 - INFO -   秒 33: 处理 13 个新请求
2025-07-30 01:43:23,936 - INFO -   秒 34: 处理 13 个新请求
2025-07-30 01:43:23,943 - INFO -   秒 35: 处理 20 个新请求
2025-07-30 01:43:23,948 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:23,948 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:23,952 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:23,953 - INFO -   秒 36: 处理 13 个新请求
2025-07-30 01:43:23,958 - INFO -   秒 37: 处理 11 个新请求
2025-07-30 01:43:23,966 - INFO -   秒 38: 处理 10 个新请求
2025-07-30 01:43:23,971 - INFO -   秒 39: 处理 30 个新请求
2025-07-30 01:43:23,978 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 7885MB, 函数类型: 4
2025-07-30 01:43:23,979 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:23,983 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:43:23,984 - INFO -   秒 40: 处理 15 个新请求
2025-07-30 01:43:23,992 - INFO - 时间槽 13，秒 40: 执行中容器 112，等待请求 0
2025-07-30 01:43:23,992 - INFO -   秒 41: 处理 11 个新请求
2025-07-30 01:43:24,001 - INFO -   秒 42: 处理 15 个新请求
2025-07-30 01:43:24,008 - INFO -   秒 43: 处理 13 个新请求
2025-07-30 01:43:24,011 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 7885MB, 函数类型: 4
2025-07-30 01:43:24,011 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:24,016 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:43:24,016 - INFO -   秒 44: 处理 17 个新请求
2025-07-30 01:43:24,025 - INFO -   秒 45: 处理 10 个新请求
2025-07-30 01:43:24,031 - INFO -   秒 46: 处理 18 个新请求
2025-07-30 01:43:24,035 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7885MB, 函数类型: 9
2025-07-30 01:43:24,035 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:24,039 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:43:24,039 - INFO -   秒 47: 处理 16 个新请求
2025-07-30 01:43:24,046 - INFO -   秒 48: 处理 13 个新请求
2025-07-30 01:43:24,051 - INFO -   秒 49: 处理 22 个新请求
2025-07-30 01:43:24,061 - INFO -   秒 50: 处理 14 个新请求
2025-07-30 01:43:24,065 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 7885MB, 函数类型: 7
2025-07-30 01:43:24,065 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:43:24,069 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 120MB，类型: 7
2025-07-30 01:43:24,070 - INFO - 时间槽 13，秒 50: 执行中容器 108，等待请求 0
2025-07-30 01:43:24,070 - INFO -   秒 51: 处理 12 个新请求
2025-07-30 01:43:24,078 - INFO -   秒 52: 处理 10 个新请求
2025-07-30 01:43:24,084 - INFO -   秒 53: 处理 16 个新请求
2025-07-30 01:43:24,092 - INFO -   秒 54: 处理 16 个新请求
2025-07-30 01:43:24,095 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 7885MB, 函数类型: 4
2025-07-30 01:43:24,095 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:24,099 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:43:24,100 - INFO -   秒 55: 处理 13 个新请求
2025-07-30 01:43:24,105 - INFO -   秒 56: 处理 7 个新请求
2025-07-30 01:43:24,109 - INFO -   秒 57: 处理 1 个新请求
2025-07-30 01:43:24,116 - INFO - 时间槽 13 结束时，还有 68 个容器正在执行，将继续在后台执行
2025-07-30 01:43:24,117 - INFO - --------处理时间槽 14 的请求，共 872 个--------
2025-07-30 01:43:24,117 - INFO - ----node memomry----
2025-07-30 01:43:24,117 - INFO - node 1 : 14565 MB
2025-07-30 01:43:24,117 - INFO - node 2 : 14525 MB
2025-07-30 01:43:24,117 - INFO - node 3 : 11065 MB
2025-07-30 01:43:24,117 - INFO - node 4 : 13815 MB
2025-07-30 01:43:24,117 - INFO - node 5 : 12310 MB
2025-07-30 01:43:24,117 - INFO - node 6 : 14360 MB
2025-07-30 01:43:24,117 - INFO - node 7 : 14495 MB
2025-07-30 01:43:24,117 - INFO - node 8 : 13485 MB
2025-07-30 01:43:24,117 - INFO - node 9 : 12540 MB
2025-07-30 01:43:24,117 - INFO - node 10 : 10725 MB
2025-07-30 01:43:24,117 - INFO - node 11 : 13605 MB
2025-07-30 01:43:24,117 - INFO - node 12 : 12865 MB
2025-07-30 01:43:24,117 - INFO - node 13 : 10790 MB
2025-07-30 01:43:24,117 - INFO - node 14 : 14340 MB
2025-07-30 01:43:24,117 - INFO - node 15 : 13955 MB
2025-07-30 01:43:24,117 - INFO - node 16 : 12960 MB
2025-07-30 01:43:24,117 - INFO - node 17 : 12805 MB
2025-07-30 01:43:24,117 - INFO - node 18 : 14620 MB
2025-07-30 01:43:24,117 - INFO - node 19 : 13135 MB
2025-07-30 01:43:24,117 - INFO - node 20 : 14350 MB
2025-07-30 01:43:24,117 - INFO - node 21 : 12920 MB
2025-07-30 01:43:24,117 - INFO - node 22 : 11730 MB
2025-07-30 01:43:24,117 - INFO - node 23 : 12490 MB
2025-07-30 01:43:24,117 - INFO - node 24 : 9735 MB
2025-07-30 01:43:24,117 - INFO - node 25 : 11760 MB
2025-07-30 01:43:24,117 - INFO - node 26 : 12325 MB
2025-07-30 01:43:24,117 - INFO - node 27 : 14020 MB
2025-07-30 01:43:24,117 - INFO - node 28 : 10925 MB
2025-07-30 01:43:24,117 - INFO - node 29 : 13580 MB
2025-07-30 01:43:24,117 - INFO - node 30 : 11535 MB
2025-07-30 01:43:24,117 - INFO - node 31 : 10325 MB
2025-07-30 01:43:24,117 - INFO - node 32 : 11690 MB
2025-07-30 01:43:24,117 - INFO - node 33 : 13945 MB
2025-07-30 01:43:24,117 - INFO - node 34 : 14015 MB
2025-07-30 01:43:24,117 - INFO - node 35 : 11270 MB
2025-07-30 01:43:24,117 - INFO - node 36 : 13935 MB
2025-07-30 01:43:24,117 - INFO - node 37 : 12065 MB
2025-07-30 01:43:24,117 - INFO - node 38 : 9520 MB
2025-07-30 01:43:24,117 - INFO - node 39 : 7560 MB
2025-07-30 01:43:24,117 - INFO - node 40 : 12460 MB
2025-07-30 01:43:24,117 - INFO - node 41 : 13910 MB
2025-07-30 01:43:24,117 - INFO - node 42 : 7965 MB
2025-07-30 01:43:24,117 - INFO - node 43 : 10815 MB
2025-07-30 01:43:24,117 - INFO - node 44 : 10290 MB
2025-07-30 01:43:24,117 - INFO - node 45 : 12000 MB
2025-07-30 01:43:24,118 - INFO - node 46 : 14380 MB
2025-07-30 01:43:24,118 - INFO - node 47 : 10190 MB
2025-07-30 01:43:24,118 - INFO - node 48 : 8755 MB
2025-07-30 01:43:24,118 - INFO - node 49 : 13360 MB
2025-07-30 01:43:24,118 - INFO - node 50 : 12595 MB
2025-07-30 01:43:24,118 - INFO - node 51 : 9080 MB
2025-07-30 01:43:24,118 - INFO - node 52 : 11710 MB
2025-07-30 01:43:24,118 - INFO - node 53 : 13940 MB
2025-07-30 01:43:24,118 - INFO - node 54 : 13645 MB
2025-07-30 01:43:24,118 - INFO - node 55 : 12335 MB
2025-07-30 01:43:24,118 - INFO - node 56 : 14205 MB
2025-07-30 01:43:24,118 - INFO - node 57 : 12145 MB
2025-07-30 01:43:24,118 - INFO - node 58 : 7715 MB
2025-07-30 01:43:24,118 - INFO - node 59 : 8405 MB
2025-07-30 01:43:24,118 - INFO - node 60 : 14765 MB
2025-07-30 01:43:24,118 - INFO - node 61 : 8330 MB
2025-07-30 01:43:24,118 - INFO - node 62 : 12570 MB
2025-07-30 01:43:24,118 - INFO - node 63 : 11395 MB
2025-07-30 01:43:24,118 - INFO - node 64 : 12675 MB
2025-07-30 01:43:24,118 - INFO - node 65 : 6290 MB
2025-07-30 01:43:24,118 - INFO - node 66 : 11505 MB
2025-07-30 01:43:24,118 - INFO - node 67 : 13905 MB
2025-07-30 01:43:24,118 - INFO - node 68 : 14850 MB
2025-07-30 01:43:24,118 - INFO - node 69 : 6550 MB
2025-07-30 01:43:24,118 - INFO - node 70 : 14695 MB
2025-07-30 01:43:24,118 - INFO - node 71 : 12290 MB
2025-07-30 01:43:24,118 - INFO - node 72 : 12870 MB
2025-07-30 01:43:24,118 - INFO - node 73 : 13615 MB
2025-07-30 01:43:24,118 - INFO - node 74 : 8445 MB
2025-07-30 01:43:24,118 - INFO - node 75 : 11070 MB
2025-07-30 01:43:24,118 - INFO - node 76 : 11645 MB
2025-07-30 01:43:24,118 - INFO - node 77 : 7085 MB
2025-07-30 01:43:24,118 - INFO - node 78 : 14645 MB
2025-07-30 01:43:24,118 - INFO - node 79 : 8460 MB
2025-07-30 01:43:24,118 - INFO - node 80 : 10050 MB
2025-07-30 01:43:24,118 - INFO - node 81 : 8025 MB
2025-07-30 01:43:24,118 - INFO - node 82 : 13340 MB
2025-07-30 01:43:24,118 - INFO - node 83 : 14215 MB
2025-07-30 01:43:24,118 - INFO - node 84 : 11235 MB
2025-07-30 01:43:24,118 - INFO - node 85 : 7350 MB
2025-07-30 01:43:24,118 - INFO - node 86 : 10110 MB
2025-07-30 01:43:24,118 - INFO - node 87 : 8825 MB
2025-07-30 01:43:24,118 - INFO - node 88 : 9705 MB
2025-07-30 01:43:24,118 - INFO - node 89 : 4580 MB
2025-07-30 01:43:24,118 - INFO - node 90 : 7355 MB
2025-07-30 01:43:24,118 - INFO - node 91 : 13710 MB
2025-07-30 01:43:24,118 - INFO - node 92 : 13035 MB
2025-07-30 01:43:24,118 - INFO - node 93 : 13120 MB
2025-07-30 01:43:24,120 - INFO - node 94 : 10760 MB
2025-07-30 01:43:24,120 - INFO - node 95 : 10700 MB
2025-07-30 01:43:24,120 - INFO - node 96 : 7885 MB
2025-07-30 01:43:24,120 - INFO - node 97 : 12655 MB
2025-07-30 01:43:24,120 - INFO - node 98 : 7080 MB
2025-07-30 01:43:24,120 - INFO - node 99 : 14145 MB
2025-07-30 01:43:24,120 - INFO - node 100 : 12725 MB
2025-07-30 01:43:24,120 - INFO - node 101 : 11080 MB
2025-07-30 01:43:24,120 - INFO - node 102 : 12035 MB
2025-07-30 01:43:24,120 - INFO - node 103 : 13350 MB
2025-07-30 01:43:24,120 - INFO - node 104 : 14075 MB
2025-07-30 01:43:24,120 - INFO - node 105 : 13920 MB
2025-07-30 01:43:24,120 - INFO - node 106 : 11810 MB
2025-07-30 01:43:24,120 - INFO - node 107 : 8590 MB
2025-07-30 01:43:24,120 - INFO - node 108 : 14765 MB
2025-07-30 01:43:24,120 - INFO - node 109 : 10655 MB
2025-07-30 01:43:24,120 - INFO - node 110 : 11465 MB
2025-07-30 01:43:24,120 - INFO - node 111 : 6730 MB
2025-07-30 01:43:24,120 - INFO - node 112 : 9410 MB
2025-07-30 01:43:24,120 - INFO - node 113 : 11165 MB
2025-07-30 01:43:24,120 - INFO - node 114 : 5965 MB
2025-07-30 01:43:24,120 - INFO - node 115 : 10690 MB
2025-07-30 01:43:24,120 - INFO - node 116 : 7195 MB
2025-07-30 01:43:24,120 - INFO - node 117 : 13530 MB
2025-07-30 01:43:24,120 - INFO - node 118 : 13745 MB
2025-07-30 01:43:24,120 - INFO - node 119 : 10600 MB
2025-07-30 01:43:24,120 - INFO - node 120 : 13770 MB
2025-07-30 01:43:24,120 - INFO - node 121 : 13870 MB
2025-07-30 01:43:24,120 - INFO - node 122 : 13655 MB
2025-07-30 01:43:24,120 - INFO - node 123 : 13910 MB
2025-07-30 01:43:24,120 - INFO - node 124 : 11840 MB
2025-07-30 01:43:24,120 - INFO - node 125 : 14425 MB
2025-07-30 01:43:24,120 - INFO -   秒 0: 处理 108 个新请求
2025-07-30 01:43:24,141 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 7885MB, 函数类型: 6
2025-07-30 01:43:24,141 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:43:24,148 - INFO - 时间槽 14，秒 0: 执行中容器 176，等待请求 0
2025-07-30 01:43:24,149 - INFO -   秒 1: 处理 5 个新请求
2025-07-30 01:43:24,154 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 70MB，类型: 6
2025-07-30 01:43:24,154 - INFO -   秒 2: 处理 3 个新请求
2025-07-30 01:43:24,158 - INFO -   秒 3: 处理 19 个新请求
2025-07-30 01:43:24,163 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 7885MB, 函数类型: 4
2025-07-30 01:43:24,164 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:24,168 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:43:24,169 - INFO -   秒 4: 处理 14 个新请求
2025-07-30 01:43:24,176 - INFO -   秒 5: 处理 14 个新请求
2025-07-30 01:43:24,185 - INFO -   秒 6: 处理 15 个新请求
2025-07-30 01:43:24,192 - INFO -   秒 7: 处理 11 个新请求
2025-07-30 01:43:24,195 - INFO - DEBUG: 节点96内存检查 - 需要内存: 60.0MB, 可用内存: 7885MB, 函数类型: 3
2025-07-30 01:43:24,195 - INFO - DEBUG: 节点96创建User层容器，减少内存 60MB，类型: 3
2025-07-30 01:43:24,201 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 60MB，类型: 3
2025-07-30 01:43:24,201 - INFO -   秒 8: 处理 12 个新请求
2025-07-30 01:43:24,207 - INFO -   秒 9: 处理 14 个新请求
2025-07-30 01:43:24,215 - INFO -   秒 10: 处理 17 个新请求
2025-07-30 01:43:24,222 - INFO - 时间槽 14，秒 10: 执行中容器 108，等待请求 0
2025-07-30 01:43:24,223 - INFO -   秒 11: 处理 13 个新请求
2025-07-30 01:43:24,225 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 7885MB, 函数类型: 6
2025-07-30 01:43:24,226 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:43:24,230 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 70MB，类型: 6
2025-07-30 01:43:24,230 - INFO -   秒 12: 处理 16 个新请求
2025-07-30 01:43:24,236 - INFO -   秒 13: 处理 12 个新请求
2025-07-30 01:43:24,245 - INFO -   秒 14: 处理 18 个新请求
2025-07-30 01:43:24,253 - INFO -   秒 15: 处理 14 个新请求
2025-07-30 01:43:24,257 - INFO - DEBUG: 节点96内存检查 - 需要内存: 360.0MB, 可用内存: 7885MB, 函数类型: 5
2025-07-30 01:43:24,257 - INFO - DEBUG: 节点96创建User层容器，减少内存 360MB，类型: 5
2025-07-30 01:43:24,261 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 360MB，类型: 5
2025-07-30 01:43:24,261 - INFO -   秒 16: 处理 10 个新请求
2025-07-30 01:43:24,268 - INFO -   秒 17: 处理 13 个新请求
2025-07-30 01:43:24,274 - INFO -   秒 18: 处理 12 个新请求
2025-07-30 01:43:24,280 - INFO -   秒 19: 处理 25 个新请求
2025-07-30 01:43:24,286 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:24,286 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:24,291 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:24,291 - INFO -   秒 20: 处理 17 个新请求
2025-07-30 01:43:24,301 - INFO - 时间槽 14，秒 20: 执行中容器 114，等待请求 0
2025-07-30 01:43:24,301 - INFO -   秒 21: 处理 13 个新请求
2025-07-30 01:43:24,307 - INFO -   秒 22: 处理 6 个新请求
2025-07-30 01:43:24,311 - INFO -   秒 23: 处理 16 个新请求
2025-07-30 01:43:24,316 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 7885MB, 函数类型: 4
2025-07-30 01:43:24,317 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:24,319 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:43:24,319 - INFO -   秒 24: 处理 19 个新请求
2025-07-30 01:43:24,328 - INFO -   秒 25: 处理 13 个新请求
2025-07-30 01:43:24,334 - INFO -   秒 26: 处理 14 个新请求
2025-07-30 01:43:24,343 - INFO -   秒 27: 处理 8 个新请求
2025-07-30 01:43:24,344 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:24,344 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:24,349 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:24,349 - INFO -   秒 28: 处理 12 个新请求
2025-07-30 01:43:24,355 - INFO -   秒 29: 处理 26 个新请求
2025-07-30 01:43:24,366 - INFO -   秒 30: 处理 16 个新请求
2025-07-30 01:43:24,373 - INFO - 时间槽 14，秒 30: 执行中容器 112，等待请求 0
2025-07-30 01:43:24,373 - INFO -   秒 31: 处理 6 个新请求
2025-07-30 01:43:24,375 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 7885MB, 函数类型: 4
2025-07-30 01:43:24,375 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:24,378 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:43:24,378 - INFO -   秒 32: 处理 15 个新请求
2025-07-30 01:43:24,386 - INFO -   秒 33: 处理 13 个新请求
2025-07-30 01:43:24,393 - INFO -   秒 34: 处理 14 个新请求
2025-07-30 01:43:24,400 - INFO -   秒 35: 处理 13 个新请求
2025-07-30 01:43:24,404 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 7885MB, 函数类型: 2
2025-07-30 01:43:24,404 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:24,407 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:43:24,407 - INFO -   秒 36: 处理 15 个新请求
2025-07-30 01:43:24,415 - INFO -   秒 37: 处理 13 个新请求
2025-07-30 01:43:24,420 - INFO -   秒 38: 处理 17 个新请求
2025-07-30 01:43:24,429 - INFO -   秒 39: 处理 22 个新请求
2025-07-30 01:43:24,435 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 7885MB, 函数类型: 2
2025-07-30 01:43:24,435 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:24,439 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:43:24,439 - INFO -   秒 40: 处理 11 个新请求
2025-07-30 01:43:24,446 - INFO - 时间槽 14，秒 40: 执行中容器 110，等待请求 0
2025-07-30 01:43:24,446 - INFO -   秒 41: 处理 12 个新请求
2025-07-30 01:43:24,453 - INFO -   秒 42: 处理 13 个新请求
2025-07-30 01:43:24,461 - INFO -   秒 43: 处理 12 个新请求
2025-07-30 01:43:24,463 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:24,463 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:24,467 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:24,467 - INFO -   秒 44: 处理 16 个新请求
2025-07-30 01:43:24,475 - INFO -   秒 45: 处理 11 个新请求
2025-07-30 01:43:24,481 - INFO -   秒 46: 处理 15 个新请求
2025-07-30 01:43:24,487 - INFO -   秒 47: 处理 13 个新请求
2025-07-30 01:43:24,489 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 7885MB, 函数类型: 6
2025-07-30 01:43:24,489 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:43:24,493 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 70MB，类型: 6
2025-07-30 01:43:24,493 - INFO -   秒 48: 处理 22 个新请求
2025-07-30 01:43:24,501 - INFO -   秒 49: 处理 11 个新请求
2025-07-30 01:43:24,507 - INFO -   秒 50: 处理 9 个新请求
2025-07-30 01:43:24,513 - INFO - 时间槽 14，秒 50: 执行中容器 105，等待请求 0
2025-07-30 01:43:24,513 - INFO -   秒 51: 处理 16 个新请求
2025-07-30 01:43:24,516 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 7885MB, 函数类型: 7
2025-07-30 01:43:24,517 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:43:24,521 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 120MB，类型: 7
2025-07-30 01:43:24,521 - INFO -   秒 52: 处理 12 个新请求
2025-07-30 01:43:24,527 - INFO -   秒 53: 处理 16 个新请求
2025-07-30 01:43:24,535 - INFO -   秒 54: 处理 9 个新请求
2025-07-30 01:43:24,542 - INFO -   秒 55: 处理 12 个新请求
2025-07-30 01:43:24,549 - INFO -   秒 56: 处理 9 个新请求
2025-07-30 01:43:24,551 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7885MB, 函数类型: 9
2025-07-30 01:43:24,551 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:24,553 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:43:24,563 - INFO - 时间槽 14 结束时，还有 74 个容器正在执行，将继续在后台执行
2025-07-30 01:43:24,566 - INFO - --------处理时间槽 15 的请求，共 872 个--------
2025-07-30 01:43:24,566 - INFO - ----node memomry----
2025-07-30 01:43:24,566 - INFO - node 1 : 14565 MB
2025-07-30 01:43:24,566 - INFO - node 2 : 14320 MB
2025-07-30 01:43:24,566 - INFO - node 3 : 11065 MB
2025-07-30 01:43:24,566 - INFO - node 4 : 13815 MB
2025-07-30 01:43:24,566 - INFO - node 5 : 12310 MB
2025-07-30 01:43:24,566 - INFO - node 6 : 14275 MB
2025-07-30 01:43:24,566 - INFO - node 7 : 14495 MB
2025-07-30 01:43:24,566 - INFO - node 8 : 13485 MB
2025-07-30 01:43:24,566 - INFO - node 9 : 12540 MB
2025-07-30 01:43:24,566 - INFO - node 10 : 10725 MB
2025-07-30 01:43:24,566 - INFO - node 11 : 13605 MB
2025-07-30 01:43:24,566 - INFO - node 12 : 12865 MB
2025-07-30 01:43:24,566 - INFO - node 13 : 10790 MB
2025-07-30 01:43:24,566 - INFO - node 14 : 14340 MB
2025-07-30 01:43:24,566 - INFO - node 15 : 13955 MB
2025-07-30 01:43:24,566 - INFO - node 16 : 12960 MB
2025-07-30 01:43:24,566 - INFO - node 17 : 12805 MB
2025-07-30 01:43:24,566 - INFO - node 18 : 14415 MB
2025-07-30 01:43:24,566 - INFO - node 19 : 13135 MB
2025-07-30 01:43:24,566 - INFO - node 20 : 14145 MB
2025-07-30 01:43:24,566 - INFO - node 21 : 12920 MB
2025-07-30 01:43:24,566 - INFO - node 22 : 11730 MB
2025-07-30 01:43:24,566 - INFO - node 23 : 12490 MB
2025-07-30 01:43:24,566 - INFO - node 24 : 9735 MB
2025-07-30 01:43:24,566 - INFO - node 25 : 11760 MB
2025-07-30 01:43:24,566 - INFO - node 26 : 12325 MB
2025-07-30 01:43:24,566 - INFO - node 27 : 14020 MB
2025-07-30 01:43:24,566 - INFO - node 28 : 10925 MB
2025-07-30 01:43:24,567 - INFO - node 29 : 13580 MB
2025-07-30 01:43:24,567 - INFO - node 30 : 11535 MB
2025-07-30 01:43:24,567 - INFO - node 31 : 10325 MB
2025-07-30 01:43:24,567 - INFO - node 32 : 11690 MB
2025-07-30 01:43:24,567 - INFO - node 33 : 13945 MB
2025-07-30 01:43:24,567 - INFO - node 34 : 14015 MB
2025-07-30 01:43:24,567 - INFO - node 35 : 11270 MB
2025-07-30 01:43:24,567 - INFO - node 36 : 13850 MB
2025-07-30 01:43:24,567 - INFO - node 37 : 12065 MB
2025-07-30 01:43:24,567 - INFO - node 38 : 9520 MB
2025-07-30 01:43:24,567 - INFO - node 39 : 7560 MB
2025-07-30 01:43:24,567 - INFO - node 40 : 12460 MB
2025-07-30 01:43:24,567 - INFO - node 41 : 13910 MB
2025-07-30 01:43:24,567 - INFO - node 42 : 7965 MB
2025-07-30 01:43:24,567 - INFO - node 43 : 10815 MB
2025-07-30 01:43:24,567 - INFO - node 44 : 10290 MB
2025-07-30 01:43:24,567 - INFO - node 45 : 12000 MB
2025-07-30 01:43:24,567 - INFO - node 46 : 14380 MB
2025-07-30 01:43:24,567 - INFO - node 47 : 10190 MB
2025-07-30 01:43:24,567 - INFO - node 48 : 8755 MB
2025-07-30 01:43:24,567 - INFO - node 49 : 13360 MB
2025-07-30 01:43:24,567 - INFO - node 50 : 12595 MB
2025-07-30 01:43:24,567 - INFO - node 51 : 9080 MB
2025-07-30 01:43:24,567 - INFO - node 52 : 11710 MB
2025-07-30 01:43:24,567 - INFO - node 53 : 13940 MB
2025-07-30 01:43:24,567 - INFO - node 54 : 13645 MB
2025-07-30 01:43:24,568 - INFO - node 55 : 12335 MB
2025-07-30 01:43:24,568 - INFO - node 56 : 14205 MB
2025-07-30 01:43:24,568 - INFO - node 57 : 12145 MB
2025-07-30 01:43:24,568 - INFO - node 58 : 7715 MB
2025-07-30 01:43:24,568 - INFO - node 59 : 8405 MB
2025-07-30 01:43:24,568 - INFO - node 60 : 14765 MB
2025-07-30 01:43:24,568 - INFO - node 61 : 8330 MB
2025-07-30 01:43:24,568 - INFO - node 62 : 12570 MB
2025-07-30 01:43:24,568 - INFO - node 63 : 11395 MB
2025-07-30 01:43:24,568 - INFO - node 64 : 12675 MB
2025-07-30 01:43:24,568 - INFO - node 65 : 6290 MB
2025-07-30 01:43:24,568 - INFO - node 66 : 11505 MB
2025-07-30 01:43:24,568 - INFO - node 67 : 13905 MB
2025-07-30 01:43:24,568 - INFO - node 68 : 14850 MB
2025-07-30 01:43:24,568 - INFO - node 69 : 6550 MB
2025-07-30 01:43:24,568 - INFO - node 70 : 14490 MB
2025-07-30 01:43:24,568 - INFO - node 71 : 12290 MB
2025-07-30 01:43:24,568 - INFO - node 72 : 12870 MB
2025-07-30 01:43:24,568 - INFO - node 73 : 13615 MB
2025-07-30 01:43:24,568 - INFO - node 74 : 8445 MB
2025-07-30 01:43:24,568 - INFO - node 75 : 11070 MB
2025-07-30 01:43:24,568 - INFO - node 76 : 11645 MB
2025-07-30 01:43:24,568 - INFO - node 77 : 7085 MB
2025-07-30 01:43:24,568 - INFO - node 78 : 14645 MB
2025-07-30 01:43:24,568 - INFO - node 79 : 8460 MB
2025-07-30 01:43:24,568 - INFO - node 80 : 10050 MB
2025-07-30 01:43:24,568 - INFO - node 81 : 8025 MB
2025-07-30 01:43:24,568 - INFO - node 82 : 13340 MB
2025-07-30 01:43:24,568 - INFO - node 83 : 14215 MB
2025-07-30 01:43:24,568 - INFO - node 84 : 11235 MB
2025-07-30 01:43:24,568 - INFO - node 85 : 7350 MB
2025-07-30 01:43:24,568 - INFO - node 86 : 10110 MB
2025-07-30 01:43:24,568 - INFO - node 87 : 8825 MB
2025-07-30 01:43:24,568 - INFO - node 88 : 9705 MB
2025-07-30 01:43:24,568 - INFO - node 89 : 4580 MB
2025-07-30 01:43:24,568 - INFO - node 90 : 7355 MB
2025-07-30 01:43:24,568 - INFO - node 91 : 13625 MB
2025-07-30 01:43:24,568 - INFO - node 92 : 13035 MB
2025-07-30 01:43:24,568 - INFO - node 93 : 13120 MB
2025-07-30 01:43:24,568 - INFO - node 94 : 10760 MB
2025-07-30 01:43:24,568 - INFO - node 95 : 10700 MB
2025-07-30 01:43:24,568 - INFO - node 96 : 7885 MB
2025-07-30 01:43:24,568 - INFO - node 97 : 12655 MB
2025-07-30 01:43:24,568 - INFO - node 98 : 7080 MB
2025-07-30 01:43:24,568 - INFO - node 99 : 14145 MB
2025-07-30 01:43:24,568 - INFO - node 100 : 12725 MB
2025-07-30 01:43:24,568 - INFO - node 101 : 11080 MB
2025-07-30 01:43:24,568 - INFO - node 102 : 12035 MB
2025-07-30 01:43:24,568 - INFO - node 103 : 13350 MB
2025-07-30 01:43:24,568 - INFO - node 104 : 14075 MB
2025-07-30 01:43:24,568 - INFO - node 105 : 13920 MB
2025-07-30 01:43:24,568 - INFO - node 106 : 11810 MB
2025-07-30 01:43:24,568 - INFO - node 107 : 8590 MB
2025-07-30 01:43:24,568 - INFO - node 108 : 14765 MB
2025-07-30 01:43:24,568 - INFO - node 109 : 10655 MB
2025-07-30 01:43:24,568 - INFO - node 110 : 11465 MB
2025-07-30 01:43:24,568 - INFO - node 111 : 6730 MB
2025-07-30 01:43:24,568 - INFO - node 112 : 9410 MB
2025-07-30 01:43:24,568 - INFO - node 113 : 11165 MB
2025-07-30 01:43:24,568 - INFO - node 114 : 5965 MB
2025-07-30 01:43:24,568 - INFO - node 115 : 10690 MB
2025-07-30 01:43:24,568 - INFO - node 116 : 7195 MB
2025-07-30 01:43:24,568 - INFO - node 117 : 13530 MB
2025-07-30 01:43:24,568 - INFO - node 118 : 13745 MB
2025-07-30 01:43:24,568 - INFO - node 119 : 10600 MB
2025-07-30 01:43:24,568 - INFO - node 120 : 13770 MB
2025-07-30 01:43:24,568 - INFO - node 121 : 13870 MB
2025-07-30 01:43:24,568 - INFO - node 122 : 13860 MB
2025-07-30 01:43:24,568 - INFO - node 123 : 13910 MB
2025-07-30 01:43:24,568 - INFO - node 124 : 11840 MB
2025-07-30 01:43:24,568 - INFO - node 125 : 14425 MB
2025-07-30 01:43:24,569 - INFO -   秒 0: 处理 109 个新请求
2025-07-30 01:43:24,593 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:24,593 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:24,599 - INFO - 时间槽 15，秒 0: 执行中容器 183，等待请求 0
2025-07-30 01:43:24,600 - INFO -   秒 1: 处理 5 个新请求
2025-07-30 01:43:24,606 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:24,606 - INFO -   秒 2: 处理 4 个新请求
2025-07-30 01:43:24,611 - INFO -   秒 3: 处理 16 个新请求
2025-07-30 01:43:24,619 - INFO -   秒 4: 处理 13 个新请求
2025-07-30 01:43:24,621 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 7885MB, 函数类型: 2
2025-07-30 01:43:24,621 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:24,623 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:43:24,623 - INFO -   秒 5: 处理 17 个新请求
2025-07-30 01:43:24,631 - INFO -   秒 6: 处理 14 个新请求
2025-07-30 01:43:24,639 - INFO -   秒 7: 处理 13 个新请求
2025-07-30 01:43:24,640 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:24,640 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:24,644 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:24,645 - INFO -   秒 8: 处理 13 个新请求
2025-07-30 01:43:24,651 - INFO -   秒 9: 处理 11 个新请求
2025-07-30 01:43:24,657 - INFO -   秒 10: 处理 18 个新请求
2025-07-30 01:43:24,665 - INFO - 时间槽 15，秒 10: 执行中容器 113，等待请求 0
2025-07-30 01:43:24,665 - INFO -   秒 11: 处理 13 个新请求
2025-07-30 01:43:24,670 - INFO -   秒 12: 处理 17 个新请求
2025-07-30 01:43:24,675 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 7885MB, 函数类型: 7
2025-07-30 01:43:24,675 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:43:24,678 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 120MB，类型: 7
2025-07-30 01:43:24,679 - INFO -   秒 13: 处理 12 个新请求
2025-07-30 01:43:24,687 - INFO -   秒 14: 处理 15 个新请求
2025-07-30 01:43:24,695 - INFO -   秒 15: 处理 12 个新请求
2025-07-30 01:43:24,702 - INFO -   秒 16: 处理 15 个新请求
2025-07-30 01:43:24,705 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7885MB, 函数类型: 9
2025-07-30 01:43:24,705 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:24,709 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:43:24,710 - INFO -   秒 17: 处理 14 个新请求
2025-07-30 01:43:24,717 - INFO -   秒 18: 处理 9 个新请求
2025-07-30 01:43:24,722 - INFO -   秒 19: 处理 18 个新请求
2025-07-30 01:43:24,728 - INFO -   秒 20: 处理 25 个新请求
2025-07-30 01:43:24,734 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 7885MB, 函数类型: 6
2025-07-30 01:43:24,734 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:43:24,737 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 70MB，类型: 6
2025-07-30 01:43:24,738 - INFO - 时间槽 15，秒 20: 执行中容器 126，等待请求 0
2025-07-30 01:43:24,738 - INFO -   秒 21: 处理 9 个新请求
2025-07-30 01:43:24,745 - INFO -   秒 22: 处理 11 个新请求
2025-07-30 01:43:24,753 - INFO -   秒 23: 处理 18 个新请求
2025-07-30 01:43:24,760 - INFO -   秒 24: 处理 13 个新请求
2025-07-30 01:43:24,762 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 7885MB, 函数类型: 4
2025-07-30 01:43:24,763 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:24,767 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:43:24,767 - INFO -   秒 25: 处理 12 个新请求
2025-07-30 01:43:24,772 - INFO -   秒 26: 处理 18 个新请求
2025-07-30 01:43:24,780 - INFO -   秒 27: 处理 7 个新请求
2025-07-30 01:43:24,783 - INFO -   秒 28: 处理 11 个新请求
2025-07-30 01:43:24,785 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:24,785 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:24,789 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:24,789 - INFO -   秒 29: 处理 23 个新请求
2025-07-30 01:43:24,799 - INFO -   秒 30: 处理 17 个新请求
2025-07-30 01:43:24,806 - INFO - 时间槽 15，秒 30: 执行中容器 120，等待请求 0
2025-07-30 01:43:24,806 - INFO -   秒 31: 处理 8 个新请求
2025-07-30 01:43:24,812 - INFO -   秒 32: 处理 20 个新请求
2025-07-30 01:43:24,818 - INFO - DEBUG: 节点96内存检查 - 需要内存: 360.0MB, 可用内存: 7885MB, 函数类型: 5
2025-07-30 01:43:24,818 - INFO - DEBUG: 节点96创建User层容器，减少内存 360MB，类型: 5
2025-07-30 01:43:24,821 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 360MB，类型: 5
2025-07-30 01:43:24,822 - INFO -   秒 33: 处理 7 个新请求
2025-07-30 01:43:24,826 - INFO -   秒 34: 处理 13 个新请求
2025-07-30 01:43:24,836 - INFO -   秒 35: 处理 18 个新请求
2025-07-30 01:43:24,843 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7885MB, 函数类型: 1
2025-07-30 01:43:24,843 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:43:24,846 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:43:24,846 - INFO -   秒 36: 处理 14 个新请求
2025-07-30 01:43:24,855 - INFO -   秒 37: 处理 12 个新请求
2025-07-30 01:43:24,862 - INFO -   秒 38: 处理 15 个新请求
2025-07-30 01:43:24,870 - INFO -   秒 39: 处理 18 个新请求
2025-07-30 01:43:24,876 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 7885MB, 函数类型: 2
2025-07-30 01:43:24,876 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:24,882 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:43:24,883 - INFO -   秒 40: 处理 16 个新请求
2025-07-30 01:43:24,891 - INFO - 时间槽 15，秒 40: 执行中容器 119，等待请求 0
2025-07-30 01:43:24,892 - INFO -   秒 41: 处理 15 个新请求
2025-07-30 01:43:24,898 - INFO -   秒 42: 处理 9 个新请求
2025-07-30 01:43:24,906 - INFO -   秒 43: 处理 12 个新请求
2025-07-30 01:43:24,909 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 7885MB, 函数类型: 2
2025-07-30 01:43:24,909 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:43:24,912 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:43:24,913 - INFO -   秒 44: 处理 18 个新请求
2025-07-30 01:43:24,923 - INFO -   秒 45: 处理 13 个新请求
2025-07-30 01:43:24,929 - INFO -   秒 46: 处理 8 个新请求
2025-07-30 01:43:24,936 - INFO -   秒 47: 处理 19 个新请求
2025-07-30 01:43:24,940 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 7885MB, 函数类型: 6
2025-07-30 01:43:24,940 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:43:24,945 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 70MB，类型: 6
2025-07-30 01:43:24,945 - INFO -   秒 48: 处理 17 个新请求
2025-07-30 01:43:24,951 - INFO -   秒 49: 处理 18 个新请求
2025-07-30 01:43:24,958 - INFO -   秒 50: 处理 10 个新请求
2025-07-30 01:43:24,965 - INFO - 时间槽 15，秒 50: 执行中容器 120，等待请求 0
2025-07-30 01:43:24,966 - INFO -   秒 51: 处理 11 个新请求
2025-07-30 01:43:24,968 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 7885MB, 函数类型: 4
2025-07-30 01:43:24,968 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:43:24,971 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:43:24,972 - INFO -   秒 52: 处理 13 个新请求
2025-07-30 01:43:24,978 - INFO -   秒 53: 处理 14 个新请求
2025-07-30 01:43:24,985 - INFO -   秒 54: 处理 14 个新请求
2025-07-30 01:43:24,992 - INFO -   秒 55: 处理 11 个新请求
2025-07-30 01:43:24,994 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7885MB, 函数类型: 9
2025-07-30 01:43:24,994 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:43:24,998 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:43:24,998 - INFO -   秒 56: 处理 7 个新请求
2025-07-30 01:43:25,012 - INFO - ----node memomry----
2025-07-30 01:43:25,012 - INFO - node 1 : 14565 MB
2025-07-30 01:43:25,012 - INFO - node 2 : 14320 MB
2025-07-30 01:43:25,013 - INFO - node 3 : 11065 MB
2025-07-30 01:43:25,013 - INFO - node 4 : 13815 MB
2025-07-30 01:43:25,013 - INFO - node 5 : 12310 MB
2025-07-30 01:43:25,013 - INFO - node 6 : 14275 MB
2025-07-30 01:43:25,013 - INFO - node 7 : 14495 MB
2025-07-30 01:43:25,013 - INFO - node 8 : 13485 MB
2025-07-30 01:43:25,013 - INFO - node 9 : 12540 MB
2025-07-30 01:43:25,013 - INFO - node 10 : 10725 MB
2025-07-30 01:43:25,013 - INFO - node 11 : 13605 MB
2025-07-30 01:43:25,013 - INFO - node 12 : 12865 MB
2025-07-30 01:43:25,013 - INFO - node 13 : 10790 MB
2025-07-30 01:43:25,013 - INFO - node 14 : 14340 MB
2025-07-30 01:43:25,013 - INFO - node 15 : 13955 MB
2025-07-30 01:43:25,013 - INFO - node 16 : 12960 MB
2025-07-30 01:43:25,013 - INFO - node 17 : 12805 MB
2025-07-30 01:43:25,013 - INFO - node 18 : 14415 MB
2025-07-30 01:43:25,013 - INFO - node 19 : 13135 MB
2025-07-30 01:43:25,013 - INFO - node 20 : 14145 MB
2025-07-30 01:43:25,013 - INFO - node 21 : 12920 MB
2025-07-30 01:43:25,013 - INFO - node 22 : 11730 MB
2025-07-30 01:43:25,013 - INFO - node 23 : 12490 MB
2025-07-30 01:43:25,013 - INFO - node 24 : 9735 MB
2025-07-30 01:43:25,013 - INFO - node 25 : 11760 MB
2025-07-30 01:43:25,013 - INFO - node 26 : 12325 MB
2025-07-30 01:43:25,013 - INFO - node 27 : 14020 MB
2025-07-30 01:43:25,013 - INFO - node 28 : 10925 MB
2025-07-30 01:43:25,013 - INFO - node 29 : 13580 MB
2025-07-30 01:43:25,013 - INFO - node 30 : 11535 MB
2025-07-30 01:43:25,013 - INFO - node 31 : 10325 MB
2025-07-30 01:43:25,013 - INFO - node 32 : 11690 MB
2025-07-30 01:43:25,013 - INFO - node 33 : 13945 MB
2025-07-30 01:43:25,015 - INFO - node 34 : 14015 MB
2025-07-30 01:43:25,015 - INFO - node 35 : 11270 MB
2025-07-30 01:43:25,015 - INFO - node 36 : 13850 MB
2025-07-30 01:43:25,015 - INFO - node 37 : 12065 MB
2025-07-30 01:43:25,015 - INFO - node 38 : 9520 MB
2025-07-30 01:43:25,015 - INFO - node 39 : 7560 MB
2025-07-30 01:43:25,015 - INFO - node 40 : 12460 MB
2025-07-30 01:43:25,015 - INFO - node 41 : 13910 MB
2025-07-30 01:43:25,015 - INFO - node 42 : 7965 MB
2025-07-30 01:43:25,015 - INFO - node 43 : 10815 MB
2025-07-30 01:43:25,015 - INFO - node 44 : 10290 MB
2025-07-30 01:43:25,015 - INFO - node 45 : 12000 MB
2025-07-30 01:43:25,015 - INFO - node 46 : 14380 MB
2025-07-30 01:43:25,015 - INFO - node 47 : 10190 MB
2025-07-30 01:43:25,015 - INFO - node 48 : 8755 MB
2025-07-30 01:43:25,015 - INFO - node 49 : 13565 MB
2025-07-30 01:43:25,015 - INFO - node 50 : 12595 MB
2025-07-30 01:43:25,015 - INFO - node 51 : 9080 MB
2025-07-30 01:43:25,015 - INFO - node 52 : 11710 MB
2025-07-30 01:43:25,015 - INFO - node 53 : 13940 MB
2025-07-30 01:43:25,015 - INFO - node 54 : 13850 MB
2025-07-30 01:43:25,015 - INFO - node 55 : 12335 MB
2025-07-30 01:43:25,015 - INFO - node 56 : 14205 MB
2025-07-30 01:43:25,015 - INFO - node 57 : 12145 MB
2025-07-30 01:43:25,016 - INFO - node 58 : 7715 MB
2025-07-30 01:43:25,016 - INFO - node 59 : 8405 MB
2025-07-30 01:43:25,016 - INFO - node 60 : 14765 MB
2025-07-30 01:43:25,016 - INFO - node 61 : 8330 MB
2025-07-30 01:43:25,016 - INFO - node 62 : 12570 MB
2025-07-30 01:43:25,016 - INFO - node 63 : 11395 MB
2025-07-30 01:43:25,016 - INFO - node 64 : 12675 MB
2025-07-30 01:43:25,016 - INFO - node 65 : 6290 MB
2025-07-30 01:43:25,016 - INFO - node 66 : 11505 MB
2025-07-30 01:43:25,016 - INFO - node 67 : 13905 MB
2025-07-30 01:43:25,016 - INFO - node 68 : 14850 MB
2025-07-30 01:43:25,016 - INFO - node 69 : 6550 MB
2025-07-30 01:43:25,016 - INFO - node 70 : 14490 MB
2025-07-30 01:43:25,016 - INFO - node 71 : 12290 MB
2025-07-30 01:43:25,016 - INFO - node 72 : 12870 MB
2025-07-30 01:43:25,016 - INFO - node 73 : 13615 MB
2025-07-30 01:43:25,016 - INFO - node 74 : 8445 MB
2025-07-30 01:43:25,016 - INFO - node 75 : 11070 MB
2025-07-30 01:43:25,016 - INFO - node 76 : 11645 MB
2025-07-30 01:43:25,016 - INFO - node 77 : 7085 MB
2025-07-30 01:43:25,016 - INFO - node 78 : 14560 MB
2025-07-30 01:43:25,016 - INFO - node 79 : 8460 MB
2025-07-30 01:43:25,016 - INFO - node 80 : 10050 MB
2025-07-30 01:43:25,016 - INFO - node 81 : 8025 MB
2025-07-30 01:43:25,016 - INFO - node 82 : 13340 MB
2025-07-30 01:43:25,016 - INFO - node 83 : 14215 MB
2025-07-30 01:43:25,016 - INFO - node 84 : 11235 MB
2025-07-30 01:43:25,016 - INFO - node 85 : 7350 MB
2025-07-30 01:43:25,016 - INFO - node 86 : 10110 MB
2025-07-30 01:43:25,016 - INFO - node 87 : 8825 MB
2025-07-30 01:43:25,016 - INFO - node 88 : 9705 MB
2025-07-30 01:43:25,016 - INFO - node 89 : 4580 MB
2025-07-30 01:43:25,016 - INFO - node 90 : 7355 MB
2025-07-30 01:43:25,016 - INFO - node 91 : 13625 MB
2025-07-30 01:43:25,016 - INFO - node 92 : 13035 MB
2025-07-30 01:43:25,016 - INFO - node 93 : 13120 MB
2025-07-30 01:43:25,016 - INFO - node 94 : 10760 MB
2025-07-30 01:43:25,017 - INFO - node 95 : 10700 MB
2025-07-30 01:43:25,017 - INFO - node 96 : 7885 MB
2025-07-30 01:43:25,017 - INFO - node 97 : 12655 MB
2025-07-30 01:43:25,017 - INFO - node 98 : 7080 MB
2025-07-30 01:43:25,017 - INFO - node 99 : 14145 MB
2025-07-30 01:43:25,017 - INFO - node 100 : 12725 MB
2025-07-30 01:43:25,017 - INFO - node 101 : 11080 MB
2025-07-30 01:43:25,017 - INFO - node 102 : 12035 MB
2025-07-30 01:43:25,017 - INFO - node 103 : 13350 MB
2025-07-30 01:43:25,017 - INFO - node 104 : 14075 MB
2025-07-30 01:43:25,017 - INFO - node 105 : 13920 MB
2025-07-30 01:43:25,017 - INFO - node 106 : 11810 MB
2025-07-30 01:43:25,017 - INFO - node 107 : 8590 MB
2025-07-30 01:43:25,017 - INFO - node 108 : 14765 MB
2025-07-30 01:43:25,017 - INFO - node 109 : 10655 MB
2025-07-30 01:43:25,017 - INFO - node 110 : 11465 MB
2025-07-30 01:43:25,017 - INFO - node 111 : 6730 MB
2025-07-30 01:43:25,017 - INFO - node 112 : 9410 MB
2025-07-30 01:43:25,017 - INFO - node 113 : 11165 MB
2025-07-30 01:43:25,017 - INFO - node 114 : 5965 MB
2025-07-30 01:43:25,018 - INFO - node 115 : 10690 MB
2025-07-30 01:43:25,018 - INFO - node 116 : 7195 MB
2025-07-30 01:43:25,018 - INFO - node 117 : 13530 MB
2025-07-30 01:43:25,018 - INFO - node 118 : 13745 MB
2025-07-30 01:43:25,018 - INFO - node 119 : 10600 MB
2025-07-30 01:43:25,018 - INFO - node 120 : 13770 MB
2025-07-30 01:43:25,018 - INFO - node 121 : 13870 MB
2025-07-30 01:43:25,018 - INFO - node 122 : 13945 MB
2025-07-30 01:43:25,018 - INFO - node 123 : 13910 MB
2025-07-30 01:43:25,018 - INFO - node 124 : 11840 MB
2025-07-30 01:43:25,018 - INFO - node 125 : 14425 MB
2025-07-30 01:43:25,018 - INFO - -------- Schedule End --------
2025-07-30 01:43:25,018 - INFO - deploy_current_req_G: 95
2025-07-30 01:43:25,018 - INFO - deploy_neighbor_req_G: 4383
2025-07-30 01:43:25,018 - INFO - create_current_req_G: 8942
2025-07-30 01:43:25,018 - INFO - wait_count_G: 0
2025-07-30 01:43:25,018 - INFO - ------------------------------
2025-07-30 01:43:25,037 - INFO - total_req_count_G: 13420
2025-07-30 01:43:25,037 - INFO - served_req_count_G: 13420
2025-07-30 01:43:25,037 - INFO - unserved_req_count: 0
2025-07-30 01:43:25,037 - INFO - waiting_queue_total: 0
2025-07-30 01:43:25,038 - INFO - waiting_queue_served: 0
2025-07-30 01:43:25,038 - INFO - cold_req_count_G: 8942
2025-07-30 01:43:25,038 - INFO - cold_start_frequency: 0.67
2025-07-30 01:43:25,038 - INFO - ------------------------------
2025-07-30 01:43:25,039 - INFO - avg_instan_cost: 1.93
2025-07-30 01:43:25,039 - INFO - avg_run_cost: 0.10
2025-07-30 01:43:25,039 - INFO - total_cost_G: 27223.40
2025-07-30 01:43:25,039 - INFO - avg_cost: 2.03
2025-07-30 01:43:25,039 - INFO - ------------------------------
2025-07-30 01:43:25,039 - INFO - total_response_time_G: 20766.87s
2025-07-30 01:43:25,039 - INFO - avg_response_time: 1.55s
2025-07-30 01:43:25,039 - INFO - total_wait_time_G: 0.00s
2025-07-30 01:43:25,040 - INFO - avg_wait_time: 0.00s
2025-07-30 01:43:25,040 - INFO - ------------------------------
2025-07-30 01:43:25,040 - INFO - new_total_memory_G: 775190MB
2025-07-30 01:43:25,040 - INFO - avg_total_memory: 80.05MB
2025-07-30 01:43:25,127 - INFO - 传输延迟更高：111882
2025-07-30 01:43:25,127 - INFO - 创建延迟更高：1177623
