2025-07-30 01:28:30,252 - INFO - topo_file: ./data/topo/site-optus-melbCBD.csv
2025-07-30 01:28:30,252 - INFO - node_num: 125, mem_cap: 11000, cpu_Freq: 3
2025-07-30 01:28:30,252 - INFO - latency_para: 1, redu_factor: 30, slot_num: 30
2025-07-30 01:28:30,252 - INFO - cache_method: LayerCache, beta: 0.5, alpha: 0.015
2025-07-30 01:28:30,252 - INFO - open topo file succeessfully.....
2025-07-30 01:28:30,252 - INFO - node 1 lat -37.815170 long 144.974760
2025-07-30 01:28:30,253 - INFO - node 2 lat -37.815240 long 144.952560
2025-07-30 01:28:30,253 - INFO - node 3 lat -37.812390 long 144.971200
2025-07-30 01:28:30,253 - INFO - node 4 lat -37.816790 long 144.969180
2025-07-30 01:28:30,253 - INFO - node 5 lat -37.818080 long 144.956920
2025-07-30 01:28:30,253 - INFO - node 6 lat -37.811269 long 144.957909
2025-07-30 01:28:30,253 - INFO - node 7 lat -37.809081 long 144.968930
2025-07-30 01:28:30,254 - INFO - node 8 lat -37.815461 long 144.962656
2025-07-30 01:28:30,254 - INFO - node 9 lat -37.812850 long 144.955026
2025-07-30 01:28:30,254 - INFO - node 10 lat -37.816356 long 144.962313
2025-07-30 01:28:30,254 - INFO - node 11 lat -37.818071 long 144.957211
2025-07-30 01:28:30,254 - INFO - node 12 lat -37.818202 long 144.954367
2025-07-30 01:28:30,254 - INFO - node 13 lat -37.814225 long 144.971971
2025-07-30 01:28:30,254 - INFO - node 14 lat -37.815975 long 144.955905
2025-07-30 01:28:30,254 - INFO - node 15 lat -37.815493 long 144.956714
2025-07-30 01:28:30,254 - INFO - node 16 lat -37.818520 long 144.957141
2025-07-30 01:28:30,255 - INFO - node 17 lat -37.817303 long 144.962344
2025-07-30 01:28:30,255 - INFO - node 18 lat -37.818162 long 144.959934
2025-07-30 01:28:30,255 - INFO - node 19 lat -37.813381 long 144.959612
2025-07-30 01:28:30,255 - INFO - node 20 lat -37.812084 long 144.967997
2025-07-30 01:28:30,255 - INFO - node 21 lat -37.818494 long 144.957705
2025-07-30 01:28:30,255 - INFO - node 22 lat -37.812122 long 144.970710
2025-07-30 01:28:30,255 - INFO - node 23 lat -37.811625 long 144.964919
2025-07-30 01:28:30,255 - INFO - node 24 lat -37.814263 long 144.972105
2025-07-30 01:28:30,255 - INFO - node 25 lat -37.815549 long 144.966686
2025-07-30 01:28:30,255 - INFO - node 26 lat -37.818166 long 144.964831
2025-07-30 01:28:30,255 - INFO - node 27 lat -37.817384 long 144.959217
2025-07-30 01:28:30,255 - INFO - node 28 lat -37.814896 long 144.971668
2025-07-30 01:28:30,255 - INFO - node 29 lat -37.811126 long 144.962116
2025-07-30 01:28:30,256 - INFO - node 30 lat -37.818656 long 144.956626
2025-07-30 01:28:30,256 - INFO - node 31 lat -37.817261 long 144.962515
2025-07-30 01:28:30,256 - INFO - node 32 lat -37.812845 long 144.968248
2025-07-30 01:28:30,256 - INFO - node 33 lat -37.811978 long 144.962388
2025-07-30 01:28:30,256 - INFO - node 34 lat -37.812723 long 144.960164
2025-07-30 01:28:30,256 - INFO - node 35 lat -37.813463 long 144.973217
2025-07-30 01:28:30,256 - INFO - node 36 lat -37.810996 long 144.967095
2025-07-30 01:28:30,256 - INFO - node 37 lat -37.814989 long 144.960908
2025-07-30 01:28:30,256 - INFO - node 38 lat -37.820910 long 144.955155
2025-07-30 01:28:30,256 - INFO - node 39 lat -37.820123 long 144.957552
2025-07-30 01:28:30,256 - INFO - node 40 lat -37.812899 long 144.959886
2025-07-30 01:28:30,256 - INFO - node 41 lat -37.811678 long 144.962783
2025-07-30 01:28:30,256 - INFO - node 42 lat -37.819576 long 144.959850
2025-07-30 01:28:30,256 - INFO - node 43 lat -37.817069 long 144.961828
2025-07-30 01:28:30,256 - INFO - node 44 lat -37.818218 long 144.960705
2025-07-30 01:28:30,257 - INFO - node 45 lat -37.809885 long 144.963286
2025-07-30 01:28:30,257 - INFO - node 46 lat -37.815196 long 144.962970
2025-07-30 01:28:30,257 - INFO - node 47 lat -37.812799 long 144.954686
2025-07-30 01:28:30,257 - INFO - node 48 lat -37.811928 long 144.956414
2025-07-30 01:28:30,257 - INFO - node 49 lat -37.816011 long 144.972058
2025-07-30 01:28:30,257 - INFO - node 50 lat -37.814431 long 144.954653
2025-07-30 01:28:30,257 - INFO - node 51 lat -37.810382 long 144.971223
2025-07-30 01:28:30,257 - INFO - node 52 lat -37.816467 long 144.958334
2025-07-30 01:28:30,257 - INFO - node 53 lat -37.815777 long 144.970508
2025-07-30 01:28:30,257 - INFO - node 54 lat -37.812578 long 144.965314
2025-07-30 01:28:30,257 - INFO - node 55 lat -37.816070 long 144.957254
2025-07-30 01:28:30,257 - INFO - node 56 lat -37.814398 long 144.966320
2025-07-30 01:28:30,257 - INFO - node 57 lat -37.813963 long 144.957301
2025-07-30 01:28:30,258 - INFO - node 58 lat -37.816158 long 144.960706
2025-07-30 01:28:30,258 - INFO - node 59 lat -37.817779 long 144.955969
2025-07-30 01:28:30,258 - INFO - node 60 lat -37.818018 long 144.964596
2025-07-30 01:28:30,258 - INFO - node 61 lat -37.812857 long 144.968430
2025-07-30 01:28:30,258 - INFO - node 62 lat -37.811627 long 144.965056
2025-07-30 01:28:30,258 - INFO - node 63 lat -37.816122 long 144.964421
2025-07-30 01:28:30,258 - INFO - node 64 lat -37.813447 long 144.966028
2025-07-30 01:28:30,258 - INFO - node 65 lat -37.815488 long 144.966824
2025-07-30 01:28:30,258 - INFO - node 66 lat -37.814765 long 144.969286
2025-07-30 01:28:30,258 - INFO - node 67 lat -37.812088 long 144.970836
2025-07-30 01:28:30,258 - INFO - node 68 lat -37.816206 long 144.966634
2025-07-30 01:28:30,258 - INFO - node 69 lat -37.819471 long 144.960069
2025-07-30 01:28:30,258 - INFO - node 70 lat -37.810219 long 144.961800
2025-07-30 01:28:30,258 - INFO - node 71 lat -37.809538 long 144.964091
2025-07-30 01:28:30,258 - INFO - node 72 lat -37.812653 long 144.956622
2025-07-30 01:28:30,259 - INFO - node 73 lat -37.811251 long 144.964157
2025-07-30 01:28:30,259 - INFO - node 74 lat -37.813540 long 144.971421
2025-07-30 01:28:30,259 - INFO - node 75 lat -37.809969 long 144.970780
2025-07-30 01:28:30,259 - INFO - node 76 lat -37.809264 long 144.971708
2025-07-30 01:28:30,259 - INFO - node 77 lat -37.812711 long 144.959971
2025-07-30 01:28:30,259 - INFO - node 78 lat -37.816530 long 144.961967
2025-07-30 01:28:30,259 - INFO - node 79 lat -37.815387 long 144.968815
2025-07-30 01:28:30,259 - INFO - node 80 lat -37.817510 long 144.959237
2025-07-30 01:28:30,259 - INFO - node 81 lat -37.814257 long 144.963370
2025-07-30 01:28:30,259 - INFO - node 82 lat -37.809041 long 144.971828
2025-07-30 01:28:30,259 - INFO - node 83 lat -37.820744 long 144.955485
2025-07-30 01:28:30,259 - INFO - node 84 lat -37.817483 long 144.959445
2025-07-30 01:28:30,259 - INFO - node 85 lat -37.812934 long 144.952075
2025-07-30 01:28:30,259 - INFO - node 86 lat -37.812343 long 144.954135
2025-07-30 01:28:30,259 - INFO - node 87 lat -37.811533 long 144.972614
2025-07-30 01:28:30,259 - INFO - node 88 lat -37.813573 long 144.973404
2025-07-30 01:28:30,259 - INFO - node 89 lat -37.815793 long 144.958295
2025-07-30 01:28:30,259 - INFO - node 90 lat -37.818474 long 144.956805
2025-07-30 01:28:30,259 - INFO - node 91 lat -37.814395 long 144.963537
2025-07-30 01:28:30,259 - INFO - node 92 lat -37.815891 long 144.958861
2025-07-30 01:28:30,259 - INFO - node 93 lat -37.815195 long 144.974409
2025-07-30 01:28:30,260 - INFO - node 94 lat -37.815371 long 144.973076
2025-07-30 01:28:30,260 - INFO - node 95 lat -37.818151 long 144.960810
2025-07-30 01:28:30,260 - INFO - node 96 lat -37.816299 long 144.959020
2025-07-30 01:28:30,260 - INFO - node 97 lat -37.814905 long 144.954913
2025-07-30 01:28:30,260 - INFO - node 98 lat -37.816209 long 144.961635
2025-07-30 01:28:30,260 - INFO - node 99 lat -37.812379 long 144.956402
2025-07-30 01:28:30,260 - INFO - node 100 lat -37.816822 long 144.963209
2025-07-30 01:28:30,260 - INFO - node 101 lat -37.815309 long 144.969476
2025-07-30 01:28:30,260 - INFO - node 102 lat -37.809356 long 144.970729
2025-07-30 01:28:30,260 - INFO - node 103 lat -37.811313 long 144.972958
2025-07-30 01:28:30,260 - INFO - node 104 lat -37.810641 long 144.970534
2025-07-30 01:28:30,260 - INFO - node 105 lat -37.810911 long 144.962690
2025-07-30 01:28:30,261 - INFO - node 106 lat -37.810065 long 144.963281
2025-07-30 01:28:30,261 - INFO - node 107 lat -37.811398 long 144.972672
2025-07-30 01:28:30,261 - INFO - node 108 lat -37.816891 long 144.961957
2025-07-30 01:28:30,261 - INFO - node 109 lat -37.813545 long 144.971705
2025-07-30 01:28:30,261 - INFO - node 110 lat -37.814265 long 144.971685
2025-07-30 01:28:30,261 - INFO - node 111 lat -37.815992 long 144.961028
2025-07-30 01:28:30,261 - INFO - node 112 lat -37.814484 long 144.963500
2025-07-30 01:28:30,261 - INFO - node 113 lat -37.813167 long 144.965468
2025-07-30 01:28:30,261 - INFO - node 114 lat -37.819220 long 144.958485
2025-07-30 01:28:30,261 - INFO - node 115 lat -37.815858 long 144.954818
2025-07-30 01:28:30,261 - INFO - node 116 lat -37.816217 long 144.970928
2025-07-30 01:28:30,261 - INFO - node 117 lat -37.810269 long 144.969863
2025-07-30 01:28:30,261 - INFO - node 118 lat -37.819101 long 144.954229
2025-07-30 01:28:30,261 - INFO - node 119 lat -37.811022 long 144.959234
2025-07-30 01:28:30,261 - INFO - node 120 lat -37.812150 long 144.961955
2025-07-30 01:28:30,262 - INFO - node 121 lat -37.816496 long 144.965090
2025-07-30 01:28:30,262 - INFO - node 122 lat -37.815296 long 144.958369
2025-07-30 01:28:30,262 - INFO - node 123 lat -37.816310 long 144.964351
2025-07-30 01:28:30,262 - INFO - node 124 lat -37.817386 long 144.961230
2025-07-30 01:28:30,262 - INFO - node 125 lat -37.813175 long 144.952919
2025-07-30 01:28:30,262 - INFO - open request file succeessfully.....
2025-07-30 01:28:32,682 - INFO - --------Schedule Start--------
2025-07-30 01:28:32,683 - INFO - --------处理时间槽 1 的请求，共 915 个--------
2025-07-30 01:28:32,683 - INFO - ----node memomry----
2025-07-30 01:28:32,683 - INFO - node 1 : 11000 MB
2025-07-30 01:28:32,683 - INFO - node 2 : 11000 MB
2025-07-30 01:28:32,683 - INFO - node 3 : 11000 MB
2025-07-30 01:28:32,683 - INFO - node 4 : 11000 MB
2025-07-30 01:28:32,683 - INFO - node 5 : 11000 MB
2025-07-30 01:28:32,683 - INFO - node 6 : 11000 MB
2025-07-30 01:28:32,683 - INFO - node 7 : 11000 MB
2025-07-30 01:28:32,683 - INFO - node 8 : 11000 MB
2025-07-30 01:28:32,683 - INFO - node 9 : 11000 MB
2025-07-30 01:28:32,683 - INFO - node 10 : 11000 MB
2025-07-30 01:28:32,683 - INFO - node 11 : 11000 MB
2025-07-30 01:28:32,683 - INFO - node 12 : 11000 MB
2025-07-30 01:28:32,683 - INFO - node 13 : 11000 MB
2025-07-30 01:28:32,683 - INFO - node 14 : 11000 MB
2025-07-30 01:28:32,683 - INFO - node 15 : 11000 MB
2025-07-30 01:28:32,683 - INFO - node 16 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 17 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 18 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 19 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 20 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 21 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 22 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 23 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 24 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 25 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 26 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 27 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 28 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 29 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 30 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 31 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 32 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 33 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 34 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 35 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 36 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 37 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 38 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 39 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 40 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 41 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 42 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 43 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 44 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 45 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 46 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 47 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 48 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 49 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 50 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 51 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 52 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 53 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 54 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 55 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 56 : 11000 MB
2025-07-30 01:28:32,684 - INFO - node 57 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 58 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 59 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 60 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 61 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 62 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 63 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 64 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 65 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 66 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 67 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 68 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 69 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 70 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 71 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 72 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 73 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 74 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 75 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 76 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 77 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 78 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 79 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 80 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 81 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 82 : 11000 MB
2025-07-30 01:28:32,685 - INFO - node 83 : 11000 MB
2025-07-30 01:28:32,686 - INFO - node 84 : 11000 MB
2025-07-30 01:28:32,686 - INFO - node 85 : 11000 MB
2025-07-30 01:28:32,686 - INFO - node 86 : 11000 MB
2025-07-30 01:28:32,686 - INFO - node 87 : 11000 MB
2025-07-30 01:28:32,686 - INFO - node 88 : 11000 MB
2025-07-30 01:28:32,686 - INFO - node 89 : 11000 MB
2025-07-30 01:28:32,686 - INFO - node 90 : 11000 MB
2025-07-30 01:28:32,686 - INFO - node 91 : 11000 MB
2025-07-30 01:28:32,686 - INFO - node 92 : 11000 MB
2025-07-30 01:28:32,686 - INFO - node 93 : 11000 MB
2025-07-30 01:28:32,686 - INFO - node 94 : 11000 MB
2025-07-30 01:28:32,686 - INFO - node 95 : 11000 MB
2025-07-30 01:28:32,686 - INFO - node 96 : 11000 MB
2025-07-30 01:28:32,686 - INFO - node 97 : 11000 MB
2025-07-30 01:28:32,686 - INFO - node 98 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 99 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 100 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 101 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 102 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 103 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 104 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 105 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 106 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 107 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 108 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 109 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 110 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 111 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 112 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 113 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 114 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 115 : 11000 MB
2025-07-30 01:28:32,687 - INFO - node 116 : 11000 MB
2025-07-30 01:28:32,688 - INFO - node 117 : 11000 MB
2025-07-30 01:28:32,688 - INFO - node 118 : 11000 MB
2025-07-30 01:28:32,688 - INFO - node 119 : 11000 MB
2025-07-30 01:28:32,688 - INFO - node 120 : 11000 MB
2025-07-30 01:28:32,688 - INFO - node 121 : 11000 MB
2025-07-30 01:28:32,688 - INFO - node 122 : 11000 MB
2025-07-30 01:28:32,688 - INFO - node 123 : 11000 MB
2025-07-30 01:28:32,688 - INFO - node 124 : 11000 MB
2025-07-30 01:28:32,688 - INFO - node 125 : 11000 MB
2025-07-30 01:28:32,688 - INFO -   秒 0: 处理 110 个新请求
2025-07-30 01:28:32,714 - INFO - DEBUG: 节点96内存检查 - 需要内存: 190.0MB, 可用内存: 11000MB, 函数类型: 4
2025-07-30 01:28:32,714 - INFO - DEBUG: 节点96创建Bare层，减少内存 30MB，UUID: b0a9a800-9e9e-41cc-ae0e-489ada8192c3
2025-07-30 01:28:32,714 - INFO - DEBUG: 节点96创建Lang层，减少内存 120MB，UUID: 58b3d8fe-c3ce-40a4-94d4-b3a9e6dd9d1e
2025-07-30 01:28:32,714 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:32,722 - INFO - 时间槽 1，秒 0: 执行中容器 110，等待请求 0
2025-07-30 01:28:32,723 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:28:32,726 - INFO -   秒 2: 处理 3 个新请求
2025-07-30 01:28:32,727 - INFO -   秒 3: 处理 22 个新请求
2025-07-30 01:28:32,733 - INFO -   秒 4: 处理 10 个新请求
2025-07-30 01:28:32,734 - INFO -   秒 5: 处理 19 个新请求
2025-07-30 01:28:32,738 - INFO -   秒 6: 处理 15 个新请求
2025-07-30 01:28:32,743 - INFO -   秒 7: 处理 11 个新请求
2025-07-30 01:28:32,747 - INFO -   秒 8: 处理 10 个新请求
2025-07-30 01:28:32,750 - INFO -   秒 9: 处理 20 个新请求
2025-07-30 01:28:32,756 - INFO -   秒 10: 处理 15 个新请求
2025-07-30 01:28:32,760 - INFO - 时间槽 1，秒 10: 执行中容器 45，等待请求 0
2025-07-30 01:28:32,761 - INFO -   秒 11: 处理 19 个新请求
2025-07-30 01:28:32,767 - INFO -   秒 12: 处理 13 个新请求
2025-07-30 01:28:32,771 - INFO -   秒 13: 处理 11 个新请求
2025-07-30 01:28:32,775 - INFO -   秒 14: 处理 18 个新请求
2025-07-30 01:28:32,782 - INFO -   秒 15: 处理 19 个新请求
2025-07-30 01:28:32,790 - INFO -   秒 16: 处理 13 个新请求
2025-07-30 01:28:32,794 - INFO -   秒 17: 处理 13 个新请求
2025-07-30 01:28:32,797 - INFO -   秒 18: 处理 9 个新请求
2025-07-30 01:28:32,800 - INFO -   秒 19: 处理 26 个新请求
2025-07-30 01:28:32,807 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 10810MB, 函数类型: 1
2025-07-30 01:28:32,807 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:32,811 - INFO -   秒 20: 处理 20 个新请求
2025-07-30 01:28:32,817 - INFO - 时间槽 1，秒 20: 执行中容器 51，等待请求 0
2025-07-30 01:28:32,817 - INFO -   秒 21: 处理 6 个新请求
2025-07-30 01:28:32,818 - INFO -   秒 22: 处理 9 个新请求
2025-07-30 01:28:32,822 - INFO -   秒 23: 处理 22 个新请求
2025-07-30 01:28:32,825 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 10760MB, 函数类型: 4
2025-07-30 01:28:32,826 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:32,828 - INFO -   秒 24: 处理 17 个新请求
2025-07-30 01:28:32,833 - INFO -   秒 25: 处理 13 个新请求
2025-07-30 01:28:32,837 - INFO -   秒 26: 处理 8 个新请求
2025-07-30 01:28:32,842 - INFO -   秒 27: 处理 15 个新请求
2025-07-30 01:28:32,845 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 10720MB, 函数类型: 4
2025-07-30 01:28:32,845 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:32,848 - INFO -   秒 28: 处理 11 个新请求
2025-07-30 01:28:32,851 - INFO -   秒 29: 处理 26 个新请求
2025-07-30 01:28:32,857 - INFO -   秒 30: 处理 18 个新请求
2025-07-30 01:28:32,862 - INFO - 时间槽 1，秒 30: 执行中容器 54，等待请求 0
2025-07-30 01:28:32,862 - INFO -   秒 31: 处理 19 个新请求
2025-07-30 01:28:32,865 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 10680MB, 函数类型: 1
2025-07-30 01:28:32,866 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:32,867 - INFO -   秒 32: 处理 3 个新请求
2025-07-30 01:28:32,868 - INFO -   秒 33: 处理 14 个新请求
2025-07-30 01:28:32,872 - INFO -   秒 34: 处理 23 个新请求
2025-07-30 01:28:32,881 - INFO -   秒 35: 处理 15 个新请求
2025-07-30 01:28:32,887 - INFO -   秒 36: 处理 14 个新请求
2025-07-30 01:28:32,891 - INFO -   秒 37: 处理 8 个新请求
2025-07-30 01:28:32,894 - INFO -   秒 38: 处理 13 个新请求
2025-07-30 01:28:32,900 - INFO -   秒 39: 处理 25 个新请求
2025-07-30 01:28:32,909 - INFO -   秒 40: 处理 13 个新请求
2025-07-30 01:28:32,915 - INFO - 时间槽 1，秒 40: 执行中容器 49，等待请求 0
2025-07-30 01:28:32,916 - INFO -   秒 41: 处理 14 个新请求
2025-07-30 01:28:32,921 - INFO -   秒 42: 处理 10 个新请求
2025-07-30 01:28:32,923 - INFO -   秒 43: 处理 15 个新请求
2025-07-30 01:28:32,926 - INFO - DEBUG: 节点96内存检查 - 需要内存: 295.0MB, 可用内存: 10630MB, 函数类型: 9
2025-07-30 01:28:32,926 - INFO - DEBUG: 节点96创建Bare层，减少内存 30MB，UUID: 0b1458fb-0443-462e-8f29-977e3e8818c7
2025-07-30 01:28:32,926 - INFO - DEBUG: 节点96创建Lang层，减少内存 175MB，UUID: 3b3abc1d-9e81-4092-97a1-fc42cf441773
2025-07-30 01:28:32,926 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:32,931 - INFO -   秒 44: 处理 18 个新请求
2025-07-30 01:28:32,937 - INFO -   秒 45: 处理 13 个新请求
2025-07-30 01:28:32,940 - INFO -   秒 46: 处理 12 个新请求
2025-07-30 01:28:32,945 - INFO -   秒 47: 处理 19 个新请求
2025-07-30 01:28:32,952 - INFO - DEBUG: 节点96内存检查 - 需要内存: 155.0MB, 可用内存: 10335MB, 函数类型: 6
2025-07-30 01:28:32,952 - INFO - DEBUG: 节点96创建Bare层，减少内存 35MB，UUID: 2c897048-100f-4c62-bc48-7ee0d16756dc
2025-07-30 01:28:32,952 - INFO - DEBUG: 节点96创建Lang层，减少内存 50MB，UUID: b8d0cbcd-9df7-43f8-862b-e11bb0a397a4
2025-07-30 01:28:32,952 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:32,954 - INFO -   秒 48: 处理 15 个新请求
2025-07-30 01:28:32,957 - INFO -   秒 49: 处理 18 个新请求
2025-07-30 01:28:32,965 - INFO -   秒 50: 处理 13 个新请求
2025-07-30 01:28:32,967 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 10180MB, 函数类型: 1
2025-07-30 01:28:32,967 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:32,968 - INFO - 时间槽 1，秒 50: 执行中容器 47，等待请求 0
2025-07-30 01:28:32,968 - INFO -   秒 51: 处理 13 个新请求
2025-07-30 01:28:32,972 - INFO -   秒 52: 处理 11 个新请求
2025-07-30 01:28:32,975 - INFO -   秒 53: 处理 16 个新请求
2025-07-30 01:28:32,981 - INFO -   秒 54: 处理 11 个新请求
2025-07-30 01:28:32,983 - INFO -   秒 55: 处理 15 个新请求
2025-07-30 01:28:32,988 - INFO -   秒 56: 处理 10 个新请求
2025-07-30 01:28:32,996 - INFO - 时间槽 1 结束时，还有 9 个容器正在执行，将继续在后台执行
2025-07-30 01:28:32,997 - INFO - --------处理时间槽 2 的请求，共 915 个--------
2025-07-30 01:28:32,997 - INFO - ----node memomry----
2025-07-30 01:28:32,997 - INFO - node 1 : 11000 MB
2025-07-30 01:28:32,997 - INFO - node 2 : 10785 MB
2025-07-30 01:28:32,997 - INFO - node 3 : 9780 MB
2025-07-30 01:28:32,997 - INFO - node 4 : 10810 MB
2025-07-30 01:28:32,997 - INFO - node 5 : 9950 MB
2025-07-30 01:28:32,997 - INFO - node 6 : 10800 MB
2025-07-30 01:28:32,997 - INFO - node 7 : 10845 MB
2025-07-30 01:28:32,997 - INFO - node 8 : 10465 MB
2025-07-30 01:28:32,997 - INFO - node 9 : 10400 MB
2025-07-30 01:28:32,997 - INFO - node 10 : 10725 MB
2025-07-30 01:28:32,997 - INFO - node 11 : 10810 MB
2025-07-30 01:28:32,997 - INFO - node 12 : 10645 MB
2025-07-30 01:28:32,997 - INFO - node 13 : 9910 MB
2025-07-30 01:28:32,997 - INFO - node 14 : 10705 MB
2025-07-30 01:28:32,997 - INFO - node 15 : 10705 MB
2025-07-30 01:28:32,997 - INFO - node 16 : 10500 MB
2025-07-30 01:28:32,997 - INFO - node 17 : 10175 MB
2025-07-30 01:28:32,997 - INFO - node 18 : 11000 MB
2025-07-30 01:28:32,997 - INFO - node 19 : 10800 MB
2025-07-30 01:28:32,997 - INFO - node 20 : 10800 MB
2025-07-30 01:28:32,997 - INFO - node 21 : 10295 MB
2025-07-30 01:28:32,997 - INFO - node 22 : 10410 MB
2025-07-30 01:28:32,997 - INFO - node 23 : 10000 MB
2025-07-30 01:28:32,998 - INFO - node 24 : 10425 MB
2025-07-30 01:28:32,998 - INFO - node 25 : 10385 MB
2025-07-30 01:28:32,998 - INFO - node 26 : 10260 MB
2025-07-30 01:28:32,998 - INFO - node 27 : 10845 MB
2025-07-30 01:28:32,998 - INFO - node 28 : 10710 MB
2025-07-30 01:28:32,998 - INFO - node 29 : 10505 MB
2025-07-30 01:28:32,998 - INFO - node 30 : 10580 MB
2025-07-30 01:28:32,998 - INFO - node 31 : 9790 MB
2025-07-30 01:28:32,998 - INFO - node 32 : 10290 MB
2025-07-30 01:28:32,998 - INFO - node 33 : 10810 MB
2025-07-30 01:28:32,998 - INFO - node 34 : 10800 MB
2025-07-30 01:28:32,998 - INFO - node 35 : 10465 MB
2025-07-30 01:28:32,998 - INFO - node 36 : 10795 MB
2025-07-30 01:28:32,998 - INFO - node 37 : 10700 MB
2025-07-30 01:28:32,998 - INFO - node 38 : 10145 MB
2025-07-30 01:28:32,998 - INFO - node 39 : 9800 MB
2025-07-30 01:28:32,998 - INFO - node 40 : 10220 MB
2025-07-30 01:28:32,998 - INFO - node 41 : 10845 MB
2025-07-30 01:28:32,998 - INFO - node 42 : 10225 MB
2025-07-30 01:28:32,998 - INFO - node 43 : 10310 MB
2025-07-30 01:28:32,998 - INFO - node 44 : 10455 MB
2025-07-30 01:28:32,998 - INFO - node 45 : 10530 MB
2025-07-30 01:28:32,998 - INFO - node 46 : 11000 MB
2025-07-30 01:28:32,998 - INFO - node 47 : 9845 MB
2025-07-30 01:28:32,998 - INFO - node 48 : 10360 MB
2025-07-30 01:28:32,998 - INFO - node 49 : 10810 MB
2025-07-30 01:28:32,998 - INFO - node 50 : 10500 MB
2025-07-30 01:28:32,998 - INFO - node 51 : 10130 MB
2025-07-30 01:28:32,998 - INFO - node 52 : 10605 MB
2025-07-30 01:28:32,998 - INFO - node 53 : 10845 MB
2025-07-30 01:28:32,998 - INFO - node 54 : 10785 MB
2025-07-30 01:28:32,998 - INFO - node 55 : 10550 MB
2025-07-30 01:28:32,998 - INFO - node 56 : 10800 MB
2025-07-30 01:28:32,998 - INFO - node 57 : 10585 MB
2025-07-30 01:28:32,998 - INFO - node 58 : 10155 MB
2025-07-30 01:28:32,998 - INFO - node 59 : 10415 MB
2025-07-30 01:28:32,998 - INFO - node 60 : 11000 MB
2025-07-30 01:28:32,998 - INFO - node 61 : 9550 MB
2025-07-30 01:28:32,998 - INFO - node 62 : 10240 MB
2025-07-30 01:28:32,998 - INFO - node 63 : 10330 MB
2025-07-30 01:28:32,998 - INFO - node 64 : 10740 MB
2025-07-30 01:28:32,998 - INFO - node 65 : 9625 MB
2025-07-30 01:28:32,998 - INFO - node 66 : 10465 MB
2025-07-30 01:28:32,998 - INFO - node 67 : 10705 MB
2025-07-30 01:28:32,998 - INFO - node 68 : 11000 MB
2025-07-30 01:28:32,998 - INFO - node 69 : 9925 MB
2025-07-30 01:28:32,998 - INFO - node 70 : 11000 MB
2025-07-30 01:28:32,998 - INFO - node 71 : 10450 MB
2025-07-30 01:28:32,998 - INFO - node 72 : 10745 MB
2025-07-30 01:28:32,999 - INFO - node 73 : 10705 MB
2025-07-30 01:28:32,999 - INFO - node 74 : 9960 MB
2025-07-30 01:28:32,999 - INFO - node 75 : 10495 MB
2025-07-30 01:28:32,999 - INFO - node 76 : 10595 MB
2025-07-30 01:28:32,999 - INFO - node 77 : 10160 MB
2025-07-30 01:28:32,999 - INFO - node 78 : 11000 MB
2025-07-30 01:28:32,999 - INFO - node 79 : 9540 MB
2025-07-30 01:28:32,999 - INFO - node 80 : 10205 MB
2025-07-30 01:28:32,999 - INFO - node 81 : 9480 MB
2025-07-30 01:28:32,999 - INFO - node 82 : 10555 MB
2025-07-30 01:28:32,999 - INFO - node 83 : 11000 MB
2025-07-30 01:28:32,999 - INFO - node 84 : 10400 MB
2025-07-30 01:28:32,999 - INFO - node 85 : 9990 MB
2025-07-30 01:28:32,999 - INFO - node 86 : 10140 MB
2025-07-30 01:28:32,999 - INFO - node 87 : 10315 MB
2025-07-30 01:28:32,999 - INFO - node 88 : 10325 MB
2025-07-30 01:28:32,999 - INFO - node 89 : 9650 MB
2025-07-30 01:28:32,999 - INFO - node 90 : 9755 MB
2025-07-30 01:28:32,999 - INFO - node 91 : 10555 MB
2025-07-30 01:28:32,999 - INFO - node 92 : 10810 MB
2025-07-30 01:28:32,999 - INFO - node 93 : 10705 MB
2025-07-30 01:28:32,999 - INFO - node 94 : 10210 MB
2025-07-30 01:28:32,999 - INFO - node 95 : 10175 MB
2025-07-30 01:28:33,000 - INFO - node 96 : 10130 MB
2025-07-30 01:28:33,000 - INFO - node 97 : 10505 MB
2025-07-30 01:28:33,000 - INFO - node 98 : 9350 MB
2025-07-30 01:28:33,000 - INFO - node 99 : 10800 MB
2025-07-30 01:28:33,000 - INFO - node 100 : 10690 MB
2025-07-30 01:28:33,000 - INFO - node 101 : 10575 MB
2025-07-30 01:28:33,000 - INFO - node 102 : 10725 MB
2025-07-30 01:28:33,000 - INFO - node 103 : 10505 MB
2025-07-30 01:28:33,000 - INFO - node 104 : 10705 MB
2025-07-30 01:28:33,000 - INFO - node 105 : 10705 MB
2025-07-30 01:28:33,000 - INFO - node 106 : 10515 MB
2025-07-30 01:28:33,000 - INFO - node 107 : 10230 MB
2025-07-30 01:28:33,000 - INFO - node 108 : 11000 MB
2025-07-30 01:28:33,000 - INFO - node 109 : 10565 MB
2025-07-30 01:28:33,000 - INFO - node 110 : 10265 MB
2025-07-30 01:28:33,000 - INFO - node 111 : 9965 MB
2025-07-30 01:28:33,000 - INFO - node 112 : 10245 MB
2025-07-30 01:28:33,000 - INFO - node 113 : 9700 MB
2025-07-30 01:28:33,000 - INFO - node 114 : 10255 MB
2025-07-30 01:28:33,000 - INFO - node 115 : 10555 MB
2025-07-30 01:28:33,000 - INFO - node 116 : 9790 MB
2025-07-30 01:28:33,000 - INFO - node 117 : 10845 MB
2025-07-30 01:28:33,000 - INFO - node 118 : 10810 MB
2025-07-30 01:28:33,000 - INFO - node 119 : 10465 MB
2025-07-30 01:28:33,000 - INFO - node 120 : 10705 MB
2025-07-30 01:28:33,000 - INFO - node 121 : 10785 MB
2025-07-30 01:28:33,000 - INFO - node 122 : 10705 MB
2025-07-30 01:28:33,000 - INFO - node 123 : 11000 MB
2025-07-30 01:28:33,000 - INFO - node 124 : 10545 MB
2025-07-30 01:28:33,000 - INFO - node 125 : 11000 MB
2025-07-30 01:28:33,000 - INFO -   秒 0: 处理 110 个新请求
2025-07-30 01:28:33,029 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 10130MB, 函数类型: 4
2025-07-30 01:28:33,029 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:33,035 - INFO - 时间槽 2，秒 0: 执行中容器 119，等待请求 0
2025-07-30 01:28:33,035 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:28:33,042 - INFO -   秒 2: 处理 3 个新请求
2025-07-30 01:28:33,043 - INFO -   秒 3: 处理 20 个新请求
2025-07-30 01:28:33,048 - INFO -   秒 4: 处理 15 个新请求
2025-07-30 01:28:33,051 - INFO -   秒 5: 处理 14 个新请求
2025-07-30 01:28:33,055 - INFO -   秒 6: 处理 17 个新请求
2025-07-30 01:28:33,061 - INFO -   秒 7: 处理 10 个新请求
2025-07-30 01:28:33,067 - INFO -   秒 8: 处理 11 个新请求
2025-07-30 01:28:33,071 - INFO -   秒 9: 处理 22 个新请求
2025-07-30 01:28:33,076 - INFO -   秒 10: 处理 13 个新请求
2025-07-30 01:28:33,080 - INFO - 时间槽 2，秒 10: 执行中容器 53，等待请求 0
2025-07-30 01:28:33,080 - INFO -   秒 11: 处理 21 个新请求
2025-07-30 01:28:33,086 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 10090MB, 函数类型: 7
2025-07-30 01:28:33,087 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:28:33,090 - INFO -   秒 12: 处理 10 个新请求
2025-07-30 01:28:33,094 - INFO -   秒 13: 处理 11 个新请求
2025-07-30 01:28:33,099 - INFO -   秒 14: 处理 18 个新请求
2025-07-30 01:28:33,105 - INFO -   秒 15: 处理 18 个新请求
2025-07-30 01:28:33,112 - INFO -   秒 16: 处理 14 个新请求
2025-07-30 01:28:33,115 - INFO -   秒 17: 处理 10 个新请求
2025-07-30 01:28:33,119 - INFO -   秒 18: 处理 13 个新请求
2025-07-30 01:28:33,122 - INFO -   秒 19: 处理 26 个新请求
2025-07-30 01:28:33,127 - INFO - DEBUG: 节点96内存检查 - 需要内存: 360.0MB, 可用内存: 9970MB, 函数类型: 5
2025-07-30 01:28:33,128 - INFO - DEBUG: 节点96创建User层容器，减少内存 360MB，类型: 5
2025-07-30 01:28:33,130 - INFO -   秒 20: 处理 17 个新请求
2025-07-30 01:28:33,134 - INFO - 时间槽 2，秒 20: 执行中容器 66，等待请求 0
2025-07-30 01:28:33,134 - INFO -   秒 21: 处理 8 个新请求
2025-07-30 01:28:33,137 - INFO -   秒 22: 处理 14 个新请求
2025-07-30 01:28:33,140 - INFO -   秒 23: 处理 18 个新请求
2025-07-30 01:28:33,143 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 9610MB, 函数类型: 9
2025-07-30 01:28:33,143 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:33,145 - INFO -   秒 24: 处理 15 个新请求
2025-07-30 01:28:33,149 - INFO -   秒 25: 处理 13 个新请求
2025-07-30 01:28:33,154 - INFO -   秒 26: 处理 10 个新请求
2025-07-30 01:28:33,158 - INFO -   秒 27: 处理 13 个新请求
2025-07-30 01:28:33,162 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 9520MB, 函数类型: 1
2025-07-30 01:28:33,162 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:33,166 - INFO -   秒 28: 处理 14 个新请求
2025-07-30 01:28:33,171 - INFO -   秒 29: 处理 27 个新请求
2025-07-30 01:28:33,179 - INFO -   秒 30: 处理 18 个新请求
2025-07-30 01:28:33,185 - INFO - 时间槽 2，秒 30: 执行中容器 61，等待请求 0
2025-07-30 01:28:33,185 - INFO -   秒 31: 处理 15 个新请求
2025-07-30 01:28:33,188 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 9470MB, 函数类型: 9
2025-07-30 01:28:33,188 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:33,189 - INFO -   秒 32: 处理 9 个新请求
2025-07-30 01:28:33,193 - INFO -   秒 33: 处理 9 个新请求
2025-07-30 01:28:33,197 - INFO -   秒 34: 处理 18 个新请求
2025-07-30 01:28:33,202 - INFO -   秒 35: 处理 21 个新请求
2025-07-30 01:28:33,207 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 9380MB, 函数类型: 2
2025-07-30 01:28:33,207 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:33,210 - INFO -   秒 36: 处理 12 个新请求
2025-07-30 01:28:33,215 - INFO -   秒 37: 处理 11 个新请求
2025-07-30 01:28:33,218 - INFO -   秒 38: 处理 9 个新请求
2025-07-30 01:28:33,221 - INFO -   秒 39: 处理 28 个新请求
2025-07-30 01:28:33,229 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 9315MB, 函数类型: 1
2025-07-30 01:28:33,229 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:33,232 - INFO -   秒 40: 处理 13 个新请求
2025-07-30 01:28:33,234 - INFO - 时间槽 2，秒 40: 执行中容器 52，等待请求 0
2025-07-30 01:28:33,234 - INFO -   秒 41: 处理 10 个新请求
2025-07-30 01:28:33,239 - INFO -   秒 42: 处理 15 个新请求
2025-07-30 01:28:33,244 - INFO -   秒 43: 处理 12 个新请求
2025-07-30 01:28:33,249 - INFO -   秒 44: 处理 19 个新请求
2025-07-30 01:28:33,256 - INFO -   秒 45: 处理 14 个新请求
2025-07-30 01:28:33,260 - INFO -   秒 46: 处理 12 个新请求
2025-07-30 01:28:33,266 - INFO -   秒 47: 处理 21 个新请求
2025-07-30 01:28:33,272 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 9265MB, 函数类型: 4
2025-07-30 01:28:33,272 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:33,275 - INFO -   秒 48: 处理 14 个新请求
2025-07-30 01:28:33,281 - INFO -   秒 49: 处理 17 个新请求
2025-07-30 01:28:33,286 - INFO -   秒 50: 处理 13 个新请求
2025-07-30 01:28:33,291 - INFO - 时间槽 2，秒 50: 执行中容器 53，等待请求 0
2025-07-30 01:28:33,291 - INFO -   秒 51: 处理 13 个新请求
2025-07-30 01:28:33,294 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 9225MB, 函数类型: 4
2025-07-30 01:28:33,294 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:33,297 - INFO -   秒 52: 处理 14 个新请求
2025-07-30 01:28:33,301 - INFO -   秒 53: 处理 12 个新请求
2025-07-30 01:28:33,305 - INFO -   秒 54: 处理 11 个新请求
2025-07-30 01:28:33,308 - INFO -   秒 55: 处理 16 个新请求
2025-07-30 01:28:33,317 - INFO -   秒 56: 处理 8 个新请求
2025-07-30 01:28:33,323 - INFO -   秒 57: 处理 2 个新请求
2025-07-30 01:28:33,325 - INFO - 时间槽 2 结束时，还有 13 个容器正在执行，将继续在后台执行
2025-07-30 01:28:33,327 - INFO - --------处理时间槽 3 的请求，共 902 个--------
2025-07-30 01:28:33,327 - INFO - ----node memomry----
2025-07-30 01:28:33,327 - INFO - node 1 : 10705 MB
2025-07-30 01:28:33,327 - INFO - node 2 : 10720 MB
2025-07-30 01:28:33,327 - INFO - node 3 : 9535 MB
2025-07-30 01:28:33,327 - INFO - node 4 : 10595 MB
2025-07-30 01:28:33,327 - INFO - node 5 : 9665 MB
2025-07-30 01:28:33,327 - INFO - node 6 : 10735 MB
2025-07-30 01:28:33,327 - INFO - node 7 : 10645 MB
2025-07-30 01:28:33,327 - INFO - node 8 : 10400 MB
2025-07-30 01:28:33,327 - INFO - node 9 : 10145 MB
2025-07-30 01:28:33,327 - INFO - node 10 : 10315 MB
2025-07-30 01:28:33,327 - INFO - node 11 : 10760 MB
2025-07-30 01:28:33,327 - INFO - node 12 : 10565 MB
2025-07-30 01:28:33,328 - INFO - node 13 : 9790 MB
2025-07-30 01:28:33,328 - INFO - node 14 : 10705 MB
2025-07-30 01:28:33,328 - INFO - node 15 : 10490 MB
2025-07-30 01:28:33,328 - INFO - node 16 : 9785 MB
2025-07-30 01:28:33,328 - INFO - node 17 : 10060 MB
2025-07-30 01:28:33,328 - INFO - node 18 : 10810 MB
2025-07-30 01:28:33,328 - INFO - node 19 : 10440 MB
2025-07-30 01:28:33,328 - INFO - node 20 : 10645 MB
2025-07-30 01:28:33,329 - INFO - node 21 : 10190 MB
2025-07-30 01:28:33,329 - INFO - node 22 : 10255 MB
2025-07-30 01:28:33,329 - INFO - node 23 : 9885 MB
2025-07-30 01:28:33,329 - INFO - node 24 : 9510 MB
2025-07-30 01:28:33,329 - INFO - node 25 : 9970 MB
2025-07-30 01:28:33,329 - INFO - node 26 : 9850 MB
2025-07-30 01:28:33,329 - INFO - node 27 : 10645 MB
2025-07-30 01:28:33,329 - INFO - node 28 : 10515 MB
2025-07-30 01:28:33,329 - INFO - node 29 : 10455 MB
2025-07-30 01:28:33,329 - INFO - node 30 : 9965 MB
2025-07-30 01:28:33,329 - INFO - node 31 : 8725 MB
2025-07-30 01:28:33,329 - INFO - node 32 : 10085 MB
2025-07-30 01:28:33,329 - INFO - node 33 : 10810 MB
2025-07-30 01:28:33,329 - INFO - node 34 : 10735 MB
2025-07-30 01:28:33,329 - INFO - node 35 : 10020 MB
2025-07-30 01:28:33,329 - INFO - node 36 : 10605 MB
2025-07-30 01:28:33,329 - INFO - node 37 : 10505 MB
2025-07-30 01:28:33,329 - INFO - node 38 : 9725 MB
2025-07-30 01:28:33,330 - INFO - node 39 : 8830 MB
2025-07-30 01:28:33,330 - INFO - node 40 : 9740 MB
2025-07-30 01:28:33,330 - INFO - node 41 : 10630 MB
2025-07-30 01:28:33,330 - INFO - node 42 : 9415 MB
2025-07-30 01:28:33,330 - INFO - node 43 : 9735 MB
2025-07-30 01:28:33,330 - INFO - node 44 : 9810 MB
2025-07-30 01:28:33,330 - INFO - node 45 : 10280 MB
2025-07-30 01:28:33,330 - INFO - node 46 : 11000 MB
2025-07-30 01:28:33,330 - INFO - node 47 : 9540 MB
2025-07-30 01:28:33,330 - INFO - node 48 : 9645 MB
2025-07-30 01:28:33,330 - INFO - node 49 : 10365 MB
2025-07-30 01:28:33,330 - INFO - node 50 : 10220 MB
2025-07-30 01:28:33,330 - INFO - node 51 : 9470 MB
2025-07-30 01:28:33,330 - INFO - node 52 : 10075 MB
2025-07-30 01:28:33,330 - INFO - node 53 : 10635 MB
2025-07-30 01:28:33,330 - INFO - node 54 : 10735 MB
2025-07-30 01:28:33,330 - INFO - node 55 : 10220 MB
2025-07-30 01:28:33,330 - INFO - node 56 : 10760 MB
2025-07-30 01:28:33,330 - INFO - node 57 : 10185 MB
2025-07-30 01:28:33,330 - INFO - node 58 : 9470 MB
2025-07-30 01:28:33,330 - INFO - node 59 : 9815 MB
2025-07-30 01:28:33,330 - INFO - node 60 : 11000 MB
2025-07-30 01:28:33,330 - INFO - node 61 : 8975 MB
2025-07-30 01:28:33,330 - INFO - node 62 : 10100 MB
2025-07-30 01:28:33,330 - INFO - node 63 : 9735 MB
2025-07-30 01:28:33,330 - INFO - node 64 : 10700 MB
2025-07-30 01:28:33,330 - INFO - node 65 : 8320 MB
2025-07-30 01:28:33,331 - INFO - node 66 : 10250 MB
2025-07-30 01:28:33,331 - INFO - node 67 : 10550 MB
2025-07-30 01:28:33,331 - INFO - node 68 : 11000 MB
2025-07-30 01:28:33,331 - INFO - node 69 : 9340 MB
2025-07-30 01:28:33,331 - INFO - node 70 : 11000 MB
2025-07-30 01:28:33,331 - INFO - node 71 : 9895 MB
2025-07-30 01:28:33,331 - INFO - node 72 : 10255 MB
2025-07-30 01:28:33,331 - INFO - node 73 : 10705 MB
2025-07-30 01:28:33,331 - INFO - node 74 : 9325 MB
2025-07-30 01:28:33,331 - INFO - node 75 : 10050 MB
2025-07-30 01:28:33,331 - INFO - node 76 : 9765 MB
2025-07-30 01:28:33,331 - INFO - node 77 : 9620 MB
2025-07-30 01:28:33,331 - INFO - node 78 : 11000 MB
2025-07-30 01:28:33,331 - INFO - node 79 : 8815 MB
2025-07-30 01:28:33,331 - INFO - node 80 : 9715 MB
2025-07-30 01:28:33,332 - INFO - node 81 : 8515 MB
2025-07-30 01:28:33,332 - INFO - node 82 : 10355 MB
2025-07-30 01:28:33,332 - INFO - node 83 : 10810 MB
2025-07-30 01:28:33,332 - INFO - node 84 : 9940 MB
2025-07-30 01:28:33,332 - INFO - node 85 : 8715 MB
2025-07-30 01:28:33,332 - INFO - node 86 : 9565 MB
2025-07-30 01:28:33,332 - INFO - node 87 : 8990 MB
2025-07-30 01:28:33,332 - INFO - node 88 : 9795 MB
2025-07-30 01:28:33,332 - INFO - node 89 : 8705 MB
2025-07-30 01:28:33,332 - INFO - node 90 : 8755 MB
2025-07-30 01:28:33,332 - INFO - node 91 : 10340 MB
2025-07-30 01:28:33,332 - INFO - node 92 : 10615 MB
2025-07-30 01:28:33,332 - INFO - node 93 : 10335 MB
2025-07-30 01:28:33,332 - INFO - node 94 : 9885 MB
2025-07-30 01:28:33,332 - INFO - node 95 : 9760 MB
2025-07-30 01:28:33,332 - INFO - node 96 : 9185 MB
2025-07-30 01:28:33,332 - INFO - node 97 : 9810 MB
2025-07-30 01:28:33,332 - INFO - node 98 : 8960 MB
2025-07-30 01:28:33,332 - INFO - node 99 : 10505 MB
2025-07-30 01:28:33,333 - INFO - node 100 : 10570 MB
2025-07-30 01:28:33,333 - INFO - node 101 : 10000 MB
2025-07-30 01:28:33,333 - INFO - node 102 : 10490 MB
2025-07-30 01:28:33,333 - INFO - node 103 : 10395 MB
2025-07-30 01:28:33,333 - INFO - node 104 : 10505 MB
2025-07-30 01:28:33,333 - INFO - node 105 : 10615 MB
2025-07-30 01:28:33,333 - INFO - node 106 : 9900 MB
2025-07-30 01:28:33,333 - INFO - node 107 : 10025 MB
2025-07-30 01:28:33,333 - INFO - node 108 : 11000 MB
2025-07-30 01:28:33,333 - INFO - node 109 : 10040 MB
2025-07-30 01:28:33,333 - INFO - node 110 : 9830 MB
2025-07-30 01:28:33,333 - INFO - node 111 : 9100 MB
2025-07-30 01:28:33,333 - INFO - node 112 : 9865 MB
2025-07-30 01:28:33,333 - INFO - node 113 : 9450 MB
2025-07-30 01:28:33,333 - INFO - node 114 : 9210 MB
2025-07-30 01:28:33,333 - INFO - node 115 : 10000 MB
2025-07-30 01:28:33,333 - INFO - node 116 : 9310 MB
2025-07-30 01:28:33,333 - INFO - node 117 : 10550 MB
2025-07-30 01:28:33,333 - INFO - node 118 : 10655 MB
2025-07-30 01:28:33,333 - INFO - node 119 : 9880 MB
2025-07-30 01:28:33,333 - INFO - node 120 : 10500 MB
2025-07-30 01:28:33,333 - INFO - node 121 : 10490 MB
2025-07-30 01:28:33,334 - INFO - node 122 : 10550 MB
2025-07-30 01:28:33,334 - INFO - node 123 : 11000 MB
2025-07-30 01:28:33,334 - INFO - node 124 : 9800 MB
2025-07-30 01:28:33,334 - INFO - node 125 : 11000 MB
2025-07-30 01:28:33,334 - INFO -   秒 0: 处理 108 个新请求
2025-07-30 01:28:33,366 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 9185MB, 函数类型: 4
2025-07-30 01:28:33,366 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:33,373 - INFO - 时间槽 3，秒 0: 执行中容器 121，等待请求 0
2025-07-30 01:28:33,373 - INFO -   秒 1: 处理 6 个新请求
2025-07-30 01:28:33,380 - INFO -   秒 2: 处理 5 个新请求
2025-07-30 01:28:33,385 - INFO -   秒 3: 处理 17 个新请求
2025-07-30 01:28:33,393 - INFO -   秒 4: 处理 15 个新请求
2025-07-30 01:28:33,400 - INFO -   秒 5: 处理 16 个新请求
2025-07-30 01:28:33,405 - INFO -   秒 6: 处理 12 个新请求
2025-07-30 01:28:33,412 - INFO -   秒 7: 处理 13 个新请求
2025-07-30 01:28:33,419 - INFO -   秒 8: 处理 10 个新请求
2025-07-30 01:28:33,425 - INFO -   秒 9: 处理 24 个新请求
2025-07-30 01:28:33,434 - INFO -   秒 10: 处理 10 个新请求
2025-07-30 01:28:33,436 - INFO - 时间槽 3，秒 10: 执行中容器 58，等待请求 0
2025-07-30 01:28:33,437 - INFO -   秒 11: 处理 17 个新请求
2025-07-30 01:28:33,444 - INFO -   秒 12: 处理 15 个新请求
2025-07-30 01:28:33,450 - INFO -   秒 13: 处理 10 个新请求
2025-07-30 01:28:33,452 - INFO -   秒 14: 处理 19 个新请求
2025-07-30 01:28:33,457 - INFO -   秒 15: 处理 17 个新请求
2025-07-30 01:28:33,460 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 9145MB, 函数类型: 7
2025-07-30 01:28:33,460 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:28:33,465 - INFO -   秒 16: 处理 12 个新请求
2025-07-30 01:28:33,472 - INFO -   秒 17: 处理 13 个新请求
2025-07-30 01:28:33,479 - INFO -   秒 18: 处理 10 个新请求
2025-07-30 01:28:33,484 - INFO -   秒 19: 处理 30 个新请求
2025-07-30 01:28:33,494 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 9025MB, 函数类型: 9
2025-07-30 01:28:33,495 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:33,501 - INFO -   秒 20: 处理 13 个新请求
2025-07-30 01:28:33,506 - INFO - 时间槽 3，秒 20: 执行中容器 60，等待请求 0
2025-07-30 01:28:33,506 - INFO -   秒 21: 处理 10 个新请求
2025-07-30 01:28:33,511 - INFO -   秒 22: 处理 11 个新请求
2025-07-30 01:28:33,518 - INFO -   秒 23: 处理 16 个新请求
2025-07-30 01:28:33,524 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 8935MB, 函数类型: 1
2025-07-30 01:28:33,524 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:33,526 - INFO -   秒 24: 处理 16 个新请求
2025-07-30 01:28:33,534 - INFO -   秒 25: 处理 18 个新请求
2025-07-30 01:28:33,541 - INFO -   秒 26: 处理 6 个新请求
2025-07-30 01:28:33,545 - INFO -   秒 27: 处理 10 个新请求
2025-07-30 01:28:33,550 - INFO -   秒 28: 处理 18 个新请求
2025-07-30 01:28:33,556 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 8885MB, 函数类型: 2
2025-07-30 01:28:33,556 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:33,557 - INFO -   秒 29: 处理 30 个新请求
2025-07-30 01:28:33,570 - INFO -   秒 30: 处理 11 个新请求
2025-07-30 01:28:33,576 - INFO - 时间槽 3，秒 30: 执行中容器 66，等待请求 0
2025-07-30 01:28:33,576 - INFO -   秒 31: 处理 17 个新请求
2025-07-30 01:28:33,581 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 8820MB, 函数类型: 1
2025-07-30 01:28:33,581 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:33,585 - INFO -   秒 32: 处理 11 个新请求
2025-07-30 01:28:33,589 - INFO -   秒 33: 处理 7 个新请求
2025-07-30 01:28:33,593 - INFO -   秒 34: 处理 15 个新请求
2025-07-30 01:28:33,601 - INFO -   秒 35: 处理 19 个新请求
2025-07-30 01:28:33,606 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 8770MB, 函数类型: 1
2025-07-30 01:28:33,606 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:33,609 - INFO -   秒 36: 处理 14 个新请求
2025-07-30 01:28:33,615 - INFO -   秒 37: 处理 9 个新请求
2025-07-30 01:28:33,621 - INFO -   秒 38: 处理 17 个新请求
2025-07-30 01:28:33,628 - INFO -   秒 39: 处理 25 个新请求
2025-07-30 01:28:33,633 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 8720MB, 函数类型: 2
2025-07-30 01:28:33,634 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:33,636 - INFO -   秒 40: 处理 11 个新请求
2025-07-30 01:28:33,639 - INFO - 时间槽 3，秒 40: 执行中容器 61，等待请求 0
2025-07-30 01:28:33,639 - INFO -   秒 41: 处理 11 个新请求
2025-07-30 01:28:33,642 - INFO -   秒 42: 处理 15 个新请求
2025-07-30 01:28:33,650 - INFO -   秒 43: 处理 11 个新请求
2025-07-30 01:28:33,655 - INFO -   秒 44: 处理 20 个新请求
2025-07-30 01:28:33,665 - INFO -   秒 45: 处理 13 个新请求
2025-07-30 01:28:33,671 - INFO -   秒 46: 处理 14 个新请求
2025-07-30 01:28:33,678 - INFO -   秒 47: 处理 13 个新请求
2025-07-30 01:28:33,681 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 8655MB, 函数类型: 9
2025-07-30 01:28:33,682 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:33,684 - INFO -   秒 48: 处理 18 个新请求
2025-07-30 01:28:33,690 - INFO -   秒 49: 处理 19 个新请求
2025-07-30 01:28:33,696 - INFO -   秒 50: 处理 10 个新请求
2025-07-30 01:28:33,699 - INFO - 时间槽 3，秒 50: 执行中容器 56，等待请求 0
2025-07-30 01:28:33,699 - INFO -   秒 51: 处理 14 个新请求
2025-07-30 01:28:33,702 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 8565MB, 函数类型: 4
2025-07-30 01:28:33,702 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:33,703 - INFO -   秒 52: 处理 15 个新请求
2025-07-30 01:28:33,709 - INFO -   秒 53: 处理 15 个新请求
2025-07-30 01:28:33,714 - INFO -   秒 54: 处理 10 个新请求
2025-07-30 01:28:33,716 - INFO -   秒 55: 处理 12 个新请求
2025-07-30 01:28:33,719 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 8525MB, 函数类型: 1
2025-07-30 01:28:33,719 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:33,720 - INFO -   秒 56: 处理 8 个新请求
2025-07-30 01:28:33,722 - INFO -   秒 57: 处理 1 个新请求
2025-07-30 01:28:33,724 - INFO - 时间槽 3 结束时，还有 19 个容器正在执行，将继续在后台执行
2025-07-30 01:28:33,725 - INFO - --------处理时间槽 4 的请求，共 865 个--------
2025-07-30 01:28:33,725 - INFO - ----node memomry----
2025-07-30 01:28:33,725 - INFO - node 1 : 10495 MB
2025-07-30 01:28:33,725 - INFO - node 2 : 10720 MB
2025-07-30 01:28:33,725 - INFO - node 3 : 9415 MB
2025-07-30 01:28:33,725 - INFO - node 4 : 10595 MB
2025-07-30 01:28:33,725 - INFO - node 5 : 9255 MB
2025-07-30 01:28:33,725 - INFO - node 6 : 10735 MB
2025-07-30 01:28:33,725 - INFO - node 7 : 10645 MB
2025-07-30 01:28:33,725 - INFO - node 8 : 10205 MB
2025-07-30 01:28:33,726 - INFO - node 9 : 10075 MB
2025-07-30 01:28:33,726 - INFO - node 10 : 9490 MB
2025-07-30 01:28:33,726 - INFO - node 11 : 10555 MB
2025-07-30 01:28:33,726 - INFO - node 12 : 10105 MB
2025-07-30 01:28:33,726 - INFO - node 13 : 9360 MB
2025-07-30 01:28:33,726 - INFO - node 14 : 10515 MB
2025-07-30 01:28:33,726 - INFO - node 15 : 10450 MB
2025-07-30 01:28:33,726 - INFO - node 16 : 9705 MB
2025-07-30 01:28:33,726 - INFO - node 17 : 9865 MB
2025-07-30 01:28:33,726 - INFO - node 18 : 10810 MB
2025-07-30 01:28:33,726 - INFO - node 19 : 10350 MB
2025-07-30 01:28:33,726 - INFO - node 20 : 10645 MB
2025-07-30 01:28:33,726 - INFO - node 21 : 9845 MB
2025-07-30 01:28:33,726 - INFO - node 22 : 10095 MB
2025-07-30 01:28:33,726 - INFO - node 23 : 9835 MB
2025-07-30 01:28:33,726 - INFO - node 24 : 9065 MB
2025-07-30 01:28:33,726 - INFO - node 25 : 9700 MB
2025-07-30 01:28:33,726 - INFO - node 26 : 9670 MB
2025-07-30 01:28:33,727 - INFO - node 27 : 10605 MB
2025-07-30 01:28:33,727 - INFO - node 28 : 10275 MB
2025-07-30 01:28:33,727 - INFO - node 29 : 10260 MB
2025-07-30 01:28:33,727 - INFO - node 30 : 9695 MB
2025-07-30 01:28:33,727 - INFO - node 31 : 8495 MB
2025-07-30 01:28:33,727 - INFO - node 32 : 9560 MB
2025-07-30 01:28:33,727 - INFO - node 33 : 10750 MB
2025-07-30 01:28:33,727 - INFO - node 34 : 10440 MB
2025-07-30 01:28:33,727 - INFO - node 35 : 9545 MB
2025-07-30 01:28:33,727 - INFO - node 36 : 10565 MB
2025-07-30 01:28:33,727 - INFO - node 37 : 10120 MB
2025-07-30 01:28:33,727 - INFO - node 38 : 9470 MB
2025-07-30 01:28:33,727 - INFO - node 39 : 7750 MB
2025-07-30 01:28:33,727 - INFO - node 40 : 9325 MB
2025-07-30 01:28:33,727 - INFO - node 41 : 10580 MB
2025-07-30 01:28:33,727 - INFO - node 42 : 8855 MB
2025-07-30 01:28:33,727 - INFO - node 43 : 9365 MB
2025-07-30 01:28:33,727 - INFO - node 44 : 9115 MB
2025-07-30 01:28:33,727 - INFO - node 45 : 9645 MB
2025-07-30 01:28:33,727 - INFO - node 46 : 11000 MB
2025-07-30 01:28:33,727 - INFO - node 47 : 8930 MB
2025-07-30 01:28:33,727 - INFO - node 48 : 8745 MB
2025-07-30 01:28:33,727 - INFO - node 49 : 10300 MB
2025-07-30 01:28:33,727 - INFO - node 50 : 9995 MB
2025-07-30 01:28:33,727 - INFO - node 51 : 9050 MB
2025-07-30 01:28:33,727 - INFO - node 52 : 9535 MB
2025-07-30 01:28:33,727 - INFO - node 53 : 10635 MB
2025-07-30 01:28:33,727 - INFO - node 54 : 10580 MB
2025-07-30 01:28:33,727 - INFO - node 55 : 10090 MB
2025-07-30 01:28:33,727 - INFO - node 56 : 10710 MB
2025-07-30 01:28:33,727 - INFO - node 57 : 10070 MB
2025-07-30 01:28:33,727 - INFO - node 58 : 8730 MB
2025-07-30 01:28:33,727 - INFO - node 59 : 9305 MB
2025-07-30 01:28:33,727 - INFO - node 60 : 11000 MB
2025-07-30 01:28:33,727 - INFO - node 61 : 8605 MB
2025-07-30 01:28:33,727 - INFO - node 62 : 9660 MB
2025-07-30 01:28:33,727 - INFO - node 63 : 9510 MB
2025-07-30 01:28:33,727 - INFO - node 64 : 10205 MB
2025-07-30 01:28:33,727 - INFO - node 65 : 7470 MB
2025-07-30 01:28:33,727 - INFO - node 66 : 9670 MB
2025-07-30 01:28:33,727 - INFO - node 67 : 10360 MB
2025-07-30 01:28:33,727 - INFO - node 68 : 11000 MB
2025-07-30 01:28:33,727 - INFO - node 69 : 8530 MB
2025-07-30 01:28:33,727 - INFO - node 70 : 11000 MB
2025-07-30 01:28:33,727 - INFO - node 71 : 9705 MB
2025-07-30 01:28:33,727 - INFO - node 72 : 10085 MB
2025-07-30 01:28:33,727 - INFO - node 73 : 10515 MB
2025-07-30 01:28:33,727 - INFO - node 74 : 8425 MB
2025-07-30 01:28:33,727 - INFO - node 75 : 9900 MB
2025-07-30 01:28:33,728 - INFO - node 76 : 9625 MB
2025-07-30 01:28:33,728 - INFO - node 77 : 8655 MB
2025-07-30 01:28:33,728 - INFO - node 78 : 11000 MB
2025-07-30 01:28:33,728 - INFO - node 79 : 7680 MB
2025-07-30 01:28:33,728 - INFO - node 80 : 9015 MB
2025-07-30 01:28:33,728 - INFO - node 81 : 7680 MB
2025-07-30 01:28:33,728 - INFO - node 82 : 9995 MB
2025-07-30 01:28:33,728 - INFO - node 83 : 10770 MB
2025-07-30 01:28:33,728 - INFO - node 84 : 9650 MB
2025-07-30 01:28:33,728 - INFO - node 85 : 8280 MB
2025-07-30 01:28:33,728 - INFO - node 86 : 9295 MB
2025-07-30 01:28:33,728 - INFO - node 87 : 8780 MB
2025-07-30 01:28:33,728 - INFO - node 88 : 9385 MB
2025-07-30 01:28:33,728 - INFO - node 89 : 7325 MB
2025-07-30 01:28:33,728 - INFO - node 90 : 7755 MB
2025-07-30 01:28:33,728 - INFO - node 91 : 10290 MB
2025-07-30 01:28:33,728 - INFO - node 92 : 10490 MB
2025-07-30 01:28:33,728 - INFO - node 93 : 10215 MB
2025-07-30 01:28:33,728 - INFO - node 94 : 9640 MB
2025-07-30 01:28:33,728 - INFO - node 95 : 9500 MB
2025-07-30 01:28:33,728 - INFO - node 96 : 8475 MB
2025-07-30 01:28:33,728 - INFO - node 97 : 9680 MB
2025-07-30 01:28:33,728 - INFO - node 98 : 8030 MB
2025-07-30 01:28:33,728 - INFO - node 99 : 10465 MB
2025-07-30 01:28:33,728 - INFO - node 100 : 10195 MB
2025-07-30 01:28:33,728 - INFO - node 101 : 9780 MB
2025-07-30 01:28:33,728 - INFO - node 102 : 10005 MB
2025-07-30 01:28:33,728 - INFO - node 103 : 10305 MB
2025-07-30 01:28:33,728 - INFO - node 104 : 10465 MB
2025-07-30 01:28:33,728 - INFO - node 105 : 10425 MB
2025-07-30 01:28:33,728 - INFO - node 106 : 9530 MB
2025-07-30 01:28:33,728 - INFO - node 107 : 9255 MB
2025-07-30 01:28:33,728 - INFO - node 108 : 11000 MB
2025-07-30 01:28:33,728 - INFO - node 109 : 9870 MB
2025-07-30 01:28:33,728 - INFO - node 110 : 9555 MB
2025-07-30 01:28:33,728 - INFO - node 111 : 7710 MB
2025-07-30 01:28:33,728 - INFO - node 112 : 9465 MB
2025-07-30 01:28:33,728 - INFO - node 113 : 9410 MB
2025-07-30 01:28:33,728 - INFO - node 114 : 8695 MB
2025-07-30 01:28:33,728 - INFO - node 115 : 9510 MB
2025-07-30 01:28:33,729 - INFO - node 116 : 8375 MB
2025-07-30 01:28:33,729 - INFO - node 117 : 10480 MB
2025-07-30 01:28:33,729 - INFO - node 118 : 10360 MB
2025-07-30 01:28:33,729 - INFO - node 119 : 9415 MB
2025-07-30 01:28:33,729 - INFO - node 120 : 10410 MB
2025-07-30 01:28:33,729 - INFO - node 121 : 10400 MB
2025-07-30 01:28:33,729 - INFO - node 122 : 10335 MB
2025-07-30 01:28:33,729 - INFO - node 123 : 10810 MB
2025-07-30 01:28:33,729 - INFO - node 124 : 9480 MB
2025-07-30 01:28:33,729 - INFO - node 125 : 11000 MB
2025-07-30 01:28:33,729 - INFO -   秒 0: 处理 108 个新请求
2025-07-30 01:28:33,746 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 8475MB, 函数类型: 6
2025-07-30 01:28:33,746 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:33,751 - INFO - 时间槽 4，秒 0: 执行中容器 127，等待请求 0
2025-07-30 01:28:33,751 - INFO -   秒 1: 处理 6 个新请求
2025-07-30 01:28:33,755 - INFO -   秒 2: 处理 3 个新请求
2025-07-30 01:28:33,756 - INFO -   秒 3: 处理 17 个新请求
2025-07-30 01:28:33,758 - INFO -   秒 4: 处理 14 个新请求
2025-07-30 01:28:33,763 - INFO -   秒 5: 处理 15 个新请求
2025-07-30 01:28:33,767 - INFO -   秒 6: 处理 15 个新请求
2025-07-30 01:28:33,771 - INFO -   秒 7: 处理 13 个新请求
2025-07-30 01:28:33,775 - INFO -   秒 8: 处理 10 个新请求
2025-07-30 01:28:33,778 - INFO -   秒 9: 处理 14 个新请求
2025-07-30 01:28:33,785 - INFO -   秒 10: 处理 15 个新请求
2025-07-30 01:28:33,792 - INFO - 时间槽 4，秒 10: 执行中容器 52，等待请求 0
2025-07-30 01:28:33,792 - INFO -   秒 11: 处理 17 个新请求
2025-07-30 01:28:33,800 - INFO -   秒 12: 处理 15 个新请求
2025-07-30 01:28:33,803 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 8405MB, 函数类型: 1
2025-07-30 01:28:33,803 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:33,806 - INFO -   秒 13: 处理 11 个新请求
2025-07-30 01:28:33,810 - INFO -   秒 14: 处理 17 个新请求
2025-07-30 01:28:33,816 - INFO -   秒 15: 处理 12 个新请求
2025-07-30 01:28:33,820 - INFO -   秒 16: 处理 15 个新请求
2025-07-30 01:28:33,822 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 8355MB, 函数类型: 9
2025-07-30 01:28:33,822 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:33,824 - INFO -   秒 17: 处理 13 个新请求
2025-07-30 01:28:33,829 - INFO -   秒 18: 处理 11 个新请求
2025-07-30 01:28:33,834 - INFO -   秒 19: 处理 21 个新请求
2025-07-30 01:28:33,841 - INFO -   秒 20: 处理 17 个新请求
2025-07-30 01:28:33,846 - INFO - 时间槽 4，秒 20: 执行中容器 63，等待请求 0
2025-07-30 01:28:33,846 - INFO -   秒 21: 处理 14 个新请求
2025-07-30 01:28:33,850 - INFO -   秒 22: 处理 11 个新请求
2025-07-30 01:28:33,852 - INFO -   秒 23: 处理 16 个新请求
2025-07-30 01:28:33,857 - INFO -   秒 24: 处理 14 个新请求
2025-07-30 01:28:33,859 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 8265MB, 函数类型: 9
2025-07-30 01:28:33,859 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:33,861 - INFO -   秒 25: 处理 11 个新请求
2025-07-30 01:28:33,865 - INFO -   秒 26: 处理 14 个新请求
2025-07-30 01:28:33,868 - INFO -   秒 27: 处理 10 个新请求
2025-07-30 01:28:33,872 - INFO -   秒 28: 处理 11 个新请求
2025-07-30 01:28:33,874 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 8175MB, 函数类型: 2
2025-07-30 01:28:33,874 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:33,875 - INFO -   秒 29: 处理 29 个新请求
2025-07-30 01:28:33,884 - INFO -   秒 30: 处理 12 个新请求
2025-07-30 01:28:33,889 - INFO - 时间槽 4，秒 30: 执行中容器 69，等待请求 0
2025-07-30 01:28:33,890 - INFO -   秒 31: 处理 5 个新请求
2025-07-30 01:28:33,892 - INFO -   秒 32: 处理 18 个新请求
2025-07-30 01:28:33,898 - INFO -   秒 33: 处理 14 个新请求
2025-07-30 01:28:33,901 - INFO -   秒 34: 处理 15 个新请求
2025-07-30 01:28:33,907 - INFO -   秒 35: 处理 14 个新请求
2025-07-30 01:28:33,911 - INFO -   秒 36: 处理 10 个新请求
2025-07-30 01:28:33,912 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 8110MB, 函数类型: 4
2025-07-30 01:28:33,912 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:33,914 - INFO -   秒 37: 处理 11 个新请求
2025-07-30 01:28:33,917 - INFO -   秒 38: 处理 20 个新请求
2025-07-30 01:28:33,924 - INFO -   秒 39: 处理 20 个新请求
2025-07-30 01:28:33,931 - INFO -   秒 40: 处理 10 个新请求
2025-07-30 01:28:33,934 - INFO - 时间槽 4，秒 40: 执行中容器 63，等待请求 0
2025-07-30 01:28:33,934 - INFO -   秒 41: 处理 16 个新请求
2025-07-30 01:28:33,938 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 8070MB, 函数类型: 2
2025-07-30 01:28:33,938 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:33,941 - INFO -   秒 42: 处理 7 个新请求
2025-07-30 01:28:33,943 - INFO -   秒 43: 处理 14 个新请求
2025-07-30 01:28:33,950 - INFO -   秒 44: 处理 17 个新请求
2025-07-30 01:28:33,955 - INFO -   秒 45: 处理 12 个新请求
2025-07-30 01:28:33,961 - INFO -   秒 46: 处理 13 个新请求
2025-07-30 01:28:33,966 - INFO -   秒 47: 处理 14 个新请求
2025-07-30 01:28:33,972 - INFO -   秒 48: 处理 18 个新请求
2025-07-30 01:28:33,979 - INFO -   秒 49: 处理 17 个新请求
2025-07-30 01:28:33,984 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 8005MB, 函数类型: 6
2025-07-30 01:28:33,984 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:33,988 - INFO -   秒 50: 处理 10 个新请求
2025-07-30 01:28:33,992 - INFO - 时间槽 4，秒 50: 执行中容器 55，等待请求 0
2025-07-30 01:28:33,992 - INFO -   秒 51: 处理 14 个新请求
2025-07-30 01:28:33,998 - INFO -   秒 52: 处理 9 个新请求
2025-07-30 01:28:34,001 - INFO -   秒 53: 处理 12 个新请求
2025-07-30 01:28:34,003 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 7935MB, 函数类型: 6
2025-07-30 01:28:34,003 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:34,006 - INFO -   秒 54: 处理 18 个新请求
2025-07-30 01:28:34,011 - INFO -   秒 55: 处理 12 个新请求
2025-07-30 01:28:34,016 - INFO -   秒 56: 处理 4 个新请求
2025-07-30 01:28:34,020 - INFO - 时间槽 4 结束时，还有 20 个容器正在执行，将继续在后台执行
2025-07-30 01:28:34,021 - INFO - --------处理时间槽 5 的请求，共 908 个--------
2025-07-30 01:28:34,021 - INFO - ----node memomry----
2025-07-30 01:28:34,022 - INFO - node 1 : 10445 MB
2025-07-30 01:28:34,022 - INFO - node 2 : 10720 MB
2025-07-30 01:28:34,022 - INFO - node 3 : 9125 MB
2025-07-30 01:28:34,022 - INFO - node 4 : 10555 MB
2025-07-30 01:28:34,022 - INFO - node 5 : 9080 MB
2025-07-30 01:28:34,022 - INFO - node 6 : 10735 MB
2025-07-30 01:28:34,022 - INFO - node 7 : 10645 MB
2025-07-30 01:28:34,022 - INFO - node 8 : 10205 MB
2025-07-30 01:28:34,022 - INFO - node 9 : 9885 MB
2025-07-30 01:28:34,022 - INFO - node 10 : 8755 MB
2025-07-30 01:28:34,022 - INFO - node 11 : 10435 MB
2025-07-30 01:28:34,022 - INFO - node 12 : 9720 MB
2025-07-30 01:28:34,022 - INFO - node 13 : 9000 MB
2025-07-30 01:28:34,022 - INFO - node 14 : 10475 MB
2025-07-30 01:28:34,022 - INFO - node 15 : 10410 MB
2025-07-30 01:28:34,022 - INFO - node 16 : 9640 MB
2025-07-30 01:28:34,022 - INFO - node 17 : 9815 MB
2025-07-30 01:28:34,022 - INFO - node 18 : 10770 MB
2025-07-30 01:28:34,022 - INFO - node 19 : 10350 MB
2025-07-30 01:28:34,022 - INFO - node 20 : 10580 MB
2025-07-30 01:28:34,022 - INFO - node 21 : 9445 MB
2025-07-30 01:28:34,022 - INFO - node 22 : 9685 MB
2025-07-30 01:28:34,022 - INFO - node 23 : 9675 MB
2025-07-30 01:28:34,022 - INFO - node 24 : 8115 MB
2025-07-30 01:28:34,022 - INFO - node 25 : 9385 MB
2025-07-30 01:28:34,022 - INFO - node 26 : 9540 MB
2025-07-30 01:28:34,022 - INFO - node 27 : 10565 MB
2025-07-30 01:28:34,022 - INFO - node 28 : 9930 MB
2025-07-30 01:28:34,022 - INFO - node 29 : 10190 MB
2025-07-30 01:28:34,022 - INFO - node 30 : 9470 MB
2025-07-30 01:28:34,022 - INFO - node 31 : 8385 MB
2025-07-30 01:28:34,022 - INFO - node 32 : 9165 MB
2025-07-30 01:28:34,022 - INFO - node 33 : 10710 MB
2025-07-30 01:28:34,022 - INFO - node 34 : 10375 MB
2025-07-30 01:28:34,022 - INFO - node 35 : 9185 MB
2025-07-30 01:28:34,022 - INFO - node 36 : 10525 MB
2025-07-30 01:28:34,022 - INFO - node 37 : 9885 MB
2025-07-30 01:28:34,022 - INFO - node 38 : 8990 MB
2025-07-30 01:28:34,022 - INFO - node 39 : 6810 MB
2025-07-30 01:28:34,022 - INFO - node 40 : 9185 MB
2025-07-30 01:28:34,022 - INFO - node 41 : 10540 MB
2025-07-30 01:28:34,022 - INFO - node 42 : 8555 MB
2025-07-30 01:28:34,022 - INFO - node 43 : 9030 MB
2025-07-30 01:28:34,022 - INFO - node 44 : 9010 MB
2025-07-30 01:28:34,022 - INFO - node 45 : 9395 MB
2025-07-30 01:28:34,023 - INFO - node 46 : 10790 MB
2025-07-30 01:28:34,023 - INFO - node 47 : 8740 MB
2025-07-30 01:28:34,023 - INFO - node 48 : 7540 MB
2025-07-30 01:28:34,023 - INFO - node 49 : 10250 MB
2025-07-30 01:28:34,023 - INFO - node 50 : 9835 MB
2025-07-30 01:28:34,023 - INFO - node 51 : 8620 MB
2025-07-30 01:28:34,023 - INFO - node 52 : 9395 MB
2025-07-30 01:28:34,023 - INFO - node 53 : 10595 MB
2025-07-30 01:28:34,023 - INFO - node 54 : 10530 MB
2025-07-30 01:28:34,023 - INFO - node 55 : 10025 MB
2025-07-30 01:28:34,023 - INFO - node 56 : 10670 MB
2025-07-30 01:28:34,023 - INFO - node 57 : 9950 MB
2025-07-30 01:28:34,023 - INFO - node 58 : 7675 MB
2025-07-30 01:28:34,023 - INFO - node 59 : 8670 MB
2025-07-30 01:28:34,023 - INFO - node 60 : 11000 MB
2025-07-30 01:28:34,023 - INFO - node 61 : 8080 MB
2025-07-30 01:28:34,023 - INFO - node 62 : 9545 MB
2025-07-30 01:28:34,023 - INFO - node 63 : 9470 MB
2025-07-30 01:28:34,023 - INFO - node 64 : 9805 MB
2025-07-30 01:28:34,023 - INFO - node 65 : 6955 MB
2025-07-30 01:28:34,023 - INFO - node 66 : 9125 MB
2025-07-30 01:28:34,023 - INFO - node 67 : 10310 MB
2025-07-30 01:28:34,023 - INFO - node 68 : 11000 MB
2025-07-30 01:28:34,023 - INFO - node 69 : 7630 MB
2025-07-30 01:28:34,023 - INFO - node 70 : 11000 MB
2025-07-30 01:28:34,023 - INFO - node 71 : 9280 MB
2025-07-30 01:28:34,023 - INFO - node 72 : 10045 MB
2025-07-30 01:28:34,023 - INFO - node 73 : 10450 MB
2025-07-30 01:28:34,023 - INFO - node 74 : 7865 MB
2025-07-30 01:28:34,023 - INFO - node 75 : 9510 MB
2025-07-30 01:28:34,023 - INFO - node 76 : 9495 MB
2025-07-30 01:28:34,023 - INFO - node 77 : 7865 MB
2025-07-30 01:28:34,023 - INFO - node 78 : 11000 MB
2025-07-30 01:28:34,023 - INFO - node 79 : 7110 MB
2025-07-30 01:28:34,023 - INFO - node 80 : 8625 MB
2025-07-30 01:28:34,023 - INFO - node 81 : 7185 MB
2025-07-30 01:28:34,023 - INFO - node 82 : 9945 MB
2025-07-30 01:28:34,023 - INFO - node 83 : 10615 MB
2025-07-30 01:28:34,023 - INFO - node 84 : 9295 MB
2025-07-30 01:28:34,023 - INFO - node 85 : 7415 MB
2025-07-30 01:28:34,023 - INFO - node 86 : 9185 MB
2025-07-30 01:28:34,023 - INFO - node 87 : 8485 MB
2025-07-30 01:28:34,023 - INFO - node 88 : 9035 MB
2025-07-30 01:28:34,023 - INFO - node 89 : 6090 MB
2025-07-30 01:28:34,023 - INFO - node 90 : 7135 MB
2025-07-30 01:28:34,023 - INFO - node 91 : 10240 MB
2025-07-30 01:28:34,023 - INFO - node 92 : 10195 MB
2025-07-30 01:28:34,024 - INFO - node 93 : 9735 MB
2025-07-30 01:28:34,024 - INFO - node 94 : 9315 MB
2025-07-30 01:28:34,024 - INFO - node 95 : 8860 MB
2025-07-30 01:28:34,024 - INFO - node 96 : 7865 MB
2025-07-30 01:28:34,024 - INFO - node 97 : 9455 MB
2025-07-30 01:28:34,024 - INFO - node 98 : 7710 MB
2025-07-30 01:28:34,024 - INFO - node 99 : 10400 MB
2025-07-30 01:28:34,024 - INFO - node 100 : 9990 MB
2025-07-30 01:28:34,024 - INFO - node 101 : 9550 MB
2025-07-30 01:28:34,024 - INFO - node 102 : 9885 MB
2025-07-30 01:28:34,024 - INFO - node 103 : 10215 MB
2025-07-30 01:28:34,024 - INFO - node 104 : 10425 MB
2025-07-30 01:28:34,024 - INFO - node 105 : 10270 MB
2025-07-30 01:28:34,024 - INFO - node 106 : 9335 MB
2025-07-30 01:28:34,024 - INFO - node 107 : 8430 MB
2025-07-30 01:28:34,024 - INFO - node 108 : 11000 MB
2025-07-30 01:28:34,024 - INFO - node 109 : 9010 MB
2025-07-30 01:28:34,024 - INFO - node 110 : 9265 MB
2025-07-30 01:28:34,024 - INFO - node 111 : 7165 MB
2025-07-30 01:28:34,024 - INFO - node 112 : 9295 MB
2025-07-30 01:28:34,024 - INFO - node 113 : 9045 MB
2025-07-30 01:28:34,024 - INFO - node 114 : 8050 MB
2025-07-30 01:28:34,024 - INFO - node 115 : 9040 MB
2025-07-30 01:28:34,024 - INFO - node 116 : 7890 MB
2025-07-30 01:28:34,024 - INFO - node 117 : 10290 MB
2025-07-30 01:28:34,024 - INFO - node 118 : 10290 MB
2025-07-30 01:28:34,024 - INFO - node 119 : 8525 MB
2025-07-30 01:28:34,024 - INFO - node 120 : 10220 MB
2025-07-30 01:28:34,024 - INFO - node 121 : 10350 MB
2025-07-30 01:28:34,024 - INFO - node 122 : 10245 MB
2025-07-30 01:28:34,024 - INFO - node 123 : 10770 MB
2025-07-30 01:28:34,024 - INFO - node 124 : 9260 MB
2025-07-30 01:28:34,024 - INFO - node 125 : 11000 MB
2025-07-30 01:28:34,024 - INFO -   秒 0: 处理 110 个新请求
2025-07-30 01:28:34,046 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 7865MB, 函数类型: 6
2025-07-30 01:28:34,046 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:34,054 - INFO - 时间槽 5，秒 0: 执行中容器 130，等待请求 0
2025-07-30 01:28:34,054 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:28:34,059 - INFO -   秒 2: 处理 5 个新请求
2025-07-30 01:28:34,063 - INFO -   秒 3: 处理 16 个新请求
2025-07-30 01:28:34,067 - INFO -   秒 4: 处理 17 个新请求
2025-07-30 01:28:34,072 - INFO -   秒 5: 处理 18 个新请求
2025-07-30 01:28:34,077 - INFO -   秒 6: 处理 11 个新请求
2025-07-30 01:28:34,083 - INFO -   秒 7: 处理 10 个新请求
2025-07-30 01:28:34,087 - INFO -   秒 8: 处理 12 个新请求
2025-07-30 01:28:34,091 - INFO -   秒 9: 处理 21 个新请求
2025-07-30 01:28:34,097 - INFO -   秒 10: 处理 16 个新请求
2025-07-30 01:28:34,106 - INFO - 时间槽 5，秒 10: 执行中容器 69，等待请求 0
2025-07-30 01:28:34,106 - INFO -   秒 11: 处理 18 个新请求
2025-07-30 01:28:34,113 - INFO -   秒 12: 处理 10 个新请求
2025-07-30 01:28:34,120 - INFO -   秒 13: 处理 12 个新请求
2025-07-30 01:28:34,126 - INFO -   秒 14: 处理 21 个新请求
2025-07-30 01:28:34,137 - INFO -   秒 15: 处理 12 个新请求
2025-07-30 01:28:34,146 - INFO -   秒 16: 处理 17 个新请求
2025-07-30 01:28:34,150 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 7795MB, 函数类型: 2
2025-07-30 01:28:34,150 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:34,154 - INFO -   秒 17: 处理 13 个新请求
2025-07-30 01:28:34,159 - INFO -   秒 18: 处理 6 个新请求
2025-07-30 01:28:34,162 - INFO -   秒 19: 处理 33 个新请求
2025-07-30 01:28:34,172 - INFO -   秒 20: 处理 9 个新请求
2025-07-30 01:28:34,175 - INFO - 时间槽 5，秒 20: 执行中容器 70，等待请求 0
2025-07-30 01:28:34,175 - INFO -   秒 21: 处理 13 个新请求
2025-07-30 01:28:34,181 - INFO -   秒 22: 处理 9 个新请求
2025-07-30 01:28:34,184 - INFO -   秒 23: 处理 22 个新请求
2025-07-30 01:28:34,190 - INFO -   秒 24: 处理 19 个新请求
2025-07-30 01:28:34,193 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7730MB, 函数类型: 1
2025-07-30 01:28:34,193 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:34,197 - INFO -   秒 25: 处理 9 个新请求
2025-07-30 01:28:34,200 - INFO -   秒 26: 处理 9 个新请求
2025-07-30 01:28:34,203 - INFO -   秒 27: 处理 14 个新请求
2025-07-30 01:28:34,207 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 7680MB, 函数类型: 1
2025-07-30 01:28:34,207 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:34,209 - INFO -   秒 28: 处理 14 个新请求
2025-07-30 01:28:34,214 - INFO -   秒 29: 处理 26 个新请求
2025-07-30 01:28:34,221 - INFO -   秒 30: 处理 16 个新请求
2025-07-30 01:28:34,225 - INFO - 时间槽 5，秒 30: 执行中容器 75，等待请求 0
2025-07-30 01:28:34,225 - INFO -   秒 31: 处理 15 个新请求
2025-07-30 01:28:34,230 - INFO -   秒 32: 处理 12 个新请求
2025-07-30 01:28:34,233 - INFO -   秒 33: 处理 8 个新请求
2025-07-30 01:28:34,237 - INFO -   秒 34: 处理 19 个新请求
2025-07-30 01:28:34,244 - INFO -   秒 35: 处理 16 个新请求
2025-07-30 01:28:34,247 - INFO - DEBUG: 节点96内存检查 - 需要内存: 360.0MB, 可用内存: 7630MB, 函数类型: 5
2025-07-30 01:28:34,247 - INFO - DEBUG: 节点96创建User层容器，减少内存 360MB，类型: 5
2025-07-30 01:28:34,250 - INFO -   秒 36: 处理 14 个新请求
2025-07-30 01:28:34,254 - INFO -   秒 37: 处理 11 个新请求
2025-07-30 01:28:34,257 - INFO -   秒 38: 处理 13 个新请求
2025-07-30 01:28:34,263 - INFO -   秒 39: 处理 26 个新请求
2025-07-30 01:28:34,270 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 7270MB, 函数类型: 4
2025-07-30 01:28:34,270 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:34,272 - INFO -   秒 40: 处理 12 个新请求
2025-07-30 01:28:34,278 - INFO - 时间槽 5，秒 40: 执行中容器 65，等待请求 0
2025-07-30 01:28:34,278 - INFO -   秒 41: 处理 12 个新请求
2025-07-30 01:28:34,283 - INFO -   秒 42: 处理 13 个新请求
2025-07-30 01:28:34,287 - INFO -   秒 43: 处理 13 个新请求
2025-07-30 01:28:34,290 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7230MB, 函数类型: 9
2025-07-30 01:28:34,291 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:34,292 - INFO -   秒 44: 处理 20 个新请求
2025-07-30 01:28:34,301 - INFO -   秒 45: 处理 9 个新请求
2025-07-30 01:28:34,304 - INFO -   秒 46: 处理 13 个新请求
2025-07-30 01:28:34,309 - INFO -   秒 47: 处理 20 个新请求
2025-07-30 01:28:34,313 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7140MB, 函数类型: 9
2025-07-30 01:28:34,313 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:34,315 - INFO -   秒 48: 处理 13 个新请求
2025-07-30 01:28:34,319 - INFO -   秒 49: 处理 21 个新请求
2025-07-30 01:28:34,325 - INFO -   秒 50: 处理 11 个新请求
2025-07-30 01:28:34,331 - INFO - 时间槽 5，秒 50: 执行中容器 72，等待请求 0
2025-07-30 01:28:34,331 - INFO -   秒 51: 处理 9 个新请求
2025-07-30 01:28:34,335 - INFO -   秒 52: 处理 20 个新请求
2025-07-30 01:28:34,340 - INFO -   秒 53: 处理 14 个新请求
2025-07-30 01:28:34,343 - INFO -   秒 54: 处理 7 个新请求
2025-07-30 01:28:34,348 - INFO -   秒 55: 处理 12 个新请求
2025-07-30 01:28:34,350 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 7050MB, 函数类型: 9
2025-07-30 01:28:34,350 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:34,351 - INFO -   秒 56: 处理 13 个新请求
2025-07-30 01:28:34,357 - INFO - 时间槽 5 结束时，还有 24 个容器正在执行，将继续在后台执行
2025-07-30 01:28:34,358 - INFO - --------处理时间槽 6 的请求，共 904 个--------
2025-07-30 01:28:34,358 - INFO - ----node memomry----
2025-07-30 01:28:34,358 - INFO - node 1 : 10445 MB
2025-07-30 01:28:34,358 - INFO - node 2 : 10720 MB
2025-07-30 01:28:34,358 - INFO - node 3 : 8855 MB
2025-07-30 01:28:34,358 - INFO - node 4 : 10555 MB
2025-07-30 01:28:34,358 - INFO - node 5 : 8780 MB
2025-07-30 01:28:34,358 - INFO - node 6 : 10735 MB
2025-07-30 01:28:34,358 - INFO - node 7 : 10645 MB
2025-07-30 01:28:34,358 - INFO - node 8 : 10090 MB
2025-07-30 01:28:34,358 - INFO - node 9 : 9665 MB
2025-07-30 01:28:34,358 - INFO - node 10 : 8685 MB
2025-07-30 01:28:34,358 - INFO - node 11 : 10385 MB
2025-07-30 01:28:34,358 - INFO - node 12 : 9600 MB
2025-07-30 01:28:34,358 - INFO - node 13 : 8855 MB
2025-07-30 01:28:34,358 - INFO - node 14 : 10320 MB
2025-07-30 01:28:34,358 - INFO - node 15 : 10360 MB
2025-07-30 01:28:34,358 - INFO - node 16 : 9510 MB
2025-07-30 01:28:34,358 - INFO - node 17 : 9595 MB
2025-07-30 01:28:34,358 - INFO - node 18 : 10770 MB
2025-07-30 01:28:34,358 - INFO - node 19 : 10105 MB
2025-07-30 01:28:34,358 - INFO - node 20 : 10540 MB
2025-07-30 01:28:34,358 - INFO - node 21 : 9315 MB
2025-07-30 01:28:34,358 - INFO - node 22 : 9400 MB
2025-07-30 01:28:34,358 - INFO - node 23 : 9505 MB
2025-07-30 01:28:34,358 - INFO - node 24 : 7775 MB
2025-07-30 01:28:34,358 - INFO - node 25 : 9215 MB
2025-07-30 01:28:34,358 - INFO - node 26 : 9400 MB
2025-07-30 01:28:34,358 - INFO - node 27 : 10525 MB
2025-07-30 01:28:34,358 - INFO - node 28 : 9160 MB
2025-07-30 01:28:34,358 - INFO - node 29 : 10050 MB
2025-07-30 01:28:34,358 - INFO - node 30 : 8735 MB
2025-07-30 01:28:34,358 - INFO - node 31 : 7880 MB
2025-07-30 01:28:34,358 - INFO - node 32 : 9015 MB
2025-07-30 01:28:34,358 - INFO - node 33 : 10415 MB
2025-07-30 01:28:34,360 - INFO - node 34 : 10285 MB
2025-07-30 01:28:34,360 - INFO - node 35 : 8865 MB
2025-07-30 01:28:34,360 - INFO - node 36 : 10425 MB
2025-07-30 01:28:34,360 - INFO - node 37 : 9485 MB
2025-07-30 01:28:34,360 - INFO - node 38 : 8540 MB
2025-07-30 01:28:34,360 - INFO - node 39 : 6325 MB
2025-07-30 01:28:34,360 - INFO - node 40 : 9115 MB
2025-07-30 01:28:34,360 - INFO - node 41 : 10490 MB
2025-07-30 01:28:34,360 - INFO - node 42 : 7635 MB
2025-07-30 01:28:34,360 - INFO - node 43 : 8980 MB
2025-07-30 01:28:34,360 - INFO - node 44 : 8505 MB
2025-07-30 01:28:34,360 - INFO - node 45 : 9190 MB
2025-07-30 01:28:34,360 - INFO - node 46 : 10790 MB
2025-07-30 01:28:34,360 - INFO - node 47 : 8130 MB
2025-07-30 01:28:34,360 - INFO - node 48 : 7120 MB
2025-07-30 01:28:34,360 - INFO - node 49 : 9955 MB
2025-07-30 01:28:34,360 - INFO - node 50 : 9385 MB
2025-07-30 01:28:34,360 - INFO - node 51 : 7690 MB
2025-07-30 01:28:34,360 - INFO - node 52 : 9085 MB
2025-07-30 01:28:34,360 - INFO - node 53 : 10475 MB
2025-07-30 01:28:34,360 - INFO - node 54 : 10235 MB
2025-07-30 01:28:34,360 - INFO - node 55 : 9485 MB
2025-07-30 01:28:34,360 - INFO - node 56 : 10670 MB
2025-07-30 01:28:34,360 - INFO - node 57 : 9435 MB
2025-07-30 01:28:34,360 - INFO - node 58 : 6770 MB
2025-07-30 01:28:34,360 - INFO - node 59 : 8085 MB
2025-07-30 01:28:34,360 - INFO - node 60 : 11000 MB
2025-07-30 01:28:34,360 - INFO - node 61 : 7600 MB
2025-07-30 01:28:34,360 - INFO - node 62 : 9350 MB
2025-07-30 01:28:34,360 - INFO - node 63 : 8540 MB
2025-07-30 01:28:34,361 - INFO - node 64 : 9440 MB
2025-07-30 01:28:34,361 - INFO - node 65 : 6300 MB
2025-07-30 01:28:34,361 - INFO - node 66 : 8905 MB
2025-07-30 01:28:34,361 - INFO - node 67 : 10260 MB
2025-07-30 01:28:34,361 - INFO - node 68 : 11000 MB
2025-07-30 01:28:34,361 - INFO - node 69 : 7230 MB
2025-07-30 01:28:34,361 - INFO - node 70 : 11000 MB
2025-07-30 01:28:34,361 - INFO - node 71 : 9190 MB
2025-07-30 01:28:34,361 - INFO - node 72 : 9505 MB
2025-07-30 01:28:34,361 - INFO - node 73 : 10245 MB
2025-07-30 01:28:34,361 - INFO - node 74 : 7100 MB
2025-07-30 01:28:34,361 - INFO - node 75 : 9235 MB
2025-07-30 01:28:34,361 - INFO - node 76 : 9345 MB
2025-07-30 01:28:34,361 - INFO - node 77 : 7530 MB
2025-07-30 01:28:34,361 - INFO - node 78 : 11000 MB
2025-07-30 01:28:34,361 - INFO - node 79 : 6755 MB
2025-07-30 01:28:34,361 - INFO - node 80 : 8285 MB
2025-07-30 01:28:34,361 - INFO - node 81 : 6445 MB
2025-07-30 01:28:34,361 - INFO - node 82 : 9650 MB
2025-07-30 01:28:34,362 - INFO - node 83 : 10615 MB
2025-07-30 01:28:34,362 - INFO - node 84 : 9125 MB
2025-07-30 01:28:34,362 - INFO - node 85 : 6650 MB
2025-07-30 01:28:34,362 - INFO - node 86 : 8925 MB
2025-07-30 01:28:34,362 - INFO - node 87 : 7825 MB
2025-07-30 01:28:34,362 - INFO - node 88 : 8385 MB
2025-07-30 01:28:34,362 - INFO - node 89 : 4960 MB
2025-07-30 01:28:34,362 - INFO - node 90 : 6800 MB
2025-07-30 01:28:34,362 - INFO - node 91 : 10200 MB
2025-07-30 01:28:34,362 - INFO - node 92 : 10080 MB
2025-07-30 01:28:34,362 - INFO - node 93 : 9625 MB
2025-07-30 01:28:34,362 - INFO - node 94 : 8770 MB
2025-07-30 01:28:34,362 - INFO - node 95 : 8740 MB
2025-07-30 01:28:34,362 - INFO - node 96 : 6960 MB
2025-07-30 01:28:34,362 - INFO - node 97 : 9345 MB
2025-07-30 01:28:34,362 - INFO - node 98 : 6895 MB
2025-07-30 01:28:34,362 - INFO - node 99 : 10360 MB
2025-07-30 01:28:34,362 - INFO - node 100 : 9480 MB
2025-07-30 01:28:34,362 - INFO - node 101 : 8985 MB
2025-07-30 01:28:34,362 - INFO - node 102 : 9545 MB
2025-07-30 01:28:34,362 - INFO - node 103 : 10155 MB
2025-07-30 01:28:34,362 - INFO - node 104 : 10365 MB
2025-07-30 01:28:34,362 - INFO - node 105 : 10230 MB
2025-07-30 01:28:34,362 - INFO - node 106 : 9100 MB
2025-07-30 01:28:34,362 - INFO - node 107 : 7890 MB
2025-07-30 01:28:34,362 - INFO - node 108 : 11000 MB
2025-07-30 01:28:34,362 - INFO - node 109 : 8430 MB
2025-07-30 01:28:34,362 - INFO - node 110 : 9035 MB
2025-07-30 01:28:34,362 - INFO - node 111 : 6605 MB
2025-07-30 01:28:34,362 - INFO - node 112 : 8720 MB
2025-07-30 01:28:34,362 - INFO - node 113 : 8745 MB
2025-07-30 01:28:34,362 - INFO - node 114 : 7105 MB
2025-07-30 01:28:34,362 - INFO - node 115 : 8500 MB
2025-07-30 01:28:34,362 - INFO - node 116 : 6990 MB
2025-07-30 01:28:34,362 - INFO - node 117 : 10240 MB
2025-07-30 01:28:34,362 - INFO - node 118 : 10170 MB
2025-07-30 01:28:34,362 - INFO - node 119 : 8300 MB
2025-07-30 01:28:34,362 - INFO - node 120 : 10170 MB
2025-07-30 01:28:34,362 - INFO - node 121 : 10310 MB
2025-07-30 01:28:34,362 - INFO - node 122 : 9885 MB
2025-07-30 01:28:34,362 - INFO - node 123 : 10770 MB
2025-07-30 01:28:34,362 - INFO - node 124 : 9020 MB
2025-07-30 01:28:34,362 - INFO - node 125 : 11000 MB
2025-07-30 01:28:34,363 - INFO -   秒 0: 处理 108 个新请求
2025-07-30 01:28:34,383 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 6960MB, 函数类型: 1
2025-07-30 01:28:34,383 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:34,390 - INFO - 时间槽 6，秒 0: 执行中容器 132，等待请求 0
2025-07-30 01:28:34,390 - INFO -   秒 1: 处理 6 个新请求
2025-07-30 01:28:34,396 - INFO -   秒 2: 处理 3 个新请求
2025-07-30 01:28:34,398 - INFO -   秒 3: 处理 19 个新请求
2025-07-30 01:28:34,403 - INFO -   秒 4: 处理 15 个新请求
2025-07-30 01:28:34,409 - INFO -   秒 5: 处理 16 个新请求
2025-07-30 01:28:34,415 - INFO -   秒 6: 处理 13 个新请求
2025-07-30 01:28:34,418 - INFO -   秒 7: 处理 13 个新请求
2025-07-30 01:28:34,424 - INFO -   秒 8: 处理 9 个新请求
2025-07-30 01:28:34,430 - INFO -   秒 9: 处理 20 个新请求
2025-07-30 01:28:34,437 - INFO -   秒 10: 处理 17 个新请求
2025-07-30 01:28:34,446 - INFO - 时间槽 6，秒 10: 执行中容器 67，等待请求 0
2025-07-30 01:28:34,446 - INFO -   秒 11: 处理 17 个新请求
2025-07-30 01:28:34,455 - INFO -   秒 12: 处理 10 个新请求
2025-07-30 01:28:34,457 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 6910MB, 函数类型: 9
2025-07-30 01:28:34,457 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:34,461 - INFO -   秒 13: 处理 13 个新请求
2025-07-30 01:28:34,466 - INFO -   秒 14: 处理 20 个新请求
2025-07-30 01:28:34,471 - INFO -   秒 15: 处理 14 个新请求
2025-07-30 01:28:34,476 - INFO -   秒 16: 处理 14 个新请求
2025-07-30 01:28:34,478 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 6820MB, 函数类型: 2
2025-07-30 01:28:34,478 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:34,483 - INFO -   秒 17: 处理 15 个新请求
2025-07-30 01:28:34,489 - INFO -   秒 18: 处理 7 个新请求
2025-07-30 01:28:34,492 - INFO -   秒 19: 处理 27 个新请求
2025-07-30 01:28:34,500 - INFO -   秒 20: 处理 18 个新请求
2025-07-30 01:28:34,503 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 6755MB, 函数类型: 7
2025-07-30 01:28:34,503 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:28:34,505 - INFO - 时间槽 6，秒 20: 执行中容器 82，等待请求 0
2025-07-30 01:28:34,506 - INFO -   秒 21: 处理 10 个新请求
2025-07-30 01:28:34,509 - INFO -   秒 22: 处理 12 个新请求
2025-07-30 01:28:34,515 - INFO -   秒 23: 处理 16 个新请求
2025-07-30 01:28:34,520 - INFO -   秒 24: 处理 16 个新请求
2025-07-30 01:28:34,522 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 6635MB, 函数类型: 7
2025-07-30 01:28:34,522 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:28:34,524 - INFO -   秒 25: 处理 15 个新请求
2025-07-30 01:28:34,530 - INFO -   秒 26: 处理 9 个新请求
2025-07-30 01:28:34,533 - INFO -   秒 27: 处理 12 个新请求
2025-07-30 01:28:34,539 - INFO -   秒 28: 处理 13 个新请求
2025-07-30 01:28:34,542 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 6515MB, 函数类型: 1
2025-07-30 01:28:34,542 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:34,544 - INFO -   秒 29: 处理 31 个新请求
2025-07-30 01:28:34,552 - INFO -   秒 30: 处理 12 个新请求
2025-07-30 01:28:34,556 - INFO - 时间槽 6，秒 30: 执行中容器 72，等待请求 0
2025-07-30 01:28:34,556 - INFO -   秒 31: 处理 16 个新请求
2025-07-30 01:28:34,558 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 6465MB, 函数类型: 6
2025-07-30 01:28:34,558 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:34,563 - INFO -   秒 32: 处理 10 个新请求
2025-07-30 01:28:34,568 - INFO -   秒 33: 处理 8 个新请求
2025-07-30 01:28:34,571 - INFO -   秒 34: 处理 23 个新请求
2025-07-30 01:28:34,577 - INFO -   秒 35: 处理 15 个新请求
2025-07-30 01:28:34,584 - INFO -   秒 36: 处理 8 个新请求
2025-07-30 01:28:34,588 - INFO -   秒 37: 处理 14 个新请求
2025-07-30 01:28:34,594 - INFO -   秒 38: 处理 17 个新请求
2025-07-30 01:28:34,600 - INFO -   秒 39: 处理 22 个新请求
2025-07-30 01:28:34,604 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 6395MB, 函数类型: 6
2025-07-30 01:28:34,604 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:34,606 - INFO -   秒 40: 处理 13 个新请求
2025-07-30 01:28:34,611 - INFO - 时间槽 6，秒 40: 执行中容器 70，等待请求 0
2025-07-30 01:28:34,611 - INFO -   秒 41: 处理 10 个新请求
2025-07-30 01:28:34,615 - INFO -   秒 42: 处理 16 个新请求
2025-07-30 01:28:34,619 - INFO -   秒 43: 处理 15 个新请求
2025-07-30 01:28:34,623 - INFO -   秒 44: 处理 16 个新请求
2025-07-30 01:28:34,630 - INFO -   秒 45: 处理 13 个新请求
2025-07-30 01:28:34,633 - INFO -   秒 46: 处理 13 个新请求
2025-07-30 01:28:34,637 - INFO -   秒 47: 处理 18 个新请求
2025-07-30 01:28:34,641 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 6325MB, 函数类型: 2
2025-07-30 01:28:34,641 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:34,642 - INFO -   秒 48: 处理 14 个新请求
2025-07-30 01:28:34,648 - INFO -   秒 49: 处理 17 个新请求
2025-07-30 01:28:34,654 - INFO -   秒 50: 处理 12 个新请求
2025-07-30 01:28:34,658 - INFO - 时间槽 6，秒 50: 执行中容器 63，等待请求 0
2025-07-30 01:28:34,658 - INFO -   秒 51: 处理 12 个新请求
2025-07-30 01:28:34,663 - INFO -   秒 52: 处理 18 个新请求
2025-07-30 01:28:34,668 - INFO -   秒 53: 处理 13 个新请求
2025-07-30 01:28:34,673 - INFO -   秒 54: 处理 9 个新请求
2025-07-30 01:28:34,676 - INFO -   秒 55: 处理 14 个新请求
2025-07-30 01:28:34,679 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 6260MB, 函数类型: 1
2025-07-30 01:28:34,679 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:34,682 - INFO -   秒 56: 处理 7 个新请求
2025-07-30 01:28:34,685 - INFO -   秒 57: 处理 1 个新请求
2025-07-30 01:28:34,688 - INFO - 时间槽 6 结束时，还有 30 个容器正在执行，将继续在后台执行
2025-07-30 01:28:34,690 - INFO - --------处理时间槽 7 的请求，共 926 个--------
2025-07-30 01:28:34,690 - INFO - ----node memomry----
2025-07-30 01:28:34,690 - INFO - node 1 : 10445 MB
2025-07-30 01:28:34,690 - INFO - node 2 : 10720 MB
2025-07-30 01:28:34,690 - INFO - node 3 : 8495 MB
2025-07-30 01:28:34,690 - INFO - node 4 : 10505 MB
2025-07-30 01:28:34,690 - INFO - node 5 : 8690 MB
2025-07-30 01:28:34,690 - INFO - node 6 : 10695 MB
2025-07-30 01:28:34,690 - INFO - node 7 : 10645 MB
2025-07-30 01:28:34,690 - INFO - node 8 : 10090 MB
2025-07-30 01:28:34,690 - INFO - node 9 : 9575 MB
2025-07-30 01:28:34,691 - INFO - node 10 : 7915 MB
2025-07-30 01:28:34,691 - INFO - node 11 : 10315 MB
2025-07-30 01:28:34,691 - INFO - node 12 : 9330 MB
2025-07-30 01:28:34,691 - INFO - node 13 : 8660 MB
2025-07-30 01:28:34,691 - INFO - node 14 : 10255 MB
2025-07-30 01:28:34,691 - INFO - node 15 : 10155 MB
2025-07-30 01:28:34,691 - INFO - node 16 : 9510 MB
2025-07-30 01:28:34,691 - INFO - node 17 : 9595 MB
2025-07-30 01:28:34,691 - INFO - node 18 : 10770 MB
2025-07-30 01:28:34,691 - INFO - node 19 : 9935 MB
2025-07-30 01:28:34,691 - INFO - node 20 : 10490 MB
2025-07-30 01:28:34,691 - INFO - node 21 : 9200 MB
2025-07-30 01:28:34,691 - INFO - node 22 : 9245 MB
2025-07-30 01:28:34,691 - INFO - node 23 : 9250 MB
2025-07-30 01:28:34,691 - INFO - node 24 : 7335 MB
2025-07-30 01:28:34,691 - INFO - node 25 : 8670 MB
2025-07-30 01:28:34,691 - INFO - node 26 : 9195 MB
2025-07-30 01:28:34,691 - INFO - node 27 : 10485 MB
2025-07-30 01:28:34,691 - INFO - node 28 : 8670 MB
2025-07-30 01:28:34,691 - INFO - node 29 : 10010 MB
2025-07-30 01:28:34,691 - INFO - node 30 : 8475 MB
2025-07-30 01:28:34,691 - INFO - node 31 : 7485 MB
2025-07-30 01:28:34,691 - INFO - node 32 : 8800 MB
2025-07-30 01:28:34,691 - INFO - node 33 : 10210 MB
2025-07-30 01:28:34,691 - INFO - node 34 : 10235 MB
2025-07-30 01:28:34,691 - INFO - node 35 : 8590 MB
2025-07-30 01:28:34,691 - INFO - node 36 : 10375 MB
2025-07-30 01:28:34,691 - INFO - node 37 : 9340 MB
2025-07-30 01:28:34,691 - INFO - node 38 : 7810 MB
2025-07-30 01:28:34,691 - INFO - node 39 : 5950 MB
2025-07-30 01:28:34,691 - INFO - node 40 : 8930 MB
2025-07-30 01:28:34,691 - INFO - node 41 : 10195 MB
2025-07-30 01:28:34,691 - INFO - node 42 : 6925 MB
2025-07-30 01:28:34,691 - INFO - node 43 : 8640 MB
2025-07-30 01:28:34,691 - INFO - node 44 : 8125 MB
2025-07-30 01:28:34,691 - INFO - node 45 : 8905 MB
2025-07-30 01:28:34,691 - INFO - node 46 : 10790 MB
2025-07-30 01:28:34,691 - INFO - node 47 : 7935 MB
2025-07-30 01:28:34,691 - INFO - node 48 : 6460 MB
2025-07-30 01:28:34,691 - INFO - node 49 : 9595 MB
2025-07-30 01:28:34,691 - INFO - node 50 : 9240 MB
2025-07-30 01:28:34,691 - INFO - node 51 : 7295 MB
2025-07-30 01:28:34,691 - INFO - node 52 : 8895 MB
2025-07-30 01:28:34,691 - INFO - node 53 : 10180 MB
2025-07-30 01:28:34,691 - INFO - node 54 : 10165 MB
2025-07-30 01:28:34,691 - INFO - node 55 : 9365 MB
2025-07-30 01:28:34,691 - INFO - node 56 : 10630 MB
2025-07-30 01:28:34,691 - INFO - node 57 : 9115 MB
2025-07-30 01:28:34,691 - INFO - node 58 : 6205 MB
2025-07-30 01:28:34,691 - INFO - node 59 : 7335 MB
2025-07-30 01:28:34,691 - INFO - node 60 : 11000 MB
2025-07-30 01:28:34,691 - INFO - node 61 : 6740 MB
2025-07-30 01:28:34,691 - INFO - node 62 : 9195 MB
2025-07-30 01:28:34,691 - INFO - node 63 : 8305 MB
2025-07-30 01:28:34,691 - INFO - node 64 : 9350 MB
2025-07-30 01:28:34,691 - INFO - node 65 : 5180 MB
2025-07-30 01:28:34,691 - INFO - node 66 : 8365 MB
2025-07-30 01:28:34,692 - INFO - node 67 : 10220 MB
2025-07-30 01:28:34,692 - INFO - node 68 : 11000 MB
2025-07-30 01:28:34,692 - INFO - node 69 : 6040 MB
2025-07-30 01:28:34,692 - INFO - node 70 : 11000 MB
2025-07-30 01:28:34,692 - INFO - node 71 : 9100 MB
2025-07-30 01:28:34,692 - INFO - node 72 : 9400 MB
2025-07-30 01:28:34,692 - INFO - node 73 : 10195 MB
2025-07-30 01:28:34,692 - INFO - node 74 : 6425 MB
2025-07-30 01:28:34,692 - INFO - node 75 : 8695 MB
2025-07-30 01:28:34,692 - INFO - node 76 : 9165 MB
2025-07-30 01:28:34,692 - INFO - node 77 : 6175 MB
2025-07-30 01:28:34,692 - INFO - node 78 : 11000 MB
2025-07-30 01:28:34,692 - INFO - node 79 : 6440 MB
2025-07-30 01:28:34,692 - INFO - node 80 : 7875 MB
2025-07-30 01:28:34,692 - INFO - node 81 : 6195 MB
2025-07-30 01:28:34,692 - INFO - node 82 : 9580 MB
2025-07-30 01:28:34,692 - INFO - node 83 : 10615 MB
2025-07-30 01:28:34,692 - INFO - node 84 : 8780 MB
2025-07-30 01:28:34,692 - INFO - node 85 : 6160 MB
2025-07-30 01:28:34,692 - INFO - node 86 : 8695 MB
2025-07-30 01:28:34,693 - INFO - node 87 : 7125 MB
2025-07-30 01:28:34,693 - INFO - node 88 : 7865 MB
2025-07-30 01:28:34,693 - INFO - node 89 : 3965 MB
2025-07-30 01:28:34,693 - INFO - node 90 : 6350 MB
2025-07-30 01:28:34,693 - INFO - node 91 : 10140 MB
2025-07-30 01:28:34,693 - INFO - node 92 : 10015 MB
2025-07-30 01:28:34,693 - INFO - node 93 : 9545 MB
2025-07-30 01:28:34,693 - INFO - node 94 : 8440 MB
2025-07-30 01:28:34,693 - INFO - node 95 : 8460 MB
2025-07-30 01:28:34,693 - INFO - node 96 : 6210 MB
2025-07-30 01:28:34,693 - INFO - node 97 : 9305 MB
2025-07-30 01:28:34,693 - INFO - node 98 : 6025 MB
2025-07-30 01:28:34,693 - INFO - node 99 : 10300 MB
2025-07-30 01:28:34,693 - INFO - node 100 : 9300 MB
2025-07-30 01:28:34,693 - INFO - node 101 : 8530 MB
2025-07-30 01:28:34,693 - INFO - node 102 : 9175 MB
2025-07-30 01:28:34,693 - INFO - node 103 : 9975 MB
2025-07-30 01:28:34,693 - INFO - node 104 : 10275 MB
2025-07-30 01:28:34,693 - INFO - node 105 : 10180 MB
2025-07-30 01:28:34,693 - INFO - node 106 : 8605 MB
2025-07-30 01:28:34,693 - INFO - node 107 : 6800 MB
2025-07-30 01:28:34,693 - INFO - node 108 : 11000 MB
2025-07-30 01:28:34,693 - INFO - node 109 : 7795 MB
2025-07-30 01:28:34,693 - INFO - node 110 : 8485 MB
2025-07-30 01:28:34,693 - INFO - node 111 : 6160 MB
2025-07-30 01:28:34,693 - INFO - node 112 : 7985 MB
2025-07-30 01:28:34,693 - INFO - node 113 : 8485 MB
2025-07-30 01:28:34,693 - INFO - node 114 : 6255 MB
2025-07-30 01:28:34,693 - INFO - node 115 : 8195 MB
2025-07-30 01:28:34,693 - INFO - node 116 : 5790 MB
2025-07-30 01:28:34,693 - INFO - node 117 : 10190 MB
2025-07-30 01:28:34,693 - INFO - node 118 : 10130 MB
2025-07-30 01:28:34,693 - INFO - node 119 : 8100 MB
2025-07-30 01:28:34,693 - INFO - node 120 : 10080 MB
2025-07-30 01:28:34,693 - INFO - node 121 : 10105 MB
2025-07-30 01:28:34,693 - INFO - node 122 : 9835 MB
2025-07-30 01:28:34,693 - INFO - node 123 : 10325 MB
2025-07-30 01:28:34,693 - INFO - node 124 : 8890 MB
2025-07-30 01:28:34,693 - INFO - node 125 : 11000 MB
2025-07-30 01:28:34,694 - INFO -   秒 0: 处理 110 个新请求
2025-07-30 01:28:34,716 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 6210MB, 函数类型: 9
2025-07-30 01:28:34,716 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:34,722 - INFO - 时间槽 7，秒 0: 执行中容器 140，等待请求 0
2025-07-30 01:28:34,723 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:28:34,727 - INFO -   秒 2: 处理 6 个新请求
2025-07-30 01:28:34,731 - INFO -   秒 3: 处理 18 个新请求
2025-07-30 01:28:34,735 - INFO -   秒 4: 处理 15 个新请求
2025-07-30 01:28:34,742 - INFO -   秒 5: 处理 13 个新请求
2025-07-30 01:28:34,747 - INFO -   秒 6: 处理 18 个新请求
2025-07-30 01:28:34,752 - INFO -   秒 7: 处理 12 个新请求
2025-07-30 01:28:34,757 - INFO -   秒 8: 处理 10 个新请求
2025-07-30 01:28:34,762 - INFO -   秒 9: 处理 19 个新请求
2025-07-30 01:28:34,768 - INFO -   秒 10: 处理 17 个新请求
2025-07-30 01:28:34,774 - INFO - 时间槽 7，秒 10: 执行中容器 75，等待请求 0
2025-07-30 01:28:34,774 - INFO -   秒 11: 处理 15 个新请求
2025-07-30 01:28:34,781 - INFO -   秒 12: 处理 16 个新请求
2025-07-30 01:28:34,785 - INFO -   秒 13: 处理 14 个新请求
2025-07-30 01:28:34,794 - INFO -   秒 14: 处理 16 个新请求
2025-07-30 01:28:34,803 - INFO -   秒 15: 处理 18 个新请求
2025-07-30 01:28:34,807 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 6120MB, 函数类型: 4
2025-07-30 01:28:34,809 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:34,812 - INFO -   秒 16: 处理 14 个新请求
2025-07-30 01:28:34,816 - INFO -   秒 17: 处理 10 个新请求
2025-07-30 01:28:34,820 - INFO -   秒 18: 处理 11 个新请求
2025-07-30 01:28:34,824 - INFO -   秒 19: 处理 33 个新请求
2025-07-30 01:28:34,830 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 6080MB, 函数类型: 1
2025-07-30 01:28:34,830 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:34,832 - INFO -   秒 20: 处理 12 个新请求
2025-07-30 01:28:34,836 - INFO - 时间槽 7，秒 20: 执行中容器 81，等待请求 0
2025-07-30 01:28:34,836 - INFO -   秒 21: 处理 11 个新请求
2025-07-30 01:28:34,842 - INFO -   秒 22: 处理 11 个新请求
2025-07-30 01:28:34,848 - INFO -   秒 23: 处理 19 个新请求
2025-07-30 01:28:34,851 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 6030MB, 函数类型: 9
2025-07-30 01:28:34,851 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:34,855 - INFO -   秒 24: 处理 19 个新请求
2025-07-30 01:28:34,861 - INFO -   秒 25: 处理 8 个新请求
2025-07-30 01:28:34,865 - INFO -   秒 26: 处理 12 个新请求
2025-07-30 01:28:34,867 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 5940MB, 函数类型: 4
2025-07-30 01:28:34,867 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:34,869 - INFO -   秒 27: 处理 20 个新请求
2025-07-30 01:28:34,877 - INFO -   秒 28: 处理 4 个新请求
2025-07-30 01:28:34,881 - INFO -   秒 29: 处理 30 个新请求
2025-07-30 01:28:34,890 - INFO -   秒 30: 处理 16 个新请求
2025-07-30 01:28:34,897 - INFO - 时间槽 7，秒 30: 执行中容器 81，等待请求 0
2025-07-30 01:28:34,897 - INFO -   秒 31: 处理 14 个新请求
2025-07-30 01:28:34,902 - INFO -   秒 32: 处理 14 个新请求
2025-07-30 01:28:34,908 - INFO -   秒 33: 处理 9 个新请求
2025-07-30 01:28:34,911 - INFO -   秒 34: 处理 18 个新请求
2025-07-30 01:28:34,913 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 5900MB, 函数类型: 9
2025-07-30 01:28:34,913 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:34,917 - INFO -   秒 35: 处理 17 个新请求
2025-07-30 01:28:34,923 - INFO -   秒 36: 处理 12 个新请求
2025-07-30 01:28:34,928 - INFO -   秒 37: 处理 12 个新请求
2025-07-30 01:28:34,932 - INFO -   秒 38: 处理 12 个新请求
2025-07-30 01:28:34,935 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 5810MB, 函数类型: 1
2025-07-30 01:28:34,935 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:34,937 - INFO -   秒 39: 处理 29 个新请求
2025-07-30 01:28:34,943 - INFO -   秒 40: 处理 12 个新请求
2025-07-30 01:28:34,949 - INFO - 时间槽 7，秒 40: 执行中容器 80，等待请求 0
2025-07-30 01:28:34,949 - INFO -   秒 41: 处理 15 个新请求
2025-07-30 01:28:34,952 - INFO -   秒 42: 处理 13 个新请求
2025-07-30 01:28:34,956 - INFO -   秒 43: 处理 13 个新请求
2025-07-30 01:28:34,960 - INFO -   秒 44: 处理 16 个新请求
2025-07-30 01:28:34,967 - INFO -   秒 45: 处理 11 个新请求
2025-07-30 01:28:34,974 - INFO -   秒 46: 处理 21 个新请求
2025-07-30 01:28:34,979 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 5760MB, 函数类型: 1
2025-07-30 01:28:34,979 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:34,981 - INFO -   秒 47: 处理 12 个新请求
2025-07-30 01:28:34,984 - INFO -   秒 48: 处理 14 个新请求
2025-07-30 01:28:34,989 - INFO -   秒 49: 处理 20 个新请求
2025-07-30 01:28:34,995 - INFO -   秒 50: 处理 16 个新请求
2025-07-30 01:28:34,997 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 5710MB, 函数类型: 2
2025-07-30 01:28:34,997 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:34,999 - INFO - 时间槽 7，秒 50: 执行中容器 81，等待请求 0
2025-07-30 01:28:35,000 - INFO -   秒 51: 处理 9 个新请求
2025-07-30 01:28:35,002 - INFO -   秒 52: 处理 13 个新请求
2025-07-30 01:28:35,008 - INFO -   秒 53: 处理 15 个新请求
2025-07-30 01:28:35,015 - INFO -   秒 54: 处理 18 个新请求
2025-07-30 01:28:35,021 - INFO -   秒 55: 处理 12 个新请求
2025-07-30 01:28:35,025 - INFO -   秒 56: 处理 7 个新请求
2025-07-30 01:28:35,029 - INFO -   秒 57: 处理 1 个新请求
2025-07-30 01:28:35,034 - INFO - 时间槽 7 结束时，还有 37 个容器正在执行，将继续在后台执行
2025-07-30 01:28:35,035 - INFO - --------处理时间槽 8 的请求，共 834 个--------
2025-07-30 01:28:35,035 - INFO - ----node memomry----
2025-07-30 01:28:35,035 - INFO - node 1 : 10445 MB
2025-07-30 01:28:35,035 - INFO - node 2 : 10720 MB
2025-07-30 01:28:35,035 - INFO - node 3 : 8030 MB
2025-07-30 01:28:35,035 - INFO - node 4 : 10385 MB
2025-07-30 01:28:35,035 - INFO - node 5 : 8560 MB
2025-07-30 01:28:35,035 - INFO - node 6 : 10655 MB
2025-07-30 01:28:35,035 - INFO - node 7 : 10575 MB
2025-07-30 01:28:35,035 - INFO - node 8 : 10040 MB
2025-07-30 01:28:35,035 - INFO - node 9 : 8995 MB
2025-07-30 01:28:35,035 - INFO - node 10 : 7490 MB
2025-07-30 01:28:35,035 - INFO - node 11 : 10250 MB
2025-07-30 01:28:35,035 - INFO - node 12 : 8980 MB
2025-07-30 01:28:35,035 - INFO - node 13 : 8195 MB
2025-07-30 01:28:35,035 - INFO - node 14 : 10205 MB
2025-07-30 01:28:35,036 - INFO - node 15 : 10065 MB
2025-07-30 01:28:35,036 - INFO - node 16 : 9260 MB
2025-07-30 01:28:35,036 - INFO - node 17 : 9320 MB
2025-07-30 01:28:35,036 - INFO - node 18 : 10705 MB
2025-07-30 01:28:35,036 - INFO - node 19 : 9775 MB
2025-07-30 01:28:35,036 - INFO - node 20 : 10450 MB
2025-07-30 01:28:35,036 - INFO - node 21 : 9110 MB
2025-07-30 01:28:35,036 - INFO - node 22 : 8995 MB
2025-07-30 01:28:35,036 - INFO - node 23 : 8940 MB
2025-07-30 01:28:35,036 - INFO - node 24 : 6955 MB
2025-07-30 01:28:35,036 - INFO - node 25 : 8480 MB
2025-07-30 01:28:35,036 - INFO - node 26 : 9005 MB
2025-07-30 01:28:35,036 - INFO - node 27 : 10425 MB
2025-07-30 01:28:35,036 - INFO - node 28 : 8140 MB
2025-07-30 01:28:35,036 - INFO - node 29 : 9800 MB
2025-07-30 01:28:35,036 - INFO - node 30 : 8255 MB
2025-07-30 01:28:35,036 - INFO - node 31 : 7085 MB
2025-07-30 01:28:35,036 - INFO - node 32 : 8490 MB
2025-07-30 01:28:35,036 - INFO - node 33 : 10150 MB
2025-07-30 01:28:35,036 - INFO - node 34 : 10195 MB
2025-07-30 01:28:35,036 - INFO - node 35 : 8220 MB
2025-07-30 01:28:35,036 - INFO - node 36 : 10325 MB
2025-07-30 01:28:35,036 - INFO - node 37 : 9085 MB
2025-07-30 01:28:35,036 - INFO - node 38 : 7560 MB
2025-07-30 01:28:35,036 - INFO - node 39 : 5345 MB
2025-07-30 01:28:35,036 - INFO - node 40 : 8740 MB
2025-07-30 01:28:35,036 - INFO - node 41 : 10130 MB
2025-07-30 01:28:35,036 - INFO - node 42 : 6165 MB
2025-07-30 01:28:35,036 - INFO - node 43 : 8325 MB
2025-07-30 01:28:35,036 - INFO - node 44 : 7985 MB
2025-07-30 01:28:35,036 - INFO - node 45 : 8680 MB
2025-07-30 01:28:35,036 - INFO - node 46 : 10585 MB
2025-07-30 01:28:35,036 - INFO - node 47 : 7725 MB
2025-07-30 01:28:35,036 - INFO - node 48 : 6015 MB
2025-07-30 01:28:35,036 - INFO - node 49 : 9475 MB
2025-07-30 01:28:35,036 - INFO - node 50 : 9080 MB
2025-07-30 01:28:35,036 - INFO - node 51 : 6665 MB
2025-07-30 01:28:35,036 - INFO - node 52 : 8750 MB
2025-07-30 01:28:35,036 - INFO - node 53 : 10130 MB
2025-07-30 01:28:35,036 - INFO - node 54 : 10115 MB
2025-07-30 01:28:35,036 - INFO - node 55 : 8915 MB
2025-07-30 01:28:35,036 - INFO - node 56 : 10590 MB
2025-07-30 01:28:35,036 - INFO - node 57 : 8715 MB
2025-07-30 01:28:35,036 - INFO - node 58 : 5440 MB
2025-07-30 01:28:35,036 - INFO - node 59 : 6775 MB
2025-07-30 01:28:35,036 - INFO - node 60 : 11000 MB
2025-07-30 01:28:35,036 - INFO - node 61 : 6230 MB
2025-07-30 01:28:35,036 - INFO - node 62 : 9005 MB
2025-07-30 01:28:35,036 - INFO - node 63 : 8065 MB
2025-07-30 01:28:35,036 - INFO - node 64 : 9190 MB
2025-07-30 01:28:35,036 - INFO - node 65 : 4800 MB
2025-07-30 01:28:35,036 - INFO - node 66 : 8215 MB
2025-07-30 01:28:35,036 - INFO - node 67 : 10130 MB
2025-07-30 01:28:35,036 - INFO - node 68 : 11000 MB
2025-07-30 01:28:35,036 - INFO - node 69 : 4735 MB
2025-07-30 01:28:35,036 - INFO - node 70 : 11000 MB
2025-07-30 01:28:35,036 - INFO - node 71 : 8910 MB
2025-07-30 01:28:35,036 - INFO - node 72 : 9270 MB
2025-07-30 01:28:35,036 - INFO - node 73 : 10105 MB
2025-07-30 01:28:35,036 - INFO - node 74 : 5910 MB
2025-07-30 01:28:35,036 - INFO - node 75 : 8385 MB
2025-07-30 01:28:35,036 - INFO - node 76 : 9045 MB
2025-07-30 01:28:35,036 - INFO - node 77 : 5675 MB
2025-07-30 01:28:35,036 - INFO - node 78 : 11000 MB
2025-07-30 01:28:35,036 - INFO - node 79 : 5900 MB
2025-07-30 01:28:35,036 - INFO - node 80 : 7530 MB
2025-07-30 01:28:35,036 - INFO - node 81 : 5865 MB
2025-07-30 01:28:35,036 - INFO - node 82 : 9530 MB
2025-07-30 01:28:35,036 - INFO - node 83 : 10550 MB
2025-07-30 01:28:35,036 - INFO - node 84 : 8310 MB
2025-07-30 01:28:35,036 - INFO - node 85 : 5735 MB
2025-07-30 01:28:35,037 - INFO - node 86 : 7730 MB
2025-07-30 01:28:35,037 - INFO - node 87 : 6240 MB
2025-07-30 01:28:35,037 - INFO - node 88 : 7160 MB
2025-07-30 01:28:35,037 - INFO - node 89 : 3400 MB
2025-07-30 01:28:35,037 - INFO - node 90 : 5260 MB
2025-07-30 01:28:35,037 - INFO - node 91 : 10100 MB
2025-07-30 01:28:35,037 - INFO - node 92 : 9655 MB
2025-07-30 01:28:35,037 - INFO - node 93 : 9455 MB
2025-07-30 01:28:35,037 - INFO - node 94 : 7730 MB
2025-07-30 01:28:35,037 - INFO - node 95 : 8070 MB
2025-07-30 01:28:35,037 - INFO - node 96 : 5645 MB
2025-07-30 01:28:35,037 - INFO - node 97 : 9095 MB
2025-07-30 01:28:35,037 - INFO - node 98 : 5535 MB
2025-07-30 01:28:35,037 - INFO - node 99 : 10260 MB
2025-07-30 01:28:35,037 - INFO - node 100 : 9075 MB
2025-07-30 01:28:35,037 - INFO - node 101 : 7770 MB
2025-07-30 01:28:35,037 - INFO - node 102 : 8895 MB
2025-07-30 01:28:35,037 - INFO - node 103 : 9835 MB
2025-07-30 01:28:35,037 - INFO - node 104 : 10235 MB
2025-07-30 01:28:35,037 - INFO - node 105 : 10090 MB
2025-07-30 01:28:35,037 - INFO - node 106 : 8430 MB
2025-07-30 01:28:35,037 - INFO - node 107 : 6455 MB
2025-07-30 01:28:35,037 - INFO - node 108 : 11000 MB
2025-07-30 01:28:35,037 - INFO - node 109 : 7590 MB
2025-07-30 01:28:35,037 - INFO - node 110 : 8290 MB
2025-07-30 01:28:35,037 - INFO - node 111 : 5130 MB
2025-07-30 01:28:35,037 - INFO - node 112 : 6975 MB
2025-07-30 01:28:35,037 - INFO - node 113 : 8270 MB
2025-07-30 01:28:35,037 - INFO - node 114 : 4705 MB
2025-07-30 01:28:35,037 - INFO - node 115 : 7820 MB
2025-07-30 01:28:35,037 - INFO - node 116 : 5200 MB
2025-07-30 01:28:35,037 - INFO - node 117 : 9830 MB
2025-07-30 01:28:35,037 - INFO - node 118 : 9770 MB
2025-07-30 01:28:35,037 - INFO - node 119 : 7660 MB
2025-07-30 01:28:35,037 - INFO - node 120 : 9960 MB
2025-07-30 01:28:35,037 - INFO - node 121 : 10055 MB
2025-07-30 01:28:35,037 - INFO - node 122 : 9785 MB
2025-07-30 01:28:35,037 - INFO - node 123 : 10325 MB
2025-07-30 01:28:35,037 - INFO - node 124 : 8680 MB
2025-07-30 01:28:35,037 - INFO - node 125 : 10785 MB
2025-07-30 01:28:35,038 - INFO -   秒 0: 处理 106 个新请求
2025-07-30 01:28:35,064 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 5645MB, 函数类型: 9
2025-07-30 01:28:35,064 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:35,069 - INFO - 时间槽 8，秒 0: 执行中容器 143，等待请求 0
2025-07-30 01:28:35,069 - INFO -   秒 1: 处理 7 个新请求
2025-07-30 01:28:35,074 - INFO -   秒 2: 处理 1 个新请求
2025-07-30 01:28:35,076 - INFO -   秒 3: 处理 16 个新请求
2025-07-30 01:28:35,083 - INFO -   秒 4: 处理 13 个新请求
2025-07-30 01:28:35,086 - INFO -   秒 5: 处理 13 个新请求
2025-07-30 01:28:35,092 - INFO -   秒 6: 处理 17 个新请求
2025-07-30 01:28:35,099 - INFO -   秒 7: 处理 15 个新请求
2025-07-30 01:28:35,107 - INFO -   秒 8: 处理 9 个新请求
2025-07-30 01:28:35,110 - INFO -   秒 9: 处理 7 个新请求
2025-07-30 01:28:35,114 - INFO -   秒 10: 处理 14 个新请求
2025-07-30 01:28:35,117 - INFO - 时间槽 8，秒 10: 执行中容器 66，等待请求 0
2025-07-30 01:28:35,117 - INFO -   秒 11: 处理 21 个新请求
2025-07-30 01:28:35,122 - INFO - DEBUG: 节点96内存检查 - 需要内存: 60.0MB, 可用内存: 5555MB, 函数类型: 3
2025-07-30 01:28:35,123 - INFO - DEBUG: 节点96创建User层容器，减少内存 60MB，类型: 3
2025-07-30 01:28:35,125 - INFO -   秒 12: 处理 16 个新请求
2025-07-30 01:28:35,132 - INFO -   秒 13: 处理 10 个新请求
2025-07-30 01:28:35,138 - INFO -   秒 14: 处理 15 个新请求
2025-07-30 01:28:35,149 - INFO -   秒 15: 处理 18 个新请求
2025-07-30 01:28:35,157 - INFO -   秒 16: 处理 11 个新请求
2025-07-30 01:28:35,164 - INFO -   秒 17: 处理 5 个新请求
2025-07-30 01:28:35,167 - INFO -   秒 18: 处理 21 个新请求
2025-07-30 01:28:35,176 - INFO -   秒 19: 处理 12 个新请求
2025-07-30 01:28:35,184 - INFO -   秒 20: 处理 14 个新请求
2025-07-30 01:28:35,186 - INFO - DEBUG: 节点96内存检查 - 需要内存: 60.0MB, 可用内存: 5495MB, 函数类型: 3
2025-07-30 01:28:35,186 - INFO - DEBUG: 节点96创建User层容器，减少内存 60MB，类型: 3
2025-07-30 01:28:35,192 - INFO - 时间槽 8，秒 20: 执行中容器 79，等待请求 0
2025-07-30 01:28:35,192 - INFO -   秒 21: 处理 18 个新请求
2025-07-30 01:28:35,201 - INFO -   秒 22: 处理 11 个新请求
2025-07-30 01:28:35,206 - INFO -   秒 23: 处理 14 个新请求
2025-07-30 01:28:35,210 - INFO -   秒 24: 处理 14 个新请求
2025-07-30 01:28:35,214 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 5435MB, 函数类型: 9
2025-07-30 01:28:35,214 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:35,216 - INFO -   秒 25: 处理 10 个新请求
2025-07-30 01:28:35,219 - INFO -   秒 26: 处理 15 个新请求
2025-07-30 01:28:35,225 - INFO -   秒 27: 处理 14 个新请求
2025-07-30 01:28:35,232 - INFO -   秒 28: 处理 12 个新请求
2025-07-30 01:28:35,236 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 5345MB, 函数类型: 9
2025-07-30 01:28:35,236 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:35,239 - INFO -   秒 29: 处理 12 个新请求
2025-07-30 01:28:35,243 - INFO -   秒 30: 处理 18 个新请求
2025-07-30 01:28:35,250 - INFO - 时间槽 8，秒 30: 执行中容器 79，等待请求 0
2025-07-30 01:28:35,250 - INFO -   秒 31: 处理 12 个新请求
2025-07-30 01:28:35,253 - INFO -   秒 32: 处理 13 个新请求
2025-07-30 01:28:35,258 - INFO -   秒 33: 处理 15 个新请求
2025-07-30 01:28:35,264 - INFO -   秒 34: 处理 11 个新请求
2025-07-30 01:28:35,268 - INFO -   秒 35: 处理 10 个新请求
2025-07-30 01:28:35,272 - INFO -   秒 36: 处理 17 个新请求
2025-07-30 01:28:35,275 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 5255MB, 函数类型: 1
2025-07-30 01:28:35,275 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:35,279 - INFO -   秒 37: 处理 20 个新请求
2025-07-30 01:28:35,284 - INFO -   秒 38: 处理 11 个新请求
2025-07-30 01:28:35,287 - INFO -   秒 39: 处理 14 个新请求
2025-07-30 01:28:35,294 - INFO -   秒 40: 处理 14 个新请求
2025-07-30 01:28:35,301 - INFO - 时间槽 8，秒 40: 执行中容器 76，等待请求 0
2025-07-30 01:28:35,301 - INFO -   秒 41: 处理 9 个新请求
2025-07-30 01:28:35,307 - INFO -   秒 42: 处理 15 个新请求
2025-07-30 01:28:35,314 - INFO -   秒 43: 处理 11 个新请求
2025-07-30 01:28:35,319 - INFO -   秒 44: 处理 14 个新请求
2025-07-30 01:28:35,326 - INFO -   秒 45: 处理 11 个新请求
2025-07-30 01:28:35,330 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 5205MB, 函数类型: 1
2025-07-30 01:28:35,331 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:35,335 - INFO -   秒 46: 处理 18 个新请求
2025-07-30 01:28:35,343 - INFO -   秒 47: 处理 13 个新请求
2025-07-30 01:28:35,351 - INFO -   秒 48: 处理 17 个新请求
2025-07-30 01:28:35,357 - INFO -   秒 49: 处理 9 个新请求
2025-07-30 01:28:35,361 - INFO -   秒 50: 处理 14 个新请求
2025-07-30 01:28:35,364 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 5155MB, 函数类型: 9
2025-07-30 01:28:35,364 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:35,367 - INFO - 时间槽 8，秒 50: 执行中容器 73，等待请求 0
2025-07-30 01:28:35,367 - INFO -   秒 51: 处理 9 个新请求
2025-07-30 01:28:35,372 - INFO -   秒 52: 处理 16 个新请求
2025-07-30 01:28:35,380 - INFO -   秒 53: 处理 12 个新请求
2025-07-30 01:28:35,384 - INFO -   秒 54: 处理 12 个新请求
2025-07-30 01:28:35,388 - INFO -   秒 55: 处理 13 个新请求
2025-07-30 01:28:35,390 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 5065MB, 函数类型: 9
2025-07-30 01:28:35,391 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:35,392 - INFO -   秒 56: 处理 5 个新请求
2025-07-30 01:28:35,401 - INFO - 时间槽 8 结束时，还有 42 个容器正在执行，将继续在后台执行
2025-07-30 01:28:35,402 - INFO - --------处理时间槽 9 的请求，共 886 个--------
2025-07-30 01:28:35,402 - INFO - ----node memomry----
2025-07-30 01:28:35,402 - INFO - node 1 : 10445 MB
2025-07-30 01:28:35,402 - INFO - node 2 : 10680 MB
2025-07-30 01:28:35,402 - INFO - node 3 : 7790 MB
2025-07-30 01:28:35,402 - INFO - node 4 : 10265 MB
2025-07-30 01:28:35,402 - INFO - node 5 : 8420 MB
2025-07-30 01:28:35,402 - INFO - node 6 : 10655 MB
2025-07-30 01:28:35,402 - INFO - node 7 : 10535 MB
2025-07-30 01:28:35,402 - INFO - node 8 : 9640 MB
2025-07-30 01:28:35,402 - INFO - node 9 : 8845 MB
2025-07-30 01:28:35,402 - INFO - node 10 : 7080 MB
2025-07-30 01:28:35,402 - INFO - node 11 : 9850 MB
2025-07-30 01:28:35,402 - INFO - node 12 : 8865 MB
2025-07-30 01:28:35,402 - INFO - node 13 : 7635 MB
2025-07-30 01:28:35,402 - INFO - node 14 : 10135 MB
2025-07-30 01:28:35,402 - INFO - node 15 : 10025 MB
2025-07-30 01:28:35,402 - INFO - node 16 : 9210 MB
2025-07-30 01:28:35,402 - INFO - node 17 : 9060 MB
2025-07-30 01:28:35,402 - INFO - node 18 : 10705 MB
2025-07-30 01:28:35,402 - INFO - node 19 : 9665 MB
2025-07-30 01:28:35,402 - INFO - node 20 : 10400 MB
2025-07-30 01:28:35,402 - INFO - node 21 : 9070 MB
2025-07-30 01:28:35,402 - INFO - node 22 : 8840 MB
2025-07-30 01:28:35,402 - INFO - node 23 : 8740 MB
2025-07-30 01:28:35,402 - INFO - node 24 : 6610 MB
2025-07-30 01:28:35,402 - INFO - node 25 : 8215 MB
2025-07-30 01:28:35,402 - INFO - node 26 : 8645 MB
2025-07-30 01:28:35,402 - INFO - node 27 : 10355 MB
2025-07-30 01:28:35,402 - INFO - node 28 : 7665 MB
2025-07-30 01:28:35,402 - INFO - node 29 : 9685 MB
2025-07-30 01:28:35,402 - INFO - node 30 : 8095 MB
2025-07-30 01:28:35,402 - INFO - node 31 : 6720 MB
2025-07-30 01:28:35,402 - INFO - node 32 : 8260 MB
2025-07-30 01:28:35,402 - INFO - node 33 : 10060 MB
2025-07-30 01:28:35,402 - INFO - node 34 : 10145 MB
2025-07-30 01:28:35,402 - INFO - node 35 : 7870 MB
2025-07-30 01:28:35,402 - INFO - node 36 : 10285 MB
2025-07-30 01:28:35,402 - INFO - node 37 : 8695 MB
2025-07-30 01:28:35,402 - INFO - node 38 : 6950 MB
2025-07-30 01:28:35,402 - INFO - node 39 : 4990 MB
2025-07-30 01:28:35,402 - INFO - node 40 : 8650 MB
2025-07-30 01:28:35,402 - INFO - node 41 : 10010 MB
2025-07-30 01:28:35,402 - INFO - node 42 : 5610 MB
2025-07-30 01:28:35,402 - INFO - node 43 : 7585 MB
2025-07-30 01:28:35,402 - INFO - node 44 : 7695 MB
2025-07-30 01:28:35,402 - INFO - node 45 : 8520 MB
2025-07-30 01:28:35,402 - INFO - node 46 : 10585 MB
2025-07-30 01:28:35,402 - INFO - node 47 : 7415 MB
2025-07-30 01:28:35,402 - INFO - node 48 : 5375 MB
2025-07-30 01:28:35,402 - INFO - node 49 : 9475 MB
2025-07-30 01:28:35,402 - INFO - node 50 : 8915 MB
2025-07-30 01:28:35,402 - INFO - node 51 : 6005 MB
2025-07-30 01:28:35,402 - INFO - node 52 : 8515 MB
2025-07-30 01:28:35,402 - INFO - node 53 : 10040 MB
2025-07-30 01:28:35,402 - INFO - node 54 : 10045 MB
2025-07-30 01:28:35,402 - INFO - node 55 : 8875 MB
2025-07-30 01:28:35,402 - INFO - node 56 : 10295 MB
2025-07-30 01:28:35,402 - INFO - node 57 : 8575 MB
2025-07-30 01:28:35,402 - INFO - node 58 : 5025 MB
2025-07-30 01:28:35,402 - INFO - node 59 : 5585 MB
2025-07-30 01:28:35,402 - INFO - node 60 : 11000 MB
2025-07-30 01:28:35,402 - INFO - node 61 : 5670 MB
2025-07-30 01:28:35,402 - INFO - node 62 : 8845 MB
2025-07-30 01:28:35,402 - INFO - node 63 : 7975 MB
2025-07-30 01:28:35,402 - INFO - node 64 : 8780 MB
2025-07-30 01:28:35,402 - INFO - node 65 : 4075 MB
2025-07-30 01:28:35,402 - INFO - node 66 : 8000 MB
2025-07-30 01:28:35,402 - INFO - node 67 : 10010 MB
2025-07-30 01:28:35,402 - INFO - node 68 : 11000 MB
2025-07-30 01:28:35,402 - INFO - node 69 : 4020 MB
2025-07-30 01:28:35,402 - INFO - node 70 : 11000 MB
2025-07-30 01:28:35,402 - INFO - node 71 : 8665 MB
2025-07-30 01:28:35,402 - INFO - node 72 : 9120 MB
2025-07-30 01:28:35,402 - INFO - node 73 : 10040 MB
2025-07-30 01:28:35,402 - INFO - node 74 : 5420 MB
2025-07-30 01:28:35,402 - INFO - node 75 : 7720 MB
2025-07-30 01:28:35,402 - INFO - node 76 : 8235 MB
2025-07-30 01:28:35,402 - INFO - node 77 : 5100 MB
2025-07-30 01:28:35,402 - INFO - node 78 : 11000 MB
2025-07-30 01:28:35,404 - INFO - node 79 : 5555 MB
2025-07-30 01:28:35,404 - INFO - node 80 : 7270 MB
2025-07-30 01:28:35,404 - INFO - node 81 : 4615 MB
2025-07-30 01:28:35,404 - INFO - node 82 : 9480 MB
2025-07-30 01:28:35,404 - INFO - node 83 : 10255 MB
2025-07-30 01:28:35,404 - INFO - node 84 : 7930 MB
2025-07-30 01:28:35,404 - INFO - node 85 : 4940 MB
2025-07-30 01:28:35,404 - INFO - node 86 : 7380 MB
2025-07-30 01:28:35,404 - INFO - node 87 : 5885 MB
2025-07-30 01:28:35,404 - INFO - node 88 : 6460 MB
2025-07-30 01:28:35,404 - INFO - node 89 : 2835 MB
2025-07-30 01:28:35,404 - INFO - node 90 : 4270 MB
2025-07-30 01:28:35,404 - INFO - node 91 : 9805 MB
2025-07-30 01:28:35,404 - INFO - node 92 : 9230 MB
2025-07-30 01:28:35,404 - INFO - node 93 : 9390 MB
2025-07-30 01:28:35,404 - INFO - node 94 : 7420 MB
2025-07-30 01:28:35,404 - INFO - node 95 : 7745 MB
2025-07-30 01:28:35,404 - INFO - node 96 : 4975 MB
2025-07-30 01:28:35,404 - INFO - node 97 : 8990 MB
2025-07-30 01:28:35,404 - INFO - node 98 : 4135 MB
2025-07-30 01:28:35,404 - INFO - node 99 : 10195 MB
2025-07-30 01:28:35,404 - INFO - node 100 : 9005 MB
2025-07-30 01:28:35,404 - INFO - node 101 : 7535 MB
2025-07-30 01:28:35,404 - INFO - node 102 : 8765 MB
2025-07-30 01:28:35,404 - INFO - node 103 : 9745 MB
2025-07-30 01:28:35,404 - INFO - node 104 : 10185 MB
2025-07-30 01:28:35,404 - INFO - node 105 : 10050 MB
2025-07-30 01:28:35,404 - INFO - node 106 : 8090 MB
2025-07-30 01:28:35,404 - INFO - node 107 : 5670 MB
2025-07-30 01:28:35,404 - INFO - node 108 : 11000 MB
2025-07-30 01:28:35,404 - INFO - node 109 : 7255 MB
2025-07-30 01:28:35,404 - INFO - node 110 : 7980 MB
2025-07-30 01:28:35,404 - INFO - node 111 : 4130 MB
2025-07-30 01:28:35,404 - INFO - node 112 : 6565 MB
2025-07-30 01:28:35,404 - INFO - node 113 : 8010 MB
2025-07-30 01:28:35,404 - INFO - node 114 : 4375 MB
2025-07-30 01:28:35,404 - INFO - node 115 : 7495 MB
2025-07-30 01:28:35,404 - INFO - node 116 : 4530 MB
2025-07-30 01:28:35,404 - INFO - node 117 : 9710 MB
2025-07-30 01:28:35,404 - INFO - node 118 : 9650 MB
2025-07-30 01:28:35,404 - INFO - node 119 : 7360 MB
2025-07-30 01:28:35,404 - INFO - node 120 : 9895 MB
2025-07-30 01:28:35,404 - INFO - node 121 : 10055 MB
2025-07-30 01:28:35,404 - INFO - node 122 : 9745 MB
2025-07-30 01:28:35,404 - INFO - node 123 : 10325 MB
2025-07-30 01:28:35,404 - INFO - node 124 : 8490 MB
2025-07-30 01:28:35,404 - INFO - node 125 : 10785 MB
2025-07-30 01:28:35,405 - INFO -   秒 0: 处理 109 个新请求
2025-07-30 01:28:35,424 - INFO - DEBUG: 节点96内存检查 - 需要内存: 60.0MB, 可用内存: 4975MB, 函数类型: 3
2025-07-30 01:28:35,424 - INFO - DEBUG: 节点96创建User层容器，减少内存 60MB，类型: 3
2025-07-30 01:28:35,431 - INFO - 时间槽 9，秒 0: 执行中容器 151，等待请求 0
2025-07-30 01:28:35,432 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:28:35,436 - INFO -   秒 2: 处理 4 个新请求
2025-07-30 01:28:35,440 - INFO -   秒 3: 处理 19 个新请求
2025-07-30 01:28:35,446 - INFO -   秒 4: 处理 13 个新请求
2025-07-30 01:28:35,451 - INFO -   秒 5: 处理 15 个新请求
2025-07-30 01:28:35,456 - INFO -   秒 6: 处理 13 个新请求
2025-07-30 01:28:35,459 - INFO -   秒 7: 处理 14 个新请求
2025-07-30 01:28:35,466 - INFO -   秒 8: 处理 12 个新请求
2025-07-30 01:28:35,470 - INFO -   秒 9: 处理 15 个新请求
2025-07-30 01:28:35,476 - INFO -   秒 10: 处理 15 个新请求
2025-07-30 01:28:35,484 - INFO - 时间槽 9，秒 10: 执行中容器 81，等待请求 0
2025-07-30 01:28:35,484 - INFO -   秒 11: 处理 16 个新请求
2025-07-30 01:28:35,490 - INFO -   秒 12: 处理 12 个新请求
2025-07-30 01:28:35,492 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 4915MB, 函数类型: 9
2025-07-30 01:28:35,492 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:35,495 - INFO -   秒 13: 处理 11 个新请求
2025-07-30 01:28:35,501 - INFO -   秒 14: 处理 21 个新请求
2025-07-30 01:28:35,508 - INFO -   秒 15: 处理 17 个新请求
2025-07-30 01:28:35,514 - INFO -   秒 16: 处理 9 个新请求
2025-07-30 01:28:35,518 - INFO -   秒 17: 处理 13 个新请求
2025-07-30 01:28:35,523 - INFO -   秒 18: 处理 12 个新请求
2025-07-30 01:28:35,528 - INFO -   秒 19: 处理 22 个新请求
2025-07-30 01:28:35,536 - INFO -   秒 20: 处理 21 个新请求
2025-07-30 01:28:35,540 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 4825MB, 函数类型: 4
2025-07-30 01:28:35,541 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:35,543 - INFO - 时间槽 9，秒 20: 执行中容器 94，等待请求 0
2025-07-30 01:28:35,543 - INFO -   秒 21: 处理 13 个新请求
2025-07-30 01:28:35,548 - INFO -   秒 22: 处理 7 个新请求
2025-07-30 01:28:35,551 - INFO -   秒 23: 处理 14 个新请求
2025-07-30 01:28:35,554 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 4785MB, 函数类型: 9
2025-07-30 01:28:35,554 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:35,557 - INFO -   秒 24: 处理 23 个新请求
2025-07-30 01:28:35,565 - INFO -   秒 25: 处理 11 个新请求
2025-07-30 01:28:35,569 - INFO -   秒 26: 处理 11 个新请求
2025-07-30 01:28:35,575 - INFO -   秒 27: 处理 11 个新请求
2025-07-30 01:28:35,580 - INFO -   秒 28: 处理 12 个新请求
2025-07-30 01:28:35,583 - INFO -   秒 29: 处理 24 个新请求
2025-07-30 01:28:35,594 - INFO -   秒 30: 处理 17 个新请求
2025-07-30 01:28:35,604 - INFO - 时间槽 9，秒 30: 执行中容器 92，等待请求 0
2025-07-30 01:28:35,605 - INFO -   秒 31: 处理 15 个新请求
2025-07-30 01:28:35,608 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 4695MB, 函数类型: 2
2025-07-30 01:28:35,608 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:35,611 - INFO -   秒 32: 处理 8 个新请求
2025-07-30 01:28:35,615 - INFO -   秒 33: 处理 16 个新请求
2025-07-30 01:28:35,621 - INFO -   秒 34: 处理 15 个新请求
2025-07-30 01:28:35,626 - INFO -   秒 35: 处理 11 个新请求
2025-07-30 01:28:35,632 - INFO -   秒 36: 处理 14 个新请求
2025-07-30 01:28:35,636 - INFO -   秒 37: 处理 13 个新请求
2025-07-30 01:28:35,641 - INFO -   秒 38: 处理 16 个新请求
2025-07-30 01:28:35,647 - INFO -   秒 39: 处理 23 个新请求
2025-07-30 01:28:35,650 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 4630MB, 函数类型: 4
2025-07-30 01:28:35,650 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:35,653 - INFO -   秒 40: 处理 13 个新请求
2025-07-30 01:28:35,657 - INFO - 时间槽 9，秒 40: 执行中容器 92，等待请求 0
2025-07-30 01:28:35,658 - INFO -   秒 41: 处理 10 个新请求
2025-07-30 01:28:35,662 - INFO -   秒 42: 处理 14 个新请求
2025-07-30 01:28:35,666 - INFO -   秒 43: 处理 12 个新请求
2025-07-30 01:28:35,671 - INFO -   秒 44: 处理 20 个新请求
2025-07-30 01:28:35,678 - INFO -   秒 45: 处理 12 个新请求
2025-07-30 01:28:35,682 - INFO -   秒 46: 处理 11 个新请求
2025-07-30 01:28:35,685 - INFO -   秒 47: 处理 13 个新请求
2025-07-30 01:28:35,687 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 4590MB, 函数类型: 9
2025-07-30 01:28:35,687 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:35,690 - INFO -   秒 48: 处理 24 个新请求
2025-07-30 01:28:35,697 - INFO -   秒 49: 处理 14 个新请求
2025-07-30 01:28:35,701 - INFO -   秒 50: 处理 8 个新请求
2025-07-30 01:28:35,704 - INFO - 时间槽 9，秒 50: 执行中容器 76，等待请求 0
2025-07-30 01:28:35,704 - INFO -   秒 51: 处理 15 个新请求
2025-07-30 01:28:35,710 - INFO -   秒 52: 处理 11 个新请求
2025-07-30 01:28:35,715 - INFO -   秒 53: 处理 16 个新请求
2025-07-30 01:28:35,720 - INFO -   秒 54: 处理 9 个新请求
2025-07-30 01:28:35,724 - INFO -   秒 55: 处理 17 个新请求
2025-07-30 01:28:35,726 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 4500MB, 函数类型: 4
2025-07-30 01:28:35,726 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:35,731 - INFO -   秒 56: 处理 6 个新请求
2025-07-30 01:28:35,737 - INFO - 时间槽 9 结束时，还有 45 个容器正在执行，将继续在后台执行
2025-07-30 01:28:35,739 - INFO - --------处理时间槽 10 的请求，共 911 个--------
2025-07-30 01:28:35,739 - INFO - ----node memomry----
2025-07-30 01:28:35,739 - INFO - node 1 : 10445 MB
2025-07-30 01:28:35,739 - INFO - node 2 : 10680 MB
2025-07-30 01:28:35,739 - INFO - node 3 : 7340 MB
2025-07-30 01:28:35,739 - INFO - node 4 : 10145 MB
2025-07-30 01:28:35,739 - INFO - node 5 : 8380 MB
2025-07-30 01:28:35,739 - INFO - node 6 : 10360 MB
2025-07-30 01:28:35,739 - INFO - node 7 : 10535 MB
2025-07-30 01:28:35,739 - INFO - node 8 : 9640 MB
2025-07-30 01:28:35,739 - INFO - node 9 : 8695 MB
2025-07-30 01:28:35,739 - INFO - node 10 : 6855 MB
2025-07-30 01:28:35,739 - INFO - node 11 : 9810 MB
2025-07-30 01:28:35,739 - INFO - node 12 : 8865 MB
2025-07-30 01:28:35,739 - INFO - node 13 : 7405 MB
2025-07-30 01:28:35,740 - INFO - node 14 : 10135 MB
2025-07-30 01:28:35,740 - INFO - node 15 : 10025 MB
2025-07-30 01:28:35,740 - INFO - node 16 : 9050 MB
2025-07-30 01:28:35,740 - INFO - node 17 : 8930 MB
2025-07-30 01:28:35,740 - INFO - node 18 : 10705 MB
2025-07-30 01:28:35,740 - INFO - node 19 : 9185 MB
2025-07-30 01:28:35,740 - INFO - node 20 : 10400 MB
2025-07-30 01:28:35,740 - INFO - node 21 : 8960 MB
2025-07-30 01:28:35,740 - INFO - node 22 : 8490 MB
2025-07-30 01:28:35,740 - INFO - node 23 : 8610 MB
2025-07-30 01:28:35,740 - INFO - node 24 : 6235 MB
2025-07-30 01:28:35,740 - INFO - node 25 : 7840 MB
2025-07-30 01:28:35,740 - INFO - node 26 : 8455 MB
2025-07-30 01:28:35,740 - INFO - node 27 : 10315 MB
2025-07-30 01:28:35,740 - INFO - node 28 : 7035 MB
2025-07-30 01:28:35,740 - INFO - node 29 : 9645 MB
2025-07-30 01:28:35,740 - INFO - node 30 : 7805 MB
2025-07-30 01:28:35,740 - INFO - node 31 : 6540 MB
2025-07-30 01:28:35,740 - INFO - node 32 : 8020 MB
2025-07-30 01:28:35,740 - INFO - node 33 : 9995 MB
2025-07-30 01:28:35,740 - INFO - node 34 : 10105 MB
2025-07-30 01:28:35,740 - INFO - node 35 : 7555 MB
2025-07-30 01:28:35,740 - INFO - node 36 : 9990 MB
2025-07-30 01:28:35,740 - INFO - node 37 : 8205 MB
2025-07-30 01:28:35,740 - INFO - node 38 : 6360 MB
2025-07-30 01:28:35,740 - INFO - node 39 : 4440 MB
2025-07-30 01:28:35,740 - INFO - node 40 : 8600 MB
2025-07-30 01:28:35,740 - INFO - node 41 : 9960 MB
2025-07-30 01:28:35,740 - INFO - node 42 : 4365 MB
2025-07-30 01:28:35,740 - INFO - node 43 : 6985 MB
2025-07-30 01:28:35,740 - INFO - node 44 : 6950 MB
2025-07-30 01:28:35,740 - INFO - node 45 : 8290 MB
2025-07-30 01:28:35,740 - INFO - node 46 : 10585 MB
2025-07-30 01:28:35,740 - INFO - node 47 : 6830 MB
2025-07-30 01:28:35,740 - INFO - node 48 : 5275 MB
2025-07-30 01:28:35,740 - INFO - node 49 : 9410 MB
2025-07-30 01:28:35,740 - INFO - node 50 : 8755 MB
2025-07-30 01:28:35,740 - INFO - node 51 : 5640 MB
2025-07-30 01:28:35,740 - INFO - node 52 : 8235 MB
2025-07-30 01:28:35,740 - INFO - node 53 : 9980 MB
2025-07-30 01:28:35,740 - INFO - node 54 : 9685 MB
2025-07-30 01:28:35,740 - INFO - node 55 : 8785 MB
2025-07-30 01:28:35,740 - INFO - node 56 : 10255 MB
2025-07-30 01:28:35,740 - INFO - node 57 : 8415 MB
2025-07-30 01:28:35,741 - INFO - node 58 : 4410 MB
2025-07-30 01:28:35,741 - INFO - node 59 : 5170 MB
2025-07-30 01:28:35,741 - INFO - node 60 : 11000 MB
2025-07-30 01:28:35,741 - INFO - node 61 : 5020 MB
2025-07-30 01:28:35,741 - INFO - node 62 : 8640 MB
2025-07-30 01:28:35,741 - INFO - node 63 : 7475 MB
2025-07-30 01:28:35,741 - INFO - node 64 : 8715 MB
2025-07-30 01:28:35,741 - INFO - node 65 : 3245 MB
2025-07-30 01:28:35,741 - INFO - node 66 : 7660 MB
2025-07-30 01:28:35,741 - INFO - node 67 : 9970 MB
2025-07-30 01:28:35,741 - INFO - node 68 : 11000 MB
2025-07-30 01:28:35,741 - INFO - node 69 : 3360 MB
2025-07-30 01:28:35,741 - INFO - node 70 : 10845 MB
2025-07-30 01:28:35,741 - INFO - node 71 : 8445 MB
2025-07-30 01:28:35,741 - INFO - node 72 : 9010 MB
2025-07-30 01:28:35,741 - INFO - node 73 : 9975 MB
2025-07-30 01:28:35,741 - INFO - node 74 : 5030 MB
2025-07-30 01:28:35,741 - INFO - node 75 : 7230 MB
2025-07-30 01:28:35,741 - INFO - node 76 : 7885 MB
2025-07-30 01:28:35,741 - INFO - node 77 : 3775 MB
2025-07-30 01:28:35,741 - INFO - node 78 : 11000 MB
2025-07-30 01:28:35,741 - INFO - node 79 : 5165 MB
2025-07-30 01:28:35,741 - INFO - node 80 : 6645 MB
2025-07-30 01:28:35,741 - INFO - node 81 : 4390 MB
2025-07-30 01:28:35,741 - INFO - node 82 : 9430 MB
2025-07-30 01:28:35,741 - INFO - node 83 : 10215 MB
2025-07-30 01:28:35,742 - INFO - node 84 : 7605 MB
2025-07-30 01:28:35,742 - INFO - node 85 : 4050 MB
2025-07-30 01:28:35,742 - INFO - node 86 : 7080 MB
2025-07-30 01:28:35,742 - INFO - node 87 : 5670 MB
2025-07-30 01:28:35,742 - INFO - node 88 : 5935 MB
2025-07-30 01:28:35,742 - INFO - node 89 : 1270 MB
2025-07-30 01:28:35,742 - INFO - node 90 : 3865 MB
2025-07-30 01:28:35,742 - INFO - node 91 : 9715 MB
2025-07-30 01:28:35,742 - INFO - node 92 : 9140 MB
2025-07-30 01:28:35,742 - INFO - node 93 : 9260 MB
2025-07-30 01:28:35,742 - INFO - node 94 : 7180 MB
2025-07-30 01:28:35,742 - INFO - node 95 : 7125 MB
2025-07-30 01:28:35,742 - INFO - node 96 : 4460 MB
2025-07-30 01:28:35,742 - INFO - node 97 : 8825 MB
2025-07-30 01:28:35,742 - INFO - node 98 : 3555 MB
2025-07-30 01:28:35,742 - INFO - node 99 : 10145 MB
2025-07-30 01:28:35,742 - INFO - node 100 : 8825 MB
2025-07-30 01:28:35,742 - INFO - node 101 : 7390 MB
2025-07-30 01:28:35,742 - INFO - node 102 : 8195 MB
2025-07-30 01:28:35,742 - INFO - node 103 : 9490 MB
2025-07-30 01:28:35,742 - INFO - node 104 : 10125 MB
2025-07-30 01:28:35,742 - INFO - node 105 : 9960 MB
2025-07-30 01:28:35,742 - INFO - node 106 : 7900 MB
2025-07-30 01:28:35,743 - INFO - node 107 : 5220 MB
2025-07-30 01:28:35,743 - INFO - node 108 : 11000 MB
2025-07-30 01:28:35,743 - INFO - node 109 : 6875 MB
2025-07-30 01:28:35,743 - INFO - node 110 : 7790 MB
2025-07-30 01:28:35,743 - INFO - node 111 : 3340 MB
2025-07-30 01:28:35,743 - INFO - node 112 : 6090 MB
2025-07-30 01:28:35,743 - INFO - node 113 : 7855 MB
2025-07-30 01:28:35,743 - INFO - node 114 : 2915 MB
2025-07-30 01:28:35,743 - INFO - node 115 : 7155 MB
2025-07-30 01:28:35,743 - INFO - node 116 : 3825 MB
2025-07-30 01:28:35,743 - INFO - node 117 : 9620 MB
2025-07-30 01:28:35,743 - INFO - node 118 : 9580 MB
2025-07-30 01:28:35,743 - INFO - node 119 : 6935 MB
2025-07-30 01:28:35,743 - INFO - node 120 : 9830 MB
2025-07-30 01:28:35,743 - INFO - node 121 : 9935 MB
2025-07-30 01:28:35,743 - INFO - node 122 : 9695 MB
2025-07-30 01:28:35,743 - INFO - node 123 : 10030 MB
2025-07-30 01:28:35,743 - INFO - node 124 : 8070 MB
2025-07-30 01:28:35,743 - INFO - node 125 : 10785 MB
2025-07-30 01:28:35,743 - INFO -   秒 0: 处理 110 个新请求
2025-07-30 01:28:35,766 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 4460MB, 函数类型: 6
2025-07-30 01:28:35,766 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:35,774 - INFO - 时间槽 10，秒 0: 执行中容器 155，等待请求 0
2025-07-30 01:28:35,774 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:28:35,780 - INFO -   秒 2: 处理 4 个新请求
2025-07-30 01:28:35,784 - INFO -   秒 3: 处理 21 个新请求
2025-07-30 01:28:35,793 - INFO -   秒 4: 处理 12 个新请求
2025-07-30 01:28:35,799 - INFO -   秒 5: 处理 14 个新请求
2025-07-30 01:28:35,804 - INFO -   秒 6: 处理 14 个新请求
2025-07-30 01:28:35,812 - INFO -   秒 7: 处理 14 个新请求
2025-07-30 01:28:35,819 - INFO -   秒 8: 处理 10 个新请求
2025-07-30 01:28:35,827 - INFO -   秒 9: 处理 19 个新请求
2025-07-30 01:28:35,835 - INFO -   秒 10: 处理 18 个新请求
2025-07-30 01:28:35,844 - INFO - 时间槽 10，秒 10: 执行中容器 87，等待请求 0
2025-07-30 01:28:35,845 - INFO -   秒 11: 处理 21 个新请求
2025-07-30 01:28:35,852 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 4390MB, 函数类型: 9
2025-07-30 01:28:35,852 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:35,857 - INFO -   秒 12: 处理 10 个新请求
2025-07-30 01:28:35,867 - INFO -   秒 13: 处理 7 个新请求
2025-07-30 01:28:35,873 - INFO -   秒 14: 处理 16 个新请求
2025-07-30 01:28:35,885 - INFO -   秒 15: 处理 25 个新请求
2025-07-30 01:28:35,898 - INFO -   秒 16: 处理 10 个新请求
2025-07-30 01:28:35,902 - INFO -   秒 17: 处理 15 个新请求
2025-07-30 01:28:35,909 - INFO -   秒 18: 处理 9 个新请求
2025-07-30 01:28:35,914 - INFO -   秒 19: 处理 30 个新请求
2025-07-30 01:28:35,921 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 4300MB, 函数类型: 4
2025-07-30 01:28:35,921 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:35,924 - INFO -   秒 20: 处理 12 个新请求
2025-07-30 01:28:35,930 - INFO - 时间槽 10，秒 20: 执行中容器 92，等待请求 0
2025-07-30 01:28:35,930 - INFO -   秒 21: 处理 13 个新请求
2025-07-30 01:28:35,934 - INFO -   秒 22: 处理 9 个新请求
2025-07-30 01:28:35,938 - INFO -   秒 23: 处理 18 个新请求
2025-07-30 01:28:35,942 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 4260MB, 函数类型: 1
2025-07-30 01:28:35,942 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:35,946 - INFO -   秒 24: 处理 21 个新请求
2025-07-30 01:28:35,953 - INFO -   秒 25: 处理 11 个新请求
2025-07-30 01:28:35,962 - INFO -   秒 26: 处理 8 个新请求
2025-07-30 01:28:35,967 - INFO -   秒 27: 处理 13 个新请求
2025-07-30 01:28:35,972 - INFO -   秒 28: 处理 9 个新请求
2025-07-30 01:28:35,978 - INFO -   秒 29: 处理 32 个新请求
2025-07-30 01:28:35,990 - INFO -   秒 30: 处理 18 个新请求
2025-07-30 01:28:35,996 - INFO - 时间槽 10，秒 30: 执行中容器 95，等待请求 0
2025-07-30 01:28:35,996 - INFO -   秒 31: 处理 15 个新请求
2025-07-30 01:28:35,999 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 4210MB, 函数类型: 1
2025-07-30 01:28:35,999 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:36,002 - INFO -   秒 32: 处理 8 个新请求
2025-07-30 01:28:36,005 - INFO -   秒 33: 处理 10 个新请求
2025-07-30 01:28:36,011 - INFO -   秒 34: 处理 20 个新请求
2025-07-30 01:28:36,023 - INFO -   秒 35: 处理 14 个新请求
2025-07-30 01:28:36,031 - INFO -   秒 36: 处理 17 个新请求
2025-07-30 01:28:36,037 - INFO -   秒 37: 处理 8 个新请求
2025-07-30 01:28:36,044 - INFO -   秒 38: 处理 10 个新请求
2025-07-30 01:28:36,051 - INFO -   秒 39: 处理 31 个新请求
2025-07-30 01:28:36,063 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 4160MB, 函数类型: 7
2025-07-30 01:28:36,063 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:28:36,066 - INFO -   秒 40: 处理 11 个新请求
2025-07-30 01:28:36,074 - INFO - 时间槽 10，秒 40: 执行中容器 94，等待请求 0
2025-07-30 01:28:36,075 - INFO -   秒 41: 处理 12 个新请求
2025-07-30 01:28:36,085 - INFO -   秒 42: 处理 10 个新请求
2025-07-30 01:28:36,093 - INFO -   秒 43: 处理 13 个新请求
2025-07-30 01:28:36,101 - INFO -   秒 44: 处理 19 个新请求
2025-07-30 01:28:36,110 - INFO -   秒 45: 处理 14 个新请求
2025-07-30 01:28:36,117 - INFO -   秒 46: 处理 15 个新请求
2025-07-30 01:28:36,125 - INFO -   秒 47: 处理 15 个新请求
2025-07-30 01:28:36,134 - INFO -   秒 48: 处理 16 个新请求
2025-07-30 01:28:36,145 - INFO -   秒 49: 处理 20 个新请求
2025-07-30 01:28:36,156 - INFO -   秒 50: 处理 11 个新请求
2025-07-30 01:28:36,163 - INFO - 时间槽 10，秒 50: 执行中容器 88，等待请求 0
2025-07-30 01:28:36,163 - INFO -   秒 51: 处理 13 个新请求
2025-07-30 01:28:36,167 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 4040MB, 函数类型: 2
2025-07-30 01:28:36,167 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:36,171 - INFO -   秒 52: 处理 17 个新请求
2025-07-30 01:28:36,177 - INFO -   秒 53: 处理 12 个新请求
2025-07-30 01:28:36,185 - INFO -   秒 54: 处理 7 个新请求
2025-07-30 01:28:36,193 - INFO -   秒 55: 处理 16 个新请求
2025-07-30 01:28:36,202 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 3975MB, 函数类型: 9
2025-07-30 01:28:36,202 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:36,208 - INFO -   秒 56: 处理 10 个新请求
2025-07-30 01:28:36,232 - INFO - 时间槽 10 结束时，还有 50 个容器正在执行，将继续在后台执行
2025-07-30 01:28:36,234 - INFO - --------处理时间槽 11 的请求，共 889 个--------
2025-07-30 01:28:36,235 - INFO - ----node memomry----
2025-07-30 01:28:36,235 - INFO - node 1 : 10445 MB
2025-07-30 01:28:36,235 - INFO - node 2 : 10525 MB
2025-07-30 01:28:36,235 - INFO - node 3 : 7065 MB
2025-07-30 01:28:36,235 - INFO - node 4 : 10020 MB
2025-07-30 01:28:36,235 - INFO - node 5 : 8310 MB
2025-07-30 01:28:36,235 - INFO - node 6 : 10360 MB
2025-07-30 01:28:36,235 - INFO - node 7 : 10495 MB
2025-07-30 01:28:36,235 - INFO - node 8 : 9485 MB
2025-07-30 01:28:36,235 - INFO - node 9 : 8540 MB
2025-07-30 01:28:36,235 - INFO - node 10 : 6725 MB
2025-07-30 01:28:36,235 - INFO - node 11 : 9810 MB
2025-07-30 01:28:36,235 - INFO - node 12 : 8865 MB
2025-07-30 01:28:36,235 - INFO - node 13 : 6790 MB
2025-07-30 01:28:36,235 - INFO - node 14 : 10135 MB
2025-07-30 01:28:36,235 - INFO - node 15 : 9955 MB
2025-07-30 01:28:36,235 - INFO - node 16 : 8960 MB
2025-07-30 01:28:36,235 - INFO - node 17 : 8805 MB
2025-07-30 01:28:36,235 - INFO - node 18 : 10705 MB
2025-07-30 01:28:36,235 - INFO - node 19 : 9135 MB
2025-07-30 01:28:36,235 - INFO - node 20 : 10350 MB
2025-07-30 01:28:36,235 - INFO - node 21 : 8920 MB
2025-07-30 01:28:36,235 - INFO - node 22 : 7935 MB
2025-07-30 01:28:36,235 - INFO - node 23 : 8490 MB
2025-07-30 01:28:36,235 - INFO - node 24 : 5735 MB
2025-07-30 01:28:36,235 - INFO - node 25 : 7760 MB
2025-07-30 01:28:36,235 - INFO - node 26 : 8325 MB
2025-07-30 01:28:36,235 - INFO - node 27 : 10020 MB
2025-07-30 01:28:36,235 - INFO - node 28 : 6925 MB
2025-07-30 01:28:36,235 - INFO - node 29 : 9580 MB
2025-07-30 01:28:36,235 - INFO - node 30 : 7535 MB
2025-07-30 01:28:36,235 - INFO - node 31 : 6325 MB
2025-07-30 01:28:36,235 - INFO - node 32 : 7690 MB
2025-07-30 01:28:36,235 - INFO - node 33 : 9945 MB
2025-07-30 01:28:36,235 - INFO - node 34 : 10015 MB
2025-07-30 01:28:36,235 - INFO - node 35 : 7270 MB
2025-07-30 01:28:36,235 - INFO - node 36 : 9850 MB
2025-07-30 01:28:36,235 - INFO - node 37 : 8065 MB
2025-07-30 01:28:36,235 - INFO - node 38 : 5520 MB
2025-07-30 01:28:36,235 - INFO - node 39 : 3560 MB
2025-07-30 01:28:36,235 - INFO - node 40 : 8460 MB
2025-07-30 01:28:36,235 - INFO - node 41 : 9910 MB
2025-07-30 01:28:36,235 - INFO - node 42 : 3965 MB
2025-07-30 01:28:36,235 - INFO - node 43 : 6815 MB
2025-07-30 01:28:36,235 - INFO - node 44 : 6290 MB
2025-07-30 01:28:36,235 - INFO - node 45 : 8000 MB
2025-07-30 01:28:36,235 - INFO - node 46 : 10585 MB
2025-07-30 01:28:36,235 - INFO - node 47 : 6190 MB
2025-07-30 01:28:36,235 - INFO - node 48 : 4755 MB
2025-07-30 01:28:36,235 - INFO - node 49 : 9360 MB
2025-07-30 01:28:36,235 - INFO - node 50 : 8595 MB
2025-07-30 01:28:36,236 - INFO - node 51 : 5080 MB
2025-07-30 01:28:36,236 - INFO - node 52 : 7710 MB
2025-07-30 01:28:36,236 - INFO - node 53 : 9940 MB
2025-07-30 01:28:36,236 - INFO - node 54 : 9645 MB
2025-07-30 01:28:36,236 - INFO - node 55 : 8335 MB
2025-07-30 01:28:36,236 - INFO - node 56 : 10205 MB
2025-07-30 01:28:36,236 - INFO - node 57 : 8145 MB
2025-07-30 01:28:36,236 - INFO - node 58 : 3715 MB
2025-07-30 01:28:36,236 - INFO - node 59 : 4405 MB
2025-07-30 01:28:36,236 - INFO - node 60 : 11000 MB
2025-07-30 01:28:36,236 - INFO - node 61 : 4330 MB
2025-07-30 01:28:36,236 - INFO - node 62 : 8570 MB
2025-07-30 01:28:36,236 - INFO - node 63 : 7395 MB
2025-07-30 01:28:36,236 - INFO - node 64 : 8675 MB
2025-07-30 01:28:36,236 - INFO - node 65 : 2290 MB
2025-07-30 01:28:36,236 - INFO - node 66 : 7505 MB
2025-07-30 01:28:36,236 - INFO - node 67 : 9905 MB
2025-07-30 01:28:36,236 - INFO - node 68 : 11000 MB
2025-07-30 01:28:36,236 - INFO - node 69 : 2550 MB
2025-07-30 01:28:36,236 - INFO - node 70 : 10845 MB
2025-07-30 01:28:36,236 - INFO - node 71 : 8290 MB
2025-07-30 01:28:36,236 - INFO - node 72 : 8870 MB
2025-07-30 01:28:36,236 - INFO - node 73 : 9615 MB
2025-07-30 01:28:36,236 - INFO - node 74 : 4445 MB
2025-07-30 01:28:36,236 - INFO - node 75 : 7070 MB
2025-07-30 01:28:36,236 - INFO - node 76 : 7645 MB
2025-07-30 01:28:36,236 - INFO - node 77 : 3085 MB
2025-07-30 01:28:36,236 - INFO - node 78 : 11000 MB
2025-07-30 01:28:36,236 - INFO - node 79 : 4460 MB
2025-07-30 01:28:36,236 - INFO - node 80 : 6050 MB
2025-07-30 01:28:36,238 - INFO - node 81 : 4025 MB
2025-07-30 01:28:36,238 - INFO - node 82 : 9340 MB
2025-07-30 01:28:36,238 - INFO - node 83 : 10215 MB
2025-07-30 01:28:36,238 - INFO - node 84 : 7235 MB
2025-07-30 01:28:36,238 - INFO - node 85 : 3350 MB
2025-07-30 01:28:36,238 - INFO - node 86 : 6110 MB
2025-07-30 01:28:36,238 - INFO - node 87 : 4825 MB
2025-07-30 01:28:36,238 - INFO - node 88 : 5705 MB
2025-07-30 01:28:36,238 - INFO - node 89 : 580 MB
2025-07-30 01:28:36,238 - INFO - node 90 : 3355 MB
2025-07-30 01:28:36,238 - INFO - node 91 : 9625 MB
2025-07-30 01:28:36,238 - INFO - node 92 : 9035 MB
2025-07-30 01:28:36,238 - INFO - node 93 : 9120 MB
2025-07-30 01:28:36,238 - INFO - node 94 : 6760 MB
2025-07-30 01:28:36,238 - INFO - node 95 : 6700 MB
2025-07-30 01:28:36,238 - INFO - node 96 : 3885 MB
2025-07-30 01:28:36,238 - INFO - node 97 : 8655 MB
2025-07-30 01:28:36,239 - INFO - node 98 : 3080 MB
2025-07-30 01:28:36,239 - INFO - node 99 : 9940 MB
2025-07-30 01:28:36,239 - INFO - node 100 : 8725 MB
2025-07-30 01:28:36,239 - INFO - node 101 : 7080 MB
2025-07-30 01:28:36,239 - INFO - node 102 : 8035 MB
2025-07-30 01:28:36,239 - INFO - node 103 : 9350 MB
2025-07-30 01:28:36,239 - INFO - node 104 : 10075 MB
2025-07-30 01:28:36,239 - INFO - node 105 : 9920 MB
2025-07-30 01:28:36,239 - INFO - node 106 : 7810 MB
2025-07-30 01:28:36,239 - INFO - node 107 : 4590 MB
2025-07-30 01:28:36,239 - INFO - node 108 : 11000 MB
2025-07-30 01:28:36,239 - INFO - node 109 : 6655 MB
2025-07-30 01:28:36,239 - INFO - node 110 : 7465 MB
2025-07-30 01:28:36,239 - INFO - node 111 : 2730 MB
2025-07-30 01:28:36,239 - INFO - node 112 : 5410 MB
2025-07-30 01:28:36,239 - INFO - node 113 : 7165 MB
2025-07-30 01:28:36,239 - INFO - node 114 : 1965 MB
2025-07-30 01:28:36,239 - INFO - node 115 : 6690 MB
2025-07-30 01:28:36,239 - INFO - node 116 : 3195 MB
2025-07-30 01:28:36,239 - INFO - node 117 : 9530 MB
2025-07-30 01:28:36,239 - INFO - node 118 : 9540 MB
2025-07-30 01:28:36,239 - INFO - node 119 : 6600 MB
2025-07-30 01:28:36,239 - INFO - node 120 : 9770 MB
2025-07-30 01:28:36,239 - INFO - node 121 : 9870 MB
2025-07-30 01:28:36,239 - INFO - node 122 : 9655 MB
2025-07-30 01:28:36,239 - INFO - node 123 : 9910 MB
2025-07-30 01:28:36,239 - INFO - node 124 : 7840 MB
2025-07-30 01:28:36,239 - INFO - node 125 : 10630 MB
2025-07-30 01:28:36,240 - INFO -   秒 0: 处理 110 个新请求
2025-07-30 01:28:36,266 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:36,266 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:36,271 - INFO - 时间槽 11，秒 0: 执行中容器 160，等待请求 0
2025-07-30 01:28:36,271 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:28:36,275 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:36,276 - INFO -   秒 2: 处理 4 个新请求
2025-07-30 01:28:36,278 - INFO -   秒 3: 处理 19 个新请求
2025-07-30 01:28:36,283 - INFO - DEBUG: 节点96内存检查 - 需要内存: 360.0MB, 可用内存: 3885MB, 函数类型: 5
2025-07-30 01:28:36,283 - INFO - DEBUG: 节点96创建User层容器，减少内存 360MB，类型: 5
2025-07-30 01:28:36,286 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 360MB，类型: 5
2025-07-30 01:28:36,287 - INFO -   秒 4: 处理 13 个新请求
2025-07-30 01:28:36,293 - INFO -   秒 5: 处理 16 个新请求
2025-07-30 01:28:36,301 - INFO -   秒 6: 处理 12 个新请求
2025-07-30 01:28:36,307 - INFO -   秒 7: 处理 15 个新请求
2025-07-30 01:28:36,310 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:36,310 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:36,314 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:36,314 - INFO -   秒 8: 处理 8 个新请求
2025-07-30 01:28:36,317 - INFO -   秒 9: 处理 18 个新请求
2025-07-30 01:28:36,324 - INFO -   秒 10: 处理 16 个新请求
2025-07-30 01:28:36,331 - INFO - 时间槽 11，秒 10: 执行中容器 94，等待请求 0
2025-07-30 01:28:36,331 - INFO -   秒 11: 处理 17 个新请求
2025-07-30 01:28:36,334 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:36,334 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:36,337 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:36,337 - INFO -   秒 12: 处理 14 个新请求
2025-07-30 01:28:36,343 - INFO -   秒 13: 处理 9 个新请求
2025-07-30 01:28:36,349 - INFO -   秒 14: 处理 18 个新请求
2025-07-30 01:28:36,355 - INFO -   秒 15: 处理 17 个新请求
2025-07-30 01:28:36,357 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 3885MB, 函数类型: 9
2025-07-30 01:28:36,357 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:36,362 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:28:36,362 - INFO -   秒 16: 处理 10 个新请求
2025-07-30 01:28:36,367 - INFO -   秒 17: 处理 14 个新请求
2025-07-30 01:28:36,373 - INFO -   秒 18: 处理 14 个新请求
2025-07-30 01:28:36,382 - INFO -   秒 19: 处理 24 个新请求
2025-07-30 01:28:36,385 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:36,385 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:36,390 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:36,390 - INFO -   秒 20: 处理 15 个新请求
2025-07-30 01:28:36,398 - INFO - 时间槽 11，秒 20: 执行中容器 96，等待请求 0
2025-07-30 01:28:36,398 - INFO -   秒 21: 处理 14 个新请求
2025-07-30 01:28:36,404 - INFO -   秒 22: 处理 10 个新请求
2025-07-30 01:28:36,408 - INFO -   秒 23: 处理 17 个新请求
2025-07-30 01:28:36,412 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:36,412 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:36,417 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:36,417 - INFO -   秒 24: 处理 18 个新请求
2025-07-30 01:28:36,426 - INFO -   秒 25: 处理 9 个新请求
2025-07-30 01:28:36,432 - INFO -   秒 26: 处理 11 个新请求
2025-07-30 01:28:36,437 - INFO -   秒 27: 处理 12 个新请求
2025-07-30 01:28:36,439 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:36,439 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:36,443 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:36,443 - INFO -   秒 28: 处理 12 个新请求
2025-07-30 01:28:36,449 - INFO -   秒 29: 处理 21 个新请求
2025-07-30 01:28:36,455 - INFO -   秒 30: 处理 22 个新请求
2025-07-30 01:28:36,463 - INFO - 时间槽 11，秒 30: 执行中容器 103，等待请求 0
2025-07-30 01:28:36,463 - INFO -   秒 31: 处理 12 个新请求
2025-07-30 01:28:36,466 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 3885MB, 函数类型: 9
2025-07-30 01:28:36,466 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:36,470 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:28:36,470 - INFO -   秒 32: 处理 13 个新请求
2025-07-30 01:28:36,476 - INFO -   秒 33: 处理 10 个新请求
2025-07-30 01:28:36,483 - INFO -   秒 34: 处理 17 个新请求
2025-07-30 01:28:36,487 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:36,487 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:36,492 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:36,493 - INFO -   秒 35: 处理 16 个新请求
2025-07-30 01:28:36,502 - INFO -   秒 36: 处理 9 个新请求
2025-07-30 01:28:36,508 - INFO -   秒 37: 处理 14 个新请求
2025-07-30 01:28:36,515 - INFO -   秒 38: 处理 17 个新请求
2025-07-30 01:28:36,518 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:36,518 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:36,521 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:36,521 - INFO -   秒 39: 处理 22 个新请求
2025-07-30 01:28:36,530 - INFO -   秒 40: 处理 12 个新请求
2025-07-30 01:28:36,535 - INFO - 时间槽 11，秒 40: 执行中容器 94，等待请求 0
2025-07-30 01:28:36,535 - INFO -   秒 41: 处理 11 个新请求
2025-07-30 01:28:36,540 - INFO -   秒 42: 处理 14 个新请求
2025-07-30 01:28:36,542 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:36,542 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:36,545 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:36,546 - INFO -   秒 43: 处理 14 个新请求
2025-07-30 01:28:36,552 - INFO -   秒 44: 处理 17 个新请求
2025-07-30 01:28:36,559 - INFO -   秒 45: 处理 15 个新请求
2025-07-30 01:28:36,566 - INFO -   秒 46: 处理 10 个新请求
2025-07-30 01:28:36,567 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 3885MB, 函数类型: 9
2025-07-30 01:28:36,567 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:36,570 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:28:36,570 - INFO -   秒 47: 处理 15 个新请求
2025-07-30 01:28:36,574 - INFO -   秒 48: 处理 14 个新请求
2025-07-30 01:28:36,584 - INFO -   秒 49: 处理 18 个新请求
2025-07-30 01:28:36,590 - INFO -   秒 50: 处理 17 个新请求
2025-07-30 01:28:36,592 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:36,592 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:36,597 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:36,599 - INFO - 时间槽 11，秒 50: 执行中容器 100，等待请求 0
2025-07-30 01:28:36,599 - INFO -   秒 51: 处理 9 个新请求
2025-07-30 01:28:36,604 - INFO -   秒 52: 处理 13 个新请求
2025-07-30 01:28:36,611 - INFO -   秒 53: 处理 13 个新请求
2025-07-30 01:28:36,616 - INFO -   秒 54: 处理 17 个新请求
2025-07-30 01:28:36,620 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 3885MB, 函数类型: 2
2025-07-30 01:28:36,620 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:36,624 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:28:36,624 - INFO -   秒 55: 处理 9 个新请求
2025-07-30 01:28:36,630 - INFO -   秒 56: 处理 8 个新请求
2025-07-30 01:28:36,638 - INFO - 时间槽 11 结束时，还有 60 个容器正在执行，将继续在后台执行
2025-07-30 01:28:36,641 - INFO - --------处理时间槽 12 的请求，共 895 个--------
2025-07-30 01:28:36,641 - INFO - ----node memomry----
2025-07-30 01:28:36,641 - INFO - node 1 : 10445 MB
2025-07-30 01:28:36,641 - INFO - node 2 : 10525 MB
2025-07-30 01:28:36,641 - INFO - node 3 : 7065 MB
2025-07-30 01:28:36,641 - INFO - node 4 : 10020 MB
2025-07-30 01:28:36,641 - INFO - node 5 : 8310 MB
2025-07-30 01:28:36,641 - INFO - node 6 : 10360 MB
2025-07-30 01:28:36,641 - INFO - node 7 : 10495 MB
2025-07-30 01:28:36,641 - INFO - node 8 : 9690 MB
2025-07-30 01:28:36,641 - INFO - node 9 : 8540 MB
2025-07-30 01:28:36,641 - INFO - node 10 : 6725 MB
2025-07-30 01:28:36,641 - INFO - node 11 : 9605 MB
2025-07-30 01:28:36,641 - INFO - node 12 : 8865 MB
2025-07-30 01:28:36,641 - INFO - node 13 : 6790 MB
2025-07-30 01:28:36,641 - INFO - node 14 : 10340 MB
2025-07-30 01:28:36,641 - INFO - node 15 : 9955 MB
2025-07-30 01:28:36,641 - INFO - node 16 : 8960 MB
2025-07-30 01:28:36,641 - INFO - node 17 : 8805 MB
2025-07-30 01:28:36,641 - INFO - node 18 : 10620 MB
2025-07-30 01:28:36,641 - INFO - node 19 : 9135 MB
2025-07-30 01:28:36,641 - INFO - node 20 : 10350 MB
2025-07-30 01:28:36,642 - INFO - node 21 : 8920 MB
2025-07-30 01:28:36,642 - INFO - node 22 : 7730 MB
2025-07-30 01:28:36,642 - INFO - node 23 : 8490 MB
2025-07-30 01:28:36,642 - INFO - node 24 : 5735 MB
2025-07-30 01:28:36,642 - INFO - node 25 : 7760 MB
2025-07-30 01:28:36,642 - INFO - node 26 : 8325 MB
2025-07-30 01:28:36,642 - INFO - node 27 : 10020 MB
2025-07-30 01:28:36,642 - INFO - node 28 : 6925 MB
2025-07-30 01:28:36,642 - INFO - node 29 : 9785 MB
2025-07-30 01:28:36,642 - INFO - node 30 : 7535 MB
2025-07-30 01:28:36,642 - INFO - node 31 : 6325 MB
2025-07-30 01:28:36,642 - INFO - node 32 : 7690 MB
2025-07-30 01:28:36,642 - INFO - node 33 : 9945 MB
2025-07-30 01:28:36,642 - INFO - node 34 : 10015 MB
2025-07-30 01:28:36,642 - INFO - node 35 : 7270 MB
2025-07-30 01:28:36,642 - INFO - node 36 : 9935 MB
2025-07-30 01:28:36,642 - INFO - node 37 : 8065 MB
2025-07-30 01:28:36,642 - INFO - node 38 : 5520 MB
2025-07-30 01:28:36,642 - INFO - node 39 : 3560 MB
2025-07-30 01:28:36,642 - INFO - node 40 : 8460 MB
2025-07-30 01:28:36,642 - INFO - node 41 : 9910 MB
2025-07-30 01:28:36,642 - INFO - node 42 : 3965 MB
2025-07-30 01:28:36,642 - INFO - node 43 : 6815 MB
2025-07-30 01:28:36,642 - INFO - node 44 : 6290 MB
2025-07-30 01:28:36,642 - INFO - node 45 : 8000 MB
2025-07-30 01:28:36,642 - INFO - node 46 : 10380 MB
2025-07-30 01:28:36,642 - INFO - node 47 : 6190 MB
2025-07-30 01:28:36,642 - INFO - node 48 : 4755 MB
2025-07-30 01:28:36,642 - INFO - node 49 : 9360 MB
2025-07-30 01:28:36,642 - INFO - node 50 : 8595 MB
2025-07-30 01:28:36,642 - INFO - node 51 : 5080 MB
2025-07-30 01:28:36,642 - INFO - node 52 : 7710 MB
2025-07-30 01:28:36,642 - INFO - node 53 : 9940 MB
2025-07-30 01:28:36,642 - INFO - node 54 : 9645 MB
2025-07-30 01:28:36,642 - INFO - node 55 : 8335 MB
2025-07-30 01:28:36,642 - INFO - node 56 : 10205 MB
2025-07-30 01:28:36,642 - INFO - node 57 : 8145 MB
2025-07-30 01:28:36,642 - INFO - node 58 : 3715 MB
2025-07-30 01:28:36,642 - INFO - node 59 : 4405 MB
2025-07-30 01:28:36,642 - INFO - node 60 : 10850 MB
2025-07-30 01:28:36,642 - INFO - node 61 : 4330 MB
2025-07-30 01:28:36,642 - INFO - node 62 : 8570 MB
2025-07-30 01:28:36,642 - INFO - node 63 : 7395 MB
2025-07-30 01:28:36,642 - INFO - node 64 : 8675 MB
2025-07-30 01:28:36,642 - INFO - node 65 : 2290 MB
2025-07-30 01:28:36,642 - INFO - node 66 : 7505 MB
2025-07-30 01:28:36,642 - INFO - node 67 : 9905 MB
2025-07-30 01:28:36,642 - INFO - node 68 : 10850 MB
2025-07-30 01:28:36,642 - INFO - node 69 : 2550 MB
2025-07-30 01:28:36,642 - INFO - node 70 : 10695 MB
2025-07-30 01:28:36,642 - INFO - node 71 : 8290 MB
2025-07-30 01:28:36,643 - INFO - node 72 : 8870 MB
2025-07-30 01:28:36,643 - INFO - node 73 : 9615 MB
2025-07-30 01:28:36,643 - INFO - node 74 : 4445 MB
2025-07-30 01:28:36,643 - INFO - node 75 : 7070 MB
2025-07-30 01:28:36,643 - INFO - node 76 : 7645 MB
2025-07-30 01:28:36,643 - INFO - node 77 : 3085 MB
2025-07-30 01:28:36,643 - INFO - node 78 : 10795 MB
2025-07-30 01:28:36,643 - INFO - node 79 : 4460 MB
2025-07-30 01:28:36,643 - INFO - node 80 : 6050 MB
2025-07-30 01:28:36,643 - INFO - node 81 : 4025 MB
2025-07-30 01:28:36,643 - INFO - node 82 : 9340 MB
2025-07-30 01:28:36,643 - INFO - node 83 : 10215 MB
2025-07-30 01:28:36,643 - INFO - node 84 : 7235 MB
2025-07-30 01:28:36,643 - INFO - node 85 : 3350 MB
2025-07-30 01:28:36,643 - INFO - node 86 : 6110 MB
2025-07-30 01:28:36,643 - INFO - node 87 : 4825 MB
2025-07-30 01:28:36,643 - INFO - node 88 : 5705 MB
2025-07-30 01:28:36,643 - INFO - node 89 : 580 MB
2025-07-30 01:28:36,643 - INFO - node 90 : 3355 MB
2025-07-30 01:28:36,643 - INFO - node 91 : 9710 MB
2025-07-30 01:28:36,643 - INFO - node 92 : 9035 MB
2025-07-30 01:28:36,643 - INFO - node 93 : 9120 MB
2025-07-30 01:28:36,643 - INFO - node 94 : 6760 MB
2025-07-30 01:28:36,643 - INFO - node 95 : 6700 MB
2025-07-30 01:28:36,643 - INFO - node 96 : 3885 MB
2025-07-30 01:28:36,643 - INFO - node 97 : 8655 MB
2025-07-30 01:28:36,643 - INFO - node 98 : 3080 MB
2025-07-30 01:28:36,643 - INFO - node 99 : 9940 MB
2025-07-30 01:28:36,643 - INFO - node 100 : 8725 MB
2025-07-30 01:28:36,643 - INFO - node 101 : 7080 MB
2025-07-30 01:28:36,644 - INFO - node 102 : 8035 MB
2025-07-30 01:28:36,644 - INFO - node 103 : 9350 MB
2025-07-30 01:28:36,644 - INFO - node 104 : 10075 MB
2025-07-30 01:28:36,644 - INFO - node 105 : 9920 MB
2025-07-30 01:28:36,644 - INFO - node 106 : 7810 MB
2025-07-30 01:28:36,644 - INFO - node 107 : 4590 MB
2025-07-30 01:28:36,644 - INFO - node 108 : 10850 MB
2025-07-30 01:28:36,644 - INFO - node 109 : 6655 MB
2025-07-30 01:28:36,644 - INFO - node 110 : 7465 MB
2025-07-30 01:28:36,644 - INFO - node 111 : 2730 MB
2025-07-30 01:28:36,644 - INFO - node 112 : 5410 MB
2025-07-30 01:28:36,644 - INFO - node 113 : 7165 MB
2025-07-30 01:28:36,644 - INFO - node 114 : 1965 MB
2025-07-30 01:28:36,644 - INFO - node 115 : 6690 MB
2025-07-30 01:28:36,644 - INFO - node 116 : 3195 MB
2025-07-30 01:28:36,644 - INFO - node 117 : 9530 MB
2025-07-30 01:28:36,644 - INFO - node 118 : 9540 MB
2025-07-30 01:28:36,644 - INFO - node 119 : 6600 MB
2025-07-30 01:28:36,645 - INFO - node 120 : 9770 MB
2025-07-30 01:28:36,645 - INFO - node 121 : 9870 MB
2025-07-30 01:28:36,645 - INFO - node 122 : 9655 MB
2025-07-30 01:28:36,645 - INFO - node 123 : 9910 MB
2025-07-30 01:28:36,645 - INFO - node 124 : 7840 MB
2025-07-30 01:28:36,645 - INFO - node 125 : 10425 MB
2025-07-30 01:28:36,645 - INFO -   秒 0: 处理 108 个新请求
2025-07-30 01:28:36,665 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:36,665 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:36,671 - INFO - 时间槽 12，秒 0: 执行中容器 168，等待请求 0
2025-07-30 01:28:36,671 - INFO -   秒 1: 处理 3 个新请求
2025-07-30 01:28:36,675 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:36,675 - INFO -   秒 2: 处理 5 个新请求
2025-07-30 01:28:36,678 - INFO -   秒 3: 处理 21 个新请求
2025-07-30 01:28:36,683 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:36,683 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:36,688 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:36,689 - INFO -   秒 4: 处理 11 个新请求
2025-07-30 01:28:36,695 - INFO -   秒 5: 处理 17 个新请求
2025-07-30 01:28:36,704 - INFO -   秒 6: 处理 13 个新请求
2025-07-30 01:28:36,713 - INFO -   秒 7: 处理 15 个新请求
2025-07-30 01:28:36,717 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:36,717 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:36,723 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:36,724 - INFO -   秒 8: 处理 8 个新请求
2025-07-30 01:28:36,731 - INFO -   秒 9: 处理 14 个新请求
2025-07-30 01:28:36,737 - INFO -   秒 10: 处理 21 个新请求
2025-07-30 01:28:36,742 - INFO - 时间槽 12，秒 10: 执行中容器 103，等待请求 0
2025-07-30 01:28:36,744 - INFO -   秒 11: 处理 19 个新请求
2025-07-30 01:28:36,748 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:36,748 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:36,752 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:36,752 - INFO -   秒 12: 处理 8 个新请求
2025-07-30 01:28:36,758 - INFO -   秒 13: 处理 14 个新请求
2025-07-30 01:28:36,766 - INFO -   秒 14: 处理 18 个新请求
2025-07-30 01:28:36,775 - INFO -   秒 15: 处理 16 个新请求
2025-07-30 01:28:36,780 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:36,780 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:36,785 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:36,786 - INFO -   秒 16: 处理 10 个新请求
2025-07-30 01:28:36,797 - INFO -   秒 17: 处理 16 个新请求
2025-07-30 01:28:36,809 - INFO -   秒 18: 处理 10 个新请求
2025-07-30 01:28:36,819 - INFO -   秒 19: 处理 24 个新请求
2025-07-30 01:28:36,827 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:36,827 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:36,834 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:36,834 - INFO -   秒 20: 处理 14 个新请求
2025-07-30 01:28:36,844 - INFO - 时间槽 12，秒 20: 执行中容器 105，等待请求 0
2025-07-30 01:28:36,844 - INFO -   秒 21: 处理 13 个新请求
2025-07-30 01:28:36,854 - INFO -   秒 22: 处理 12 个新请求
2025-07-30 01:28:36,861 - INFO -   秒 23: 处理 17 个新请求
2025-07-30 01:28:36,866 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 3885MB, 函数类型: 7
2025-07-30 01:28:36,867 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:28:36,873 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 120MB，类型: 7
2025-07-30 01:28:36,873 - INFO -   秒 24: 处理 19 个新请求
2025-07-30 01:28:36,881 - INFO -   秒 25: 处理 10 个新请求
2025-07-30 01:28:36,885 - INFO -   秒 26: 处理 9 个新请求
2025-07-30 01:28:36,891 - INFO -   秒 27: 处理 12 个新请求
2025-07-30 01:28:36,897 - INFO -   秒 28: 处理 14 个新请求
2025-07-30 01:28:36,901 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:36,901 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:36,906 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:36,906 - INFO -   秒 29: 处理 25 个新请求
2025-07-30 01:28:36,918 - INFO -   秒 30: 处理 20 个新请求
2025-07-30 01:28:36,932 - INFO - 时间槽 12，秒 30: 执行中容器 121，等待请求 0
2025-07-30 01:28:36,933 - INFO -   秒 31: 处理 13 个新请求
2025-07-30 01:28:36,940 - INFO -   秒 32: 处理 9 个新请求
2025-07-30 01:28:36,944 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 3885MB, 函数类型: 9
2025-07-30 01:28:36,944 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:36,948 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:28:36,948 - INFO -   秒 33: 处理 12 个新请求
2025-07-30 01:28:36,956 - INFO -   秒 34: 处理 17 个新请求
2025-07-30 01:28:36,967 - INFO -   秒 35: 处理 17 个新请求
2025-07-30 01:28:36,976 - INFO -   秒 36: 处理 12 个新请求
2025-07-30 01:28:36,983 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 3885MB, 函数类型: 6
2025-07-30 01:28:36,983 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:36,988 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 70MB，类型: 6
2025-07-30 01:28:36,989 - INFO -   秒 37: 处理 10 个新请求
2025-07-30 01:28:36,999 - INFO -   秒 38: 处理 18 个新请求
2025-07-30 01:28:37,007 - INFO -   秒 39: 处理 22 个新请求
2025-07-30 01:28:37,019 - INFO -   秒 40: 处理 11 个新请求
2025-07-30 01:28:37,022 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 3885MB, 函数类型: 9
2025-07-30 01:28:37,023 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:37,026 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:28:37,026 - INFO - 时间槽 12，秒 40: 执行中容器 105，等待请求 0
2025-07-30 01:28:37,026 - INFO -   秒 41: 处理 15 个新请求
2025-07-30 01:28:37,036 - INFO -   秒 42: 处理 7 个新请求
2025-07-30 01:28:37,043 - INFO -   秒 43: 处理 17 个新请求
2025-07-30 01:28:37,055 - INFO -   秒 44: 处理 18 个新请求
2025-07-30 01:28:37,060 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:37,060 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:37,065 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:37,065 - INFO -   秒 45: 处理 11 个新请求
2025-07-30 01:28:37,072 - INFO -   秒 46: 处理 11 个新请求
2025-07-30 01:28:37,080 - INFO -   秒 47: 处理 17 个新请求
2025-07-30 01:28:37,088 - INFO -   秒 48: 处理 18 个新请求
2025-07-30 01:28:37,094 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 3885MB, 函数类型: 2
2025-07-30 01:28:37,094 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:37,101 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:28:37,102 - INFO -   秒 49: 处理 17 个新请求
2025-07-30 01:28:37,116 - INFO -   秒 50: 处理 12 个新请求
2025-07-30 01:28:37,126 - INFO - 时间槽 12，秒 50: 执行中容器 104，等待请求 0
2025-07-30 01:28:37,126 - INFO -   秒 51: 处理 13 个新请求
2025-07-30 01:28:37,139 - INFO -   秒 52: 处理 12 个新请求
2025-07-30 01:28:37,143 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:37,143 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:37,148 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:37,148 - INFO -   秒 53: 处理 16 个新请求
2025-07-30 01:28:37,157 - INFO -   秒 54: 处理 11 个新请求
2025-07-30 01:28:37,166 - INFO -   秒 55: 处理 12 个新请求
2025-07-30 01:28:37,175 - INFO -   秒 56: 处理 11 个新请求
2025-07-30 01:28:37,178 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:37,178 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:37,182 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:37,197 - INFO - 时间槽 12 结束时，还有 61 个容器正在执行，将继续在后台执行
2025-07-30 01:28:37,202 - INFO - --------处理时间槽 13 的请求，共 926 个--------
2025-07-30 01:28:37,203 - INFO - ----node memomry----
2025-07-30 01:28:37,203 - INFO - node 1 : 10565 MB
2025-07-30 01:28:37,203 - INFO - node 2 : 10525 MB
2025-07-30 01:28:37,203 - INFO - node 3 : 7065 MB
2025-07-30 01:28:37,203 - INFO - node 4 : 9815 MB
2025-07-30 01:28:37,203 - INFO - node 5 : 8310 MB
2025-07-30 01:28:37,203 - INFO - node 6 : 10360 MB
2025-07-30 01:28:37,203 - INFO - node 7 : 10495 MB
2025-07-30 01:28:37,203 - INFO - node 8 : 9485 MB
2025-07-30 01:28:37,203 - INFO - node 9 : 8540 MB
2025-07-30 01:28:37,203 - INFO - node 10 : 6725 MB
2025-07-30 01:28:37,203 - INFO - node 11 : 9605 MB
2025-07-30 01:28:37,203 - INFO - node 12 : 8865 MB
2025-07-30 01:28:37,203 - INFO - node 13 : 6790 MB
2025-07-30 01:28:37,203 - INFO - node 14 : 10340 MB
2025-07-30 01:28:37,203 - INFO - node 15 : 9955 MB
2025-07-30 01:28:37,203 - INFO - node 16 : 8960 MB
2025-07-30 01:28:37,203 - INFO - node 17 : 8805 MB
2025-07-30 01:28:37,203 - INFO - node 18 : 10620 MB
2025-07-30 01:28:37,203 - INFO - node 19 : 9135 MB
2025-07-30 01:28:37,203 - INFO - node 20 : 10350 MB
2025-07-30 01:28:37,203 - INFO - node 21 : 8920 MB
2025-07-30 01:28:37,203 - INFO - node 22 : 7730 MB
2025-07-30 01:28:37,203 - INFO - node 23 : 8490 MB
2025-07-30 01:28:37,203 - INFO - node 24 : 5735 MB
2025-07-30 01:28:37,203 - INFO - node 25 : 7760 MB
2025-07-30 01:28:37,203 - INFO - node 26 : 8325 MB
2025-07-30 01:28:37,203 - INFO - node 27 : 10020 MB
2025-07-30 01:28:37,203 - INFO - node 28 : 6925 MB
2025-07-30 01:28:37,203 - INFO - node 29 : 9580 MB
2025-07-30 01:28:37,204 - INFO - node 30 : 7535 MB
2025-07-30 01:28:37,204 - INFO - node 31 : 6325 MB
2025-07-30 01:28:37,204 - INFO - node 32 : 7690 MB
2025-07-30 01:28:37,204 - INFO - node 33 : 9945 MB
2025-07-30 01:28:37,204 - INFO - node 34 : 10015 MB
2025-07-30 01:28:37,204 - INFO - node 35 : 7270 MB
2025-07-30 01:28:37,204 - INFO - node 36 : 9935 MB
2025-07-30 01:28:37,204 - INFO - node 37 : 8065 MB
2025-07-30 01:28:37,204 - INFO - node 38 : 5520 MB
2025-07-30 01:28:37,204 - INFO - node 39 : 3560 MB
2025-07-30 01:28:37,204 - INFO - node 40 : 8460 MB
2025-07-30 01:28:37,204 - INFO - node 41 : 9910 MB
2025-07-30 01:28:37,204 - INFO - node 42 : 3965 MB
2025-07-30 01:28:37,204 - INFO - node 43 : 6815 MB
2025-07-30 01:28:37,204 - INFO - node 44 : 6290 MB
2025-07-30 01:28:37,204 - INFO - node 45 : 8000 MB
2025-07-30 01:28:37,204 - INFO - node 46 : 10380 MB
2025-07-30 01:28:37,204 - INFO - node 47 : 6190 MB
2025-07-30 01:28:37,204 - INFO - node 48 : 4755 MB
2025-07-30 01:28:37,204 - INFO - node 49 : 9360 MB
2025-07-30 01:28:37,204 - INFO - node 50 : 8595 MB
2025-07-30 01:28:37,204 - INFO - node 51 : 5080 MB
2025-07-30 01:28:37,204 - INFO - node 52 : 7710 MB
2025-07-30 01:28:37,204 - INFO - node 53 : 9940 MB
2025-07-30 01:28:37,204 - INFO - node 54 : 9645 MB
2025-07-30 01:28:37,204 - INFO - node 55 : 8335 MB
2025-07-30 01:28:37,204 - INFO - node 56 : 10205 MB
2025-07-30 01:28:37,204 - INFO - node 57 : 8145 MB
2025-07-30 01:28:37,204 - INFO - node 58 : 3715 MB
2025-07-30 01:28:37,204 - INFO - node 59 : 4405 MB
2025-07-30 01:28:37,204 - INFO - node 60 : 10850 MB
2025-07-30 01:28:37,204 - INFO - node 61 : 4330 MB
2025-07-30 01:28:37,204 - INFO - node 62 : 8570 MB
2025-07-30 01:28:37,204 - INFO - node 63 : 7395 MB
2025-07-30 01:28:37,205 - INFO - node 64 : 8675 MB
2025-07-30 01:28:37,205 - INFO - node 65 : 2290 MB
2025-07-30 01:28:37,205 - INFO - node 66 : 7505 MB
2025-07-30 01:28:37,205 - INFO - node 67 : 9905 MB
2025-07-30 01:28:37,205 - INFO - node 68 : 10850 MB
2025-07-30 01:28:37,205 - INFO - node 69 : 2550 MB
2025-07-30 01:28:37,205 - INFO - node 70 : 10695 MB
2025-07-30 01:28:37,205 - INFO - node 71 : 8290 MB
2025-07-30 01:28:37,205 - INFO - node 72 : 8870 MB
2025-07-30 01:28:37,205 - INFO - node 73 : 9615 MB
2025-07-30 01:28:37,205 - INFO - node 74 : 4445 MB
2025-07-30 01:28:37,205 - INFO - node 75 : 7070 MB
2025-07-30 01:28:37,205 - INFO - node 76 : 7645 MB
2025-07-30 01:28:37,205 - INFO - node 77 : 3085 MB
2025-07-30 01:28:37,205 - INFO - node 78 : 10645 MB
2025-07-30 01:28:37,205 - INFO - node 79 : 4460 MB
2025-07-30 01:28:37,205 - INFO - node 80 : 6050 MB
2025-07-30 01:28:37,205 - INFO - node 81 : 4025 MB
2025-07-30 01:28:37,206 - INFO - node 82 : 9340 MB
2025-07-30 01:28:37,206 - INFO - node 83 : 10215 MB
2025-07-30 01:28:37,206 - INFO - node 84 : 7235 MB
2025-07-30 01:28:37,206 - INFO - node 85 : 3350 MB
2025-07-30 01:28:37,206 - INFO - node 86 : 6110 MB
2025-07-30 01:28:37,206 - INFO - node 87 : 4825 MB
2025-07-30 01:28:37,206 - INFO - node 88 : 5705 MB
2025-07-30 01:28:37,206 - INFO - node 89 : 580 MB
2025-07-30 01:28:37,206 - INFO - node 90 : 3355 MB
2025-07-30 01:28:37,206 - INFO - node 91 : 9710 MB
2025-07-30 01:28:37,206 - INFO - node 92 : 9035 MB
2025-07-30 01:28:37,206 - INFO - node 93 : 9120 MB
2025-07-30 01:28:37,206 - INFO - node 94 : 6760 MB
2025-07-30 01:28:37,206 - INFO - node 95 : 6700 MB
2025-07-30 01:28:37,206 - INFO - node 96 : 3885 MB
2025-07-30 01:28:37,206 - INFO - node 97 : 8655 MB
2025-07-30 01:28:37,206 - INFO - node 98 : 3080 MB
2025-07-30 01:28:37,206 - INFO - node 99 : 10145 MB
2025-07-30 01:28:37,207 - INFO - node 100 : 8725 MB
2025-07-30 01:28:37,207 - INFO - node 101 : 7080 MB
2025-07-30 01:28:37,207 - INFO - node 102 : 8035 MB
2025-07-30 01:28:37,207 - INFO - node 103 : 9350 MB
2025-07-30 01:28:37,207 - INFO - node 104 : 10075 MB
2025-07-30 01:28:37,207 - INFO - node 105 : 9920 MB
2025-07-30 01:28:37,207 - INFO - node 106 : 7810 MB
2025-07-30 01:28:37,207 - INFO - node 107 : 4590 MB
2025-07-30 01:28:37,207 - INFO - node 108 : 10765 MB
2025-07-30 01:28:37,207 - INFO - node 109 : 6655 MB
2025-07-30 01:28:37,207 - INFO - node 110 : 7465 MB
2025-07-30 01:28:37,207 - INFO - node 111 : 2730 MB
2025-07-30 01:28:37,207 - INFO - node 112 : 5410 MB
2025-07-30 01:28:37,207 - INFO - node 113 : 7165 MB
2025-07-30 01:28:37,207 - INFO - node 114 : 1965 MB
2025-07-30 01:28:37,207 - INFO - node 115 : 6690 MB
2025-07-30 01:28:37,207 - INFO - node 116 : 3195 MB
2025-07-30 01:28:37,207 - INFO - node 117 : 9530 MB
2025-07-30 01:28:37,207 - INFO - node 118 : 9540 MB
2025-07-30 01:28:37,207 - INFO - node 119 : 6600 MB
2025-07-30 01:28:37,207 - INFO - node 120 : 9770 MB
2025-07-30 01:28:37,207 - INFO - node 121 : 9870 MB
2025-07-30 01:28:37,207 - INFO - node 122 : 9655 MB
2025-07-30 01:28:37,207 - INFO - node 123 : 9910 MB
2025-07-30 01:28:37,207 - INFO - node 124 : 7840 MB
2025-07-30 01:28:37,209 - INFO - node 125 : 10425 MB
2025-07-30 01:28:37,209 - INFO -   秒 0: 处理 109 个新请求
2025-07-30 01:28:37,243 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 3885MB, 函数类型: 9
2025-07-30 01:28:37,243 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:37,254 - INFO - 时间槽 13，秒 0: 执行中容器 170，等待请求 0
2025-07-30 01:28:37,254 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:28:37,262 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:28:37,263 - INFO -   秒 2: 处理 6 个新请求
2025-07-30 01:28:37,269 - INFO -   秒 3: 处理 21 个新请求
2025-07-30 01:28:37,278 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:37,278 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:37,285 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:37,285 - INFO -   秒 4: 处理 13 个新请求
2025-07-30 01:28:37,296 - INFO -   秒 5: 处理 16 个新请求
2025-07-30 01:28:37,307 - INFO -   秒 6: 处理 16 个新请求
2025-07-30 01:28:37,318 - INFO -   秒 7: 处理 10 个新请求
2025-07-30 01:28:37,323 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:37,323 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:37,329 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:37,329 - INFO -   秒 8: 处理 10 个新请求
2025-07-30 01:28:37,337 - INFO -   秒 9: 处理 23 个新请求
2025-07-30 01:28:37,349 - INFO -   秒 10: 处理 12 个新请求
2025-07-30 01:28:37,357 - INFO - 时间槽 13，秒 10: 执行中容器 104，等待请求 0
2025-07-30 01:28:37,357 - INFO -   秒 11: 处理 19 个新请求
2025-07-30 01:28:37,364 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:37,364 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:37,368 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:37,368 - INFO -   秒 12: 处理 15 个新请求
2025-07-30 01:28:37,379 - INFO -   秒 13: 处理 14 个新请求
2025-07-30 01:28:37,391 - INFO -   秒 14: 处理 15 个新请求
2025-07-30 01:28:37,411 - INFO -   秒 15: 处理 18 个新请求
2025-07-30 01:28:37,419 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 3885MB, 函数类型: 9
2025-07-30 01:28:37,419 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:37,427 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:28:37,430 - INFO -   秒 16: 处理 13 个新请求
2025-07-30 01:28:37,441 - INFO -   秒 17: 处理 12 个新请求
2025-07-30 01:28:37,453 - INFO -   秒 18: 处理 11 个新请求
2025-07-30 01:28:37,463 - INFO -   秒 19: 处理 34 个新请求
2025-07-30 01:28:37,473 - INFO - DEBUG: 节点96内存检查 - 需要内存: 360.0MB, 可用内存: 3885MB, 函数类型: 5
2025-07-30 01:28:37,473 - INFO - DEBUG: 节点96创建User层容器，减少内存 360MB，类型: 5
2025-07-30 01:28:37,478 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 360MB，类型: 5
2025-07-30 01:28:37,479 - INFO -   秒 20: 处理 11 个新请求
2025-07-30 01:28:37,489 - INFO - 时间槽 13，秒 20: 执行中容器 113，等待请求 0
2025-07-30 01:28:37,489 - INFO -   秒 21: 处理 10 个新请求
2025-07-30 01:28:37,497 - INFO -   秒 22: 处理 11 个新请求
2025-07-30 01:28:37,504 - INFO -   秒 23: 处理 18 个新请求
2025-07-30 01:28:37,507 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:37,507 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:37,513 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:37,514 - INFO -   秒 24: 处理 19 个新请求
2025-07-30 01:28:37,522 - INFO -   秒 25: 处理 9 个新请求
2025-07-30 01:28:37,526 - INFO -   秒 26: 处理 12 个新请求
2025-07-30 01:28:37,534 - INFO -   秒 27: 处理 18 个新请求
2025-07-30 01:28:37,538 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 3885MB, 函数类型: 9
2025-07-30 01:28:37,538 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:37,542 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:28:37,542 - INFO -   秒 28: 处理 7 个新请求
2025-07-30 01:28:37,548 - INFO -   秒 29: 处理 30 个新请求
2025-07-30 01:28:37,559 - INFO -   秒 30: 处理 14 个新请求
2025-07-30 01:28:37,565 - INFO - 时间槽 13，秒 30: 执行中容器 101，等待请求 0
2025-07-30 01:28:37,565 - INFO -   秒 31: 处理 14 个新请求
2025-07-30 01:28:37,567 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 3885MB, 函数类型: 9
2025-07-30 01:28:37,567 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:37,570 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:28:37,571 - INFO -   秒 32: 处理 13 个新请求
2025-07-30 01:28:37,576 - INFO -   秒 33: 处理 13 个新请求
2025-07-30 01:28:37,581 - INFO -   秒 34: 处理 13 个新请求
2025-07-30 01:28:37,588 - INFO -   秒 35: 处理 20 个新请求
2025-07-30 01:28:37,591 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:37,591 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:37,596 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:37,596 - INFO -   秒 36: 处理 13 个新请求
2025-07-30 01:28:37,602 - INFO -   秒 37: 处理 11 个新请求
2025-07-30 01:28:37,610 - INFO -   秒 38: 处理 10 个新请求
2025-07-30 01:28:37,616 - INFO -   秒 39: 处理 30 个新请求
2025-07-30 01:28:37,625 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:37,625 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:37,631 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:37,631 - INFO -   秒 40: 处理 15 个新请求
2025-07-30 01:28:37,638 - INFO - 时间槽 13，秒 40: 执行中容器 112，等待请求 0
2025-07-30 01:28:37,638 - INFO -   秒 41: 处理 11 个新请求
2025-07-30 01:28:37,643 - INFO -   秒 42: 处理 15 个新请求
2025-07-30 01:28:37,651 - INFO -   秒 43: 处理 13 个新请求
2025-07-30 01:28:37,653 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:37,653 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:37,656 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:37,656 - INFO -   秒 44: 处理 17 个新请求
2025-07-30 01:28:37,665 - INFO -   秒 45: 处理 10 个新请求
2025-07-30 01:28:37,670 - INFO -   秒 46: 处理 18 个新请求
2025-07-30 01:28:37,674 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 3885MB, 函数类型: 9
2025-07-30 01:28:37,674 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:37,678 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:28:37,678 - INFO -   秒 47: 处理 16 个新请求
2025-07-30 01:28:37,685 - INFO -   秒 48: 处理 13 个新请求
2025-07-30 01:28:37,690 - INFO -   秒 49: 处理 22 个新请求
2025-07-30 01:28:37,698 - INFO -   秒 50: 处理 14 个新请求
2025-07-30 01:28:37,701 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 3885MB, 函数类型: 7
2025-07-30 01:28:37,701 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:28:37,704 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 120MB，类型: 7
2025-07-30 01:28:37,705 - INFO - 时间槽 13，秒 50: 执行中容器 108，等待请求 0
2025-07-30 01:28:37,705 - INFO -   秒 51: 处理 12 个新请求
2025-07-30 01:28:37,710 - INFO -   秒 52: 处理 10 个新请求
2025-07-30 01:28:37,716 - INFO -   秒 53: 处理 16 个新请求
2025-07-30 01:28:37,722 - INFO -   秒 54: 处理 16 个新请求
2025-07-30 01:28:37,725 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:37,725 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:37,729 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:37,730 - INFO -   秒 55: 处理 13 个新请求
2025-07-30 01:28:37,736 - INFO -   秒 56: 处理 7 个新请求
2025-07-30 01:28:37,742 - INFO -   秒 57: 处理 1 个新请求
2025-07-30 01:28:37,752 - INFO - 时间槽 13 结束时，还有 68 个容器正在执行，将继续在后台执行
2025-07-30 01:28:37,757 - INFO - --------处理时间槽 14 的请求，共 872 个--------
2025-07-30 01:28:37,757 - INFO - ----node memomry----
2025-07-30 01:28:37,757 - INFO - node 1 : 10565 MB
2025-07-30 01:28:37,757 - INFO - node 2 : 10525 MB
2025-07-30 01:28:37,757 - INFO - node 3 : 7065 MB
2025-07-30 01:28:37,757 - INFO - node 4 : 9815 MB
2025-07-30 01:28:37,757 - INFO - node 5 : 8310 MB
2025-07-30 01:28:37,757 - INFO - node 6 : 10360 MB
2025-07-30 01:28:37,757 - INFO - node 7 : 10495 MB
2025-07-30 01:28:37,757 - INFO - node 8 : 9485 MB
2025-07-30 01:28:37,758 - INFO - node 9 : 8540 MB
2025-07-30 01:28:37,758 - INFO - node 10 : 6725 MB
2025-07-30 01:28:37,758 - INFO - node 11 : 9605 MB
2025-07-30 01:28:37,758 - INFO - node 12 : 8865 MB
2025-07-30 01:28:37,758 - INFO - node 13 : 6790 MB
2025-07-30 01:28:37,758 - INFO - node 14 : 10340 MB
2025-07-30 01:28:37,758 - INFO - node 15 : 9955 MB
2025-07-30 01:28:37,758 - INFO - node 16 : 8960 MB
2025-07-30 01:28:37,758 - INFO - node 17 : 8805 MB
2025-07-30 01:28:37,758 - INFO - node 18 : 10620 MB
2025-07-30 01:28:37,758 - INFO - node 19 : 9135 MB
2025-07-30 01:28:37,758 - INFO - node 20 : 10350 MB
2025-07-30 01:28:37,758 - INFO - node 21 : 8920 MB
2025-07-30 01:28:37,758 - INFO - node 22 : 7730 MB
2025-07-30 01:28:37,758 - INFO - node 23 : 8490 MB
2025-07-30 01:28:37,758 - INFO - node 24 : 5735 MB
2025-07-30 01:28:37,758 - INFO - node 25 : 7760 MB
2025-07-30 01:28:37,758 - INFO - node 26 : 8325 MB
2025-07-30 01:28:37,758 - INFO - node 27 : 10020 MB
2025-07-30 01:28:37,758 - INFO - node 28 : 6925 MB
2025-07-30 01:28:37,758 - INFO - node 29 : 9580 MB
2025-07-30 01:28:37,758 - INFO - node 30 : 7535 MB
2025-07-30 01:28:37,758 - INFO - node 31 : 6325 MB
2025-07-30 01:28:37,758 - INFO - node 32 : 7690 MB
2025-07-30 01:28:37,758 - INFO - node 33 : 9945 MB
2025-07-30 01:28:37,758 - INFO - node 34 : 10015 MB
2025-07-30 01:28:37,758 - INFO - node 35 : 7270 MB
2025-07-30 01:28:37,758 - INFO - node 36 : 9935 MB
2025-07-30 01:28:37,758 - INFO - node 37 : 8065 MB
2025-07-30 01:28:37,758 - INFO - node 38 : 5520 MB
2025-07-30 01:28:37,758 - INFO - node 39 : 3560 MB
2025-07-30 01:28:37,758 - INFO - node 40 : 8460 MB
2025-07-30 01:28:37,758 - INFO - node 41 : 9910 MB
2025-07-30 01:28:37,758 - INFO - node 42 : 3965 MB
2025-07-30 01:28:37,758 - INFO - node 43 : 6815 MB
2025-07-30 01:28:37,758 - INFO - node 44 : 6290 MB
2025-07-30 01:28:37,758 - INFO - node 45 : 8000 MB
2025-07-30 01:28:37,758 - INFO - node 46 : 10380 MB
2025-07-30 01:28:37,758 - INFO - node 47 : 6190 MB
2025-07-30 01:28:37,758 - INFO - node 48 : 4755 MB
2025-07-30 01:28:37,758 - INFO - node 49 : 9360 MB
2025-07-30 01:28:37,758 - INFO - node 50 : 8595 MB
2025-07-30 01:28:37,758 - INFO - node 51 : 5080 MB
2025-07-30 01:28:37,758 - INFO - node 52 : 7710 MB
2025-07-30 01:28:37,758 - INFO - node 53 : 9940 MB
2025-07-30 01:28:37,758 - INFO - node 54 : 9645 MB
2025-07-30 01:28:37,758 - INFO - node 55 : 8335 MB
2025-07-30 01:28:37,759 - INFO - node 56 : 10205 MB
2025-07-30 01:28:37,759 - INFO - node 57 : 8145 MB
2025-07-30 01:28:37,759 - INFO - node 58 : 3715 MB
2025-07-30 01:28:37,759 - INFO - node 59 : 4405 MB
2025-07-30 01:28:37,759 - INFO - node 60 : 10765 MB
2025-07-30 01:28:37,759 - INFO - node 61 : 4330 MB
2025-07-30 01:28:37,759 - INFO - node 62 : 8570 MB
2025-07-30 01:28:37,759 - INFO - node 63 : 7395 MB
2025-07-30 01:28:37,759 - INFO - node 64 : 8675 MB
2025-07-30 01:28:37,759 - INFO - node 65 : 2290 MB
2025-07-30 01:28:37,759 - INFO - node 66 : 7505 MB
2025-07-30 01:28:37,759 - INFO - node 67 : 9905 MB
2025-07-30 01:28:37,759 - INFO - node 68 : 10850 MB
2025-07-30 01:28:37,759 - INFO - node 69 : 2550 MB
2025-07-30 01:28:37,759 - INFO - node 70 : 10695 MB
2025-07-30 01:28:37,759 - INFO - node 71 : 8290 MB
2025-07-30 01:28:37,759 - INFO - node 72 : 8870 MB
2025-07-30 01:28:37,759 - INFO - node 73 : 9615 MB
2025-07-30 01:28:37,759 - INFO - node 74 : 4445 MB
2025-07-30 01:28:37,759 - INFO - node 75 : 7070 MB
2025-07-30 01:28:37,759 - INFO - node 76 : 7645 MB
2025-07-30 01:28:37,760 - INFO - node 77 : 3085 MB
2025-07-30 01:28:37,760 - INFO - node 78 : 10645 MB
2025-07-30 01:28:37,760 - INFO - node 79 : 4460 MB
2025-07-30 01:28:37,760 - INFO - node 80 : 6050 MB
2025-07-30 01:28:37,760 - INFO - node 81 : 4025 MB
2025-07-30 01:28:37,760 - INFO - node 82 : 9340 MB
2025-07-30 01:28:37,760 - INFO - node 83 : 10215 MB
2025-07-30 01:28:37,760 - INFO - node 84 : 7235 MB
2025-07-30 01:28:37,760 - INFO - node 85 : 3350 MB
2025-07-30 01:28:37,760 - INFO - node 86 : 6110 MB
2025-07-30 01:28:37,760 - INFO - node 87 : 4825 MB
2025-07-30 01:28:37,760 - INFO - node 88 : 5705 MB
2025-07-30 01:28:37,760 - INFO - node 89 : 580 MB
2025-07-30 01:28:37,760 - INFO - node 90 : 3355 MB
2025-07-30 01:28:37,760 - INFO - node 91 : 9710 MB
2025-07-30 01:28:37,760 - INFO - node 92 : 9035 MB
2025-07-30 01:28:37,760 - INFO - node 93 : 9120 MB
2025-07-30 01:28:37,760 - INFO - node 94 : 6760 MB
2025-07-30 01:28:37,760 - INFO - node 95 : 6700 MB
2025-07-30 01:28:37,760 - INFO - node 96 : 3885 MB
2025-07-30 01:28:37,760 - INFO - node 97 : 8655 MB
2025-07-30 01:28:37,760 - INFO - node 98 : 3080 MB
2025-07-30 01:28:37,760 - INFO - node 99 : 10145 MB
2025-07-30 01:28:37,760 - INFO - node 100 : 8725 MB
2025-07-30 01:28:37,760 - INFO - node 101 : 7080 MB
2025-07-30 01:28:37,760 - INFO - node 102 : 8035 MB
2025-07-30 01:28:37,760 - INFO - node 103 : 9350 MB
2025-07-30 01:28:37,760 - INFO - node 104 : 10075 MB
2025-07-30 01:28:37,760 - INFO - node 105 : 9920 MB
2025-07-30 01:28:37,760 - INFO - node 106 : 7810 MB
2025-07-30 01:28:37,760 - INFO - node 107 : 4590 MB
2025-07-30 01:28:37,760 - INFO - node 108 : 10765 MB
2025-07-30 01:28:37,760 - INFO - node 109 : 6655 MB
2025-07-30 01:28:37,760 - INFO - node 110 : 7465 MB
2025-07-30 01:28:37,760 - INFO - node 111 : 2730 MB
2025-07-30 01:28:37,760 - INFO - node 112 : 5410 MB
2025-07-30 01:28:37,761 - INFO - node 113 : 7165 MB
2025-07-30 01:28:37,761 - INFO - node 114 : 1965 MB
2025-07-30 01:28:37,761 - INFO - node 115 : 6690 MB
2025-07-30 01:28:37,761 - INFO - node 116 : 3195 MB
2025-07-30 01:28:37,761 - INFO - node 117 : 9530 MB
2025-07-30 01:28:37,761 - INFO - node 118 : 9745 MB
2025-07-30 01:28:37,761 - INFO - node 119 : 6600 MB
2025-07-30 01:28:37,761 - INFO - node 120 : 9770 MB
2025-07-30 01:28:37,761 - INFO - node 121 : 9870 MB
2025-07-30 01:28:37,761 - INFO - node 122 : 9655 MB
2025-07-30 01:28:37,761 - INFO - node 123 : 9910 MB
2025-07-30 01:28:37,761 - INFO - node 124 : 7840 MB
2025-07-30 01:28:37,761 - INFO - node 125 : 10425 MB
2025-07-30 01:28:37,761 - INFO -   秒 0: 处理 108 个新请求
2025-07-30 01:28:37,798 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 3885MB, 函数类型: 6
2025-07-30 01:28:37,799 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:37,807 - INFO - 时间槽 14，秒 0: 执行中容器 176，等待请求 0
2025-07-30 01:28:37,807 - INFO -   秒 1: 处理 5 个新请求
2025-07-30 01:28:37,815 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 70MB，类型: 6
2025-07-30 01:28:37,816 - INFO -   秒 2: 处理 3 个新请求
2025-07-30 01:28:37,820 - INFO -   秒 3: 处理 19 个新请求
2025-07-30 01:28:37,826 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:37,826 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:37,833 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:37,833 - INFO -   秒 4: 处理 14 个新请求
2025-07-30 01:28:37,842 - INFO -   秒 5: 处理 14 个新请求
2025-07-30 01:28:37,850 - INFO -   秒 6: 处理 15 个新请求
2025-07-30 01:28:37,858 - INFO -   秒 7: 处理 11 个新请求
2025-07-30 01:28:37,862 - INFO - DEBUG: 节点96内存检查 - 需要内存: 60.0MB, 可用内存: 3885MB, 函数类型: 3
2025-07-30 01:28:37,862 - INFO - DEBUG: 节点96创建User层容器，减少内存 60MB，类型: 3
2025-07-30 01:28:37,865 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 60MB，类型: 3
2025-07-30 01:28:37,866 - INFO -   秒 8: 处理 12 个新请求
2025-07-30 01:28:37,872 - INFO -   秒 9: 处理 14 个新请求
2025-07-30 01:28:37,882 - INFO -   秒 10: 处理 17 个新请求
2025-07-30 01:28:37,890 - INFO - 时间槽 14，秒 10: 执行中容器 108，等待请求 0
2025-07-30 01:28:37,890 - INFO -   秒 11: 处理 13 个新请求
2025-07-30 01:28:37,893 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 3885MB, 函数类型: 6
2025-07-30 01:28:37,893 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:37,897 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 70MB，类型: 6
2025-07-30 01:28:37,898 - INFO -   秒 12: 处理 16 个新请求
2025-07-30 01:28:37,905 - INFO -   秒 13: 处理 12 个新请求
2025-07-30 01:28:37,915 - INFO -   秒 14: 处理 18 个新请求
2025-07-30 01:28:37,924 - INFO -   秒 15: 处理 14 个新请求
2025-07-30 01:28:37,927 - INFO - DEBUG: 节点96内存检查 - 需要内存: 360.0MB, 可用内存: 3885MB, 函数类型: 5
2025-07-30 01:28:37,927 - INFO - DEBUG: 节点96创建User层容器，减少内存 360MB，类型: 5
2025-07-30 01:28:37,932 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 360MB，类型: 5
2025-07-30 01:28:37,932 - INFO -   秒 16: 处理 10 个新请求
2025-07-30 01:28:37,941 - INFO -   秒 17: 处理 13 个新请求
2025-07-30 01:28:37,952 - INFO -   秒 18: 处理 12 个新请求
2025-07-30 01:28:37,961 - INFO -   秒 19: 处理 25 个新请求
2025-07-30 01:28:37,967 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:37,967 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:37,973 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:37,973 - INFO -   秒 20: 处理 17 个新请求
2025-07-30 01:28:37,982 - INFO - 时间槽 14，秒 20: 执行中容器 114，等待请求 0
2025-07-30 01:28:37,982 - INFO -   秒 21: 处理 13 个新请求
2025-07-30 01:28:37,991 - INFO -   秒 22: 处理 6 个新请求
2025-07-30 01:28:37,997 - INFO -   秒 23: 处理 16 个新请求
2025-07-30 01:28:38,002 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:38,003 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:38,008 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:38,010 - INFO -   秒 24: 处理 19 个新请求
2025-07-30 01:28:38,024 - INFO -   秒 25: 处理 13 个新请求
2025-07-30 01:28:38,033 - INFO -   秒 26: 处理 14 个新请求
2025-07-30 01:28:38,040 - INFO -   秒 27: 处理 8 个新请求
2025-07-30 01:28:38,043 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:38,043 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:38,052 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:38,053 - INFO -   秒 28: 处理 12 个新请求
2025-07-30 01:28:38,066 - INFO -   秒 29: 处理 26 个新请求
2025-07-30 01:28:38,088 - INFO -   秒 30: 处理 16 个新请求
2025-07-30 01:28:38,098 - INFO - 时间槽 14，秒 30: 执行中容器 112，等待请求 0
2025-07-30 01:28:38,098 - INFO -   秒 31: 处理 6 个新请求
2025-07-30 01:28:38,099 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:38,099 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:38,101 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:38,102 - INFO -   秒 32: 处理 15 个新请求
2025-07-30 01:28:38,110 - INFO -   秒 33: 处理 13 个新请求
2025-07-30 01:28:38,119 - INFO -   秒 34: 处理 14 个新请求
2025-07-30 01:28:38,126 - INFO -   秒 35: 处理 13 个新请求
2025-07-30 01:28:38,130 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 3885MB, 函数类型: 2
2025-07-30 01:28:38,130 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:38,132 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:28:38,133 - INFO -   秒 36: 处理 15 个新请求
2025-07-30 01:28:38,144 - INFO -   秒 37: 处理 13 个新请求
2025-07-30 01:28:38,152 - INFO -   秒 38: 处理 17 个新请求
2025-07-30 01:28:38,161 - INFO -   秒 39: 处理 22 个新请求
2025-07-30 01:28:38,165 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 3885MB, 函数类型: 2
2025-07-30 01:28:38,166 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:38,171 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:28:38,171 - INFO -   秒 40: 处理 11 个新请求
2025-07-30 01:28:38,177 - INFO - 时间槽 14，秒 40: 执行中容器 110，等待请求 0
2025-07-30 01:28:38,177 - INFO -   秒 41: 处理 12 个新请求
2025-07-30 01:28:38,183 - INFO -   秒 42: 处理 13 个新请求
2025-07-30 01:28:38,191 - INFO -   秒 43: 处理 12 个新请求
2025-07-30 01:28:38,193 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:38,193 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:38,197 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:38,198 - INFO -   秒 44: 处理 16 个新请求
2025-07-30 01:28:38,208 - INFO -   秒 45: 处理 11 个新请求
2025-07-30 01:28:38,216 - INFO -   秒 46: 处理 15 个新请求
2025-07-30 01:28:38,226 - INFO -   秒 47: 处理 13 个新请求
2025-07-30 01:28:38,228 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 3885MB, 函数类型: 6
2025-07-30 01:28:38,230 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:38,233 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 70MB，类型: 6
2025-07-30 01:28:38,233 - INFO -   秒 48: 处理 22 个新请求
2025-07-30 01:28:38,242 - INFO -   秒 49: 处理 11 个新请求
2025-07-30 01:28:38,249 - INFO -   秒 50: 处理 9 个新请求
2025-07-30 01:28:38,257 - INFO - 时间槽 14，秒 50: 执行中容器 105，等待请求 0
2025-07-30 01:28:38,257 - INFO -   秒 51: 处理 16 个新请求
2025-07-30 01:28:38,263 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 3885MB, 函数类型: 7
2025-07-30 01:28:38,263 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:28:38,270 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 120MB，类型: 7
2025-07-30 01:28:38,271 - INFO -   秒 52: 处理 12 个新请求
2025-07-30 01:28:38,280 - INFO -   秒 53: 处理 16 个新请求
2025-07-30 01:28:38,291 - INFO -   秒 54: 处理 9 个新请求
2025-07-30 01:28:38,299 - INFO -   秒 55: 处理 12 个新请求
2025-07-30 01:28:38,308 - INFO -   秒 56: 处理 9 个新请求
2025-07-30 01:28:38,310 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 3885MB, 函数类型: 9
2025-07-30 01:28:38,310 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:38,315 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:28:38,324 - INFO - 时间槽 14 结束时，还有 74 个容器正在执行，将继续在后台执行
2025-07-30 01:28:38,326 - INFO - --------处理时间槽 15 的请求，共 872 个--------
2025-07-30 01:28:38,326 - INFO - ----node memomry----
2025-07-30 01:28:38,326 - INFO - node 1 : 10565 MB
2025-07-30 01:28:38,327 - INFO - node 2 : 10320 MB
2025-07-30 01:28:38,327 - INFO - node 3 : 7065 MB
2025-07-30 01:28:38,327 - INFO - node 4 : 9815 MB
2025-07-30 01:28:38,327 - INFO - node 5 : 8310 MB
2025-07-30 01:28:38,327 - INFO - node 6 : 10275 MB
2025-07-30 01:28:38,327 - INFO - node 7 : 10495 MB
2025-07-30 01:28:38,327 - INFO - node 8 : 9485 MB
2025-07-30 01:28:38,327 - INFO - node 9 : 8540 MB
2025-07-30 01:28:38,327 - INFO - node 10 : 6725 MB
2025-07-30 01:28:38,328 - INFO - node 11 : 9605 MB
2025-07-30 01:28:38,328 - INFO - node 12 : 8865 MB
2025-07-30 01:28:38,328 - INFO - node 13 : 6790 MB
2025-07-30 01:28:38,328 - INFO - node 14 : 10340 MB
2025-07-30 01:28:38,328 - INFO - node 15 : 9955 MB
2025-07-30 01:28:38,328 - INFO - node 16 : 8960 MB
2025-07-30 01:28:38,328 - INFO - node 17 : 8805 MB
2025-07-30 01:28:38,328 - INFO - node 18 : 10415 MB
2025-07-30 01:28:38,328 - INFO - node 19 : 9135 MB
2025-07-30 01:28:38,328 - INFO - node 20 : 10145 MB
2025-07-30 01:28:38,328 - INFO - node 21 : 8920 MB
2025-07-30 01:28:38,328 - INFO - node 22 : 7730 MB
2025-07-30 01:28:38,328 - INFO - node 23 : 8490 MB
2025-07-30 01:28:38,328 - INFO - node 24 : 5735 MB
2025-07-30 01:28:38,328 - INFO - node 25 : 7760 MB
2025-07-30 01:28:38,328 - INFO - node 26 : 8325 MB
2025-07-30 01:28:38,328 - INFO - node 27 : 10020 MB
2025-07-30 01:28:38,328 - INFO - node 28 : 6925 MB
2025-07-30 01:28:38,328 - INFO - node 29 : 9580 MB
2025-07-30 01:28:38,328 - INFO - node 30 : 7535 MB
2025-07-30 01:28:38,328 - INFO - node 31 : 6325 MB
2025-07-30 01:28:38,328 - INFO - node 32 : 7690 MB
2025-07-30 01:28:38,328 - INFO - node 33 : 9945 MB
2025-07-30 01:28:38,328 - INFO - node 34 : 10015 MB
2025-07-30 01:28:38,328 - INFO - node 35 : 7270 MB
2025-07-30 01:28:38,328 - INFO - node 36 : 9850 MB
2025-07-30 01:28:38,328 - INFO - node 37 : 8065 MB
2025-07-30 01:28:38,328 - INFO - node 38 : 5520 MB
2025-07-30 01:28:38,328 - INFO - node 39 : 3560 MB
2025-07-30 01:28:38,328 - INFO - node 40 : 8460 MB
2025-07-30 01:28:38,328 - INFO - node 41 : 9910 MB
2025-07-30 01:28:38,328 - INFO - node 42 : 3965 MB
2025-07-30 01:28:38,328 - INFO - node 43 : 6815 MB
2025-07-30 01:28:38,330 - INFO - node 44 : 6290 MB
2025-07-30 01:28:38,330 - INFO - node 45 : 8000 MB
2025-07-30 01:28:38,330 - INFO - node 46 : 10380 MB
2025-07-30 01:28:38,330 - INFO - node 47 : 6190 MB
2025-07-30 01:28:38,330 - INFO - node 48 : 4755 MB
2025-07-30 01:28:38,330 - INFO - node 49 : 9360 MB
2025-07-30 01:28:38,330 - INFO - node 50 : 8595 MB
2025-07-30 01:28:38,330 - INFO - node 51 : 5080 MB
2025-07-30 01:28:38,330 - INFO - node 52 : 7710 MB
2025-07-30 01:28:38,330 - INFO - node 53 : 9940 MB
2025-07-30 01:28:38,330 - INFO - node 54 : 9645 MB
2025-07-30 01:28:38,330 - INFO - node 55 : 8335 MB
2025-07-30 01:28:38,330 - INFO - node 56 : 10205 MB
2025-07-30 01:28:38,330 - INFO - node 57 : 8145 MB
2025-07-30 01:28:38,330 - INFO - node 58 : 3715 MB
2025-07-30 01:28:38,330 - INFO - node 59 : 4405 MB
2025-07-30 01:28:38,330 - INFO - node 60 : 10765 MB
2025-07-30 01:28:38,330 - INFO - node 61 : 4330 MB
2025-07-30 01:28:38,330 - INFO - node 62 : 8570 MB
2025-07-30 01:28:38,330 - INFO - node 63 : 7395 MB
2025-07-30 01:28:38,330 - INFO - node 64 : 8675 MB
2025-07-30 01:28:38,330 - INFO - node 65 : 2290 MB
2025-07-30 01:28:38,330 - INFO - node 66 : 7505 MB
2025-07-30 01:28:38,330 - INFO - node 67 : 9905 MB
2025-07-30 01:28:38,331 - INFO - node 68 : 10850 MB
2025-07-30 01:28:38,331 - INFO - node 69 : 2550 MB
2025-07-30 01:28:38,331 - INFO - node 70 : 10490 MB
2025-07-30 01:28:38,331 - INFO - node 71 : 8290 MB
2025-07-30 01:28:38,331 - INFO - node 72 : 8870 MB
2025-07-30 01:28:38,331 - INFO - node 73 : 9615 MB
2025-07-30 01:28:38,331 - INFO - node 74 : 4445 MB
2025-07-30 01:28:38,331 - INFO - node 75 : 7070 MB
2025-07-30 01:28:38,331 - INFO - node 76 : 7645 MB
2025-07-30 01:28:38,331 - INFO - node 77 : 3085 MB
2025-07-30 01:28:38,331 - INFO - node 78 : 10645 MB
2025-07-30 01:28:38,331 - INFO - node 79 : 4460 MB
2025-07-30 01:28:38,331 - INFO - node 80 : 6050 MB
2025-07-30 01:28:38,331 - INFO - node 81 : 4025 MB
2025-07-30 01:28:38,331 - INFO - node 82 : 9340 MB
2025-07-30 01:28:38,331 - INFO - node 83 : 10215 MB
2025-07-30 01:28:38,331 - INFO - node 84 : 7235 MB
2025-07-30 01:28:38,331 - INFO - node 85 : 3350 MB
2025-07-30 01:28:38,331 - INFO - node 86 : 6110 MB
2025-07-30 01:28:38,331 - INFO - node 87 : 4825 MB
2025-07-30 01:28:38,331 - INFO - node 88 : 5705 MB
2025-07-30 01:28:38,331 - INFO - node 89 : 580 MB
2025-07-30 01:28:38,331 - INFO - node 90 : 3355 MB
2025-07-30 01:28:38,332 - INFO - node 91 : 9625 MB
2025-07-30 01:28:38,332 - INFO - node 92 : 9035 MB
2025-07-30 01:28:38,332 - INFO - node 93 : 9120 MB
2025-07-30 01:28:38,332 - INFO - node 94 : 6760 MB
2025-07-30 01:28:38,332 - INFO - node 95 : 6700 MB
2025-07-30 01:28:38,332 - INFO - node 96 : 3885 MB
2025-07-30 01:28:38,332 - INFO - node 97 : 8655 MB
2025-07-30 01:28:38,332 - INFO - node 98 : 3080 MB
2025-07-30 01:28:38,332 - INFO - node 99 : 10145 MB
2025-07-30 01:28:38,332 - INFO - node 100 : 8725 MB
2025-07-30 01:28:38,332 - INFO - node 101 : 7080 MB
2025-07-30 01:28:38,332 - INFO - node 102 : 8035 MB
2025-07-30 01:28:38,332 - INFO - node 103 : 9350 MB
2025-07-30 01:28:38,332 - INFO - node 104 : 10075 MB
2025-07-30 01:28:38,332 - INFO - node 105 : 9920 MB
2025-07-30 01:28:38,332 - INFO - node 106 : 7810 MB
2025-07-30 01:28:38,332 - INFO - node 107 : 4590 MB
2025-07-30 01:28:38,332 - INFO - node 108 : 10765 MB
2025-07-30 01:28:38,332 - INFO - node 109 : 6655 MB
2025-07-30 01:28:38,332 - INFO - node 110 : 7465 MB
2025-07-30 01:28:38,332 - INFO - node 111 : 2730 MB
2025-07-30 01:28:38,332 - INFO - node 112 : 5410 MB
2025-07-30 01:28:38,332 - INFO - node 113 : 7165 MB
2025-07-30 01:28:38,332 - INFO - node 114 : 1965 MB
2025-07-30 01:28:38,332 - INFO - node 115 : 6690 MB
2025-07-30 01:28:38,332 - INFO - node 116 : 3195 MB
2025-07-30 01:28:38,332 - INFO - node 117 : 9530 MB
2025-07-30 01:28:38,332 - INFO - node 118 : 9745 MB
2025-07-30 01:28:38,332 - INFO - node 119 : 6600 MB
2025-07-30 01:28:38,332 - INFO - node 120 : 9770 MB
2025-07-30 01:28:38,332 - INFO - node 121 : 9870 MB
2025-07-30 01:28:38,332 - INFO - node 122 : 9860 MB
2025-07-30 01:28:38,333 - INFO - node 123 : 9910 MB
2025-07-30 01:28:38,333 - INFO - node 124 : 7840 MB
2025-07-30 01:28:38,333 - INFO - node 125 : 10425 MB
2025-07-30 01:28:38,333 - INFO -   秒 0: 处理 109 个新请求
2025-07-30 01:28:38,363 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:38,363 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:38,369 - INFO - 时间槽 15，秒 0: 执行中容器 183，等待请求 0
2025-07-30 01:28:38,370 - INFO -   秒 1: 处理 5 个新请求
2025-07-30 01:28:38,375 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:38,375 - INFO -   秒 2: 处理 4 个新请求
2025-07-30 01:28:38,381 - INFO -   秒 3: 处理 16 个新请求
2025-07-30 01:28:38,389 - INFO -   秒 4: 处理 13 个新请求
2025-07-30 01:28:38,392 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 3885MB, 函数类型: 2
2025-07-30 01:28:38,392 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:38,397 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:28:38,398 - INFO -   秒 5: 处理 17 个新请求
2025-07-30 01:28:38,406 - INFO -   秒 6: 处理 14 个新请求
2025-07-30 01:28:38,416 - INFO -   秒 7: 处理 13 个新请求
2025-07-30 01:28:38,418 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:38,418 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:38,424 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:38,425 - INFO -   秒 8: 处理 13 个新请求
2025-07-30 01:28:38,434 - INFO -   秒 9: 处理 11 个新请求
2025-07-30 01:28:38,442 - INFO -   秒 10: 处理 18 个新请求
2025-07-30 01:28:38,452 - INFO - 时间槽 15，秒 10: 执行中容器 113，等待请求 0
2025-07-30 01:28:38,452 - INFO -   秒 11: 处理 13 个新请求
2025-07-30 01:28:38,460 - INFO -   秒 12: 处理 17 个新请求
2025-07-30 01:28:38,465 - INFO - DEBUG: 节点96内存检查 - 需要内存: 120.0MB, 可用内存: 3885MB, 函数类型: 7
2025-07-30 01:28:38,465 - INFO - DEBUG: 节点96创建User层容器，减少内存 120MB，类型: 7
2025-07-30 01:28:38,471 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 120MB，类型: 7
2025-07-30 01:28:38,471 - INFO -   秒 13: 处理 12 个新请求
2025-07-30 01:28:38,477 - INFO -   秒 14: 处理 15 个新请求
2025-07-30 01:28:38,488 - INFO -   秒 15: 处理 12 个新请求
2025-07-30 01:28:38,497 - INFO -   秒 16: 处理 15 个新请求
2025-07-30 01:28:38,501 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 3885MB, 函数类型: 9
2025-07-30 01:28:38,501 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:38,506 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:28:38,507 - INFO -   秒 17: 处理 14 个新请求
2025-07-30 01:28:38,515 - INFO -   秒 18: 处理 9 个新请求
2025-07-30 01:28:38,523 - INFO -   秒 19: 处理 18 个新请求
2025-07-30 01:28:38,530 - INFO -   秒 20: 处理 25 个新请求
2025-07-30 01:28:38,536 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 3885MB, 函数类型: 6
2025-07-30 01:28:38,536 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:38,541 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 70MB，类型: 6
2025-07-30 01:28:38,541 - INFO - 时间槽 15，秒 20: 执行中容器 126，等待请求 0
2025-07-30 01:28:38,541 - INFO -   秒 21: 处理 9 个新请求
2025-07-30 01:28:38,550 - INFO -   秒 22: 处理 11 个新请求
2025-07-30 01:28:38,558 - INFO -   秒 23: 处理 18 个新请求
2025-07-30 01:28:38,567 - INFO -   秒 24: 处理 13 个新请求
2025-07-30 01:28:38,571 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:38,573 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:38,576 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:38,576 - INFO -   秒 25: 处理 12 个新请求
2025-07-30 01:28:38,585 - INFO -   秒 26: 处理 18 个新请求
2025-07-30 01:28:38,594 - INFO -   秒 27: 处理 7 个新请求
2025-07-30 01:28:38,600 - INFO -   秒 28: 处理 11 个新请求
2025-07-30 01:28:38,601 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:38,601 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:38,606 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:38,606 - INFO -   秒 29: 处理 23 个新请求
2025-07-30 01:28:38,616 - INFO -   秒 30: 处理 17 个新请求
2025-07-30 01:28:38,623 - INFO - 时间槽 15，秒 30: 执行中容器 120，等待请求 0
2025-07-30 01:28:38,623 - INFO -   秒 31: 处理 8 个新请求
2025-07-30 01:28:38,628 - INFO -   秒 32: 处理 20 个新请求
2025-07-30 01:28:38,634 - INFO - DEBUG: 节点96内存检查 - 需要内存: 360.0MB, 可用内存: 3885MB, 函数类型: 5
2025-07-30 01:28:38,634 - INFO - DEBUG: 节点96创建User层容器，减少内存 360MB，类型: 5
2025-07-30 01:28:38,638 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 360MB，类型: 5
2025-07-30 01:28:38,638 - INFO -   秒 33: 处理 7 个新请求
2025-07-30 01:28:38,644 - INFO -   秒 34: 处理 13 个新请求
2025-07-30 01:28:38,653 - INFO -   秒 35: 处理 18 个新请求
2025-07-30 01:28:38,657 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:38,658 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:38,660 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:38,660 - INFO -   秒 36: 处理 14 个新请求
2025-07-30 01:28:38,667 - INFO -   秒 37: 处理 12 个新请求
2025-07-30 01:28:38,673 - INFO -   秒 38: 处理 15 个新请求
2025-07-30 01:28:38,681 - INFO -   秒 39: 处理 18 个新请求
2025-07-30 01:28:38,685 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 3885MB, 函数类型: 2
2025-07-30 01:28:38,686 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:38,691 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:28:38,692 - INFO -   秒 40: 处理 16 个新请求
2025-07-30 01:28:38,701 - INFO - 时间槽 15，秒 40: 执行中容器 119，等待请求 0
2025-07-30 01:28:38,703 - INFO -   秒 41: 处理 15 个新请求
2025-07-30 01:28:38,715 - INFO -   秒 42: 处理 9 个新请求
2025-07-30 01:28:38,721 - INFO -   秒 43: 处理 12 个新请求
2025-07-30 01:28:38,723 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 3885MB, 函数类型: 2
2025-07-30 01:28:38,724 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:38,726 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:28:38,726 - INFO -   秒 44: 处理 18 个新请求
2025-07-30 01:28:38,735 - INFO -   秒 45: 处理 13 个新请求
2025-07-30 01:28:38,744 - INFO -   秒 46: 处理 8 个新请求
2025-07-30 01:28:38,750 - INFO -   秒 47: 处理 19 个新请求
2025-07-30 01:28:38,754 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 3885MB, 函数类型: 6
2025-07-30 01:28:38,755 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:38,759 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 70MB，类型: 6
2025-07-30 01:28:38,760 - INFO -   秒 48: 处理 17 个新请求
2025-07-30 01:28:38,766 - INFO -   秒 49: 处理 18 个新请求
2025-07-30 01:28:38,775 - INFO -   秒 50: 处理 10 个新请求
2025-07-30 01:28:38,782 - INFO - 时间槽 15，秒 50: 执行中容器 120，等待请求 0
2025-07-30 01:28:38,783 - INFO -   秒 51: 处理 11 个新请求
2025-07-30 01:28:38,786 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:38,787 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:38,791 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:38,791 - INFO -   秒 52: 处理 13 个新请求
2025-07-30 01:28:38,797 - INFO -   秒 53: 处理 14 个新请求
2025-07-30 01:28:38,803 - INFO -   秒 54: 处理 14 个新请求
2025-07-30 01:28:38,814 - INFO -   秒 55: 处理 11 个新请求
2025-07-30 01:28:38,816 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 3885MB, 函数类型: 9
2025-07-30 01:28:38,816 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:38,822 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:28:38,822 - INFO -   秒 56: 处理 7 个新请求
2025-07-30 01:28:38,840 - INFO - 时间槽 15 结束时，还有 79 个容器正在执行，将继续在后台执行
2025-07-30 01:28:38,843 - INFO - --------处理时间槽 16 的请求，共 861 个--------
2025-07-30 01:28:38,843 - INFO - ----node memomry----
2025-07-30 01:28:38,843 - INFO - node 1 : 10565 MB
2025-07-30 01:28:38,843 - INFO - node 2 : 10320 MB
2025-07-30 01:28:38,843 - INFO - node 3 : 7065 MB
2025-07-30 01:28:38,843 - INFO - node 4 : 9815 MB
2025-07-30 01:28:38,843 - INFO - node 5 : 8310 MB
2025-07-30 01:28:38,843 - INFO - node 6 : 10275 MB
2025-07-30 01:28:38,843 - INFO - node 7 : 10495 MB
2025-07-30 01:28:38,843 - INFO - node 8 : 9485 MB
2025-07-30 01:28:38,843 - INFO - node 9 : 8540 MB
2025-07-30 01:28:38,843 - INFO - node 10 : 6725 MB
2025-07-30 01:28:38,843 - INFO - node 11 : 9605 MB
2025-07-30 01:28:38,843 - INFO - node 12 : 8865 MB
2025-07-30 01:28:38,843 - INFO - node 13 : 6790 MB
2025-07-30 01:28:38,843 - INFO - node 14 : 10340 MB
2025-07-30 01:28:38,843 - INFO - node 15 : 9955 MB
2025-07-30 01:28:38,843 - INFO - node 16 : 8960 MB
2025-07-30 01:28:38,843 - INFO - node 17 : 8805 MB
2025-07-30 01:28:38,843 - INFO - node 18 : 10415 MB
2025-07-30 01:28:38,844 - INFO - node 19 : 9135 MB
2025-07-30 01:28:38,844 - INFO - node 20 : 10145 MB
2025-07-30 01:28:38,844 - INFO - node 21 : 8920 MB
2025-07-30 01:28:38,844 - INFO - node 22 : 7730 MB
2025-07-30 01:28:38,844 - INFO - node 23 : 8490 MB
2025-07-30 01:28:38,844 - INFO - node 24 : 5735 MB
2025-07-30 01:28:38,844 - INFO - node 25 : 7760 MB
2025-07-30 01:28:38,844 - INFO - node 26 : 8325 MB
2025-07-30 01:28:38,844 - INFO - node 27 : 10020 MB
2025-07-30 01:28:38,844 - INFO - node 28 : 6925 MB
2025-07-30 01:28:38,844 - INFO - node 29 : 9580 MB
2025-07-30 01:28:38,844 - INFO - node 30 : 7535 MB
2025-07-30 01:28:38,844 - INFO - node 31 : 6325 MB
2025-07-30 01:28:38,845 - INFO - node 32 : 7690 MB
2025-07-30 01:28:38,845 - INFO - node 33 : 9945 MB
2025-07-30 01:28:38,846 - INFO - node 34 : 10015 MB
2025-07-30 01:28:38,846 - INFO - node 35 : 7270 MB
2025-07-30 01:28:38,847 - INFO - node 36 : 9850 MB
2025-07-30 01:28:38,847 - INFO - node 37 : 8065 MB
2025-07-30 01:28:38,847 - INFO - node 38 : 5520 MB
2025-07-30 01:28:38,847 - INFO - node 39 : 3560 MB
2025-07-30 01:28:38,847 - INFO - node 40 : 8460 MB
2025-07-30 01:28:38,847 - INFO - node 41 : 9910 MB
2025-07-30 01:28:38,847 - INFO - node 42 : 3965 MB
2025-07-30 01:28:38,847 - INFO - node 43 : 6815 MB
2025-07-30 01:28:38,847 - INFO - node 44 : 6290 MB
2025-07-30 01:28:38,847 - INFO - node 45 : 8000 MB
2025-07-30 01:28:38,847 - INFO - node 46 : 10380 MB
2025-07-30 01:28:38,847 - INFO - node 47 : 6190 MB
2025-07-30 01:28:38,847 - INFO - node 48 : 4755 MB
2025-07-30 01:28:38,847 - INFO - node 49 : 9565 MB
2025-07-30 01:28:38,847 - INFO - node 50 : 8595 MB
2025-07-30 01:28:38,847 - INFO - node 51 : 5080 MB
2025-07-30 01:28:38,847 - INFO - node 52 : 7710 MB
2025-07-30 01:28:38,847 - INFO - node 53 : 9940 MB
2025-07-30 01:28:38,847 - INFO - node 54 : 9850 MB
2025-07-30 01:28:38,847 - INFO - node 55 : 8335 MB
2025-07-30 01:28:38,847 - INFO - node 56 : 10205 MB
2025-07-30 01:28:38,847 - INFO - node 57 : 8145 MB
2025-07-30 01:28:38,847 - INFO - node 58 : 3715 MB
2025-07-30 01:28:38,847 - INFO - node 59 : 4405 MB
2025-07-30 01:28:38,847 - INFO - node 60 : 10765 MB
2025-07-30 01:28:38,847 - INFO - node 61 : 4330 MB
2025-07-30 01:28:38,847 - INFO - node 62 : 8570 MB
2025-07-30 01:28:38,847 - INFO - node 63 : 7395 MB
2025-07-30 01:28:38,847 - INFO - node 64 : 8675 MB
2025-07-30 01:28:38,847 - INFO - node 65 : 2290 MB
2025-07-30 01:28:38,847 - INFO - node 66 : 7505 MB
2025-07-30 01:28:38,847 - INFO - node 67 : 9905 MB
2025-07-30 01:28:38,848 - INFO - node 68 : 10850 MB
2025-07-30 01:28:38,848 - INFO - node 69 : 2550 MB
2025-07-30 01:28:38,848 - INFO - node 70 : 10490 MB
2025-07-30 01:28:38,848 - INFO - node 71 : 8290 MB
2025-07-30 01:28:38,848 - INFO - node 72 : 8870 MB
2025-07-30 01:28:38,848 - INFO - node 73 : 9615 MB
2025-07-30 01:28:38,848 - INFO - node 74 : 4445 MB
2025-07-30 01:28:38,848 - INFO - node 75 : 7070 MB
2025-07-30 01:28:38,848 - INFO - node 76 : 7645 MB
2025-07-30 01:28:38,848 - INFO - node 77 : 3085 MB
2025-07-30 01:28:38,848 - INFO - node 78 : 10560 MB
2025-07-30 01:28:38,848 - INFO - node 79 : 4460 MB
2025-07-30 01:28:38,848 - INFO - node 80 : 6050 MB
2025-07-30 01:28:38,848 - INFO - node 81 : 4025 MB
2025-07-30 01:28:38,848 - INFO - node 82 : 9340 MB
2025-07-30 01:28:38,848 - INFO - node 83 : 10215 MB
2025-07-30 01:28:38,848 - INFO - node 84 : 7235 MB
2025-07-30 01:28:38,848 - INFO - node 85 : 3350 MB
2025-07-30 01:28:38,848 - INFO - node 86 : 6110 MB
2025-07-30 01:28:38,848 - INFO - node 87 : 4825 MB
2025-07-30 01:28:38,848 - INFO - node 88 : 5705 MB
2025-07-30 01:28:38,848 - INFO - node 89 : 580 MB
2025-07-30 01:28:38,848 - INFO - node 90 : 3355 MB
2025-07-30 01:28:38,848 - INFO - node 91 : 9625 MB
2025-07-30 01:28:38,848 - INFO - node 92 : 9035 MB
2025-07-30 01:28:38,848 - INFO - node 93 : 9120 MB
2025-07-30 01:28:38,848 - INFO - node 94 : 6760 MB
2025-07-30 01:28:38,848 - INFO - node 95 : 6700 MB
2025-07-30 01:28:38,848 - INFO - node 96 : 3885 MB
2025-07-30 01:28:38,848 - INFO - node 97 : 8655 MB
2025-07-30 01:28:38,848 - INFO - node 98 : 3080 MB
2025-07-30 01:28:38,848 - INFO - node 99 : 10145 MB
2025-07-30 01:28:38,848 - INFO - node 100 : 8725 MB
2025-07-30 01:28:38,848 - INFO - node 101 : 7080 MB
2025-07-30 01:28:38,848 - INFO - node 102 : 8035 MB
2025-07-30 01:28:38,848 - INFO - node 103 : 9350 MB
2025-07-30 01:28:38,848 - INFO - node 104 : 10075 MB
2025-07-30 01:28:38,848 - INFO - node 105 : 9920 MB
2025-07-30 01:28:38,848 - INFO - node 106 : 7810 MB
2025-07-30 01:28:38,848 - INFO - node 107 : 4590 MB
2025-07-30 01:28:38,848 - INFO - node 108 : 10765 MB
2025-07-30 01:28:38,849 - INFO - node 109 : 6655 MB
2025-07-30 01:28:38,849 - INFO - node 110 : 7465 MB
2025-07-30 01:28:38,849 - INFO - node 111 : 2730 MB
2025-07-30 01:28:38,849 - INFO - node 112 : 5410 MB
2025-07-30 01:28:38,849 - INFO - node 113 : 7165 MB
2025-07-30 01:28:38,849 - INFO - node 114 : 1965 MB
2025-07-30 01:28:38,849 - INFO - node 115 : 6690 MB
2025-07-30 01:28:38,849 - INFO - node 116 : 3195 MB
2025-07-30 01:28:38,849 - INFO - node 117 : 9530 MB
2025-07-30 01:28:38,849 - INFO - node 118 : 9745 MB
2025-07-30 01:28:38,849 - INFO - node 119 : 6600 MB
2025-07-30 01:28:38,849 - INFO - node 120 : 9770 MB
2025-07-30 01:28:38,849 - INFO - node 121 : 9870 MB
2025-07-30 01:28:38,849 - INFO - node 122 : 9945 MB
2025-07-30 01:28:38,849 - INFO - node 123 : 9910 MB
2025-07-30 01:28:38,849 - INFO - node 124 : 7840 MB
2025-07-30 01:28:38,849 - INFO - node 125 : 10425 MB
2025-07-30 01:28:38,849 - INFO -   秒 0: 处理 110 个新请求
2025-07-30 01:28:38,884 - INFO - DEBUG: 节点96内存检查 - 需要内存: 360.0MB, 可用内存: 3885MB, 函数类型: 5
2025-07-30 01:28:38,884 - INFO - DEBUG: 节点96创建User层容器，减少内存 360MB，类型: 5
2025-07-30 01:28:38,891 - INFO - 时间槽 16，秒 0: 执行中容器 189，等待请求 0
2025-07-30 01:28:38,893 - INFO -   秒 1: 处理 4 个新请求
2025-07-30 01:28:38,901 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 360MB，类型: 5
2025-07-30 01:28:38,902 - INFO -   秒 2: 处理 4 个新请求
2025-07-30 01:28:38,908 - INFO -   秒 3: 处理 16 个新请求
2025-07-30 01:28:38,917 - INFO -   秒 4: 处理 13 个新请求
2025-07-30 01:28:38,919 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 3885MB, 函数类型: 2
2025-07-30 01:28:38,919 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:38,924 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:28:38,924 - INFO -   秒 5: 处理 17 个新请求
2025-07-30 01:28:38,937 - INFO -   秒 6: 处理 15 个新请求
2025-07-30 01:28:38,948 - INFO -   秒 7: 处理 9 个新请求
2025-07-30 01:28:38,956 - INFO -   秒 8: 处理 13 个新请求
2025-07-30 01:28:38,958 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 3885MB, 函数类型: 6
2025-07-30 01:28:38,958 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:38,963 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 70MB，类型: 6
2025-07-30 01:28:38,963 - INFO -   秒 9: 处理 13 个新请求
2025-07-30 01:28:38,971 - INFO -   秒 10: 处理 15 个新请求
2025-07-30 01:28:38,982 - INFO - 时间槽 16，秒 10: 执行中容器 117，等待请求 0
2025-07-30 01:28:38,982 - INFO -   秒 11: 处理 16 个新请求
2025-07-30 01:28:38,993 - INFO -   秒 12: 处理 16 个新请求
2025-07-30 01:28:38,998 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 3885MB, 函数类型: 2
2025-07-30 01:28:38,998 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:39,002 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:28:39,002 - INFO -   秒 13: 处理 11 个新请求
2025-07-30 01:28:39,012 - INFO -   秒 14: 处理 19 个新请求
2025-07-30 01:28:39,021 - INFO -   秒 15: 处理 8 个新请求
2025-07-30 01:28:39,030 - INFO -   秒 16: 处理 17 个新请求
2025-07-30 01:28:39,035 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 3885MB, 函数类型: 2
2025-07-30 01:28:39,035 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:39,041 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:28:39,041 - INFO -   秒 17: 处理 13 个新请求
2025-07-30 01:28:39,051 - INFO -   秒 18: 处理 11 个新请求
2025-07-30 01:28:39,058 - INFO -   秒 19: 处理 21 个新请求
2025-07-30 01:28:39,068 - INFO -   秒 20: 处理 16 个新请求
2025-07-30 01:28:39,072 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:39,072 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:39,076 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:39,076 - INFO - 时间槽 16，秒 20: 执行中容器 123，等待请求 0
2025-07-30 01:28:39,076 - INFO -   秒 21: 处理 15 个新请求
2025-07-30 01:28:39,087 - INFO -   秒 22: 处理 12 个新请求
2025-07-30 01:28:39,093 - INFO -   秒 23: 处理 13 个新请求
2025-07-30 01:28:39,101 - INFO -   秒 24: 处理 13 个新请求
2025-07-30 01:28:39,104 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:39,104 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:39,110 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:39,110 - INFO -   秒 25: 处理 15 个新请求
2025-07-30 01:28:39,119 - INFO -   秒 26: 处理 17 个新请求
2025-07-30 01:28:39,130 - INFO -   秒 27: 处理 6 个新请求
2025-07-30 01:28:39,136 - INFO -   秒 28: 处理 10 个新请求
2025-07-30 01:28:39,139 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:39,139 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:39,146 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:39,146 - INFO -   秒 29: 处理 30 个新请求
2025-07-30 01:28:39,157 - INFO -   秒 30: 处理 8 个新请求
2025-07-30 01:28:39,163 - INFO - 时间槽 16，秒 30: 执行中容器 122，等待请求 0
2025-07-30 01:28:39,163 - INFO -   秒 31: 处理 7 个新请求
2025-07-30 01:28:39,172 - INFO -   秒 32: 处理 18 个新请求
2025-07-30 01:28:39,175 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 3885MB, 函数类型: 2
2025-07-30 01:28:39,175 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:39,180 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:28:39,181 - INFO -   秒 33: 处理 16 个新请求
2025-07-30 01:28:39,187 - INFO -   秒 34: 处理 8 个新请求
2025-07-30 01:28:39,193 - INFO -   秒 35: 处理 15 个新请求
2025-07-30 01:28:39,204 - INFO -   秒 36: 处理 12 个新请求
2025-07-30 01:28:39,210 - INFO -   秒 37: 处理 18 个新请求
2025-07-30 01:28:39,214 - INFO - DEBUG: 节点96内存检查 - 需要内存: 60.0MB, 可用内存: 3885MB, 函数类型: 3
2025-07-30 01:28:39,214 - INFO - DEBUG: 节点96创建User层容器，减少内存 60MB，类型: 3
2025-07-30 01:28:39,218 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 60MB，类型: 3
2025-07-30 01:28:39,219 - INFO -   秒 38: 处理 15 个新请求
2025-07-30 01:28:39,226 - INFO -   秒 39: 处理 16 个新请求
2025-07-30 01:28:39,235 - INFO -   秒 40: 处理 15 个新请求
2025-07-30 01:28:39,243 - INFO - 时间槽 16，秒 40: 执行中容器 126，等待请求 0
2025-07-30 01:28:39,244 - INFO -   秒 41: 处理 12 个新请求
2025-07-30 01:28:39,248 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 3885MB, 函数类型: 2
2025-07-30 01:28:39,248 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:39,251 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:28:39,251 - INFO -   秒 42: 处理 12 个新请求
2025-07-30 01:28:39,258 - INFO -   秒 43: 处理 16 个新请求
2025-07-30 01:28:39,264 - INFO -   秒 44: 处理 10 个新请求
2025-07-30 01:28:39,270 - INFO -   秒 45: 处理 15 个新请求
2025-07-30 01:28:39,274 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 3885MB, 函数类型: 2
2025-07-30 01:28:39,274 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:39,278 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:28:39,280 - INFO -   秒 46: 处理 13 个新请求
2025-07-30 01:28:39,290 - INFO -   秒 47: 处理 13 个新请求
2025-07-30 01:28:39,299 - INFO -   秒 48: 处理 15 个新请求
2025-07-30 01:28:39,307 - INFO -   秒 49: 处理 19 个新请求
2025-07-30 01:28:39,318 - INFO -   秒 50: 处理 12 个新请求
2025-07-30 01:28:39,319 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:39,319 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:39,323 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:39,324 - INFO - 时间槽 16，秒 50: 执行中容器 118，等待请求 0
2025-07-30 01:28:39,324 - INFO -   秒 51: 处理 9 个新请求
2025-07-30 01:28:39,330 - INFO -   秒 52: 处理 15 个新请求
2025-07-30 01:28:39,337 - INFO -   秒 53: 处理 12 个新请求
2025-07-30 01:28:39,344 - INFO -   秒 54: 处理 14 个新请求
2025-07-30 01:28:39,348 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 3885MB, 函数类型: 6
2025-07-30 01:28:39,348 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:39,352 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 70MB，类型: 6
2025-07-30 01:28:39,352 - INFO -   秒 55: 处理 12 个新请求
2025-07-30 01:28:39,358 - INFO -   秒 56: 处理 5 个新请求
2025-07-30 01:28:39,362 - INFO -   秒 57: 处理 1 个新请求
2025-07-30 01:28:39,370 - INFO - 时间槽 16 结束时，还有 81 个容器正在执行，将继续在后台执行
2025-07-30 01:28:39,372 - INFO - --------处理时间槽 17 的请求，共 886 个--------
2025-07-30 01:28:39,372 - INFO - ----node memomry----
2025-07-30 01:28:39,372 - INFO - node 1 : 10565 MB
2025-07-30 01:28:39,372 - INFO - node 2 : 10320 MB
2025-07-30 01:28:39,372 - INFO - node 3 : 7065 MB
2025-07-30 01:28:39,372 - INFO - node 4 : 9815 MB
2025-07-30 01:28:39,372 - INFO - node 5 : 8310 MB
2025-07-30 01:28:39,372 - INFO - node 6 : 10275 MB
2025-07-30 01:28:39,372 - INFO - node 7 : 10290 MB
2025-07-30 01:28:39,372 - INFO - node 8 : 9485 MB
2025-07-30 01:28:39,372 - INFO - node 9 : 8540 MB
2025-07-30 01:28:39,374 - INFO - node 10 : 6725 MB
2025-07-30 01:28:39,374 - INFO - node 11 : 9605 MB
2025-07-30 01:28:39,374 - INFO - node 12 : 8865 MB
2025-07-30 01:28:39,374 - INFO - node 13 : 6790 MB
2025-07-30 01:28:39,374 - INFO - node 14 : 10340 MB
2025-07-30 01:28:39,374 - INFO - node 15 : 9955 MB
2025-07-30 01:28:39,374 - INFO - node 16 : 8960 MB
2025-07-30 01:28:39,374 - INFO - node 17 : 8805 MB
2025-07-30 01:28:39,374 - INFO - node 18 : 10415 MB
2025-07-30 01:28:39,374 - INFO - node 19 : 9135 MB
2025-07-30 01:28:39,374 - INFO - node 20 : 10145 MB
2025-07-30 01:28:39,374 - INFO - node 21 : 8920 MB
2025-07-30 01:28:39,374 - INFO - node 22 : 7730 MB
2025-07-30 01:28:39,374 - INFO - node 23 : 8490 MB
2025-07-30 01:28:39,374 - INFO - node 24 : 5735 MB
2025-07-30 01:28:39,374 - INFO - node 25 : 7760 MB
2025-07-30 01:28:39,374 - INFO - node 26 : 8325 MB
2025-07-30 01:28:39,374 - INFO - node 27 : 10020 MB
2025-07-30 01:28:39,374 - INFO - node 28 : 6925 MB
2025-07-30 01:28:39,374 - INFO - node 29 : 9580 MB
2025-07-30 01:28:39,374 - INFO - node 30 : 7535 MB
2025-07-30 01:28:39,374 - INFO - node 31 : 6325 MB
2025-07-30 01:28:39,374 - INFO - node 32 : 7690 MB
2025-07-30 01:28:39,374 - INFO - node 33 : 9945 MB
2025-07-30 01:28:39,374 - INFO - node 34 : 10015 MB
2025-07-30 01:28:39,374 - INFO - node 35 : 7270 MB
2025-07-30 01:28:39,374 - INFO - node 36 : 9850 MB
2025-07-30 01:28:39,374 - INFO - node 37 : 8065 MB
2025-07-30 01:28:39,374 - INFO - node 38 : 5520 MB
2025-07-30 01:28:39,374 - INFO - node 39 : 3560 MB
2025-07-30 01:28:39,374 - INFO - node 40 : 8460 MB
2025-07-30 01:28:39,374 - INFO - node 41 : 10115 MB
2025-07-30 01:28:39,374 - INFO - node 42 : 3965 MB
2025-07-30 01:28:39,375 - INFO - node 43 : 6815 MB
2025-07-30 01:28:39,375 - INFO - node 44 : 6290 MB
2025-07-30 01:28:39,375 - INFO - node 45 : 8000 MB
2025-07-30 01:28:39,375 - INFO - node 46 : 10380 MB
2025-07-30 01:28:39,375 - INFO - node 47 : 6190 MB
2025-07-30 01:28:39,375 - INFO - node 48 : 4755 MB
2025-07-30 01:28:39,375 - INFO - node 49 : 9565 MB
2025-07-30 01:28:39,375 - INFO - node 50 : 8595 MB
2025-07-30 01:28:39,375 - INFO - node 51 : 5080 MB
2025-07-30 01:28:39,375 - INFO - node 52 : 7710 MB
2025-07-30 01:28:39,375 - INFO - node 53 : 9940 MB
2025-07-30 01:28:39,375 - INFO - node 54 : 9850 MB
2025-07-30 01:28:39,375 - INFO - node 55 : 8335 MB
2025-07-30 01:28:39,375 - INFO - node 56 : 10205 MB
2025-07-30 01:28:39,375 - INFO - node 57 : 8145 MB
2025-07-30 01:28:39,375 - INFO - node 58 : 3715 MB
2025-07-30 01:28:39,375 - INFO - node 59 : 4405 MB
2025-07-30 01:28:39,375 - INFO - node 60 : 10765 MB
2025-07-30 01:28:39,375 - INFO - node 61 : 4330 MB
2025-07-30 01:28:39,375 - INFO - node 62 : 8570 MB
2025-07-30 01:28:39,375 - INFO - node 63 : 7395 MB
2025-07-30 01:28:39,375 - INFO - node 64 : 8675 MB
2025-07-30 01:28:39,375 - INFO - node 65 : 2290 MB
2025-07-30 01:28:39,375 - INFO - node 66 : 7505 MB
2025-07-30 01:28:39,375 - INFO - node 67 : 9905 MB
2025-07-30 01:28:39,375 - INFO - node 68 : 10765 MB
2025-07-30 01:28:39,375 - INFO - node 69 : 2550 MB
2025-07-30 01:28:39,375 - INFO - node 70 : 10490 MB
2025-07-30 01:28:39,375 - INFO - node 71 : 8290 MB
2025-07-30 01:28:39,375 - INFO - node 72 : 8870 MB
2025-07-30 01:28:39,375 - INFO - node 73 : 9615 MB
2025-07-30 01:28:39,375 - INFO - node 74 : 4445 MB
2025-07-30 01:28:39,375 - INFO - node 75 : 7070 MB
2025-07-30 01:28:39,375 - INFO - node 76 : 7645 MB
2025-07-30 01:28:39,375 - INFO - node 77 : 3085 MB
2025-07-30 01:28:39,375 - INFO - node 78 : 10560 MB
2025-07-30 01:28:39,375 - INFO - node 79 : 4460 MB
2025-07-30 01:28:39,375 - INFO - node 80 : 6050 MB
2025-07-30 01:28:39,375 - INFO - node 81 : 4025 MB
2025-07-30 01:28:39,375 - INFO - node 82 : 9425 MB
2025-07-30 01:28:39,375 - INFO - node 83 : 10215 MB
2025-07-30 01:28:39,375 - INFO - node 84 : 7235 MB
2025-07-30 01:28:39,375 - INFO - node 85 : 3350 MB
2025-07-30 01:28:39,375 - INFO - node 86 : 6110 MB
2025-07-30 01:28:39,375 - INFO - node 87 : 4825 MB
2025-07-30 01:28:39,375 - INFO - node 88 : 5705 MB
2025-07-30 01:28:39,375 - INFO - node 89 : 580 MB
2025-07-30 01:28:39,375 - INFO - node 90 : 3355 MB
2025-07-30 01:28:39,375 - INFO - node 91 : 9625 MB
2025-07-30 01:28:39,375 - INFO - node 92 : 9035 MB
2025-07-30 01:28:39,376 - INFO - node 93 : 9120 MB
2025-07-30 01:28:39,376 - INFO - node 94 : 6760 MB
2025-07-30 01:28:39,376 - INFO - node 95 : 6700 MB
2025-07-30 01:28:39,376 - INFO - node 96 : 3885 MB
2025-07-30 01:28:39,376 - INFO - node 97 : 8655 MB
2025-07-30 01:28:39,376 - INFO - node 98 : 3080 MB
2025-07-30 01:28:39,376 - INFO - node 99 : 10145 MB
2025-07-30 01:28:39,376 - INFO - node 100 : 8725 MB
2025-07-30 01:28:39,376 - INFO - node 101 : 7080 MB
2025-07-30 01:28:39,376 - INFO - node 102 : 8035 MB
2025-07-30 01:28:39,376 - INFO - node 103 : 9350 MB
2025-07-30 01:28:39,376 - INFO - node 104 : 10075 MB
2025-07-30 01:28:39,376 - INFO - node 105 : 9920 MB
2025-07-30 01:28:39,376 - INFO - node 106 : 7810 MB
2025-07-30 01:28:39,376 - INFO - node 107 : 4590 MB
2025-07-30 01:28:39,376 - INFO - node 108 : 10765 MB
2025-07-30 01:28:39,376 - INFO - node 109 : 6655 MB
2025-07-30 01:28:39,376 - INFO - node 110 : 7465 MB
2025-07-30 01:28:39,376 - INFO - node 111 : 2730 MB
2025-07-30 01:28:39,376 - INFO - node 112 : 5410 MB
2025-07-30 01:28:39,376 - INFO - node 113 : 7165 MB
2025-07-30 01:28:39,376 - INFO - node 114 : 1965 MB
2025-07-30 01:28:39,376 - INFO - node 115 : 6690 MB
2025-07-30 01:28:39,376 - INFO - node 116 : 3195 MB
2025-07-30 01:28:39,376 - INFO - node 117 : 9530 MB
2025-07-30 01:28:39,376 - INFO - node 118 : 9745 MB
2025-07-30 01:28:39,376 - INFO - node 119 : 6600 MB
2025-07-30 01:28:39,376 - INFO - node 120 : 9975 MB
2025-07-30 01:28:39,376 - INFO - node 121 : 9870 MB
2025-07-30 01:28:39,376 - INFO - node 122 : 9740 MB
2025-07-30 01:28:39,376 - INFO - node 123 : 9910 MB
2025-07-30 01:28:39,376 - INFO - node 124 : 7840 MB
2025-07-30 01:28:39,376 - INFO - node 125 : 10425 MB
2025-07-30 01:28:39,377 - INFO -   秒 0: 处理 108 个新请求
2025-07-30 01:28:39,395 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:39,395 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:39,400 - INFO - 时间槽 17，秒 0: 执行中容器 189，等待请求 0
2025-07-30 01:28:39,400 - INFO -   秒 1: 处理 5 个新请求
2025-07-30 01:28:39,407 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:39,408 - INFO -   秒 2: 处理 2 个新请求
2025-07-30 01:28:39,413 - INFO -   秒 3: 处理 20 个新请求
2025-07-30 01:28:39,416 - INFO - DEBUG: 节点96内存检查 - 需要内存: 70.0MB, 可用内存: 3885MB, 函数类型: 6
2025-07-30 01:28:39,417 - INFO - DEBUG: 节点96创建User层容器，减少内存 70MB，类型: 6
2025-07-30 01:28:39,420 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 70MB，类型: 6
2025-07-30 01:28:39,420 - INFO -   秒 4: 处理 12 个新请求
2025-07-30 01:28:39,425 - INFO -   秒 5: 处理 19 个新请求
2025-07-30 01:28:39,434 - INFO -   秒 6: 处理 12 个新请求
2025-07-30 01:28:39,440 - INFO -   秒 7: 处理 14 个新请求
2025-07-30 01:28:39,443 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:39,443 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:39,448 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:39,448 - INFO -   秒 8: 处理 8 个新请求
2025-07-30 01:28:39,455 - INFO -   秒 9: 处理 15 个新请求
2025-07-30 01:28:39,464 - INFO -   秒 10: 处理 18 个新请求
2025-07-30 01:28:39,470 - INFO - 时间槽 17，秒 10: 执行中容器 121，等待请求 0
2025-07-30 01:28:39,470 - INFO -   秒 11: 处理 14 个新请求
2025-07-30 01:28:39,472 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 3885MB, 函数类型: 2
2025-07-30 01:28:39,472 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:39,477 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:28:39,478 - INFO -   秒 12: 处理 16 个新请求
2025-07-30 01:28:39,485 - INFO -   秒 13: 处理 13 个新请求
2025-07-30 01:28:39,492 - INFO -   秒 14: 处理 20 个新请求
2025-07-30 01:28:39,498 - INFO -   秒 15: 处理 12 个新请求
2025-07-30 01:28:39,501 - INFO - DEBUG: 节点96内存检查 - 需要内存: 65.0MB, 可用内存: 3885MB, 函数类型: 2
2025-07-30 01:28:39,501 - INFO - DEBUG: 节点96创建User层容器，减少内存 65MB，类型: 2
2025-07-30 01:28:39,503 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 65MB，类型: 2
2025-07-30 01:28:39,504 - INFO -   秒 16: 处理 8 个新请求
2025-07-30 01:28:39,508 - INFO -   秒 17: 处理 15 个新请求
2025-07-30 01:28:39,517 - INFO -   秒 18: 处理 10 个新请求
2025-07-30 01:28:39,524 - INFO -   秒 19: 处理 26 个新请求
2025-07-30 01:28:39,533 - INFO -   秒 20: 处理 19 个新请求
2025-07-30 01:28:39,537 - INFO - DEBUG: 节点96内存检查 - 需要内存: 50.0MB, 可用内存: 3885MB, 函数类型: 1
2025-07-30 01:28:39,537 - INFO - DEBUG: 节点96创建User层容器，减少内存 50MB，类型: 1
2025-07-30 01:28:39,542 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 50MB，类型: 1
2025-07-30 01:28:39,542 - INFO - 时间槽 17，秒 20: 执行中容器 133，等待请求 0
2025-07-30 01:28:39,542 - INFO -   秒 21: 处理 14 个新请求
2025-07-30 01:28:39,549 - INFO -   秒 22: 处理 7 个新请求
2025-07-30 01:28:39,553 - INFO -   秒 23: 处理 16 个新请求
2025-07-30 01:28:39,558 - INFO -   秒 24: 处理 22 个新请求
2025-07-30 01:28:39,562 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:39,564 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
2025-07-30 01:28:39,567 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 40MB，类型: 4
2025-07-30 01:28:39,568 - INFO -   秒 25: 处理 8 个新请求
2025-07-30 01:28:39,572 - INFO -   秒 26: 处理 12 个新请求
2025-07-30 01:28:39,577 - INFO -   秒 27: 处理 12 个新请求
2025-07-30 01:28:39,584 - INFO -   秒 28: 处理 13 个新请求
2025-07-30 01:28:39,586 - INFO - DEBUG: 节点96内存检查 - 需要内存: 90.0MB, 可用内存: 3885MB, 函数类型: 9
2025-07-30 01:28:39,587 - INFO - DEBUG: 节点96创建User层容器，减少内存 90MB，类型: 9
2025-07-30 01:28:39,593 - INFO - DEBUG: 节点96 User层容器超时降级，释放内存 90MB，类型: 9
2025-07-30 01:28:39,593 - INFO -   秒 29: 处理 24 个新请求
2025-07-30 01:28:39,605 - INFO -   秒 30: 处理 15 个新请求
2025-07-30 01:28:39,612 - INFO - 时间槽 17，秒 30: 执行中容器 132，等待请求 0
2025-07-30 01:28:39,612 - INFO -   秒 31: 处理 14 个新请求
2025-07-30 01:28:39,614 - INFO - DEBUG: 节点96内存检查 - 需要内存: 40.0MB, 可用内存: 3885MB, 函数类型: 4
2025-07-30 01:28:39,614 - INFO - DEBUG: 节点96创建User层容器，减少内存 40MB，类型: 4
