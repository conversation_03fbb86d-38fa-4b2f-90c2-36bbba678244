# 缓存性能模拟比较

这个项目包含了运行和比较不同缓存方法性能的脚本。

## 文件说明

- `simulation.py`：主模拟程序
- `run_simulations.py`：运行不同缓存方法模拟的脚本
- `run_compare.py`：比较不同缓存方法结果的脚本
- `run_and_compare.py`：一键运行模拟并比较结果的脚本

## 使用方法

### 一键运行并比较

最简单的方法是运行主脚本：

```bash
python run_and_compare.py
```

这将依次运行以下步骤：

1. 运行三种缓存方法（LayerCache、CrossEdge和OpenWhisk）的模拟
2. 比较结果并生成对比表格

### 分步运行

如果需要分步运行，可以：

1. 仅运行模拟：

```bash
python run_simulations.py
```

2. 仅比较已有结果：

```bash
python run_compare.py [--beta BETA] [--redu_factor REDU_FACTOR]
```

参数说明：

- `--beta`：beta参数值，0.5, 1.0, 1.5
- `--redu_factor`：reduction factor参数值，默认为10

## 结果文件

模拟运行后，结果文件将保存在 `./result/`目录下：

- `LayerCache-{beta}-{redu_factor}.csv`：LayerCache方法的结果
- `CrossEdge-{beta}-{redu_factor}.csv`：CrossEdge方法的结果
- `OpenWhisk-{beta}-{redu_factor}.csv`：OpenWhisk方法的结果

其中，`{beta}`和 `{redu_factor}`分别表示模拟参数中的beta值和reduction factor值。

## 关键性能指标

比较中的主要关注指标：

- **总请求数(total_req_count)**：模拟中的总请求数
- **服务请求数(served_req_count)**：成功服务的请求数，越高越好
- **冷启动请求数(cold_req_count)**：需要冷启动的请求数，越低越好
- **冷启动频率(cold_start_frequency)**：冷启动请求数占总请求数的比例，越低越好
- **加权响应时间(weighted_response_time)**：考虑了服务成功率的响应时间，越低越好
