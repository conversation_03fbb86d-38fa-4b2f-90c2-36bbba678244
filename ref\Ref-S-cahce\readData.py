#!/usr/bin/env python3
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import math
import csv

## 从Azure 2021年数据集中提取特定函数的调用次数
def read_azure_2021(file_name = 'AzureFunctionTrace.txt', func_id = '48cc770d590d3c5a7691b3b4e9302f82ec3be5ddc2a037d94ad2e76f44dd8946'):
    # 读取文件内容
    with open(file_name,'r') as f:
        contents = f.readlines()
    app = []          # 应用ID列表
    func = []         # 函数ID列表
    timestamp = []    # 时间戳列表
    duration = []     # 持续时间列表
    startTime = []    # 开始时间列表
    roundStartTime = [] # 取整的开始时间列表
    # 时间槽大小为1209600（两周的分钟数）
    count  = [0]*1209600

    # 遍历每一行数据（跳过标题行）
    for index in range(1,len(contents)):
        parameters = contents[index].split(",")
        app.append(parameters[0])
        func.append(parameters[1])
        timestamp.append(float(parameters[2]))
        duration.append(float(parameters[3]))
        startTime.append(float(parameters[2])-float(parameters[3]))
        roundStartTime.append(int(math.floor(float(parameters[2]) - float(parameters[3]))))
        # 如果函数ID匹配，在相应时间槽增加计数
        if parameters[1] == func_id:
            count[roundStartTime[-1]] += 1

    # 创建DataFrame并返回
    data = pd.DataFrame(np.array([count]))
    data = data.transpose()
    data.columns = ['count']

    return data

## 从Azure 2019年数据集中读取特定函数的调用数据
def read_azure_2019(file_name, function_id):
    # 读取文件内容
    with open(file_name,'r') as f:
        contents = f.readlines()
    owner = []      # 所有者列表
    app = []        # 应用ID列表
    function = []   # 函数ID列表
    trigger = []    # 触发器列表
    time_slot = []  # 时间槽数据

    # 查找特定函数ID的数据行
    for index in range(1,len(contents)):
        parameters = contents[index].split(",")
        if parameters[2] == function_id:
            owner.append(parameters[0])
            app.append(parameters[1])
            function.append(parameters[2])
            trigger.append(parameters[3])
            time_slot = parameters[4:]  # 获取时间槽数据（每分钟的调用次数）
            break

    # 创建DataFrame并返回
    data = pd.DataFrame(np.array([time_slot], dtype = np.float32))
    # test_data_size = 120960
    data = data.transpose()
    data.columns = ['Invocation']

    return data

## 读取处理后的CSV文件（默认为top 4应用的数据）
def read_file(file_name = './request/final/sortedd01.csv'):
    # 以下是早期版本的代码，现已注释掉
    # with open(file_name,'r') as f:
    #     contents = f.readlines()
    # app = []
    # totalNum = []
    # time_slot = []
    # data = pd.DataFrame( dtype = np.float32)
    # the size of all time slot
    # count  = [0]*1209600
    

    # titles = contents[0]
    # print(titles)
    # for index in range(1,len(contents)):
    #     parameters = contents[index].split(",")
    #     print(parameters[0])
    #     app.append(parameters[0])
    #     totalNum.append(parameters[1])
    #     time_slot = parameters[2:]
    #     tmp = pd.DataFrame(np.array([time_slot], dtype = np.float32))
    #     tmp = tmp.transpose()
    #     pd.concat([tmp, data])

    # data.columns = titles

    # 读取CSV文件并转置（行列互换）
    df = pd.read_csv(file_name) 
    df = df.transpose()
    # print(df)

    return df

## 合并数据并按频率排序
def consolidate_data(file_names, output_name):
    # 处理每个输入文件
    for file_name in file_names:
        with open(file_name,'r') as f:
            contents = f.readlines()

        data_map = {}  # 用于存储应用ID与时间槽数据的映射
        time_slot = [1440]  # 一天的分钟数
        
        # 遍历每行数据（跳过标题行）
        for index in range(1,len(contents)):
            parameters = contents[index].split(",")
            app_id = parameters[1]  # 获取应用ID
            time = parameters[4:]   # 获取时间槽数据
            # 将字符串转换为整数
            time_int = list(map(int, time))
            time_slot = np.array(time_int)
                
            # 如果是新的应用ID，直接添加
            if data_map.get(app_id) is None:
                data_map.update({app_id : time_slot})
            # 如果应用ID已存在，累加调用次数
            else:
                current_time_slot = data_map[app_id]
                update_time_slot = np.add(time_slot, current_time_slot)
                data_map.update({app_id : update_time_slot})

    ## 将数据写入CSV文件
    f = open(output_name, 'w')
    writer = csv.writer(f)

    # 写入表头
    f.write('App')
    f.write(',')
    f.write('totalNumber')
    f.write(',')
    time_slot_index = list(np.arange(1,1440+1))  # 创建1到1440的时间槽索引
    writer.writerow(time_slot_index)

    # 写入每个应用的数据
    for key, value in data_map.items():
        f.write(key)  # 应用ID
        f.write(',')
        f.write("%s" % np.sum(value))  # 总调用次数
        f.write(',')
        writer.writerow(value)  # 每分钟的调用次数
    f.close()

## 工具函数：将数据写入CSV文件
def write_file(output_name, data_map):
     ## 写入数据到CSV文件
    f = open(output_name, 'w')
    writer = csv.writer(f)

    # 写入表头
    f.write('App')
    f.write(',')
    f.write('totalNumber')
    f.write(',')
    time_slot_index = list(np.arange(1,1440+1))  # 创建1到1440的时间槽索引
    writer.writerow(time_slot_index)

    # 写入每个应用的数据
    for key, value in data_map.items():
        f.write(key)  # 应用ID
        f.write(',')
        f.write("%s" % np.sum(value))  # 总调用次数
        f.write(',')
        writer.writerow(value)  # 每分钟的调用次数
    f.close()

## 读取并合并13个文件的数据，计算每个应用的总调用次数
def consolidate_all_files(output_name):
    file_names =[]
    # 生成13天的文件名
    for i in range(1,14):
        if i < 10:
            file_index = '0'+str(i)
        else:
            file_index = str(i)
        file_name = './request/invocations_per_function_md.anon.d' + file_index + '.csv'
        print(file_name)
        file_names.append(file_name)
    
    # 合并数据
    consolidate_data(file_names, output_name)

## 根据调用次数对应用进行排序
def sort_data(file_name, output_name):
    # 读取CSV文件
    dataFrame = pd.read_csv(file_name)

    print(dataFrame.head(3))  # 打印排序前的前3行

    # 根据totalNumber列降序排序
    dataFrame = dataFrame.sort_values(by = ['totalNumber'], ascending=False, na_position = 'last')
   
    # 选取前4名并写入CSV文件
    dataFrame.head(4).to_csv(output_name, index = False)
    
    print('after sorting')
    print(dataFrame.head(3))  # 打印排序后的前3行

## 在所有文件中查找特定应用ID的数据
def find_app(file_names, ids):
    # 遍历每个文件
    file_count = 1
    for file_name in file_names:
        with open(file_name,'r') as f:
            contents = f.readlines()

        data_map = {}  # 用于存储应用ID与时间槽数据的映射
        time_slot = [1440]  # 一天的分钟数
        
        # 遍历每行数据（跳过标题行）
        for index in range(1,len(contents)):
            parameters = contents[index].split(",")
            app_id = parameters[1]  # 获取应用ID
            # 如果应用ID不在目标列表中，跳过
            if app_id not in ids:
                continue
            else:
                time = parameters[4:]  # 获取时间槽数据
                # 将字符串转换为整数
                time_int = list(map(int, time))
                time_slot = np.array(time_int)
                
                # 如果是新的应用ID，直接添加
                if data_map.get(app_id) is None:
                    data_map.update({app_id : time_slot})
                # 如果应用ID已存在，累加调用次数
                else:
                    current_time_slot = data_map[app_id]
                    update_time_slot = np.add(time_slot, current_time_slot)
                    data_map.update({app_id : update_time_slot})
        
        # 将数据写入CSV文件
        write_file('./request/final/'+ 'invocations_per_function_md.anon.d'+ str(file_count) + '.csv', data_map)
        file_count += 1


## 从排序后的CSV文件中获取调用次数最多的k个应用ID
def get_top_apps(file_name, k):
     df = pd.read_csv(file_name)
     app_ids = df.loc[:, 'App'].tolist()  # 提取应用ID列表

     return app_ids[: k]  # 返回前k个应用ID

## 查找最常用的应用，并生成只包含这些应用的13个文件
def find_apps():
    # 获取调用次数最多的4个应用ID
    app_ids = get_top_apps('/request/final/sortedd01.csv', 4)

    file_names =[]
    # 生成13天的文件名
    for i in range(1,14):
        if i < 10:
            file_index = '0'+str(i)
        else:
            file_index = str(i)
        file_name = './request/invocations_per_function_md.anon.d' + file_index + '.csv'
        print(file_name)
        file_names.append(file_name)

    # 在所有文件中查找特定的应用ID
    find_app(file_names, app_ids)

## 主程序：合并13个文件的数据
## 注释: consolidate_all_files('./request/request_data_set.csv')

## 创建调用次数最多的前4个应用的数据集
def create_top_4():
    filenames = []
    # 只使用第一天的数据
    filename = './request/invocations_per_function_md.anon.d01.csv'
    filenames.append(filename)
    # 合并数据
    consolidate_data(filenames, './request/final/mergedd01.csv')
    # 排序并选取前4个应用
    sort_data('./request/final/mergedd01.csv', './request/final/sortedd01.csv')

## 注释: find_apps()

## 获取使用最多的应用，输出仅包含这些应用的13个文件

## 主函数
def main():
    # 注释: create_top_4()
    # 读取排序后的数据文件
    read_file()

## 程序入口
if __name__ == "__main__":
   main()